{"private": true, "name": "console", "version": "2.0.0", "description": "Administration Portal for myDevices PaaS.", "author": "myDevices Team <<EMAIL>>", "license": "UNLICENSED", "type": "module", "sideEffects": true, "scripts": {"knip": "knip", "build": "tsc && vite build", "dev": "NODE_ENV=development tsx server", "start": "tsx server", "check": "tsc", "lint": "tsc && biome check --write .", "format": "biome check --fix --unsafe", "test": "TZ=UTC vitest --reporter=dot", "coverage": "TZ=UTC vitest run --coverage", "clean": "rimraf build coverage dev-dist .vscode/*.log stats.html temp node_modules/.vite", "gen:label": "tsx cli/white-label", "gen:label:update": "tsx cli/white-label-update", "gen:inherit": "tsx cli/inherit-label", "gen:assets": "tsx cli/pwa-assets", "assets:download:bulk": "tsx cli/assets-download-bulk.ts", "store:theme": "tsx cli/bulk-store-theme.ts", "store:theme:org": "tsx cli/bulk-store-theme-org.ts", "store:users": "tsx cli/bulk-store-users.ts", "store:users:org": "tsx cli/bulk-store-users-org.ts", "s3:upload": "tsx cli/s3-upload", "theme": "tsx cli/theme-update.ts", "theme:download": "tsx cli/theme-download.ts", "theme:download:bulk": "tsx cli/theme-download-bulk.ts", "find:bitmap-svg": "tsx cli/find-bitmap-svg.ts", "check:embed-img": "tsx cli/check-embed-img.ts"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version"]}, "engines": {"node": ">=22"}, "dependencies": {"@ark-ui/react": "^5.13.0", "@aws-sdk/client-s3": "^3.826.0", "@aws-sdk/lib-storage": "^3.826.0", "@casl/ability": "^6.7.3", "@chakra-ui/react": "2.8.2", "@chakra-ui/system": "^2.6.2", "@chakra-ui/theme-tools": "^2.2.6", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@emotion/styled": "^11.14.0", "@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^5.1.0", "@internationalized/date": "^3.8.2", "@microsoft/fetch-event-source": "^2.0.1", "@monaco-editor/react": "^4.7.0", "@nikolovlazar/chakra-ui-prose": "^1.2.1", "@react-hookz/deep-equal": "^3.0.4", "@react-hookz/web": "^25.1.1", "@sentry/node": "^9.27.0", "@sentry/react": "^9.27.0", "@tanstack/react-query": "^5.80.6", "@tiptap/core": "^2.14.0", "@tiptap/extension-bold": "^2.14.0", "@tiptap/extension-bullet-list": "^2.14.0", "@tiptap/extension-code": "^2.14.0", "@tiptap/extension-code-block": "^2.14.0", "@tiptap/extension-document": "^2.14.0", "@tiptap/extension-heading": "^2.14.0", "@tiptap/extension-history": "^2.14.0", "@tiptap/extension-italic": "^2.14.0", "@tiptap/extension-link": "^2.14.0", "@tiptap/extension-list-item": "^2.14.0", "@tiptap/extension-ordered-list": "^2.14.0", "@tiptap/extension-paragraph": "^2.14.0", "@tiptap/extension-strike": "^2.14.0", "@tiptap/extension-text": "^2.14.0", "@tiptap/extension-underline": "^2.14.0", "@tiptap/pm": "^2.14.0", "@tiptap/react": "^2.14.0", "axios": "^1.9.0", "better-sse": "^0.15.1", "body-parser": "1.20.3", "check-password-strength": "^3.0.0", "chroma-js": "^3.1.2", "compression": "^1.8.0", "cors": "^2.8.5", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "diff": "^8.0.2", "dompurify": "^3.2.6", "dotenv": "^16.5.0", "echarts": "^5.6.0", "express": "^5.1.0", "express-http-proxy": "^2.1.1", "express-static-gzip": "^3.0.0", "file-saver-es": "^2.0.5", "focus-visible": "^5.2.1", "framer-motion": "^11.18.2", "fuse.js": "^7.1.0", "hash-wasm": "^4.12.0", "helmet": "^8.1.0", "jotai": "^2.12.5", "jotai-optics": "^0.4.0", "jsonrepair": "^3.12.0", "lodash": "^4.17.21", "lucide-react": "^0.513.0", "monaco-editor": "^0.52.2", "morgan": "^1.10.0", "optics-ts": "^2.4.1", "p-map": "^7.0.3", "papaparse": "^5.5.3", "picocolors": "^1.1.1", "png-to-ico": "^2.1.8", "polished": "^4.3.1", "powerbi-client": "^2.23.1", "qr-scanner": "^1.4.2", "ramda": "^0.30.1", "react": "^18.3.1", "react-colorful": "^5.6.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-focus-lock": "^2.13.6", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.57.0", "react-intersection-observer": "^9.16.0", "react-resizable-panels": "^3.0.2", "react-router": "^7.6.2", "react-select": "^5.10.1", "react-select-async-paginate": "^0.7.10", "react-syntax-highlighter": "^15.6.1", "react-use": "^17.6.0", "sharp": "^0.34.2", "slugify": "^1.6.6", "sonner": "^2.0.5", "svgo": "^3.3.2", "uint8array-extras": "^1.4.0", "usehooks-ts": "^3.1.1", "uuid": "^11.1.0", "xlsx": "npm:@e965/xlsx@0.20.3", "xml2js": "^0.6.2", "zod": "^3.25.56"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@testing-library/dom": "^10.0.0", "@testing-library/react": "^16.3.0", "@types/chroma-js": "^3.1.1", "@types/cli-progress": "^3.11.6", "@types/compression": "^1.8.1", "@types/cors": "^2.8.19", "@types/diff": "^7.0.2", "@types/express": "^5.0.3", "@types/express-http-proxy": "^1.6.6", "@types/file-saver-es": "^2.0.3", "@types/jsdom": "^21.1.7", "@types/morgan": "^1.9.10", "@types/papaparse": "^5.3.16", "@types/ramda": "^0.30.2", "@types/react": "18.3.18", "@types/react-dom": "18.3.5", "@types/react-syntax-highlighter": "^15.5.13", "@types/uuid": "^10.0.0", "@types/xml2js": "^0.4.14", "@vitejs/plugin-react-swc": "^3.10.1", "@vitest/coverage-v8": "^3.2.2", "cli-progress": "^3.12.0", "enquirer": "^2.4.1", "jsdom": "^26.1.0", "knip": "^5.60.2", "prettier": "^3.5.3", "rimraf": "^6.0.1", "tsx": "^4.19.4", "typescript": "5.8.3", "vite": "^6.3.5", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.2"}}