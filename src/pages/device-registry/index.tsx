import * as Sentry from '@sentry/react'
import {
  lazy,
  Suspense,
  useCallback,
  useMemo,
  useDeferredValue,
  memo,
} from 'react'
import { Outlet, useNavigate } from 'react-router'
import { Helmet } from 'react-helmet-async'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { useMountEffect } from '@react-hookz/web'
import { useAtom } from 'jotai'
import { saveAs } from 'file-saver-es'
import {
  Badge,
  Box,
  Button,
  ButtonGroup,
  Flex,
  HStack,
  Menu,
  MenuButton,
  MenuItem,
  MenuList,
  useColorModeValue,
} from '@chakra-ui/react'
import { CloudUploadIcon, SearchIcon, ChevronDownIcon } from 'lucide-react'
import { Title } from '@/components/title'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { Clipboard } from '@/components/data-table/clipboard'
import { DataTable } from '@/components/data-table/data-table'
import { useModal } from '@/hooks/use-modal'
import { useAbility } from '@/hooks/use-ability'
import { useOrganization } from '@/hooks/use-organization'
import { useColorMap } from '@/hooks/use-color-map'
import { useToast } from '@/hooks/use-toast'
import { pluralize } from '@/utils/pluralize'
import { metaTitle } from '@/utils/meta-title'
import { format } from 'date-fns'
import { tableAtom } from '@/utils/stores/table'
import { deactivateThing } from '@/api/things/things'
import { getNetworks } from '@/api/networks'
import { getRegistries, getRegistryKeys } from '@/api/registries'
import { useAuth } from '@/contexts/use-auth'
import { formatDate } from '@/utils/date/format-date'
import type { RegistryModel } from '@/types/models/registry'
import type { GetResponse } from '@/types/api'
import type { ColumnProps, ActionProps } from '@/types/table'
import type { NetworkModel } from '@/types/models/network'
import type { DeactivateThingInput } from '@/types/api'

const ConfirmDialog = lazy(() =>
  import('@/components/confirm-dialog').then(({ ConfirmDialog }) => ({
    default: ConfirmDialog,
  }))
)

const NetworkActivity = lazy(() =>
  import('@/pages/device-registry/network-activity').then(
    ({ NetworkActivity }) => ({
      default: NetworkActivity,
    })
  )
)

const Edit = lazy(() =>
  import('@/pages/device-registry/edit').then(({ Edit }) => ({
    default: Edit,
  }))
)

const BulkSearch = lazy(() =>
  import('@/pages/device-registry/bulk-search').then(({ BulkSearch }) => ({
    default: BulkSearch,
  }))
)

const AccountDetails = lazy(() =>
  import('@/pages/device-registry/account-details').then(
    ({ AccountDetails }) => ({
      default: AccountDetails,
    })
  )
)

const AssignSku = lazy(() =>
  import('@/pages/device-registry/assign-sku').then(({ AssignSku }) => ({
    default: AssignSku,
  }))
)

const AssignNetwork = lazy(() =>
  import('@/pages/device-registry/assign-network').then(
    ({ AssignNetwork }) => ({
      default: AssignNetwork,
    })
  )
)

const NetworkProvision = lazy(() =>
  import('@/pages/device-registry/network-provision').then(
    ({ NetworkProvision }) => ({
      default: NetworkProvision,
    })
  )
)

const MoveDevices = lazy(() =>
  import('@/pages/device-registry/move-devices').then(({ MoveDevices }) => ({
    default: MoveDevices,
  }))
)

const BulkDeactivate = lazy(() =>
  import('@/pages/device-registry/bulk-deactivate').then(
    ({ BulkDeactivate }) => ({
      default: BulkDeactivate,
    })
  )
)

const SendDownlink = lazy(() =>
  import('@/pages/device-registry/send-downlink').then(({ SendDownlink }) => ({
    default: SendDownlink,
  }))
)

const View = lazy(() =>
  import('@/pages/device-registry/view').then(({ View }) => ({
    default: View,
  }))
)

const DeviceHistory = lazy(() =>
  import('@/pages/customers/device-history').then(({ DeviceHistory }) => ({
    default: DeviceHistory,
  }))
)

const statusOptions = [
  { label: 'Pending', value: 'PENDING' },
  { label: 'Activated', value: 'ACTIVATED' },
  { label: 'Paired', value: 'PAIRED' },
  { label: 'Decommissioned', value: 'DECOMMISSIONED' },
]

const collator = new Intl.Collator('en-US')

const DeviceRegistry = memo(() => {
  const heading = 'Device Registry'
  const tableName = 'device-registry'
  const modal = useModal()
  const toast = useToast()
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  const { applicationId, organizationId } = useOrganization()
  const { isInternalAdmin } = useAuth()
  const { can } = useAbility()
  const { mapColor } = useColorMap()

  const buttonBg = useColorModeValue('gray.50', 'transparent')
  const countColor = useColorModeValue('gray.800', 'whiteAlpha.900')
  const countColorDisabled = useColorModeValue('gray.400', 'whiteAlpha.600')

  const [{ limit, currPage, filter, filters, checkedIds }, setTable] = useAtom(
    tableAtom(tableName)
  )

  const deferredFilter = useDeferredValue(filter)

  const checkedState = useMemo<string>(() => {
    const num = new Intl.NumberFormat('en-US').format(checkedIds.length)
    const str = pluralize(checkedIds.length, ' device')
    return [num, str].join(' ')
  }, [checkedIds.length])

  useMountEffect(() => {
    setTable((prev) => ({
      ...prev,
      hiddenColumns: ['sku', 'created_at', 'paired_at'],
      limitOptions: [5, 15, 25, 50, 100, 500],
    }))
  })

  const { mutateAsync: deactivateThingMutation } = useMutation<
    boolean,
    Error,
    DeactivateThingInput
  >({
    mutationFn: (input) => deactivateThing(input),
    onSuccess: () => {
      const cache = ['GetDevices', 'GetRegistries', 'GetThings']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const { data } = useQuery<
    Omit<GetResponse<RegistryModel>, 'page'> | null,
    Error
  >({
    queryKey: [
      'GetRegistries',
      organizationId,
      applicationId,
      limit,
      currPage,
      deferredFilter,
    ],
    queryFn: ({ signal }) =>
      organizationId && applicationId
        ? getRegistries({
            organizationId,
            applicationId,
            filter: deferredFilter,
            limit,
            page: currPage,
            signal,
          })
        : null,
    enabled: !!organizationId || !!applicationId,
  })

  const records = useMemo<Omit<GetResponse<RegistryModel>, 'page'>>(
    () =>
      data ?? {
        count: 0,
        rows: [],
      },
    [data]
  )

  const { data: networkOptions } = useQuery<
    NetworkModel[],
    Error,
    BaseOption[]
  >({
    queryKey: ['GetNetworks'],
    queryFn: ({ signal }) =>
      getNetworks({
        signal,
      }),
    select: useCallback((data: NetworkModel[]) => {
      const options: BaseOption<string>[] = []
      for (const { id, name } of data) {
        options.push({
          label: name,
          value: id,
        })
      }
      return options.sort((a, b) => collator.compare(a.label, b.label))
    }, []),
  })

  const networkName = useCallback(
    (id: string): string => {
      const network = (networkOptions ?? []).find(({ value }) => id === value)
      return network?.label ?? id
    },
    [networkOptions]
  )

  const isActive = useCallback(
    ({ status }: RegistryModel): boolean => status !== 'PENDING',
    []
  )

  const hasUserAttached = useCallback(
    ({ devices }: RegistryModel): boolean =>
      !!devices?.slice(0, 1).shift()?.user_id,
    []
  )

  const isLora = useCallback(
    ({ device_type }: RegistryModel): boolean =>
      device_type?.subcategory?.toLowerCase() === 'lora',
    []
  )

  const canEditRegistry = can(['edit', 'applications'])

  const canViewAccountDetails = useCallback(
    (row?: RegistryModel): boolean =>
      !!row &&
      hasUserAttached(row) &&
      isActive(row) &&
      can(['view', 'applications']),
    [can, isActive, hasUserAttached]
  )

  const canDeactivateDevice = useCallback(
    (row?: RegistryModel): boolean =>
      !!row && isActive(row) && can(['edit', 'applications']),
    [can, isActive]
  )

  const canViewNetworkLogs = useCallback(
    (row?: RegistryModel): boolean =>
      !!row && isLora(row) && can(['view', 'applications']),
    [can, isLora]
  )

  const canViewDevices = can(['view', 'applications'])

  const onClear = (): void => {
    setTable((prev) => ({
      ...prev,
      currPage: 0,
      filter: undefined,
      filters: [],
      checkedIds: [],
    }))
  }

  const onBulkSearch = (): void => {
    modal({
      size: '4xl',
      component: <BulkSearch />,
      config: {
        onCallback: (ids: string[]) => {
          if (ids.length === 0) {
            return
          }
          setTable((prev) => ({
            ...prev,
            currPage: 0,
            checkedIds: [],
            filter: `hardware_id in ${ids.join(' ')}`,
            filters: [],
          }))
        },
      },
    })
  }

  const onUpload = (): void => {
    navigate('/manage/device-registry/upload-devices', {
      viewTransition: true,
    })
  }

  const onBulkDeactivate = useCallback((): void => {
    modal({
      size: '4xl',
      scrollBehavior: 'inside',
      component: <BulkDeactivate />,
      config: {
        data: records.rows.filter(({ id }) => checkedIds.includes(id)) ?? [],
        onCallback: () => ({}),
      },
    })
  }, [modal, checkedIds, records.rows])

  const onMoveDevices = useCallback((): void => {
    modal({
      size: '4xl',
      component: <MoveDevices />,
      config: {
        data: checkedIds,
        onCallback: () => ({}),
      },
    })
  }, [modal, checkedIds])

  const onAssignNetwork = useCallback((): void => {
    modal({
      size: '4xl',
      component: <AssignNetwork />,
      config: {
        data: checkedIds,
        onCallback: () => ({}),
      },
    })
  }, [modal, checkedIds])

  const onNetworkProvision = useCallback(() => {
    modal({
      size: '4xl',
      component: <NetworkProvision />,
      config: {
        data: checkedIds,
        onCallback: () => {},
      },
    })
  }, [modal, checkedIds])

  const onAssignSku = useCallback((): void => {
    modal({
      size: 'md',
      component: <AssignSku />,
      config: {
        data: checkedIds,
        onCallback: () => ({}),
      },
    })
  }, [modal, checkedIds])

  const onSendDownlink = useCallback((): void => {
    modal({
      size: '4xl',
      scrollBehavior: 'inside',
      component: <SendDownlink />,
      config: {
        data: records.rows.filter(({ id }) => checkedIds.includes(id)) ?? [],
        onCallback: () => ({}),
      },
    })
  }, [modal, checkedIds, records.rows])

  const onNetworkActivity = useCallback(
    (row: RegistryModel): void => {
      modal({
        size: '6xl',
        scrollBehavior: 'inside',
        component: <NetworkActivity />,
        config: {
          data: row,
          onCallback: () => ({}),
        },
      })
    },
    [modal]
  )

  const onHistory = useCallback(
    (row: RegistryModel): void => {
      modal({
        size: '6xl',
        component: <DeviceHistory />,
        scrollBehavior: 'inside',
        config: {
          data: row,
          onCallback: () => ({}),
        },
      })
    },
    [modal]
  )

  const onView = useCallback(
    (row: RegistryModel): void => {
      modal({
        component: <View />,
        scrollBehavior: 'inside',
        config: {
          data: row,
          onCallback: () => ({}),
        },
      })
    },
    [modal]
  )

  const onEdit = useCallback(
    (row: RegistryModel): void => {
      modal({
        component: <Edit />,
        config: {
          data: row,
          onCallback: () => ({}),
        },
      })
    },
    [modal]
  )

  const onAccountDetails = useCallback(
    (row: RegistryModel): void => {
      modal({
        component: <AccountDetails />,
        config: {
          data: row,
          onCallback: () => ({}),
        },
      })
    },
    [modal]
  )

  const onDeactivate = useCallback(
    ({ hardware_id }: RegistryModel): void => {
      if (!(organizationId && applicationId)) {
        return
      }

      modal({
        size: 'md',
        component: <ConfirmDialog />,
        config: {
          title: 'Device Deactivation',
          description: `Are you sure you want to deactivate "${hardware_id}"?`,
          confirmLabel: 'Deactivate',
          onCallback: async () => {
            try {
              await deactivateThingMutation({
                organizationId,
                applicationId,
                hardwareId: hardware_id,
              })
              toast({
                status: 'success',
                msg: `"${hardware_id}" has been deactivated.`,
              })
            } catch (error: unknown) {
              Sentry.captureException(error)
              toast({
                status: 'error',
                msg: `Unable to deactivate "${hardware_id}".`,
              })
            }
          },
        },
      })
    },
    [modal, toast, deactivateThingMutation, organizationId, applicationId]
  )

  const onExportToCsv = useCallback(async (): Promise<void> => {
    if (!(organizationId && applicationId)) {
      return
    }

    try {
      const cellSep = ';'
      const lineSep = '\r\n'
      const currentDate = format(new Date(), 'yyyy-MM-dd-SSS')
      const selectedDevices = records.rows.filter(({ id }) =>
        checkedIds.includes(id)
      )

      const devicesWithKeys = await Promise.all(
        selectedDevices.map(async (device) => {
          try {
            const keys = await queryClient.fetchQuery({
              queryKey: [
                'GetRegistryKeys',
                organizationId,
                applicationId,
                device.hardware_id,
              ],
              queryFn: ({ signal }) =>
                getRegistryKeys({
                  organizationId: organizationId,
                  applicationId: applicationId,
                  hardwareId: device.hardware_id,
                  signal,
                }),
            })
            return { ...device, keys }
          } catch {
            return { ...device, keys: null }
          }
        })
      )

      const csv = [
        [
          'Hardware ID',
          'Device Template',
          'Network',
          'SKU',
          'Status',
          'Created',
          'Paired to App',
          'Paired',
          'AppEUI',
          'AppKey',
        ],
        ...devicesWithKeys.map((device) => [
          device.hardware_id,
          device.device_type?.name ?? '',
          networkName(device.network),
          device.sku ?? '',
          device.status,
          formatDate(device.created_at),
          device.paired_to_app_id ?? '',
          device.paired_at ? formatDate(device.paired_at) : '',
          device.keys?.appeui ?? '',
          device.keys?.appkey ?? '',
        ]),
      ]
        .map((line) => line.join(cellSep))
        .join(lineSep)

      saveAs(
        new Blob([csv], { type: 'text/plain;charset=utf-8' }),
        `device-registry-${currentDate}.csv`
      )
    } catch (error: unknown) {
      Sentry.captureException(error)
      toast({
        status: 'error',
        msg: 'Unable to create CSV file from data.',
      })
    }
  }, [
    records.rows,
    checkedIds,
    networkName,
    toast,
    organizationId,
    applicationId,
    queryClient,
  ])

  const columns = useMemo<ColumnProps<RegistryModel>[]>(
    () => [
      {
        name: 'Hardware ID',
        id: 'hardware_id',
        canHide: false,
        filter: {
          type: 'input',
          condition: 'like',
        },
        cell: ({ hardware_id }) => <Clipboard>{hardware_id}</Clipboard>,
      },
      {
        name: 'Device Template',
        id: 'device_type_id',
        canHide: true,
        filter: {
          type: 'select-type',
          condition: 'eq',
        },
        cell: ({ device_type }) =>
          device_type?.name && <Clipboard>{device_type?.name}</Clipboard>,
      },
      {
        name: 'Network',
        id: 'network',
        canHide: true,
        filter: {
          type: 'select',
          condition: 'eq',
          options: networkOptions,
        },
        cell: ({ network }) => <Clipboard>{networkName(network)}</Clipboard>,
      },
      {
        name: 'SKU',
        id: 'sku',
        canHide: true,
        filter: {
          type: 'input',
          condition: 'like',
        },
        cell: ({ sku }) => sku && <Clipboard>{sku}</Clipboard>,
      },
      {
        name: 'Status',
        id: 'status',
        filter: {
          type: 'select',
          condition: 'eq',
          options: statusOptions,
        },
        cell: ({ status }) => (
          <Badge colorScheme={mapColor(status)}>{status}</Badge>
        ),
      },
      {
        name: 'Created',
        id: 'created_at',
        canHide: true,
        filter: {
          type: 'date',
          condition: 'between',
        },
        cell: ({ created_at }) => (
          <Box noOfLines={1}>{formatDate(created_at)}</Box>
        ),
      },
      {
        name: 'Paired to App',
        id: 'paired_to_app_id',
        canHide: true,
        filter: {
          type: 'input',
          condition: 'eq',
        },
        cell: ({ paired_to_app_id }) =>
          paired_to_app_id && <Clipboard>{paired_to_app_id}</Clipboard>,
      },
      {
        name: 'Paired',
        id: 'paired_at',
        canHide: true,
        filter: {
          type: 'date',
          condition: 'between',
        },
        cell: ({ paired_at }) =>
          paired_at && <Box noOfLines={1}>{formatDate(paired_at)}</Box>,
      },
    ],
    [mapColor, networkName, networkOptions]
  )

  const actions = useMemo<ActionProps<RegistryModel>[]>(
    () => [
      {
        label: 'Overview',
        onClick: onView,
        canView: () => true,
      },
      {
        label: 'Edit Registry',
        onClick: onEdit,
        canView: () => canEditRegistry,
      },
      {
        label: 'End User Account Details',
        onClick: onAccountDetails,
        canView: (row) => canViewAccountDetails(row),
      },
      {
        label: 'Deactivate Device',
        onClick: onDeactivate,
        canView: (row) => canDeactivateDevice(row),
      },
      {
        label: 'Network Activity',
        onClick: onNetworkActivity,
        canView: (row) => canViewNetworkLogs(row),
      },
      {
        label: 'Device History',
        onClick: onHistory,
        canView: () => canViewDevices,
      },
    ],
    [
      onView,
      onEdit,
      canEditRegistry,
      onAccountDetails,
      canViewAccountDetails,
      onDeactivate,
      canDeactivateDevice,
      onNetworkActivity,
      canViewNetworkLogs,
      onHistory,
      canViewDevices,
    ]
  )

  return (
    <>
      <Helmet>
        <title>{metaTitle([heading])}</title>
      </Helmet>
      <Box px={4}>
        <Suspense fallback={null}>
          <DataTable
            isCheckable
            tableName={tableName}
            data={records}
            columns={columns}
            actions={actions}
            disabledRow={({ status }) => status === 'PENDING'}
            header={() => (
              <Flex pl={2} pr={3} align="center" justify="space-between">
                <Title title={heading} count={data?.count} suffix="Device" />
                <Suspense fallback={null}>
                  <HStack>
                    <ButtonGroup
                      isAttached
                      isDisabled={checkedIds.length === 0}
                      sx={{
                        border: 0,
                        borderRadius: 'base',
                        bg: buttonBg,
                      }}
                    >
                      <Button
                        variant="unstyled"
                        isDisabled
                        sx={{
                          p: 0,
                          minW: 150,
                          border: 0,
                          color:
                            checkedIds.length === 0
                              ? countColorDisabled
                              : countColor,
                          bg: buttonBg,
                          fontWeight: 'medium',
                          _disabled: {
                            opacity: 1,
                            cursor: 'default',
                          },
                        }}
                      >
                        {checkedState}
                      </Button>
                      <Button
                        variant="link"
                        colorScheme="primary"
                        onClick={onClear}
                        isDisabled={
                          checkedIds.length === 0 &&
                          filters.length === 0 &&
                          !filter
                        }
                        sx={{
                          px: 4,
                          bg: buttonBg,
                          _hover: {
                            bg: buttonBg,
                          },
                        }}
                      >
                        Clear
                      </Button>
                      <Menu>
                        <MenuButton
                          colorScheme="primary"
                          as={Button}
                          rightIcon={<ChevronDownIcon size={16} />}
                        >
                          Bulk Actions
                        </MenuButton>
                        <MenuList
                          sx={{
                            fontSize: 'md',
                            zIndex: 'dropdown',
                            p: 2,
                          }}
                        >
                          {canEditRegistry && (
                            <MenuItem onClick={onExportToCsv} sx={{ py: 3 }}>
                              Export to CSV
                            </MenuItem>
                          )}
                          {canEditRegistry && (
                            <MenuItem onClick={onMoveDevices} sx={{ py: 3 }}>
                              Move to new Device Template
                            </MenuItem>
                          )}
                          {canEditRegistry && (
                            <MenuItem onClick={onAssignNetwork} sx={{ py: 3 }}>
                              Assign to new Network
                            </MenuItem>
                          )}
                          {isInternalAdmin && (
                            <MenuItem
                              onClick={onNetworkProvision}
                              sx={{ py: 3 }}
                            >
                              Provision to Network
                            </MenuItem>
                          )}
                          {isInternalAdmin && (
                            <MenuItem onClick={onAssignSku} sx={{ py: 3 }}>
                              Assign SKU
                            </MenuItem>
                          )}
                          <MenuItem onClick={onSendDownlink} sx={{ py: 3 }}>
                            Send downlink command
                          </MenuItem>
                          <MenuItem onClick={onBulkDeactivate} sx={{ py: 3 }}>
                            Bulk Deactivation
                          </MenuItem>
                        </MenuList>
                      </Menu>
                    </ButtonGroup>
                    <Button
                      type="button"
                      aria-label="Bulk Search"
                      leftIcon={<SearchIcon size={16} />}
                      colorScheme="primary"
                      boxShadow="sm"
                      onClick={onBulkSearch}
                    >
                      Bulk Search
                    </Button>
                    <Button
                      type="button"
                      aria-label="Upload Devices"
                      leftIcon={<CloudUploadIcon size={16} />}
                      colorScheme="primary"
                      boxShadow="sm"
                      onClick={onUpload}
                    >
                      Upload Devices
                    </Button>
                  </HStack>
                </Suspense>
              </Flex>
            )}
          />
        </Suspense>
      </Box>
      <Outlet />
    </>
  )
})

export async function loader() {
  const scopes = ['view:applications']

  const { checkLoaderPermissions } = await import('@/utils/loader-auth')
  await checkLoaderPermissions(scopes)

  return {
    title: 'Device Registry',
    scopes,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <DeviceRegistry />
}
