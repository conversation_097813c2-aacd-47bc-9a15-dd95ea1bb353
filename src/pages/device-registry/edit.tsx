import * as Sentry from '@sentry/react'
import { memo, lazy, useCallback, useEffect, useMemo, useState } from 'react'
import { useForm, useWatch } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { isEmpty } from 'ramda'
import { useAtomValue } from 'jotai'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  Alert,
  AlertIcon,
  Box,
  Button,
  Grid,
  HStack,
  Link,
  ModalBody,
  ModalCloseButton,
  ModalFooter,
  ModalHeader,
  useModalContext,
} from '@chakra-ui/react'
import { useAuth } from '@/contexts/use-auth'
import { InputField } from '@/components/input-field'
import { SelectField } from '@/components/select-field'
import { SelectTypeField } from '@/components/select-type-field'
import { useAbility } from '@/hooks/use-ability'
import { useModal } from '@/hooks/use-modal'
import { useOrganization } from '@/hooks/use-organization'
import { useToast } from '@/hooks/use-toast'
import { modalAtom } from '@/utils/stores/modal'
import {
  getRegistryKeys,
  updateRegistry,
  deleteRegistry,
} from '@/api/registries'
import { getNetworks } from '@/api/networks'
import type {
  RegistryModel,
  RegistryDeviceTypeModel,
  RegistryKeysModel,
} from '@/types/models/registry'
import type { UpdateRegistryInput } from '@/types/api'
import type { NetworkModel } from '@/types/models/network'
import type { ModalAtomProps } from '@/types/store'

const ConfirmDialog = lazy(() =>
  import('@/components/confirm-dialog').then(({ ConfirmDialog }) => ({
    default: ConfirmDialog,
  }))
)

const activationModeOptions = [
  { label: 'MANUAL', value: 'MANUAL' },
  { label: 'OTAA', value: 'OTAA' },
  { label: 'ABP', value: 'ABP' },
] satisfies BaseOption<ActivationModeEnum>[]

const statusOptions = [
  { label: 'Pending', value: 'PENDING' },
  { label: 'Activated', value: 'ACTIVATED' },
  { label: 'Paired', value: 'PAIRED' },
  { label: 'Decommissioned', value: 'DECOMMISSIONED' },
] satisfies BaseOption<StatusEnum>[]

const schema = z.object({
  hardware_id: z.string().min(1, 'Hardware ID is required.'),
  sku: z.string(),
  catalog: z.string(),
  device_type_id: z.string().min(1, 'Device Template is required.'),
  status: z.enum(['PENDING', 'ACTIVATED', 'PAIRED', 'DECOMMISSIONED']),
  network: z.string().nullable(),
  keys: z
    .object({
      activation_url: z.string().nullable(),
      activation_mode: z.enum(['MANUAL', 'OTAA', 'ABP']),
      appeui: z.string().nullable(),
      appkey: z.string().nullable(),
      nwkskey: z.string().nullable(),
      appskey: z.string().nullable(),
    })
    .optional(),
})

type FormInputProps = z.infer<typeof schema>
type StatusEnum = 'PENDING' | 'ACTIVATED' | 'PAIRED' | 'DECOMMISSIONED'
type ActivationModeEnum = 'MANUAL' | 'OTAA' | 'ABP'

export const Edit = memo(() => {
  const modal = useModal()
  const toast = useToast()
  const queryClient = useQueryClient()
  const { onClose } = useModalContext()
  const { organizationId, applicationId } = useOrganization()
  const { isInternalAdmin } = useAuth()
  const { can } = useAbility()

  const canEdit = can(['edit', 'applications'])

  const [defaultNetwork, setDefaultNetwork] =
    useState<BaseOption<string> | null>(null)

  const {
    config: { data },
  } = useAtomValue<ModalAtomProps<RegistryModel>>(modalAtom)

  const hardwareId = useMemo<string | undefined>(
    () => data?.hardware_id,
    [data]
  )

  const getCatalog = (deviceType?: RegistryDeviceTypeModel | null): string => {
    if (!deviceType) {
      return 'public'
    }
    if (deviceType.is_public && deviceType.is_example) {
      return 'all'
    }
    return 'public'
  }

  const {
    control,
    setValue,
    handleSubmit,
    formState: { isValid, isSubmitting },
  } = useForm({
    mode: 'onTouched',
    resolver: zodResolver(schema),
    defaultValues: {
      hardware_id: data?.hardware_id ?? '',
      sku: data?.sku ?? '',
      catalog: getCatalog(data?.device_type),
      device_type_id: data?.device_type_id ?? '',
      status: (data?.status as StatusEnum) ?? ('PAIRED' as StatusEnum),
      network: data?.network ?? null,
      keys: {
        activation_url: null,
        activation_mode: 'MANUAL' as ActivationModeEnum,
        appeui: null,
        appkey: null,
        nwkskey: null,
        appskey: null,
      },
    },
  })

  const keys = useWatch({
    name: 'keys',
    control,
  })

  const catalog = useWatch({
    name: 'catalog',
    control,
  })

  const [isSaved, setIsSaved] = useState<boolean>(false)

  const [canEditAppKey, canEditAppEui, canEditNwksKey, canEditAppsKey] =
    useMemo(
      () =>
        [keys?.appkey, keys?.appeui, keys?.nwkskey, keys?.appskey].map(
          (a) => (a == null || !isSaved) && canEdit
        ),
      [keys, canEdit, isSaved]
    )

  const { mutateAsync: updateRegistryMutation } = useMutation<
    number[],
    Error,
    {
      organizationId: string
      applicationId: string
      registryId: string
      input: UpdateRegistryInput
    }
  >({
    mutationFn: ({ organizationId, applicationId, registryId, input }) =>
      updateRegistry({ organizationId, applicationId, registryId, input }),
    onSuccess: () => {
      const cache = ['GetDevices', 'GetRegistries']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const { mutateAsync: removeRegistryMutation } = useMutation<
    boolean,
    Error,
    {
      organizationId: string
      applicationId: string
      registryId: string
    }
  >({
    mutationFn: ({ organizationId, applicationId, registryId }) =>
      deleteRegistry({ organizationId, applicationId, registryId }),
    onSuccess: () => {
      const cache = ['GetDevices', 'GetRegistries']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const isLoRaWan = useCallback(
    (deviceType: any): boolean =>
      String(deviceType?.subcategory).toLowerCase() === 'lora',
    []
  )

  const { data: dataKeys } = useQuery<RegistryKeysModel | null, Error>({
    queryKey: ['GetRegistryKeys', organizationId, applicationId, hardwareId],
    queryFn: ({ signal }) =>
      organizationId && applicationId && hardwareId
        ? getRegistryKeys({
            organizationId,
            applicationId,
            hardwareId,
            signal,
          })
        : null,
    enabled:
      !!organizationId && !!applicationId && isLoRaWan(data?.device_type),
  })

  useEffect(() => {
    if (!dataKeys) {
      return
    }

    setIsSaved(true)

    const keysMap = [
      'activation_url',
      'activation_mode',
      'appeui',
      'appkey',
      'nwkskey',
      'appskey',
    ]

    setValue(
      'keys',
      keysMap.reduce((acc: any, key) => {
        acc[key] = dataKeys[key] ?? null
        return acc
      }, {})
    )
  }, [setValue, dataKeys])

  const { data: networkOptions, isFetching: isFetchingNetworks } = useQuery<
    NetworkModel[],
    Error,
    BaseOption<string>[]
  >({
    queryKey: ['GetNetworks'],
    queryFn: ({ signal }) =>
      getNetworks({
        signal,
      }),
    enabled: !!data?.network,
    select: useCallback(
      (data: NetworkModel[]) =>
        data.reduce((acc: BaseOption<string>[], { id, name }) => {
          acc.push({
            label: name,
            value: id,
          })
          return acc
        }, []),
      []
    ),
  })

  useEffect(() => {
    if (!networkOptions) {
      return
    }
    const network = networkOptions.find(({ value }) => value === data?.network)
    setDefaultNetwork(network ?? null)
  }, [networkOptions, data?.network])

  const templateCatalogOptions = useMemo<BaseOption<string>[]>(
    () => [
      ...(isInternalAdmin ? [{ label: 'All', value: 'all' }] : []),
      { label: 'Application', value: 'application' },
      { label: 'Public', value: 'public' },
    ],
    [isInternalAdmin]
  )

  const onRemove = (data?: RegistryModel): void => {
    if (!(data && organizationId && applicationId)) {
      return
    }

    const { id, hardware_id } = data

    modal({
      size: 'md',
      component: <ConfirmDialog />,
      config: {
        title: 'Remove Registry',
        description: `Are you sure you want to remove “${hardware_id}”?`,
        confirmLabel: 'Remove',
        onCallback: async () => {
          try {
            await removeRegistryMutation({
              organizationId,
              applicationId,
              registryId: id,
            })
            toast({
              status: 'success',
              msg: `“${hardware_id}” has been removed.`,
            })
          } catch (error: unknown) {
            Sentry.captureException(error)
            toast({
              status: 'error',
              msg: `Unable to remove “${hardware_id}”.`,
            })
          }
        },
      },
    })
  }

  const onSubmit = async (values: FormInputProps): Promise<boolean> => {
    if (!(data?.id && organizationId && applicationId)) {
      return false
    }

    const input = {
      ...values,
      sku: isEmpty(values.sku) ? null : values.sku,
    } satisfies UpdateRegistryInput

    try {
      await updateRegistryMutation({
        organizationId: organizationId,
        applicationId: data?.application_id ?? applicationId,
        registryId: data?.id,
        input,
      })
      toast({
        status: 'success',
        msg: 'Registry has been updated.',
      })
      setIsSaved(true)
      return true
    } catch (error: unknown) {
      Sentry.captureException(error)
      toast({
        status: 'error',
        msg: 'Unable to update registry.',
      })
      return false
    } finally {
      onClose()
    }
  }

  return (
    <Box as="form" noValidate onSubmit={handleSubmit(onSubmit)}>
      <ModalHeader>Edit Registry</ModalHeader>
      <ModalCloseButton />
      <ModalBody
        sx={{
          maxH: '70vh',
          overflow: 'auto',
        }}
      >
        <Alert status="info" mb={6}>
          <AlertIcon />
          <Link
            href="https://iot-help.scrollhelp.site/partners/change-device-template"
            target="_blank"
            rel="noopener noreferrer"
          >
            Read more about changing device template.
          </Link>
        </Alert>
        <Grid gap={4} templateColumns={{ base: '1fr', lg: 'repeat(2, 1fr)' }}>
          <InputField
            name="hardware_id"
            label="Hardware ID"
            control={control}
            isReadOnly
          />
          <InputField name="sku" label="SKU" control={control} />
          <SelectField
            name="status"
            label="Status"
            control={control}
            options={statusOptions}
            isDisabled={!canEdit}
            isRequired
            menuPosition="fixed"
          />
          <SelectField
            name="catalog"
            label="Template Catalog"
            options={templateCatalogOptions}
            control={control}
            isDisabled={!canEdit}
            isRequired
            menuPosition="fixed"
          />
          <SelectTypeField
            name="device_type_id"
            label="Device Template"
            control={control}
            catalog={catalog}
            isDisabled={!canEdit}
            isRequired
            menuPosition="fixed"
            sx={{
              gridColumn: '1 / -1',
            }}
          />
          {isLoRaWan(data?.device_type) && (
            <>
              <Box gridColumn="1 / -1">
                <SelectField
                  name="network"
                  label="LoRaWAN Network Server"
                  placeholder="None"
                  control={control}
                  defaultValue={defaultNetwork}
                  options={networkOptions}
                  isLoading={isFetchingNetworks}
                  isDisabled={!canEdit}
                  isSearchable
                  menuPosition="fixed"
                  render={
                    keys?.activation_url && (
                      <Link
                        href={keys?.activation_url}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        Network Server Console
                      </Link>
                    )
                  }
                />
                <SelectField
                  name="keys.activation_mode"
                  label="LoRaWAN Activation Mode"
                  control={control}
                  options={activationModeOptions}
                  isDisabled={!canEdit}
                  isSearchable
                  menuPosition="fixed"
                />
              </Box>
              {keys?.activation_mode === 'OTAA' && (
                <InputField
                  name="keys.appeui"
                  label="LoRaWAN AppEUI"
                  control={control}
                  isDisabled={!canEditAppEui}
                />
              )}
              {keys?.activation_mode === 'OTAA' && (
                <InputField
                  name="keys.appkey"
                  label="LoRaWAN AppKey"
                  control={control}
                  isDisabled={!canEditAppKey}
                />
              )}
              {keys?.activation_mode === 'ABP' && (
                <InputField
                  name="keys.nwkskey"
                  label="LoRaWAN NWK Session Key"
                  control={control}
                  isDisabled={!canEditNwksKey}
                />
              )}
              {keys?.activation_mode === 'ABP' && (
                <InputField
                  name="keys.appskey"
                  label="LoRaWAN APP Session Key"
                  control={control}
                  isDisabled={!canEditAppsKey}
                  isReadOnly
                />
              )}
            </>
          )}
        </Grid>
      </ModalBody>
      <ModalFooter justifyContent="space-between">
        <Box>
          <Button
            type="button"
            aria-label="Remove"
            colorScheme="red"
            variant="ghost"
            onClick={() => onRemove(data)}
          >
            Remove
          </Button>
        </Box>
        <HStack>
          <Button
            type="button"
            aria-label="Cancel"
            variant="ghost"
            colorScheme="gray"
            onClick={onClose}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            minW="140px"
            aria-label="Save"
            colorScheme="green"
            isLoading={isSubmitting}
            isDisabled={!isValid || isSubmitting || !canEdit}
            loadingText="Saving"
          >
            Save
          </Button>
        </HStack>
      </ModalFooter>
    </Box>
  )
})
