import * as Sentry from '@sentry/react'
import {
  memo,
  Suspense,
  useCallback,
  useMemo,
  useState,
  type ChangeEvent,
} from 'react'
import { Helmet } from 'react-helmet-async'
import { useNavigate } from 'react-router'
import { useCustomCompareEffect } from '@react-hookz/web'
import { useForm, FormProvider, useWatch } from 'react-hook-form'
import { equals } from 'ramda'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import * as Papa from 'papaparse'
import { saveAs } from 'file-saver-es'
import pMap from 'p-map'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  Box,
  Flex,
  Button,
  Grid,
  HStack,
  Divider,
  Container,
} from '@chakra-ui/react'
import { CheckIcon, CloudIcon, CloudDownloadIcon } from 'lucide-react'
import { AxiosError } from 'axios'
import { Title } from '@/components/title'
import { BackButton } from '@/components/back-button'
import { CheckboxField } from '@/components/checkbox-field'
import { CsvPreview } from '@/components/csv-preview'
import { FileField } from '@/components/file-field'
import { InputField } from '@/components/input-field'
import { RadioField } from '@/components/radio-field'
import { SelectField } from '@/components/select-field'
import { SelectOrganizationField } from '@/components/select-organization-field'
import { SelectApplicationField } from '@/components/select-application-field'
import { SelectTypeField } from '@/components/select-type-field'
import { TextareaField } from '@/components/textarea-field'
import { UploadResultTable } from '@/features/device-registry/upload-result-table'
import { registrationStatus as STATUS } from '@/features/device-registry/registration-status'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { useAuth } from '@/contexts/use-auth'
import { useOrganization } from '@/hooks/use-organization'
import { useToast } from '@/hooks/use-toast'
import { useRouter } from '@/hooks/use-router'
import { metaTitle } from '@/utils/meta-title'
import { pluralize } from '@/utils/pluralize'
import { reduceEmptyObject } from '@/utils/reduce-empty-object'
import { getApplications } from '@/api/applications'
import { activateNetwork, checkConnectivity, getNetworks } from '@/api/networks'
import { createRegistry } from '@/api/registries'
import type { RegistryModel } from '@/types/models/registry'
import type { CreateRegistryInput } from '@/types/api'
import type {
  CheckConnectivityResponse,
  CheckConnectivityInput,
  ActivateNetworkInput,
} from '@/types/api'
import type { NetworkModel } from '@/types/models/network'
import type { RegistrationResult } from '@/types/models/registry'

const schema = z
  .object({
    device_type: z.string().min(1, { message: 'Device Template is required' }),
    sku: z.string().nullable().optional(),
    registry_type: z.enum(['global', 'specific']),
    organization: z.string().nullable().optional(),
    application: z.string().nullable().optional(),
    upload_type: z.enum(['text', 'csv']),
    ids: z.string().nullable().optional(),
    file: z.any().nullable().optional(),
    catalog: z.string().default('public'),
    network: z.string().default(''),
    activateToLNS: z.boolean().default(false),
    options: z.record(z.any()).default({}),
    keys: z
      .object({
        activation_mode: z.string().nullable().default(null),
        deveui: z.string().nullable().optional(),
        appeui: z.string().nullable().optional(),
        appkey: z.string().nullable().optional(),
        nwkskey: z.string().nullable().optional(),
        appskey: z.string().nullable().optional(),
      })
      .default({}),
  })
  .superRefine((data, ctx) => {
    // registry_type conditional
    if (data.registry_type === 'specific') {
      if (!data.organization || data.organization.length < 2) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Organization is required',
          path: ['organization'],
        })
      }
      if (!data.application || data.application.length < 2) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Application is required',
          path: ['application'],
        })
      }
    }
    // upload_type conditional
    if (data.upload_type === 'text') {
      if (!data.ids || data.ids.length < 2) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Device IDs are required',
          path: ['ids'],
        })
      }
    }
    if (data.upload_type === 'csv') {
      if (!data.file) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'CSV File is required',
          path: ['file'],
        })
      }
    }
    // keys conditional
    if (data.activateToLNS && data.upload_type === 'text') {
      if (data.keys.activation_mode === 'OTAA') {
        if (!data.keys.appeui || data.keys.appeui.length !== 16) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'AppEUI must be 16 characters',
            path: ['keys', 'appeui'],
          })
        }
        if (!data.keys.appkey || data.keys.appkey.length !== 32) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'AppKey must be 32 characters',
            path: ['keys', 'appkey'],
          })
        }
      }
      if (data.keys.activation_mode === 'ABP') {
        if (!data.keys.nwkskey || data.keys.nwkskey.length !== 32) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'NwkSKey must be 32 characters',
            path: ['keys', 'nwkskey'],
          })
        }
        if (!data.keys.appskey || data.keys.appskey.length !== 32) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'AppsKey must be 32 characters',
            path: ['keys', 'appskey'],
          })
        }
      }
    }
  })

interface SupportedNetworkOption {
  label: string
  value: string
  activationSupported: boolean
  networkOptions: Dict<any>
}

interface LoRaKeys {
  [key: string]: string
  activation_mode: string
  deveui: string
  appeui: string
  appkey: string
  nwkskey: string
  appskey: string
}

interface RegistrationPayload {
  devices: Partial<LoRaKeys>[]
  activate: boolean
  application_id: Maybe<string>
  device_type: string
  network: string
  options: Dict<any>
}

type FormInputProps = z.infer<typeof schema>

const registryTypeOptions = [
  {
    label: 'Global',
    value: 'global',
  },
  {
    label: 'Specific Application',
    value: 'specific',
  },
]

const uploadTypeOptions = [
  {
    label: 'Text Input',
    value: 'text',
  },
  {
    label: 'CSV File',
    value: 'csv',
  },
]

const activationModeOptions = [
  { label: 'OTAA', value: 'OTAA' },
  { label: 'ABP', value: 'ABP' },
]

const csvOptions = [
  {
    search: ['Unused'],
    value: 'unused',
  },
  {
    search: [
      'DevEUI',
      'Dev EUI',
      'Dev_EUI',
      'DeviceID',
      'Device ID',
      'Device_ID',
      'HardwareID',
      'Hardware ID',
      'Hardware_ID',
    ],
    value: 'deveui',
  },
  {
    search: ['AppEUI', 'App EUI', 'App_EUI'],
    value: 'appeui',
  },
  {
    search: ['AppKey', 'App Key', 'App_Key'],
    value: 'appkey',
  },
  {
    search: ['NwkSKey'],
    value: 'nwkskey',
  },
  {
    search: ['AppSKey'],
    value: 'appskey',
  },
  {
    search: ['SKU', 'Serial', 'Serial Number', 'Serial_Number'],
    value: 'sku',
  },
].map((option) => ({
  ...option,
  label: option.search[0] || '',
  search: option.search.map((label) => label.toLowerCase()),
}))

const getErrorMessage = (object: any): string =>
  object?.response?.error ??
  object?.response?.data?.message ??
  object?.response?.message ??
  object?.message ??
  object?.toString()

const UploadDevices = memo(() => {
  const toast = useToast()
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  const { title } = useRouter()
  const { organizationId, applicationId } = useOrganization()
  const { isInternalAdmin } = useAuth()

  // form
  const [lora, setLora] = useState<boolean>(false)
  const [isDeviceActivationSupported, setDeviceActivationSupported] =
    useState<boolean>(false)
  const [isLnsConnecting, setLnsConnecting] = useState<boolean>(false)
  const [isLnsConnected, setLnsConnected] = useState<boolean>(false)

  // file event
  const [csv, setCsv] = useState<any[]>([])
  const [devices, setDevices] = useState<any[]>([])
  const [failed, setFailed] = useState<number>(0)
  const [registrations, setRegistrations] = useState<RegistrationResult[]>([])

  // submit event
  const [isProcessing, setProcessing] = useState<boolean>(false)
  const [isAborted, setAborted] = useState<boolean>(false)

  const methods = useForm({
    mode: 'onChange',
    resolver: zodResolver(schema),
    defaultValues: {
      device_type: '',
      sku: undefined,
      registry_type: 'global',
      organization: undefined,
      application: undefined,
      upload_type: 'text',
      ids: '',
      file: undefined,
      catalog: 'public',
      network: '',
      activateToLNS: false,
      options: {},
      keys: {},
    },
  })

  const {
    control,
    setValue,
    resetField,
    handleSubmit,
    formState: { isValid, isSubmitting },
  } = methods

  const catalog = useWatch({
    name: 'catalog',
    control,
  })

  const registryType = useWatch({
    name: 'registry_type',
    control,
  })

  const uploadType = useWatch({
    name: 'upload_type',
    control,
  })

  const activateToLns = useWatch({
    name: 'activateToLNS',
    control,
  })

  const network = useWatch({
    name: 'network',
    control,
  })

  const options = useWatch({
    name: 'options',
    control,
  })

  const ids = useWatch({
    name: 'ids',
    control,
  })

  const keys = useWatch({
    name: 'keys',
    control,
  })

  const { mutateAsync: activateNetworkMutation } = useMutation<
    any,
    Error,
    ActivateNetworkInput
  >({
    mutationFn: (input) => activateNetwork(input),
  })

  const { mutateAsync: createRegistryMutation } = useMutation<
    RegistryModel,
    Error,
    {
      organizationId: string
      applicationId: string
      input: CreateRegistryInput
    }
  >({
    mutationFn: ({ organizationId, applicationId, input }) =>
      createRegistry({ organizationId, applicationId, input }),
    onSuccess: () =>
      queryClient.invalidateQueries({ queryKey: ['GetRegistries'] }),
  })

  const { mutateAsync: checkConnectivityMutation } = useMutation<
    CheckConnectivityResponse,
    Error,
    CheckConnectivityInput
  >({
    mutationFn: (input) => checkConnectivity(input),
  })

  const { data: networks, isFetching: isFetchingNetworks } = useQuery<
    NetworkModel[],
    Error
  >({
    queryKey: ['GetNetworks'],
    queryFn: ({ signal }) =>
      getNetworks({
        signal,
      }),
  })

  const supportedNetworkOptions = useMemo<SupportedNetworkOption[]>(
    () =>
      networks
        ? [
            {
              id: '',
              name: 'None',
              activationSupported: false,
              options: {},
            },
            ...networks,
          ].reduce((acc: SupportedNetworkOption[], network) => {
            acc.push({
              label: network.name,
              value: network.id,
              activationSupported: network.activationSupported,
              networkOptions: network?.options ?? {},
            })
            return acc
          }, [])
        : [],
    [networks]
  )

  const templateCatalogOptions = useMemo<BaseOption<string>[]>(
    () => [
      ...(isInternalAdmin ? [{ label: 'All', value: 'all' }] : []),
      { label: 'Application', value: 'application' },
      { label: 'Public', value: 'public' },
    ],
    [isInternalAdmin]
  )

  const onReadFile = (event: ChangeEvent<HTMLInputElement>) => {
    const file = event.target?.files?.[0]
    if (!file) {
      return
    }

    Papa.parse(file, {
      worker: true,
      skipEmptyLines: true,
      complete: ({ data }) => setCsv(data),
      error: (error) => {
        toast({
          status: 'error',
          msg: `Unable to read CSV file. ${getErrorMessage(error)}`,
          duration: 6000,
        })
      },
    })
  }

  const getTextDevices = useCallback((): void => {
    if (!ids) {
      return
    }

    const results = ids
      .split(/[\s,]+/g)
      .filter((line: string) => line.length > 0)
      .map((line: string) => ({
        ...reduceEmptyObject(keys ?? {}),
        deveui: line,
        activation_mode: keys?.activation_mode ?? 'MANUAL',
      }))
      .filter(Boolean)

    setDevices(results)
  }, [ids, keys])

  useCustomCompareEffect(
    () => {
      if (ids) {
        getTextDevices()
      }
    },
    [ids, keys],
    (a, b) => equals(a, b)
  )

  const getCsvDevices = useCallback(
    async (data: any): Promise<void> => {
      const { headers, rows } = data

      if (!headers.includes('deveui')) {
        toast({
          status: 'info',
          msg: 'DevEUI column required to be set for CSV data.',
        })
        return
      }

      const findValue = (row: any, key: string) =>
        row[headers.indexOf(key)] || null

      const results = rows
        .map((row: any) => ({
          deveui: findValue(row, 'deveui') ?? '',
          appeui: findValue(row, 'appeui') ?? '',
          appkey: findValue(row, 'appkey') ?? '',
          nwkskey: findValue(row, 'nwkskey') ?? '',
          appskey: findValue(row, 'appskey') ?? '',
          sku: findValue(row, 'sku') ?? '',
        }))
        .map((row: any) => {
          let activationMode: string
          if (row.appeui && row.appkey) {
            activationMode = 'OTAA'
          } else if (row.appskey && row.nwkskey) {
            activationMode = 'ABP'
          } else {
            activationMode = 'MANUAL'
          }
          return {
            ...row,
            activation_mode: activationMode,
          }
        })
        .reduce((acc: any[], row: any) => {
          acc.push(reduceEmptyObject(row ?? {}))
          return acc
        }, [])

      setDevices(results)
    },
    [toast]
  )

  const onCheckConnectivity = useCallback(async (): Promise<void> => {
    setLnsConnecting(true)
    setLnsConnected(false)

    const input = {
      network: network ?? '',
      options: options ?? {},
    } satisfies CheckConnectivityInput

    try {
      await checkConnectivityMutation(input)
      toast({
        status: 'success',
        msg: 'Connection ok!',
      })
      setLnsConnected(true)
    } catch (error: unknown) {
      Sentry.captureException(error)
      toast({
        status: 'error',
        msg: getErrorMessage(error),
        duration: 6000,
      })
    } finally {
      setLnsConnecting(false)
    }
  }, [toast, checkConnectivityMutation, options, network])

  const isLnsActivationSupported = useMemo<boolean>(
    () =>
      supportedNetworkOptions.some(
        ({ value, activationSupported }) =>
          value === (network ?? '') && activationSupported
      ),
    [network, supportedNetworkOptions]
  )

  const currentSupportedNetworkOptions = useMemo<Dict<any>>(() => {
    const match = supportedNetworkOptions.find(
      ({ value }) => value === (network ?? '')
    )
    return match?.networkOptions || {}
  }, [network, supportedNetworkOptions])

  const hasLnsValues = useMemo<boolean>(() => {
    return (
      Object.values(options ?? {}).filter((v) => v != null).length ===
      Object.keys(currentSupportedNetworkOptions).length
    )
  }, [options, currentSupportedNetworkOptions])

  const downloadResult = () => {
    try {
      const cellSep = ';'
      const lineSep = '\r\n'
      const csv = [
        ['Hardware ID', 'LNS Activation', 'Registry'],
        ...registrations.map(
          ({ deveui, activation, registration }: RegistrationResult) => [
            deveui,
            activation.message,
            registration.message,
          ]
        ),
      ]
        .map((line) => line.join(cellSep))
        .join(lineSep)

      saveAs(
        new Blob([csv], { type: 'text/plain;charset=utf-8' }),
        'result.csv.txt'
      )
    } catch (error: unknown) {
      Sentry.captureException(error)
      toast({
        status: 'error',
        msg: 'Unable to create file from data.',
      })
    }
  }

  const onChangeOrganization = async (value: string) => {
    setValue('application', undefined)

    const applications = await queryClient.fetchQuery({
      queryKey: ['GetApplications', value],
      queryFn: ({ signal }) =>
        getApplications({
          organizationId: value,
          signal,
        }),
    })

    const apps = (applications?.rows ?? []).reduce(
      (acc: BaseOption<string>[], { id, name }) => {
        acc.push({
          label: name,
          value: id,
        })
        return acc
      },
      []
    )

    const firstId = apps?.[0]?.value

    if (!firstId) {
      toast({
        status: 'warning',
        msg: 'Unable to fetch applications.',
      })
      return
    }

    setValue('organization', value)
    setValue('application', firstId)
  }

  const onResetSubFields = (): void => {
    const keys = [
      'network',
      'keys',
      'activateToLNS',
      'organization',
      'application',
    ] as const

    for (const key of keys) {
      resetField(key)
    }
  }

  const onResetSourceEvent = (): void => {
    resetField('organization')
    resetField('application')
    resetField('ids')
    resetField('file')
    resetField('keys')
    setCsv([])
    setDevices([])
  }

  const onReset = (): void => {
    onResetSourceEvent()
    setFailed(0)
    setRegistrations([])
  }

  const onGoBack = (): void => {
    navigate('/manage/device-registry', {
      viewTransition: true,
    })
  }

  const onSubmit = async (values: FormInputProps): Promise<boolean> => {
    const { sku: masterSku } = values

    if (devices.length === 0) {
      toast({
        status: 'info',
        msg: 'No devices to process.',
      })
      return false
    }

    setProcessing(true)

    const results: RegistrationResult[] = []
    const payload: RegistrationPayload = {
      devices,
      activate: values.activateToLNS ?? false,
      application_id: values.application ?? null,
      device_type: values.device_type ?? '',
      network: values.network ?? '',
      options: values.options ?? {},
    }

    await pMap(
      devices,
      async (device, i) => {
        if (isAborted || !organizationId || !applicationId) {
          return
        }
        const { sku: deviceSku } = device
        const sku = masterSku ?? deviceSku
        device.sku = undefined

        let result: RegistrationResult = {
          number: i + 1,
          deveui: device.deveui ?? '',
          activation: {
            status: payload.activate ? STATUS.PENDING : STATUS.SKIP,
            message: payload.activate ? 'Pending' : 'Skip',
            activationUrl: null,
          },
          registration: {
            status: STATUS.PENDING,
            message: 'Pending',
          },
        }

        if (payload.activate) {
          try {
            const res = await activateNetworkMutation({
              network: payload.network,
              devices: [device],
              options: payload.options,
            })

            result = {
              ...result,
              activation: {
                status: STATUS.OK,
                message: 'OK',
                activationUrl: res[0]?.activation_url,
              },
            }
          } catch (error: unknown) {
            Sentry.captureException(error)
            result = {
              ...result,
              activation: {
                status: STATUS.ERROR,
                message: getErrorMessage(error),
                activationUrl: null,
              },
            }
          }
        }

        try {
          const input = {
            application_id: payload?.application_id ?? undefined,
            hardware_id: device.deveui ?? '',
            sku: sku?.length > 0 ? sku : null,
            device_type_id: payload.device_type,
            network: payload.network,
            keys: {
              activation_mode: device.activation_mode ?? null,
              ...reduceEmptyObject(device ?? {}),
            },
          } satisfies CreateRegistryInput

          await createRegistryMutation({
            organizationId,
            applicationId,
            input,
          })

          result = {
            ...result,
            registration: {
              status: STATUS.OK,
              message: 'OK',
            },
          }
        } catch (error: unknown) {
          Sentry.captureException(error)

          let message: string | undefined
          if (error instanceof AxiosError) {
            switch (error?.response?.data?.statusCode) {
              case 409:
                message = 'Already Registered'
                break
              default:
                message = getErrorMessage(error)
                break
            }
          }

          result = {
            ...result,
            registration: {
              status: STATUS.ERROR,
              message: message ?? 'Unknown Error',
            },
          }
        } finally {
          results.push(result)
        }
      },
      { concurrency: 1 }
    )

    const stats = {
      total: devices.length,
      ok: results.reduce(
        (n: number, { registration, activation }) =>
          n +
          Number(
            [registration.status, activation.status].some(
              (status) => status === STATUS.OK
            )
          ),
        0
      ),
      rejected: results.reduce(
        (n: number, { registration, activation }) =>
          n +
          Number(
            [registration.status, activation.status].some(
              (status) => status === STATUS.ERROR
            )
          ),
        0
      ),
      registry: {
        ok: results.reduce(
          (n: number, { registration }) =>
            n + Number(registration.status === STATUS.OK),
          0
        ),
        rejected: results.reduce(
          (n: number, { registration }) =>
            n + Number(registration.status === STATUS.ERROR),
          0
        ),
      },
      lns: {
        ok: results.reduce(
          (n: number, { activation }) =>
            n + Number(activation.status === STATUS.OK),
          0
        ),
        rejected: results.reduce(
          (n: number, { activation }) =>
            n + Number(activation.status === STATUS.ERROR),
          0
        ),
      },
    }

    setRegistrations(results)
    setFailed(stats.rejected)

    const fulfilled: number = stats.total - stats.rejected

    if (stats.lns.rejected > 0) {
      toast({
        status: 'error',
        msg: `(${stats.lns.rejected}) activation issues.`,
        duration: 6000,
      })
    }

    if (stats.registry.rejected > 0) {
      toast({
        status: 'error',
        msg: `(${stats.registry.rejected}) registration issues.`,
        duration: 6000,
      })
    }

    if (stats.ok > 0) {
      toast({
        status: 'success',
        msg: `${fulfilled}/${stats.total} ${pluralize(
          fulfilled,
          'device'
        )} has been uploaded.`,

        duration: 6000,
      })
    }

    setDevices([])
    setProcessing(false)
    return true
  }

  return (
    <>
      <Helmet>
        <title>{metaTitle(title)}</title>
      </Helmet>
      <Flex align="center" justify="space-between" p={4}>
        <HStack>
          <BackButton onClick={onGoBack} />
          <Title title={title} />
        </HStack>
      </Flex>
      <Box px={4} pb={8}>
        <Suspense fallback={null}>
          <FormProvider {...methods}>
            <Box as="form" noValidate onSubmit={handleSubmit(onSubmit)}>
              <Container maxW="container.xl" m={0} pt={4}>
                <Grid
                  sx={{
                    gap: 4,
                    gridTemplateColumns: 'repeat(2, 1fr)',
                  }}
                >
                  <SelectField
                    name="catalog"
                    label="Template Catalog"
                    options={templateCatalogOptions}
                    control={control}
                    isRequired
                  />

                  <SelectTypeField
                    name="device_type"
                    label="Device Template"
                    control={control}
                    catalog={catalog ?? ''}
                    isRequired
                    onChange={({ value, category, subcategory }: any) => {
                      onResetSubFields()
                      setValue('device_type', value ?? '')
                      setDeviceActivationSupported(category === 'module')
                      setLora(subcategory === 'lora')
                    }}
                  />

                  <InputField
                    name="sku"
                    label="SKU (Optional)"
                    control={control}
                    onBlur={getTextDevices}
                  />

                  <RadioField
                    name="registry_type"
                    label="Registry Type"
                    control={control}
                    options={registryTypeOptions}
                    onChange={onResetSourceEvent}
                  />

                  {registryType === 'specific' && (
                    <>
                      <SelectOrganizationField
                        name="organization"
                        label="Organization"
                        control={control}
                        isRequired
                        onChange={({ value }: any) =>
                          onChangeOrganization(value)
                        }
                      />
                      <SelectApplicationField
                        name="application"
                        label="Application"
                        control={control}
                        isRequired
                      />
                    </>
                  )}

                  {lora && (
                    <>
                      <SelectField
                        name="network"
                        label="Network Server"
                        options={supportedNetworkOptions ?? []}
                        isLoading={isFetchingNetworks}
                        control={control}
                        isRequired
                        isSearchable
                        sx={{
                          gridColumn: '1 / -1',
                        }}
                      />
                      {isDeviceActivationSupported &&
                        isLnsActivationSupported && (
                          <Grid
                            sx={{
                              p: 4,
                              gap: 4,
                              gridTemplateColumns: '1fr 1fr',
                              borderRadius: 'base',
                              gridColumn: '1 / -1',
                              bg: 'blackAlpha.50',
                            }}
                          >
                            <CheckboxField
                              name="activateToLNS"
                              label="Register to the LNS"
                              control={control}
                              sx={{
                                py: 1,
                              }}
                            />
                            {activateToLns && (
                              <>
                                <Box
                                  sx={{
                                    textAlign: 'right',
                                  }}
                                >
                                  <Button
                                    type="button"
                                    aria-label="Check Connectivity"
                                    onClick={onCheckConnectivity}
                                    isDisabled={!hasLnsValues}
                                    isLoading={isLnsConnecting}
                                    loadingText="Connecting"
                                    colorScheme={
                                      isLnsConnected ? 'green' : 'secondary'
                                    }
                                    leftIcon={
                                      isLnsConnected ? (
                                        <CheckIcon />
                                      ) : (
                                        <CloudIcon />
                                      )
                                    }
                                  >
                                    {isLnsConnected
                                      ? 'Connected'
                                      : 'Check Connectivity'}
                                  </Button>
                                </Box>
                                {Object.entries(
                                  currentSupportedNetworkOptions ?? {}
                                ).map(
                                  ([
                                    key,
                                    {
                                      flags: { description },
                                      examples,
                                    },
                                  ]: any) => (
                                    <InputField
                                      key={key}
                                      name={`options.${key}`}
                                      label={description}
                                      placeholder={examples?.[0]}
                                      control={control}
                                      isRequired
                                      onChange={(
                                        e: React.ChangeEvent<HTMLInputElement>
                                      ) => {
                                        setLnsConnected(false)
                                        setValue('options', {
                                          ...options,
                                          [key]: e.target.value,
                                        })
                                      }}
                                      sx={{
                                        gridColumn: '1 / -1',
                                        label: {
                                          textTransform: 'capitalize',
                                        },
                                      }}
                                    />
                                  )
                                )}
                              </>
                            )}
                          </Grid>
                        )}
                    </>
                  )}

                  <RadioField
                    name="upload_type"
                    label="Device IDs Source"
                    control={control}
                    options={uploadTypeOptions}
                    onChange={onResetSourceEvent}
                    sx={{
                      gridColumn: '1 / -1',
                    }}
                  />

                  {uploadType === 'text' ? (
                    <TextareaField
                      name="ids"
                      label="Device IDs"
                      shortInfo="Paste in comma or newline separated identifiers."
                      control={control}
                      onBlur={getTextDevices}
                      rows={8}
                      isRequired
                      sx={{
                        gridColumn: '1 / -1',
                      }}
                    />
                  ) : (
                    <FileField
                      name="file"
                      label="Upload a CSV file"
                      placeholder="Your CSV file..."
                      acceptedFileTypes=".csv"
                      shortInfo="SKU (above) will override CSV values if any."
                      control={control}
                      onCallback={onReadFile}
                      isRequired
                      sx={{
                        gridColumn: '1 / -1',
                      }}
                    />
                  )}

                  {activateToLns && uploadType === 'text' && (
                    <SelectField
                      name="keys.activation_mode"
                      label="LoRaWAN Activation mode"
                      control={control}
                      options={activationModeOptions}
                    />
                  )}

                  {activateToLns &&
                    uploadType === 'text' &&
                    keys?.activation_mode === 'OTAA' && (
                      <>
                        <InputField
                          name="keys.appeui"
                          label="LoRaWAN AppEUI"
                          control={control}
                          isRequired
                        />
                        <InputField
                          name="keys.appkey"
                          label="LoRaWAN AppKey"
                          control={control}
                          isRequired
                          sx={{
                            gridColumn: '1 / -1',
                          }}
                        />
                      </>
                    )}

                  {activateToLns &&
                    uploadType === 'text' &&
                    keys?.activation_mode === 'ABP' && (
                      <>
                        <InputField
                          name="keys.nwkskey"
                          label="LoRaWAN NWK Session Key"
                          control={control}
                          isRequired
                        />
                        <InputField
                          name="keys.appskey"
                          label="LoRaWAN APP Session Key"
                          control={control}
                          isRequired
                        />
                      </>
                    )}

                  {uploadType === 'csv' && (
                    <CsvPreview
                      data={csv}
                      options={csvOptions}
                      exportable={true}
                      onCallback={getCsvDevices}
                      sx={{
                        gridColumn: '1 /span 2',
                      }}
                    />
                  )}

                  <UploadResultTable registrations={registrations} />

                  <Divider
                    sx={{
                      mt: 5,
                      gridColumn: '1 / -1',
                    }}
                  />

                  <HStack
                    sx={{
                      pt: 3,
                      gridColumn: '1 / -1',
                    }}
                  >
                    <Button
                      type="submit"
                      aria-label="Start Upload"
                      colorScheme="green"
                      isLoading={isSubmitting || isProcessing}
                      loadingText="Uploading"
                      isDisabled={
                        !isValid ||
                        isSubmitting ||
                        devices.length === 0 ||
                        failed > 0 ||
                        (activateToLns && !isLnsConnected)
                      }
                      sx={{
                        minW: 120,
                      }}
                    >
                      Start Upload
                    </Button>

                    {registrations.length > 0 && (
                      <>
                        <Button
                          type="button"
                          colorScheme="secondary"
                          leftIcon={<CloudDownloadIcon size={16} />}
                          isDisabled={isSubmitting || isProcessing}
                          onClick={downloadResult}
                        >
                          Download Results
                        </Button>
                        <Button
                          type="button"
                          colorScheme="gray"
                          variant="ghost"
                          isDisabled={isSubmitting || isProcessing}
                          onClick={onReset}
                        >
                          Reset
                        </Button>
                      </>
                    )}

                    {isProcessing ? (
                      <Button
                        type="button"
                        aria-label="Abort"
                        colorScheme="red"
                        variant="ghost"
                        onClick={() => {
                          setAborted(true)
                          setProcessing(false)
                        }}
                      >
                        Abort
                      </Button>
                    ) : (
                      <Button
                        type="button"
                        aria-label="Cancel"
                        variant="ghost"
                        colorScheme="gray"
                        onClick={onGoBack}
                      >
                        Cancel
                      </Button>
                    )}
                  </HStack>
                </Grid>
              </Container>
            </Box>
          </FormProvider>
        </Suspense>
      </Box>
    </>
  )
})

export async function loader() {
  const scopes = ['view:applications']

  const { checkLoaderPermissions } = await import('@/utils/loader-auth')
  await checkLoaderPermissions(scopes)

  return {
    title: 'Upload Devices',
    scopes,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <UploadDevices />
}
