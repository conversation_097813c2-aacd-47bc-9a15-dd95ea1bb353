import {
  lazy,
  memo,
  Suspense,
  useCallback,
  useState,
  useEffect,
  useMemo,
} from 'react'
import { Helm<PERSON> } from 'react-helmet-async'
import { Box, Flex, Link } from '@chakra-ui/react'
import pMap from 'p-map'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { useAtomValue } from 'jotai'
import { Title } from '@/components/title'
import { AddButton } from '@/components/add-button'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { Clipboard } from '@/components/data-table/clipboard'
import { DataTable } from '@/components/data-table/data-table'
import { useRouter } from '@/hooks/use-router'
import { useModal } from '@/hooks/use-modal'
import { useAbility } from '@/hooks/use-ability'
import { useOrganization } from '@/hooks/use-organization'
import { metaTitle } from '@/utils/meta-title'
import { tableAtom } from '@/utils/stores/table'
import { getApplications } from '@/api/applications'
import { getSettings } from '@/api/settings'
import type { ApplicationModel } from '@/types/models/application'
import type { PaginatedQueryResponse } from '@/types/api'
import type { ColumnProps, ActionProps } from '@/types/table'

interface ApplicationRecord {
  id: string
  name: string
  mydevices_url?: Maybe<string>
  custom_url?: Maybe<string>
}

const Add = lazy(() =>
  import('@/pages/applications/add').then(({ Add }) => ({
    default: Add,
  }))
)

const Edit = lazy(() =>
  import('@/pages/applications/edit').then(({ Edit }) => ({
    default: Edit,
  }))
)

const Applications = memo(() => {
  const tableName = 'applications'
  const modal = useModal()
  const { title } = useRouter()
  const queryClient = useQueryClient()
  const { organizationId } = useOrganization()
  const { can } = useAbility()
  const { limit, currPage } = useAtomValue(tableAtom(tableName))

  const canAdd = can(['create', 'applications'])
  const canEdit = can(['edit', 'applications'])

  const [records, setRecords] = useState<{
    count: number
    rows: ApplicationRecord[]
  }>({
    count: 0,
    rows: [],
  })

  const { data } = useQuery<
    PaginatedQueryResponse<ApplicationModel> | null,
    Error
  >({
    queryKey: ['GetApplications', organizationId, limit, currPage],
    queryFn: ({ signal }) =>
      organizationId
        ? getApplications({
            organizationId,
            limit,
            page: currPage,
            signal,
          })
        : null,
    enabled: !!organizationId,
  })

  useEffect(() => {
    if (!data) {
      return
    }

    const getData = async () => {
      const ids: string[] = data.rows.map(({ id }: ApplicationModel) => id)
      const settings = await pMap(
        ids,
        async (id) => {
          const { rows } = await queryClient.fetchQuery({
            queryKey: ['GetSettings', id],
            queryFn: ({ signal }) =>
              getSettings({
                applicationId: id,
                signal,
              }),
          })

          const mydevicesUrlSetting = rows.find(
            (r) => r.name === 'mydevices_url'
          )
          const customUrlSetting = rows.find((r) => r.name === 'custom_url')

          return {
            id,
            mydevices_url: mydevicesUrlSetting?.value ?? null,
            custom_url: customUrlSetting?.value ?? null,
          }
        },
        {
          concurrency: 1,
        }
      )

      const rows = data.rows.reduce(
        (acc: ApplicationRecord[], { id, name }) => {
          const setting = settings.find((s) => s.id === id)
          acc.push({
            id,
            name,
            mydevices_url: setting?.mydevices_url,
            custom_url: setting?.custom_url,
          })

          return acc
        },
        []
      )

      setRecords({
        ...data,
        rows,
      })
    }

    getData()
  }, [data, queryClient])

  const onAdd = (): void => {
    modal({
      component: <Add />,
      config: {
        data: {},
        onCallback: () => ({}),
      },
    })
  }

  const onEdit = useCallback(
    (row: ApplicationRecord): void => {
      modal({
        component: <Edit />,
        config: {
          title: `Edit “${row.name}”`,
          data: row,
          onCallback: () => ({}),
        },
      })
    },
    [modal]
  )

  const columns = useMemo<ColumnProps<ApplicationRecord>[]>(
    () => [
      {
        name: 'Applications',
        id: 'name',
        cell: ({ name }) => <Clipboard>{name}</Clipboard>,
      },
      {
        name: 'MyDevices Sub-Domain',
        id: 'mydevices_url',
        cell: ({ mydevices_url }) => {
          if (!mydevices_url) return <Box>-</Box>
          const domain = new URL(mydevices_url).hostname
          return (
            <Link
              href={mydevices_url}
              target="_blank"
              rel="noopener noreferrer"
            >
              {domain}
            </Link>
          )
        },
      },
      {
        name: 'Custom Domain',
        id: 'custom_url',
        cell: ({ custom_url }) => {
          if (!custom_url) return <Box>-</Box>
          const domain = new URL(custom_url).hostname
          return (
            <Link href={custom_url} target="_blank" rel="noopener noreferrer">
              {domain}
            </Link>
          )
        },
      },
    ],
    []
  )

  const actions = useMemo<ActionProps<ApplicationRecord>[]>(
    () => [
      {
        label: 'Edit Application',
        onClick: onEdit,
        canView: () => canEdit,
      },
    ],
    [onEdit, canEdit]
  )

  return (
    <>
      <Helmet>
        <title>{metaTitle([title])}</title>
      </Helmet>
      <Suspense fallback={null}>
        <Flex align="center" justify="space-between" p={4}>
          <Title title={title} count={records.count} suffix="Record" />
          {canAdd && <AddButton label="Add Application" onClick={onAdd} />}
        </Flex>
        <Box px={4}>
          <DataTable
            tableName={tableName}
            data={records}
            columns={columns}
            actions={actions}
          />
        </Box>
      </Suspense>
    </>
  )
})

export async function loader() {
  const scopes = ['manage:all']

  const { checkLoaderPermissions } = await import('@/utils/loader-auth')
  await checkLoaderPermissions(scopes)

  return {
    title: 'Applications',
    scopes,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <Applications />
}
