import { memo } from 'react'
import { Helmet } from 'react-helmet-async'
import {
  redirect,
  type ActionFunctionArgs,
  type LoaderFunctionArgs,
} from 'react-router'
import { Box, Flex } from '@chakra-ui/react'
import { Title } from '@/components/title'
import { Tabs } from '@/components/tabs'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { useRouter } from '@/hooks/use-router'
import { metaTitle } from '@/utils/meta-title'
import { getPortalSession } from '@/api/billing'
import { SubscriptionList } from '@/features/subscription/components/subscription-list'
import { SubscriptionsTabs } from '@/features/subscription/components/subscriptions-tabs'

const Subscription = memo(() => {
  const heading = 'Subscriptions & Billing'
  const { title, currentPath } = useRouter()

  return (
    <>
      <Helmet>
        <title>{metaTitle([title, heading])}</title>
      </Helmet>
      <Flex align="center" justify="space-between" p={4}>
        <Title title={heading} />
      </Flex>
      <Box px={4}>
        <Tabs current={currentPath}>
          <SubscriptionsTabs />
          <SubscriptionList />
        </Tabs>
      </Box>
    </>
  )
})

export async function loader({ params }: LoaderFunctionArgs) {
  const scopes = ['edit:organizations'] as string[]
  // enabledFeatures?.billing_powerbi_console_subscription_feature_enabled

  const { checkLoaderPermissions } = await import('@/utils/loader-auth')
  await checkLoaderPermissions(scopes)

  const { getFilteredTabs } = await import('@/utils/tab-loader')
  const tabs = await getFilteredTabs('account-tabs', params)

  return {
    title: 'Subscriptions & Billing',
    scopes,
    tabs,
  }
}

export async function action({ request }: ActionFunctionArgs) {
  const formData = await request.formData()
  const returnUrl = formData.get('return_url') as string
  const subscriptionId = formData.get('subscription_id') as string

  if (!returnUrl) {
    throw new Error('Missing return url')
  }

  if (!subscriptionId) {
    throw new Error('Missing subscription id')
  }

  const { url } = await getPortalSession(subscriptionId, {
    return_url: returnUrl,
  })

  return redirect(url)
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <Subscription />
}
