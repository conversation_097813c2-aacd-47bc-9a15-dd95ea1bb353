import {
  redirect,
  type ActionFunctionArgs,
  type LoaderFunctionArgs,
} from 'react-router'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { PageSpinner } from '@/components/page-spinner'
import { getPortalSession } from '@/api/billing'
import {
  createPowerBiSubscription,
  createTriggersSubscription,
} from '@/api/subscriptions'

type Category = 'app-powerbi' | 'app-triggers'

export async function action({ request }: ActionFunctionArgs) {
  const formData = await request.formData()
  const returnUrl = formData.get('return_url') as string
  const category = formData.get('category') as Category
  const quantity = formData.get('quantity') as string
  const organizationId = formData.get('organization_id') as string

  if (!returnUrl) {
    throw new Error('Missing return url')
  }

  if (!category) {
    throw new Error('Missing category')
  }

  if (category === 'app-powerbi' && !quantity) {
    throw new Error('Missing quantity')
  }

  if (category === 'app-triggers') {
    const subscription = await createTriggersSubscription()

    const { url } = await getPortalSession(subscription.id, {
      return_url: returnUrl,
    })

    return redirect(url)
  }

  if (category === 'app-powerbi') {
    const subscription = await createPowerBiSubscription(
      organizationId,
      Number(quantity)
    )

    const { url } = await getPortalSession(subscription.id, {
      return_url: returnUrl,
    })

    return redirect(url)
  }

  throw new Error('Invalid category')
}

export async function loader({ params }: LoaderFunctionArgs) {
  const scopes = ['edit:organizations']

  const { checkLoaderPermissions } = await import('@/utils/loader-auth')
  await checkLoaderPermissions(scopes)

  const { getFilteredTabs } = await import('@/utils/tab-loader')
  const mainTabs = await getFilteredTabs('account-tabs', params)
  const subTabs = await getFilteredTabs('subscription-tabs', params)

  return {
    title: 'Create Subscription',
    scopes,
    tabs: mainTabs,
    subTabs,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return (
    <>
      <PageSpinner />
    </>
  )
}
