import * as Sentry from '@sentry/react'
import type { LoaderFunctionArgs } from 'react-router'
import { Suspense, useEffect, useMemo, memo } from 'react'
import { Helmet } from 'react-helmet-async'
import { useForm, FormProvider } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { Box, Flex, VStack, StackDivider } from '@chakra-ui/react'
import { Title } from '@/components/title'
import { Tabs } from '@/components/tabs'
import { SignOutButton } from '@/components/signout-button'
import { EditableField } from '@/components/editable-field'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { useRouter } from '@/hooks/use-router'
import { useAbility } from '@/hooks/use-ability'
import { useOrganization } from '@/hooks/use-organization'
import { useToast } from '@/hooks/use-toast'
import { getOrganization, updateOrganization } from '@/api/organizations'
import { useAuth } from '@/contexts/use-auth'
import { metaTitle } from '@/utils/meta-title'
import type { OrganizationModel } from '@/types/models/organization'
import type { OrganizationInput } from '@/types/api'

const schema = z.object({
  organization: z.string().min(2, 'Organization is required.'),
})

type FormInputProps = z.infer<typeof schema>

const Organization = memo(() => {
  const heading = 'Account'
  const toast = useToast()
  const queryClient = useQueryClient()
  const { title, currentPath } = useRouter()
  const { organizationId } = useOrganization()
  const { can } = useAbility()
  const { isInternalAdmin } = useAuth()

  const canEdit = useMemo<boolean>(
    () => can(['edit', 'organizations']) && !isInternalAdmin,
    [can, isInternalAdmin]
  )

  const methods = useForm({
    mode: 'onTouched',
    resolver: zodResolver(schema),
    defaultValues: {
      organization: isInternalAdmin ? 'IoT in a Box' : '',
    },
  })

  const { setValue, control, handleSubmit } = methods

  const { mutateAsync: updateOrganizationMutation } = useMutation<
    OrganizationModel,
    Error,
    {
      organizationId: string
      input: OrganizationInput
    }
  >({
    mutationFn: ({ organizationId, input }) =>
      updateOrganization({ organizationId, input }),
    onSuccess: () => {
      const cache = ['GetOrganizations', 'GetOrganization']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const { data } = useQuery<OrganizationModel | null, Error>({
    queryKey: ['GetOrganization', organizationId],
    queryFn: ({ signal }) =>
      organizationId
        ? getOrganization({
            organizationId,
            signal,
          })
        : null,
    refetchOnWindowFocus: false,
    enabled: !!organizationId && !isInternalAdmin,
  })

  useEffect(() => {
    if (!data?.name) {
      return
    }
    setValue('organization', data.name)
  }, [data?.name, setValue])

  const onSubmit = async (values: FormInputProps): Promise<boolean> => {
    if (!organizationId || isInternalAdmin) {
      return false
    }

    const input = {
      name: values.organization,
    } satisfies OrganizationInput

    try {
      await updateOrganizationMutation({
        organizationId,
        input,
      })
      toast({
        status: 'success',
        msg: 'Organization has been updated.',
      })
      return true
    } catch (error: unknown) {
      Sentry.captureException(error)
      toast({
        status: 'error',
        msg: 'Unable to update organization.',
      })
      return false
    }
  }

  return (
    <>
      <Helmet>
        <title>{metaTitle([title, heading])}</title>
      </Helmet>
      <Flex align="center" justify="space-between" p={4}>
        <Title title={heading} />
        <SignOutButton />
      </Flex>
      <Box px={4}>
        <Tabs current={currentPath}>
          <Suspense fallback={null}>
            <FormProvider {...methods}>
              <Box as="form" noValidate onSubmit={handleSubmit(onSubmit)}>
                <VStack
                  align="stretch"
                  divider={
                    <StackDivider
                      sx={{
                        borderColor: 'gray.200',
                        _dark: { borderColor: 'blackAlpha.300' },
                      }}
                    />
                  }
                  spacing={0}
                >
                  <EditableField
                    name="organization"
                    label="Organization"
                    control={control}
                    onSubmit={onSubmit}
                    isDisabled={!canEdit}
                  />
                </VStack>
              </Box>
            </FormProvider>
          </Suspense>
        </Tabs>
      </Box>
    </>
  )
})

export async function loader({ params }: LoaderFunctionArgs) {
  const scopes = [] as string[]

  const { checkLoaderPermissions } = await import('@/utils/loader-auth')
  await checkLoaderPermissions(scopes)

  const { getFilteredTabs } = await import('@/utils/tab-loader')
  const tabs = await getFilteredTabs('account-tabs', params)

  return {
    title: 'Organization',
    scopes,
    tabs,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <Organization />
}
