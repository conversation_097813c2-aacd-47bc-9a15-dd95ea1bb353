import { memo } from 'react'
import { Helmet } from 'react-helmet-async'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { SubscriptionUserList } from '@/features/subscription/components/users/subscription-user-list'
import { useRouter } from '@/hooks/use-router'
import { Title } from '@/components/title'
import { NestedTabs } from '@/components/nested-tabs'
import { metaTitle } from '@/utils/meta-title'
import { Flex, Box } from '@chakra-ui/react'
import type { LoaderFunctionArgs } from 'react-router'

const SubscriptionUsers = memo(() => {
  const heading = 'Subscriptions & Billing'
  const { title, currentPath } = useRouter()

  return (
    <>
      <Helmet>
        <title>{metaTitle([title, heading])}</title>
      </Helmet>
      <Flex align="center" justify="space-between" p={4}>
        <Title title={heading} />
      </Flex>
      <Box px={4}>
        <NestedTabs current={currentPath} subTabGroup="subscription-tabs">
          <SubscriptionUserList />
        </NestedTabs>
      </Box>
    </>
  )
})

export async function loader({ params }: LoaderFunctionArgs) {
  const scopes = ['edit:organizations']

  const { checkLoaderPermissions } = await import('@/utils/loader-auth')
  await checkLoaderPermissions(scopes)

  const { getFilteredTabs } = await import('@/utils/tab-loader')
  const mainTabs = await getFilteredTabs('account-tabs', params)
  const subTabs = await getFilteredTabs('subscription-tabs', params)

  return {
    title: 'Subscription & Billing',
    scopes,
    tabs: mainTabs,
    subTabs,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <SubscriptionUsers />
}
