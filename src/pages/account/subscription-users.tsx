import { memo } from 'react'
import { Helmet } from 'react-helmet-async'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { SubscriptionsTabs } from '@/features/subscription/components/subscriptions-tabs'
import { SubscriptionUserList } from '@/features/subscription/components/users/subscription-user-list'
import { useRouter } from '@/hooks/use-router'
import { Title } from '@/components/title'
import { Tabs } from '@/components/tabs'
import { metaTitle } from '@/utils/meta-title'
import { Flex, Box } from '@chakra-ui/react'

const SubscriptionUsers = memo(() => {
  const heading = 'Subscriptions & Billing'
  const { title, currentPath } = useRouter()

  return (
    <>
      <Helmet>
        <title>{metaTitle([title, heading])}</title>
      </Helmet>
      <Flex align="center" justify="space-between" p={4}>
        <Title title={heading} />
      </Flex>
      <Box px={4}>
        <Tabs current={currentPath}>
          <SubscriptionsTabs />
          <SubscriptionUserList />
        </Tabs>
      </Box>
    </>
  )
})

export async function loader() {
  return {
    title: 'Subscription & Billing',
    scopes: ['edit:organizations'],
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <SubscriptionUsers />
}
