import * as Sentry from '@sentry/react'
import type { LoaderFunctionArgs } from 'react-router'
import { Suspense, useEffect, memo } from 'react'
import { Helm<PERSON> } from 'react-helmet-async'
import { useForm, FormProvider } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { isEmpty } from 'ramda'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { Box, Flex, VStack, StackDivider } from '@chakra-ui/react'
import { Title } from '@/components/title'
import { Tabs } from '@/components/tabs'
import { SignOutButton } from '@/components/signout-button'
import { EditableField } from '@/components/editable-field'
import { EditablePasswordField } from '@/components/editable-password-field/editable-password-field'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { useAuth } from '@/contexts/use-auth'
import { useRouter } from '@/hooks/use-router'
import { useToast } from '@/hooks/use-toast'
import { metaTitle } from '@/utils/meta-title'
import { getCurrentUser, updateCurrentUser } from '@/api/users'
import { checkPasswordStrength } from '@/utils/check-password-strength'
import type { CurrentUserModel } from '@/types/models/user'
import type { UserInput } from '@/types/api'

const schema = z
  .object({
    firstName: z.string().min(1, 'First Name is required.'),
    lastName: z.string().min(1, 'Last Name is required.'),
    email: z.string().email(),
    phoneNumber: z.string().min(1, 'Mobile Number is required.'),
    locale: z.string().min(1, 'Locale is required.'),
    password: z
      .string()
      .min(8, 'Password must be at least 8 characters.')
      .or(z.literal('')),
    passwordConfirmation: z.string(),
  })
  .superRefine(({ password }, ctx) => {
    const result = checkPasswordStrength(password)
    if (result.id < 2) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['password'],
        message:
          'Password must contain upper/lowercase letters, numbers, & special chars.',
      })
    }
  })
  .superRefine((values, ctx) => {
    if (values.password !== values.passwordConfirmation) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Passwords do not match.',
        path: ['passwordConfirmation'],
      })
    }

    return z.NEVER
  })

type FormInputProps = z.infer<typeof schema>

const Profile = memo(() => {
  const heading = 'Account'
  const toast = useToast()
  const queryClient = useQueryClient()
  const { user } = useAuth()
  const { title, currentPath } = useRouter()

  const methods = useForm({
    mode: 'onTouched',
    resolver: zodResolver(schema),
    defaultValues: {
      firstName: '',
      lastName: '',
      email: user?.email ?? '',
      phoneNumber: '',
      locale: 'en-us',
      password: '',
      passwordConfirmation: '',
    },
  })

  const { reset, control, handleSubmit } = methods

  const { mutateAsync: updateCurrentUserMutation } = useMutation<
    CurrentUserModel,
    Error,
    Partial<UserInput>
  >({
    mutationFn: (input) => updateCurrentUser(input),
    onSuccess: () =>
      queryClient.invalidateQueries({ queryKey: ['GetCurrentUser'] }),
  })

  const { data: account } = useQuery<CurrentUserModel | null, Error>({
    queryKey: ['GetCurrentUser', user?.token],
    queryFn: ({ signal }) =>
      getCurrentUser({
        signal,
      }),
    refetchOnWindowFocus: false,
  })

  useEffect(() => {
    if (!account) {
      return
    }

    reset({
      firstName: account.firstName,
      lastName: account.lastName,
      email: account.email,
      phoneNumber: account?.attributes?.phoneNumber?.[0] ?? '',
      locale: account.locale ?? 'en-us',
      password: '',
      passwordConfirmation: '',
    })
  }, [reset, account])

  const onSubmit = async (values: FormInputProps): Promise<boolean> => {
    if (!user?.realm) {
      return false
    }

    let input: Partial<UserInput> = {
      firstName: values.firstName,
      lastName: values.lastName,
      attributes: {
        phoneNumber: values.phoneNumber,
        locale: values.locale,
      },
    }

    if (!isEmpty(values?.password)) {
      input = {
        ...input,
        password: values.password,
      }
    }

    try {
      await updateCurrentUserMutation(input)
      toast({
        status: 'success',
        msg: 'Account has been updated.',
      })
      return true
    } catch (error: unknown) {
      Sentry.captureException(error)
      toast({
        status: 'error',
        msg: 'Unable to update account.',
      })
      return false
    }
  }

  return (
    <>
      <Helmet>
        <title>{metaTitle([title, heading])}</title>
      </Helmet>
      <Flex align="center" justify="space-between" p={4}>
        <Title title={heading} />
        <SignOutButton />
      </Flex>
      <Box px={4}>
        <Tabs current={currentPath}>
          <Suspense fallback={null}>
            <FormProvider {...methods}>
              <Box as="form" noValidate onSubmit={handleSubmit(onSubmit)}>
                <VStack
                  align="stretch"
                  spacing={0}
                  divider={
                    <StackDivider
                      sx={{
                        borderColor: 'gray.200',
                        _dark: {
                          borderColor: 'blackAlpha.300',
                        },
                      }}
                    />
                  }
                >
                  <EditableField
                    name="firstName"
                    label="First Name"
                    control={control}
                    onSubmit={onSubmit}
                  />
                  <EditableField
                    name="lastName"
                    label="Last Name"
                    control={control}
                    onSubmit={onSubmit}
                  />
                  <EditableField
                    name="email"
                    type="email"
                    label="Email"
                    control={control}
                    onSubmit={onSubmit}
                    isDisabled
                  />
                  <EditableField
                    name="phoneNumber"
                    type="tel"
                    label="Mobile Number"
                    control={control}
                    onSubmit={onSubmit}
                  />
                  <EditablePasswordField
                    name="password"
                    label="Password"
                    control={control}
                    onSubmit={onSubmit}
                  />
                </VStack>
              </Box>
            </FormProvider>
          </Suspense>
        </Tabs>
      </Box>
    </>
  )
})

export async function loader({ params }: LoaderFunctionArgs) {
  const scopes = [] as string[]

  const { checkLoaderPermissions } = await import('@/utils/loader-auth')
  await checkLoaderPermissions(scopes)

  const { getFilteredTabs } = await import('@/utils/tab-loader')
  const tabs = await getFilteredTabs('account-tabs', params)

  return {
    title: 'Profile',
    scopes,
    tabs,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <Profile />
}
