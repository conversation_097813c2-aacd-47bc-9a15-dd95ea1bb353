import { lazy, memo, Suspense, useMemo, useCallback } from 'react'
import type { LoaderFunctionArgs } from 'react-router'
import { Helmet } from 'react-helmet-async'
import { useQuery } from '@tanstack/react-query'
import {
  Box,
  Flex,
  Grid,
  GridItem,
  Heading,
  Icon,
  Text,
  VStack,
  Container,
} from '@chakra-ui/react'
import { CheckIcon } from 'lucide-react'
import { Title } from '@/components/title'
import { Tabs } from '@/components/tabs'
import { SignOutButton } from '@/components/signout-button'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { useAuth } from '@/contexts/use-auth'
import { useRouter } from '@/hooks/use-router'
import { metaTitle } from '@/utils/meta-title'
import { upperFirst } from '@/utils/upper-first'
import { getCurrentUser } from '@/api/users'
import { getCurrentUserApplications } from '@/api/applications'
import { getOrganization } from '@/api/organizations'
import type { ApplicationModel } from '@/types/models/application'
import type { OrganizationModel } from '@/types/models/organization'
import type { CurrentUserModel } from '@/types/models/user'
import type { PaginatedQueryResponse } from '@/types/api'

const DomainUrl = lazy(() =>
  import('@/components/domain-url').then(({ DomainUrl }) => ({
    default: DomainUrl,
  }))
)

const Permissions = memo(() => {
  const heading = 'Account'
  const { title, currentPath } = useRouter()
  const { user, isInternalAdmin } = useAuth()

  const isEnabled = useMemo<boolean>(
    () => !!user?.organization,
    [user?.organization]
  )

  const { data: account } = useQuery<CurrentUserModel | null, Error>({
    queryKey: ['GetCurrentUser', user?.token],
    queryFn: ({ signal }) =>
      getCurrentUser({
        signal,
      }),
    enabled: isEnabled,
  })

  const { data: organization } = useQuery<OrganizationModel | null, Error>({
    queryKey: ['GetOrganization', user?.organization],
    queryFn: ({ signal }) =>
      user?.organization
        ? getOrganization({
            organizationId: user?.organization,
            signal,
          })
        : null,
    enabled: isEnabled,
  })

  const { data: apps } = useQuery<
    PaginatedQueryResponse<ApplicationModel> | null,
    Error,
    Pick<ApplicationModel, 'id' | 'name'>[]
  >({
    queryKey: ['GetApplications', user?.organization],
    queryFn: ({ signal }) =>
      user?.organization
        ? getCurrentUserApplications({
            signal,
          })
        : null,
    enabled: isEnabled,
    select: useCallback(
      (data: PaginatedQueryResponse<ApplicationModel> | null) =>
        (data?.rows ?? []).reduce(
          (acc: Pick<ApplicationModel, 'id' | 'name'>[], { id, name }) => {
            acc.push({ id, name })
            return acc
          },
          []
        ),
      []
    ),
  })

  const roleName = useMemo<string>(() => {
    if (user?.role === 'report') {
      return 'Reports Viewer'
    }
    return [upperFirst(user?.role ?? ''), 'Admin'].filter(Boolean).join(' ')
  }, [user?.role])

  const roleDescription = useMemo<string>(() => {
    const org = organization?.name
    const name = account?.firstName ?? ''
    if (isInternalAdmin) {
      return `${name}, you have access to all applications.`
    }
    if (user?.role === 'report') {
      return upperFirst(
        [account?.firstName ?? '', `you can view reports only for “${org}”.`]
          .filter(Boolean)
          .join(' ')
      )
    }
    return upperFirst(
      [name, `you have access to the following “${org}” applications:`]
        .filter(Boolean)
        .join(' ')
    )
  }, [user, account, organization, isInternalAdmin])

  return (
    <>
      <Helmet>
        <title>{metaTitle([title, heading])}</title>
      </Helmet>
      <Flex align="center" justify="space-between" p={4}>
        <Title title={heading} />
        <SignOutButton />
      </Flex>
      <Box px={4}>
        <Tabs current={currentPath}>
          <Suspense fallback={null}>
            <Container maxW="container.xl" m={0} pt={0}>
              {isEnabled ? (
                <Grid
                  templateColumns={{ base: '1fr', xl: 'repeat(6, 1fr)' }}
                  gap={4}
                  mt={4}
                >
                  <GridItem colSpan={4} p={5}>
                    <Heading as="h1" size="md" fontWeight="medium">
                      {roleDescription}
                    </Heading>
                    {!isInternalAdmin && (
                      <VStack spacing={2} align="stretch" mt={8}>
                        {(apps ?? []).map(({ id, name }) => (
                          <Flex
                            key={id}
                            align="center"
                            justify="space-between"
                            bg="blackAlpha.50"
                            borderRadius="base"
                            p={2}
                          >
                            <Flex align="center">
                              <Icon
                                as={CheckIcon}
                                sx={{
                                  mr: 4,
                                  color: 'secondary.500',
                                  _dark: {
                                    color: 'secondary.200',
                                  },
                                }}
                              />
                              <Text>{name}</Text>
                            </Flex>
                            <Suspense fallback={null}>
                              <DomainUrl applicationId={id} />
                            </Suspense>
                          </Flex>
                        ))}
                      </VStack>
                    )}
                  </GridItem>
                  <GridItem p={5} colSpan={2} colEnd={7}>
                    <Heading
                      as="h2"
                      size="md"
                      fontWeight="medium"
                      maxW={{ base: '100%', lg: '80%' }}
                    >
                      Role
                    </Heading>
                    <Box mt={4} mb={6}>
                      {roleName}
                    </Box>
                  </GridItem>
                </Grid>
              ) : (
                <Box m={4}>Unable to detect organization.</Box>
              )}
            </Container>
          </Suspense>
        </Tabs>
      </Box>
    </>
  )
})

export async function loader({ params }: LoaderFunctionArgs) {
  const scopes = [] as string[]

  const { checkLoaderPermissions } = await import('@/utils/loader-auth')
  await checkLoaderPermissions(scopes)

  const { getFilteredTabs } = await import('@/utils/tab-loader')
  const tabs = await getFilteredTabs('account-tabs', params)

  return {
    title: 'Permissions',
    scopes,
    tabs,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <Permissions />
}
