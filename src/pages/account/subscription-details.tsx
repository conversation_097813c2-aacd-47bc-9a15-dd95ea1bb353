import { memo } from 'react'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { SubscriptionDetails } from '@/features/subscription/components/subscription-details'
import { useRouter } from '@/hooks/use-router'
import { Helmet } from 'react-helmet-async'
import { Flex, Box } from '@chakra-ui/react'
import { metaTitle } from '@/utils/meta-title'
import { NestedTabs } from '@/components/nested-tabs'
import { Title } from '@/components/title'
import type { LoaderFunctionArgs } from 'react-router'

const Subscription = memo(() => {
  const heading = 'Subscriptions & Billing'
  const { title, currentPath } = useRouter()

  return (
    <>
      <Helmet>
        <title>{metaTitle([title, heading])}</title>
      </Helmet>
      <Flex align="center" justify="space-between" p={4}>
        <Title title={heading} />
      </Flex>
      <Box px={4}>
        <NestedTabs current={currentPath} subTabGroup="subscription-tabs">
          <SubscriptionDetails />
        </NestedTabs>
      </Box>
    </>
  )
})

export async function loader({ params }: LoaderFunctionArgs) {
  const scopes = ['edit:organizations']

  const { checkLoaderPermissions } = await import('@/utils/loader-auth')
  await checkLoaderPermissions(scopes)

  const { getFilteredTabs } = await import('@/utils/tab-loader')
  const mainTabs = await getFilteredTabs('account-tabs', params)
  const subTabs = await getFilteredTabs('subscription-tabs', params)

  return {
    title: 'Subscription & Billing',
    scopes,
    tabs: mainTabs,
    subTabs,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <Subscription />
}
