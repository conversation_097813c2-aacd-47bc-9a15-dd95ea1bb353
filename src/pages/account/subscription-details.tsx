import { memo } from 'react'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { SubscriptionsTabs } from '@/features/subscription/components/subscriptions-tabs'
import { SubscriptionDetails } from '@/features/subscription/components/subscription-details'
import { useRouter } from '@/hooks/use-router'
import { Helmet } from 'react-helmet-async'
import { Flex, Box } from '@chakra-ui/react'
import { metaTitle } from '@/utils/meta-title'
import { Tabs } from '@/components/tabs'
import { Title } from '@/components/title'

const Subscription = memo(() => {
  const heading = 'Subscriptions & Billing'
  const { title, currentPath } = useRouter()

  return (
    <>
      <Helmet>
        <title>{metaTitle([title, heading])}</title>
      </Helmet>
      <Flex align="center" justify="space-between" p={4}>
        <Title title={heading} />
      </Flex>
      <Box px={4}>
        <Tabs current={currentPath}>
          <SubscriptionsTabs />
          <SubscriptionDetails />
        </Tabs>
      </Box>
    </>
  )
})

export async function loader() {
  return {
    title: 'Subscription & Billing',
    scopes: ['edit:organizations'],
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return (
    <>
      <SubscriptionsTabs />
      <Subscription />
    </>
  )
}
