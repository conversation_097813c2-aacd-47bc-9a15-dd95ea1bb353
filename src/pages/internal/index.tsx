import { useCallback, useMemo, memo, useEffect } from 'react'
import { Navigate, useSearchParams } from 'react-router'
import { Helmet } from 'react-helmet-async'
import { useAtomValue } from 'jotai'
import { PageSpinner } from '@/components/page-spinner'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { idpAuthorize } from '@/api/keycloak'
import { useAuth } from '@/contexts/use-auth'
import { metaTitle } from '@/utils/meta-title'
import { lastLocationAtom } from '@/utils/stores/last-location'

const Internal = memo(() => {
  const [qs] = useSearchParams()
  const { login, isInternalAdmin } = useAuth()
  const lastLocation = useAtomValue(lastLocationAtom)

  const realm = useMemo(() => 'master', [])
  const state = useMemo(() => qs.get('state'), [qs])
  const code = useMemo(() => qs.get('code'), [qs])

  const redirectPath = useMemo(
    () => lastLocation?.pathname ?? '/manage/customers',
    [lastLocation]
  )

  const pageTitle = useMemo(() => metaTitle(['Loading']), [])

  const handleAuthorize = useCallback(() => {
    const idpUrl = idpAuthorize(realm, true)
    window.location.assign(decodeURIComponent(idpUrl))
  }, [realm])

  useEffect(() => {
    if (state && code) {
      login({ isInternal: true, realm, code, state })
    } else if (!isInternalAdmin) {
      handleAuthorize()
    }
  }, [state, code, isInternalAdmin, login, realm, handleAuthorize])

  const redirectComponent = useMemo(
    () => isInternalAdmin && <Navigate to={redirectPath} replace />,
    [isInternalAdmin, redirectPath]
  )

  if (isInternalAdmin) {
    return redirectComponent
  }

  return (
    <>
      <Helmet>
        <title>{pageTitle}</title>
      </Helmet>
      <PageSpinner />
    </>
  )
})

export async function loader() {
  const scopes = [] as string[]

  return {
    title: 'Internal Signin',
    scopes,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <Internal />
}
