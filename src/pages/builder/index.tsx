import { memo, lazy, Suspense, useRef } from 'react'
import { Helm<PERSON> } from 'react-helmet-async'
import type { EventSourceMessage } from '@microsoft/fetch-event-source'
import { useDeepCompareEffect, useMountEffect } from '@react-hookz/web'
import { useQueryClient } from '@tanstack/react-query'
import { useForm, useWatch, FormProvider } from 'react-hook-form'
import { useAtom, useSetAtom } from 'jotai'
import { useResetAtom } from 'jotai/utils'
import chroma from 'chroma-js'
import { sort, isNil, is } from 'ramda'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import {
  Box,
  Grid,
  GridItem,
  Button,
  HStack,
  Accordion,
  useDisclosure,
} from '@chakra-ui/react'
import {
  Panel,
  PanelGroup,
  type ImperativePanelHandle,
  type ImperativePanelGroupHandle,
} from 'react-resizable-panels'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { useRouter } from '@/hooks/use-router'
import { useToast } from '@/hooks/use-toast'
import { useRemoteAssistance } from '@/hooks/use-remote-assistance'
import { fallbackColors } from '@/utils/theme/fallback-theme'
import { metaTitle } from '@/utils/meta-title'
import { getApplicationUsers } from '@/api/users'
import type { UserRoleModel } from '@/types/models/user'

import { defaultValues } from '@/features/builder/utils/default-values'
import { formSchema } from '@/features/builder/utils/schema/form'
import { validPostParamSchema } from '@/features/builder/utils/schema/valid-post-params'
import type { FormInputProps } from '@/features/builder/utils/schema/types'
import { composeTheme } from '@/features/builder/utils/compose-theme'
import { PanelResize } from '@/features/builder/components/panel-resize'
import { useStream } from '@/hooks/use-stream'
import {
  resetAtom,
  jsonThemeAtom,
  panelSizeAtom,
} from '@/features/builder/utils/atoms'
import { getFontObject } from '@/api/builder'
import { logAtom } from '@/features/builder/utils/atoms'

const SectionBrand = lazy(() =>
  import('@/features/builder/components/section-brand').then(
    ({ SectionBrand }) => ({ default: SectionBrand })
  )
)

const SectionAssets = lazy(() =>
  import('@/features/builder/components/section-assets').then(
    ({ SectionAssets }) => ({ default: SectionAssets })
  )
)

const SectionTheme = lazy(() =>
  import('@/features/builder/components/section-theme').then(
    ({ SectionTheme }) => ({ default: SectionTheme })
  )
)

const SectionUi = lazy(() =>
  import('@/features/builder/components/section-ui').then(({ SectionUi }) => ({
    default: SectionUi,
  }))
)

const SectionColors = lazy(() =>
  import('@/features/builder/components/section-colors').then(
    ({ SectionColors }) => ({ default: SectionColors })
  )
)

const BuilderPreview = lazy(() =>
  import('@/features/builder/components/builder-preview').then(
    ({ BuilderPreview }) => ({
      default: BuilderPreview,
    })
  )
)

const BuilderHeader = lazy(() =>
  import('@/features/builder/components/builder-header').then(
    ({ BuilderHeader }) => ({ default: BuilderHeader })
  )
)

const JobIndicator = lazy(() =>
  import('@/features/builder/components/job-indicator').then(
    ({ JobIndicator }) => ({ default: JobIndicator })
  )
)

const BuilderLog = lazy(() =>
  import('@/features/builder/components/builder-log').then(
    ({ BuilderLog }) => ({ default: BuilderLog })
  )
)

const Builder = memo(() => {
  const toast = useToast()
  const { title } = useRouter()
  const resetLog = useResetAtom(resetAtom)
  const setPanelSize = useSetAtom(panelSizeAtom)
  const [jsonTheme, setJsonTheme] = useAtom(jsonThemeAtom)
  const { isOpen, onOpen, onClose } = useDisclosure()
  const queryClient = useQueryClient()
  const impersonate = useRemoteAssistance()
  const setLog = useSetAtom(logAtom)

  // @todo - add visual indicator for the connection state
  const { readyState, connect, disconnect } = useStream({
    onMessage: ({ id, data }: EventSourceMessage) => {
      if (id.length > 0) {
        const log = JSON.parse(data)
        setLog((prev) => [...prev, log])
      }
    },
  })

  const panelGroupRef = useRef<ImperativePanelGroupHandle>(null)
  const panelRef = useRef<ImperativePanelHandle>(null)

  useMountEffect(() => {
    const panelGroup = panelGroupRef.current
    if (panelGroup) {
      panelGroup.setLayout([50, 50])
      setPanelSize(50)
    }
  })

  const methods = useForm({
    mode: 'all',
    resolver: zodResolver(formSchema),
    defaultValues,
  })

  const {
    reset,
    control,
    getValues,
    handleSubmit,
    formState: { isValid, isValidating, isSubmitting },
  } = methods

  const formValues = useWatch({
    control,
  })

  const impersonatedUser = async (
    organizationId: string,
    applicationId: string
  ) => {
    try {
      const data = await queryClient.fetchQuery({
        queryKey: ['GetRoleUsers', organizationId, applicationId],
        queryFn: ({ signal }) =>
          getApplicationUsers({
            organizationId,
            applicationId,
            signal,
          }),
      })

      const sorted = sort(
        (a, b) => Date.parse(a.createdAt) - Date.parse(b.createdAt),
        data ?? []
      )

      const users = sorted.reduce((acc: UserRoleModel[], row) => {
        const match = row.roles.find((role) => role.role_id === 1)
        if (!(match && row.enabled)) {
          return acc
        }
        acc.push(row)
        return acc
      }, [])

      return users[0]
    } catch {
      return
    }
  }

  const onImpersonate = async () => {
    const values = getValues()
    const organizationId = values.orgInheritId
    const applicationId = values.appInheritId

    if (!(organizationId && applicationId)) {
      return
    }

    const user = await impersonatedUser(organizationId, applicationId)

    if (user?.tina_user_id) {
      impersonate({
        organizationId,
        applicationId,
        user,
      })
    } else {
      toast({
        msg: 'No admin user found.',
        status: 'error',
      })
    }
  }

  // Build the theme.json and set the jsonTheme atom
  const buildTheme = async (): Promise<void> => {
    if (!isValid || isSubmitting) {
      return
    }

    try {
      const values = getValues()

      const colors = {
        ...fallbackColors,
        primary: values?.primaryColor,
        secondary: values?.secondaryColor,
      }

      const validColors = Object.values(colors).every((color) =>
        chroma.valid(color)
      )

      const fontData = await queryClient.fetchQuery({
        queryKey: ['getFontObject', values.headingFont, values.bodyFont],
        queryFn: ({ signal }) =>
          getFontObject({
            headingFont: values.headingFont,
            bodyFont: values.bodyFont,
            signal,
          }),
      })

      const font = {
        font_url: fontData?.url,
        fonts: {
          body: fontData?.body,
          heading: fontData?.heading,
          mono: 'Menlo, monospace',
        },
      }

      if (!(values.themeId && font && validColors)) {
        return
      }

      const buttonRoundness =
        typeof values.buttonRoundness === 'string'
          ? Number(values.buttonRoundness)
          : (values.buttonRoundness ?? 6)

      const dashboardLogoHeight =
        typeof values.dashboardLogoHeight === 'string'
          ? Number(values.dashboardLogoHeight)
          : (values.dashboardLogoHeight ?? 40)

      const startLogoHeight =
        typeof values.startLogoHeight === 'string'
          ? Number(values.startLogoHeight)
          : (values.startLogoHeight ?? 120)

      const appIconSize =
        typeof values.appIconSize === 'string'
          ? Number(values.appIconSize)
          : (values.appIconSize ?? 100)

      const iconPadding =
        typeof values.iconPadding === 'string'
          ? Number(values.iconPadding)
          : (values.iconPadding ?? 20)

      const composed = composeTheme({
        ...values,
        hasDashboardLogo:
          !!values.dashboardLogo?.startsWith('data:image/svg+xml'),
        font,
        colors,
        buttonRoundness,
        dashboardLogoHeight,
        startLogoHeight,
        appIconSize,
        iconPadding,
      })

      setJsonTheme(JSON.stringify(composed))
    } catch (error: unknown) {
      setJsonTheme(null)
      if (error instanceof Error) {
        console.error(error)
      }
    }
  }

  const onDragging = () => {
    const size = panelRef.current?.getSize()
    if (is(Number, size)) {
      setPanelSize(size)
    }
  }

  useDeepCompareEffect(() => {
    buildTheme()
  }, [formValues])

  const onClear = () => {
    resetLog()
    setJsonTheme(null)
    reset(defaultValues)
    disconnect()
  }

  const onSubmit = async (values: FormInputProps): Promise<boolean> => {
    try {
      if (isNil(jsonTheme)) {
        throw new Error('Theme is not built')
      }

      resetLog()

      const endpoint = values.buildMode

      const payload = validPostParamSchema.parse({
        ...values,
        backgroundImage: values.background.backgroundImageFile,
        theme: JSON.stringify(JSON.parse(jsonTheme)),
      })

      console.info(`POST /api/builder/${endpoint}`, payload)
      await connect(`api/builder/${endpoint}`, payload)
      return true
    } catch (error: unknown) {
      disconnect()

      if (error instanceof z.ZodError) {
        toast({
          msg: error.issues?.[0]?.message ?? 'Unknown error',
          status: 'error',
        })
      } else if (error instanceof Error) {
        toast({
          msg: error.message,
          status: 'error',
        })
      }
      return false
    }
  }

  return (
    <>
      <Helmet>
        <title>{metaTitle(title)}</title>
      </Helmet>
      <FormProvider {...methods}>
        <Box as="form" noValidate onSubmit={handleSubmit(onSubmit)}>
          <Grid
            sx={{
              gap: 0,
              h: '100vh',
              gridTemplateRows: '65px auto 60px',
            }}
          >
            <Suspense fallback={null}>
              <BuilderHeader />
            </Suspense>
            <GridItem>
              <PanelGroup
                className="panel-group"
                direction="horizontal"
                ref={panelGroupRef}
              >
                <Panel ref={panelRef}>
                  <Accordion
                    defaultIndex={[0]}
                    reduceMotion
                    sx={{
                      h: '100%',
                    }}
                  >
                    <SectionBrand />
                    <SectionAssets control={control} />
                    <SectionTheme control={control} />
                    <SectionUi control={control} />
                    <SectionColors />
                  </Accordion>
                </Panel>
                <PanelResize onDragging={onDragging} />
                <Panel>
                  <Suspense fallback={null}>
                    <BuilderPreview control={control} />
                  </Suspense>
                </Panel>
              </PanelGroup>
            </GridItem>
            <GridItem
              sx={{
                display: 'flex',
                borderTopWidth: '2px',
                borderTopStyle: 'solid',
                borderTopColor: 'blackAlpha.300',
                alignItems: 'center',
                justifyContent: 'space-between',
                px: 4,
                bg: 'white',
                _dark: {
                  bg: 'gray.900',
                },
              }}
            >
              <HStack spacing={2}>
                <Suspense fallback={null}>
                  <JobIndicator />
                </Suspense>
              </HStack>
              <HStack spacing={2}>
                {formValues.appInheritId && formValues.email && (
                  <Button
                    type="button"
                    aria-label="Impersonate"
                    colorScheme="gray"
                    onClick={onImpersonate}
                    isDisabled={isSubmitting || isValidating}
                    minW="120px"
                  >
                    Impersonate
                  </Button>
                )}
                <Button
                  type="button"
                  aria-label="Reset"
                  colorScheme="gray"
                  onClick={onClear}
                  isDisabled={isSubmitting || isValidating}
                  minW="120px"
                >
                  Reset
                </Button>
                <Button
                  type="button"
                  aria-label="Build Log"
                  colorScheme="blue"
                  onClick={() => onOpen()}
                  minW="120px"
                >
                  Build Log
                </Button>
                <Button
                  type="submit"
                  aria-label="Submit"
                  colorScheme="green"
                  minW="120px"
                  isDisabled={
                    !isValid || isSubmitting || isValidating || readyState === 1
                  }
                  loadingText="Submitting"
                  isLoading={isSubmitting || readyState === 1}
                >
                  Submit
                </Button>
              </HStack>
            </GridItem>
          </Grid>
        </Box>
      </FormProvider>
      <Suspense fallback={null}>
        <BuilderLog isOpen={isOpen} onClose={onClose} />
      </Suspense>
    </>
  )
})

export async function loader() {
  const scopes = ['manage:all']

  const { checkLoaderPermissions } = await import('@/utils/loader-auth')
  await checkLoaderPermissions(scopes)

  return {
    title: 'Builder',
    scopes,
  }
}

export function Component() {
  return <Builder />
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}
