import * as Sentry from '@sentry/react'
import { useState, useRef, memo } from 'react'
import { useUnmountEffect } from '@react-hookz/web'
import { Helmet } from 'react-helmet-async'
import QrScanner from 'qr-scanner'
import { uniq } from 'ramda'
import { useAtom } from 'jotai'
import { Box, Button, Flex, Grid, Image, Stack, HStack } from '@chakra-ui/react'
import { VideoIcon, VideoOffIcon } from 'lucide-react'
import { Title } from '@/components/title'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { CodeItem } from '@/features/qr-reader/code-item'
import { EmptyItem } from '@/features/qr-reader/empty-item'
import { useRouter } from '@/hooks/use-router'
import { useToast } from '@/hooks/use-toast'
import { metaTitle } from '@/utils/meta-title'
import { qrAtom, codeAtom } from '@/utils/stores/qr'
import { QrCode } from '@/utils/qr-code'
import { blank64Gif } from '@/utils/constants'

const LIMIT = 10
const QrFrame =
  'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTI4IiBoZWlnaHQ9IjEyOCIgdmlld0JveD0iMCAwIDEyOCAxMjgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiAgICA8cGF0aAogICAgICAgIGQ9Ik0zMiAxMjdDMzIgMTI3LjU1IDMxLjU1IDEyOCAzMSAxMjhMMSAxMjhDMC40NSAxMjggNS4zNjYyZS0wOSAxMjcuNTUgMS4xOTI0OWUtMDggMTI3TDMuNjk2NzFlLTA3IDk3QzMuNzYyM2UtMDcgOTYuNDUgMC40NSA5NiAxIDk2QzEuNTUgOTYgMiA5Ni40NSAyIDk3TDIgMTI2TDMxIDEyNkMzMS41NSAxMjYgMzIgMTI2LjQ1IDMyIDEyN1oiCiAgICAgICAgZmlsbD0iI0ZFRkVGRSIgLz4KICAgIDxwYXRoCiAgICAgICAgZD0iTTEyNyA5NkMxMjcuNTUgOTYgMTI4IDk2LjQ1IDEyOCA5N0wxMjggMTI3QzEyOCAxMjcuNTUgMTI3LjU1IDEyOCAxMjcgMTI4TDk3IDEyOEM5Ni40NSAxMjggOTYgMTI3LjU1IDk2IDEyN0M5NiAxMjYuNDUgOTYuNDUgMTI2IDk3IDEyNkwxMjYgMTI2TDEyNiA5N0MxMjYgOTYuNDUgMTI2LjQ1IDk2IDEyNyA5NloiCiAgICAgICAgZmlsbD0iI0ZFRkVGRSIgLz4KICAgIDxwYXRoCiAgICAgICAgZD0iTTEgMzJDMC40NSAzMiAwIDMxLjU1IDAgMzFWMUMwIDAuNDUgMC40NSAwIDEgMEgzMUMzMS41NSAwIDMyIDAuNDUgMzIgMUMzMiAxLjU1IDMxLjU1IDIgMzEgMkgyVjMxQzIgMzEuNTUgMS41NSAzMiAxIDMyWiIKICAgICAgICBmaWxsPSIjRkVGRUZFIiAvPgogICAgPHBhdGgKICAgICAgICBkPSJNOTYgMC45OTk5OTlDOTYgMC40NDk5OTkgOTYuNDUgLTEuMzc5MDllLTA2IDk3IC0xLjM1NTA1ZS0wNkwxMjcgLTQuMzcxMTRlLTA4QzEyNy41NSAtMS45NjcwMWUtMDggMTI4IDAuNDUgMTI4IDFMMTI4IDMxQzEyOCAzMS41NSAxMjcuNTUgMzIgMTI3IDMyQzEyNi40NSAzMiAxMjYgMzEuNTUgMTI2IDMxTDEyNiAyTDk3IDJDOTYuNDUgMiA5NiAxLjU1IDk2IDAuOTk5OTk5WiIKICAgICAgICBmaWxsPSIjRkVGRUZFIiAvPgo8L3N2Zz4='

const QrReader = memo(() => {
  const toast = useToast()
  const { title } = useRouter()
  const scanner = useRef<QrScanner>(undefined)
  const videoRef = useRef<HTMLVideoElement>(null)
  const qrBoxRef = useRef<HTMLDivElement>(null)
  const [codes, setCodes] = useAtom(codeAtom)
  const [qr, setQr] = useAtom(qrAtom)
  const [svg, setSvg] = useState<string>(blank64Gif)
  const [qrOn, setQrOn] = useState<boolean>(false)

  const onScanSuccess = (result: QrScanner.ScanResult) => {
    if (!result?.data) {
      return
    }
    const code = result.data
    let ary = codes.length >= LIMIT ? codes.pop() : codes
    ary = uniq([code, ...codes])
    setCodes(ary)
    setQr(code)

    // Generate QR code
    const matrix = QrCode.generate(code)
    const uri = QrCode.render('svg-uri', matrix)
    setSvg(uri as string)
  }

  const onStart = async () => {
    if (!videoRef?.current) {
      return
    }

    if (scanner.current) {
      scanner.current.start()
      return
    }

    scanner.current = new QrScanner(videoRef?.current, onScanSuccess, {
      preferredCamera: 'environment',
      highlightScanRegion: true,
      highlightCodeOutline: true,
      overlay: qrBoxRef?.current || undefined,
      maxScansPerSecond: 1,
    })

    try {
      await scanner?.current?.start()
      setQrOn(true)
      toast({
        status: 'info',
        msg: 'Camera started.',
      })
    } catch (error: unknown) {
      Sentry.captureException(error)
      setQrOn(false)
      if (error instanceof ErrorEvent) {
        toast({
          status: 'error',
          msg: 'Unable to start camera.',
        })
      }
    }
  }

  const onStop = (): void => {
    if (!scanner?.current) {
      return
    }
    scanner?.current?.stop()
    setQrOn(false)
    setCodes([])
    toast({
      status: 'info',
      msg: 'Camera stopped.',
    })
  }

  useUnmountEffect(() => {
    if (scanner?.current) {
      scanner?.current?.destroy()
      setQrOn(false)
      setCodes([])
      setQr('')
    }
  })

  return (
    <>
      <Helmet>
        <title>{metaTitle([title])}</title>
      </Helmet>
      <Flex
        sx={{
          p: 4,
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <Title title={title} />
        <HStack>
          <Button
            type="button"
            aria-label="Start"
            leftIcon={<VideoIcon size={16} />}
            colorScheme="green"
            onClick={onStart}
          >
            Start
          </Button>
          <Button
            type="button"
            aria-label="Stop"
            leftIcon={<VideoOffIcon size={16} />}
            colorScheme="red"
            onClick={onStop}
          >
            Stop
          </Button>
        </HStack>
      </Flex>
      <Box px={4}>
        <Grid templateColumns="750px auto" gridGap={4}>
          <Flex
            sx={{
              w: '750px',
              h: '560px',
              position: 'relative',
              borderRadius: 'base',
              overflow: 'hidden',
              justifyContent: 'center',
              alignItems: 'center',
              bg: 'black',
              boxShadow: 'md',
              _dark: {
                bg: 'blackAlpha.400',
                boxShadow: 'dark-md',
              },
            }}
          >
            <Box
              as="video"
              muted
              ref={videoRef}
              sx={{
                w: '100%',
                h: 'auto',
                objectFit: 'cover',
              }}
            />
            <Box
              ref={qrBoxRef}
              sx={{
                w: '100%',
                left: 0,
              }}
            >
              <Image
                src={QrFrame}
                sx={{
                  position: 'absolute',
                  fill: 'none',
                  left: '50%',
                  top: '50%',
                  transform: 'translate(-50%, -50%)',
                }}
              />
            </Box>
          </Flex>
          <Stack spacing={2}>
            {codes.length === 0 && <EmptyItem paused={!qrOn} />}
            {codes.map((code, key) => (
              <CodeItem key={key} code={code} />
            ))}
            {qr && (
              <>
                <Flex
                  as="pre"
                  sx={{
                    p: 4,
                    fontSize: 'xs',
                    borderRadius: 'base',
                    justifyContent: 'stretch',
                    color: 'gray.500',
                    bg: 'white',
                    borderWidth: '1px',
                    _dark: {
                      color: 'yellow.100',
                      bg: 'blackAlpha.400',
                      borderWidth: 0,
                    },
                  }}
                >
                  {`Last scan: ${qr}`}
                </Flex>
                <Image
                  src={svg}
                  sx={{
                    w: '200px',
                    h: 'auto',
                    borderRadius: 'base',
                    border: '1px solid',
                    bg: 'white',
                    borderColor: 'gray.100',
                    _dark: {
                      bg: 'blackAlpha.400',
                      borderWidth: 0,
                    },
                  }}
                />
              </>
            )}
          </Stack>
        </Grid>
      </Box>
    </>
  )
})

export async function loader() {
  const scopes = ['manage:all']

  const { checkLoaderPermissions } = await import('@/utils/loader-auth')
  await checkLoaderPermissions(scopes)

  return {
    title: 'QR Reader',
    scopes,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <QrReader />
}
