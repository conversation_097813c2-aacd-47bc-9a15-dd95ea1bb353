import * as Sentry from '@sentry/react'
import {
  memo,
  lazy,
  Suspense,
  useCallback,
  useMemo,
  useDeferredValue,
} from 'react'
import { Helmet } from 'react-helmet-async'
import { useNavigate } from 'react-router'
import { useAtomValue } from 'jotai'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { Badge, Box, Flex } from '@chakra-ui/react'
import { Title } from '@/components/title'
import { AddButton } from '@/components/add-button'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { Clipboard } from '@/components/data-table/clipboard'
import { DataTable } from '@/components/data-table/data-table'
import { useRouter } from '@/hooks/use-router'
import { useModal } from '@/hooks/use-modal'
import { useColorMap } from '@/hooks/use-color-map'
import { useToast } from '@/hooks/use-toast'
import { metaTitle } from '@/utils/meta-title'
import { formatDate } from '@/utils/date/format-date'
import { tableAtom } from '@/utils/stores/table'
import { deleteFuse, getFuses } from '@/api/fuses'
import type { FuseModel } from '@/types/models/fuse'
import type { GetResponse } from '@/types/api'
import type { ColumnProps, ActionProps } from '@/types/table'

const ConfirmDialog = lazy(() =>
  import('@/components/confirm-dialog').then(({ ConfirmDialog }) => ({
    default: ConfirmDialog,
  }))
)

const View = lazy(() =>
  import('@/pages/integrations-active/view').then(({ View }) => ({
    default: View,
  }))
)

const eventOptions = [
  { label: 'Alert', value: 'alert' },
  { label: 'Uplink', value: 'uplink' },
  { label: 'Ping', value: 'ping' },
] satisfies BaseOption<string>[]

const statusOptions = [
  { label: 'Running', value: 'RUNNING' },
  { label: 'Pending', value: 'PENDING' },
  { label: 'Failed', value: 'FAIL' },
  { label: 'Stopped', value: 'STOPPED' },
] satisfies BaseOption<string>[]

const globalOptions = [
  { label: 'Yes', value: '1' },
  { label: 'No', value: '0' },
] satisfies BaseOption<string>[]

const ActiveIntegrations = memo(() => {
  const tableName = 'active-integrations'
  const modal = useModal()
  const toast = useToast()
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  const { mapColor } = useColorMap()
  const { title } = useRouter()
  const { limit, currPage, filter } = useAtomValue(tableAtom(tableName))
  const deferredFilter = useDeferredValue(filter)

  const { mutateAsync: deleteFuseMutation } = useMutation<
    boolean,
    Error,
    string
  >({
    mutationFn: (fuseId) => deleteFuse(fuseId),
    onSuccess: () => {
      const cache = ['GetFuses', 'GetFuse']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const { data } = useQuery<GetResponse<FuseModel>, Error>({
    queryKey: ['GetFuses', limit, currPage, deferredFilter],
    queryFn: ({ signal }) =>
      getFuses({
        limit,
        page: currPage,
        filter: deferredFilter,
        signal,
      }),
  })

  const records = useMemo<GetResponse<FuseModel>>(
    () => data ?? { page: 0, count: 0, rows: [] },
    [data]
  )

  const onSetup = (): void => {
    navigate('/manage/setup-integration', {
      viewTransition: true,
    })
  }

  const onView = useCallback(
    (row: FuseModel): void => {
      modal({
        component: <View />,
        scrollBehavior: 'inside',
        config: {
          data: row,
          onCallback: () => ({}),
        },
      })
    },
    [modal]
  )

  const onRemove = useCallback(
    ({ id, name }: FuseModel): void => {
      modal({
        size: 'md',
        component: <ConfirmDialog />,
        config: {
          title: 'Remove Integration?',
          description: `Are you sure you want to remove “${name}”?`,
          confirmLabel: 'Remove',
          onCallback: async () => {
            try {
              await deleteFuseMutation(id)
              toast({
                status: 'success',
                msg: `“${name}” has been removed.`,
              })
            } catch (error: unknown) {
              Sentry.captureException(error)
              toast({
                status: 'error',
                msg: `Unable to delete “${name}”.`,
              })
            }
          },
        },
      })
    },
    [modal, toast, deleteFuseMutation]
  )

  const columns = useMemo<ColumnProps<FuseModel>[]>(
    () => [
      {
        name: 'Name',
        id: 'name',
        filter: {
          type: 'input',
          condition: 'like',
        },
        cell: ({ name }) => <Clipboard>{name}</Clipboard>,
      },
      {
        name: 'Fuse ID',
        id: 'id',
        canHide: true,
        filter: {
          type: 'input',
          condition: 'like',
        },
        cell: ({ id }) => <Clipboard>{id}</Clipboard>,
      },
      {
        name: 'Account ID',
        id: 'account_id',
        canHide: true,
        filter: {
          type: 'input',
          condition: 'like',
        },
        cell: ({ account_id }) => <Clipboard>{account_id}</Clipboard>,
      },
      {
        name: 'Events',
        id: 'event_subscriptions',
        w: '15%',
        filter: {
          type: 'select',
          options: eventOptions,
        },
        cell: ({ event_subscriptions }) =>
          event_subscriptions.split(',').map((v: string, i: number) => (
            <Badge mr={1} key={i} colorScheme={mapColor(v)}>
              {v}
            </Badge>
          )),
      },
      {
        name: 'Status',
        id: 'status',
        w: '10%',
        filter: {
          type: 'select',
          options: statusOptions,
        },
        cell: ({ status }) => (
          <Badge colorScheme={mapColor(status)}>{status}</Badge>
        ),
      },
      {
        name: 'Global',
        id: 'is_global',
        w: '10%',
        filter: {
          type: 'select',
          options: globalOptions,
        },
        cell: ({ is_global }) => (
          <Badge colorScheme={is_global === 1 ? 'secondary' : 'gray'}>
            {is_global === 1 ? 'Yes' : 'No'}
          </Badge>
        ),
      },
      {
        name: 'Application ID',
        id: 'application_id',
        w: '15%',
        canHide: true,
        filter: {
          type: 'input',
          condition: 'eq',
        },
        cell: ({ application_id }) =>
          application_id && <Clipboard>{application_id}</Clipboard>,
      },
      {
        name: 'Failed',
        id: 'failed_at',
        canHide: true,
        w: '10%',
        cell: ({ failed_at }) =>
          failed_at && <Box noOfLines={1}>{formatDate(failed_at)}</Box>,
      },
    ],
    [mapColor]
  )

  const actions = useMemo<ActionProps<FuseModel>[]>(
    () => [
      {
        label: 'View Integration',
        onClick: onView,
        canView: (): boolean => true,
      },
      {
        label: 'Remove Integration',
        onClick: onRemove,
        canView: (): boolean => true,
      },
    ],
    [onView, onRemove]
  )

  return (
    <>
      <Helmet>
        <title>{metaTitle([title])}</title>
      </Helmet>
      <Flex align="center" justify="space-between" p={4}>
        <Title title={title} count={records.count} suffix="Record" />
        <AddButton label="Setup Integration" onClick={onSetup} />
      </Flex>
      <Box px={4}>
        <Suspense fallback={null}>
          <DataTable
            tableName={tableName}
            data={records}
            columns={columns}
            actions={actions}
          />
        </Suspense>
      </Box>
    </>
  )
})

export async function loader() {
  const scopes = ['manage:all']

  const { checkLoaderPermissions } = await import('@/utils/loader-auth')
  await checkLoaderPermissions(scopes)

  return {
    title: 'Active Integrations',
    scopes,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <ActiveIntegrations />
}
