import * as Sentry from '@sentry/react'
import { memo, Suspense, useEffect, useMemo } from 'react'
import type { LoaderFunctionArgs } from 'react-router'
import { Helmet } from 'react-helmet-async'
import { useForm, useWatch } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { omit, without, isEmpty } from 'ramda'
import { startCase } from 'lodash'
import {
  Box,
  Button,
  Container,
  Divider,
  HStack,
  Flex,
  Grid,
  Link,
} from '@chakra-ui/react'
import { Title } from '@/components/title'
import { Tabs } from '@/components/tabs'
import { InputField } from '@/components/input-field'
import { SelectField } from '@/components/select-field'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { useAuth } from '@/contexts/use-auth'
import { useRouter } from '@/hooks/use-router'
import { useAbility } from '@/hooks/use-ability'
import { useOrganization } from '@/hooks/use-organization'
import { useToast } from '@/hooks/use-toast'
import { getApplication, updateApplication } from '@/api/applications'
import { getSettings, updateSettings } from '@/api/settings'
import { LANGUAGES } from '@/data/languages'
import { metaTitle } from '@/utils/meta-title'
import type { ApplicationModel } from '@/types/models/application'
import type { SettingModel } from '@/types/models/setting'
import type {
  SettingInput,
  PaginatedQueryResponse,
  UpdateApplicationInput,
} from '@/types/api'

const setupLink =
  'https://iot-help.scrollhelp.site/partners/Use-your-own-custom-domain.2511667250.html'

const schema = z.object({
  name: z.string().min(1, 'Application Name is required.'),
  description: z.string().min(1, 'Application Description is required.'),
  brand_name: z.string().min(2, 'Brand Name is required.'),
  mydevices_url: z.string().url({ message: 'Invalid URL.' }),
  custom_url: z.string().url({ message: 'Invalid URL.' }).or(z.literal('')),
  mydevices_console_url: z
    .string()
    .url({ message: 'Invalid URL.' })
    .or(z.literal('')),
  custom_console_url: z
    .string()
    .url({ message: 'Invalid URL.' })
    .or(z.literal('')),
  default_language: z.string(),
  company: z.string(),
  address: z.string(),
  contact_email: z.string(),
  faq_url: z.string().url({ message: 'Invalid URL.' }).or(z.literal('')),
  copyright_info: z.string(),
  sla_url: z.string().url({ message: 'Invalid URL.' }).or(z.literal('')),
  terms_url: z.string().url({ message: 'Invalid URL.' }).or(z.literal('')),
  ccpa_url: z.string().url({ message: 'Invalid URL.' }).or(z.literal('')),
  privacy_policy_url: z
    .string()
    .url({ message: 'Invalid URL.' })
    .or(z.literal('')),
  cookie_banner: z.string(),
  contact_email_for_application_errors: z
    .string()
    .email({ message: 'Invalid email address.' })
    .or(z.literal('')),
})

type FormInputProps = z.infer<typeof schema> & {
  [key: string]: any
}

const Product = memo(() => {
  const heading = 'Settings'
  const toast = useToast()
  const { title, currentPath } = useRouter()
  const { organizationId, applicationId } = useOrganization()
  const queryClient = useQueryClient()
  const { isInternalAdmin } = useAuth()
  const { can } = useAbility()

  const canEdit = can(['edit', 'applications'])

  const {
    reset,
    control,
    handleSubmit,
    formState: { isValid, isSubmitting },
  } = useForm({
    mode: 'onTouched',
    resolver: zodResolver(schema),
    defaultValues: {
      name: '',
      description: '',
      brand_name: '',
      mydevices_url: '',
      custom_url: '',
      mydevices_console_url: '',
      custom_console_url: '',
      default_language: 'en-US',
      company: '',
      address: '',
      contact_email: '',
      faq_url: '',
      copyright_info: '',
      sla_url: '',
      terms_url: '',
      ccpa_url: '',
      privacy_policy_url: '',
      cookie_banner: '',
      contact_email_for_application_errors: '',
    },
  })

  const company = useWatch({
    name: 'company',
    control,
  })

  const { mutateAsync: updateApplicationMutation } = useMutation<
    ApplicationModel,
    Error,
    UpdateApplicationInput
  >({
    mutationFn: (input) => updateApplication(input),
    onSuccess: () => {
      const cache = ['GetApplications', 'GetApplication']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const { mutateAsync: updateSettingMutation } = useMutation<
    SettingModel[],
    Error,
    {
      organizationId: string
      applicationId: string
      input: SettingInput
    }
  >({
    mutationFn: ({ organizationId, applicationId, input }) =>
      updateSettings({ organizationId, applicationId, input }),
    onSuccess: () =>
      queryClient.invalidateQueries({ queryKey: ['GetSettings'] }),
  })

  const { data: app } = useQuery<ApplicationModel | null, Error>({
    queryKey: ['GetApplication', organizationId, applicationId, currentPath],
    queryFn: ({ signal }) =>
      organizationId && applicationId
        ? getApplication({
            organizationId,
            applicationId,
            signal,
          })
        : null,
    enabled: !!organizationId && !!applicationId,
  })

  const { data } = useQuery<PaginatedQueryResponse<SettingModel> | null, Error>(
    {
      queryKey: ['GetSettings', applicationId, currentPath],
      queryFn: ({ signal }) =>
        applicationId
          ? getSettings({
              applicationId,
              signal,
            })
          : null,
      enabled: !!applicationId,
    }
  )

  const setting = useMemo<Dict<string>>(
    () =>
      data
        ? data.rows.reduce((acc: Dict<string>, row) => {
            acc[row.name] = row.value
            return acc
          }, {})
        : {},
    [data]
  )

  const copyrightPlaceholder = useMemo<string>(() => {
    const year = new Date().getFullYear()
    return `© ${year} ${company}. All Rights Reserved.`
  }, [company])

  useEffect(() => {
    if (!(setting && app)) {
      return
    }

    reset({
      name: app.name ?? '',
      description: app.description ?? '',
      brand_name: setting.brand_name ?? '',
      mydevices_url: setting.mydevices_url ?? '',
      custom_url: setting.custom_url ?? '',
      mydevices_console_url: setting.mydevices_console_url ?? '',
      custom_console_url: setting.custom_console_url ?? '',
      default_language: setting.default_language ?? 'en-US',
      company: setting.company ?? '',
      address: setting.address ?? '',
      contact_email: setting.contact_email ?? '',
      faq_url: setting.faq_url ?? '',
      copyright_info: isEmpty(setting.copyright_info)
        ? copyrightPlaceholder
        : setting.copyright_info,
      sla_url: setting.sla_url ?? '',
      terms_url: setting.terms_url ?? '',
      ccpa_url: setting.ccpa_url ?? '',
      privacy_policy_url: setting.privacy_policy_url ?? '',
      cookie_banner: setting.cookie_banner ?? '',
      contact_email_for_application_errors:
        setting.contact_email_for_application_errors ?? '',
    })
  }, [reset, setting, app, copyrightPlaceholder])

  const onSubmit = async (values: FormInputProps): Promise<boolean> => {
    if (!(organizationId && applicationId)) {
      return false
    }

    const params = without(
      ['name', 'description', 'mydevices_url'],
      (data?.rows ?? [])
        .map(({ name }) => name)
        .filter((name) => Object.keys(schema.shape).includes(name))
        .concat(Object.keys(schema.shape))
    )

    const privateParams = ['mydevices_console_url', 'custom_console_url']

    const input = params.reduce((acc: SettingInput, param) => {
      const setting = (data?.rows ?? []).find(({ name }) => name === param)
      if (setting) {
        acc.push(
          omit(['id', 'application_id'], {
            ...setting,
            value: values[setting.name],
          })
        )
      } else {
        acc.push({
          name: param,
          label: startCase(param),
          value: values[param],
          optional: 1,
          public: privateParams.includes(param) ? 0 : 1,
        })
      }
      return acc
    }, [])

    try {
      await updateApplicationMutation({
        organizationId,
        applicationId,
        name: values.name,
        description: values.description,
      })

      await updateSettingMutation({
        organizationId,
        applicationId,
        input,
      })

      toast({
        status: 'success',
        msg: 'Settings has been updated.',
      })
      return true
    } catch (error: unknown) {
      Sentry.captureException(error)
      toast({
        status: 'error',
        msg: 'Unable to update setting.',
      })
      return false
    }
  }

  return (
    <>
      <Helmet>
        <title>{metaTitle([title, heading])}</title>
      </Helmet>
      <Flex align="center" justify="space-between" p={4}>
        <Title title={heading} />
      </Flex>
      <Box px={4} pb={10}>
        <Tabs current={currentPath}>
          <Suspense fallback={null}>
            <Box as="form" noValidate onSubmit={handleSubmit(onSubmit)}>
              <Container maxW="container.xl" m={0} pt={4}>
                <Grid
                  gap={4}
                  templateColumns={{ base: '1fr', lg: 'repeat(2, 1fr)' }}
                >
                  <InputField
                    name="name"
                    label="Application Name"
                    control={control}
                    isRequired
                    autoFocus
                  />
                  <InputField
                    name="brand_name"
                    label="Brand Name"
                    type="url"
                    control={control}
                    isDisabled={!canEdit}
                    isRequired
                  />
                  <InputField
                    name="description"
                    label="Application Description"
                    isRequired
                    control={control}
                  />
                  <InputField
                    name="company"
                    label="Company Name"
                    autoComplete="organization"
                    control={control}
                    isRequired
                  />
                  <InputField
                    name="mydevices_url"
                    label="MyDevices Url"
                    type="url"
                    isRequired
                    control={control}
                    isDisabled={!isInternalAdmin}
                  />
                  <InputField
                    name="custom_url"
                    label="Custom Domain"
                    type="url"
                    control={control}
                    isDisabled={!canEdit}
                    render={
                      <Link
                        href={setupLink}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        Setup Instructions
                      </Link>
                    }
                  />
                  {isInternalAdmin && (
                    <>
                      <InputField
                        name="mydevices_console_url"
                        label="myDevices Console Url"
                        type="url"
                        control={control}
                      />
                      <InputField
                        name="custom_console_url"
                        label="Custom Console Domain"
                        type="url"
                        control={control}
                      />
                    </>
                  )}
                  <SelectField
                    name="default_language"
                    label="Default Language"
                    control={control}
                    options={LANGUAGES}
                    isDisabled={!canEdit}
                  />
                  <InputField
                    name="address"
                    label="Physical Mailing Address"
                    autoComplete="street-address"
                    control={control}
                    isDisabled={!canEdit}
                  />
                  <InputField
                    name="copyright_info"
                    label="Copyright Info"
                    placeholder={copyrightPlaceholder}
                    control={control}
                  />
                  <InputField
                    name="faq_url"
                    label="FAQ / Help Url"
                    type="url"
                    control={control}
                    isDisabled={!canEdit}
                  />
                  <InputField
                    name="sla_url"
                    label="SLA Url"
                    type="url"
                    control={control}
                  />
                  <InputField
                    name="terms_url"
                    label="Terms of Service Url"
                    type="url"
                    control={control}
                    isDisabled={!canEdit}
                  />
                  <InputField
                    name="ccpa_url"
                    label="CCPA Url"
                    type="url"
                    control={control}
                    isDisabled={!canEdit}
                  />
                  <InputField
                    name="privacy_policy_url"
                    label="Privacy Policy Url"
                    type="url"
                    control={control}
                    isDisabled={!canEdit}
                  />
                  <InputField
                    name="contact_email"
                    label="Contact Us (e.g. Link or Email)"
                    autoComplete="email"
                    control={control}
                  />
                  <InputField
                    autoComplete="email"
                    name="contact_email_for_application_errors"
                    label="Contact Email for Errors"
                    type="email"
                    control={control}
                    isDisabled={!canEdit}
                  />
                  {/* <TextareaField
                    name="cookie_banner"
                    label="Cookie Banner Code"
                    control={control}
                    isDisabled={!canEdit}
                    shortInfo="Ensure this feature is enabled in Show/Hide UI if used."
                    sx={{
                      gridColumn: '1 / -1',
                    }}
                  /> */}
                  {canEdit && (
                    <>
                      <Divider gridColumn="1 / -1" />
                      <Box gridColumn="1 / -1">
                        <HStack mt={3}>
                          <Button
                            type="submit"
                            aria-label="Save"
                            colorScheme="green"
                            minW={120}
                            isLoading={isSubmitting}
                            isDisabled={!isValid || isSubmitting}
                            loadingText="Saving"
                          >
                            Save
                          </Button>
                        </HStack>
                      </Box>
                    </>
                  )}
                </Grid>
              </Container>
            </Box>
          </Suspense>
        </Tabs>
      </Box>
    </>
  )
})

export async function loader({ params }: LoaderFunctionArgs) {
  const scopes = ['view:applications']

  const { checkLoaderPermissions } = await import('@/utils/loader-auth')
  await checkLoaderPermissions(scopes)

  const { getFilteredTabs } = await import('@/utils/tab-loader')
  const tabs = await getFilteredTabs('settings-tabs', params)

  return {
    title: 'Settings',
    scopes,
    tabs,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <Product />
}
