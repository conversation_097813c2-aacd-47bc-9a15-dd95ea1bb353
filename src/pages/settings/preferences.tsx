import * as Sentry from '@sentry/react'
import {
  lazy,
  memo,
  Fragment,
  Suspense,
  useMemo,
  useState,
  useEffect,
  useCallback,
  useDeferredValue,
  type ChangeEvent,
} from 'react'
import type { LoaderFunctionArgs } from 'react-router'
import { Helmet } from 'react-helmet-async'
import { isNil, omit } from 'ramda'
import Fuse from 'fuse.js'
import { useSet } from '@react-hookz/web'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  Box,
  Button,
  Container,
  Flex,
  HStack,
  Icon,
  Switch,
  Table,
  Tbody,
  Td,
  Th,
  Tooltip,
  Tr,
  Input,
  InputGroup,
  InputLeftElement,
  InputRightElement,
  IconButton,
  useColorModeValue,
} from '@chakra-ui/react'
import {
  InfoIcon,
  LockIcon,
  LockOpenIcon,
  SearchIcon,
  XIcon,
} from 'lucide-react'
import { Title } from '@/components/title'
import { Tabs } from '@/components/tabs'
import { FormHeading } from '@/components/form-heading'
import { Select } from '@/components/select'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { PrefEditableField } from '@/features/preferences/pref-editable-field'
import { PrefCreatableField } from '@/features/preferences/pref-creatable-field'
import { PrefColorPickerField } from '@/features/preferences/pref-color-picker-field'
import { PrefImageField } from '@/features/preferences/pref-image-field'
import { useAuth } from '@/contexts/use-auth'
import { useRouter } from '@/hooks/use-router'
import { useAbility } from '@/hooks/use-ability'
import { useModal } from '@/hooks/use-modal'
import { useOrganization } from '@/hooks/use-organization'
import { useToast } from '@/hooks/use-toast'
import { getSettings, updateSettings, deleteSetting } from '@/api/settings'
import { uploadImage, deleteImage } from '@/api/uploads'
import { postShopPriceBook, postShopPublic } from '@/api/shop'
import { LANGUAGES } from '@/data/languages'
import { PREFERENCES, type PrefProps } from '@/data/preferences'
import { metaTitle } from '@/utils/meta-title'
import type { SettingModel } from '@/types/models/setting'
import type { PaginatedQueryResponse, SettingInput } from '@/types/api'

const PasswordDialog = lazy(() =>
  import('@/components/password-dialog').then(({ PasswordDialog }) => ({
    default: PasswordDialog,
  }))
)

type Prefs = Dict<boolean>
type PrefGroup = {
  label: string
  password?: boolean
  internal?: boolean
  preferences: PrefProps[]
}

interface PrefFieldProps {
  pref: PrefProps
  currLang: BaseOption<string> | undefined
  isDisabled: boolean
  params: {
    value: boolean
    label?: SettingModel
    list?: SettingModel
    option?: SettingModel
    color?: SettingModel
    string?: SettingModel
    image?: SettingModel
    visible: boolean
  }
  onRemovePref: (pref: PrefProps) => void
}

const prefName = (name: string): string => {
  return name.replace(/_feature_enabled$/gi, '')
}

const isEnabled = (pref: PrefProps, prefs: Prefs): boolean => {
  if (isNil(pref.name) || pref.required === true) {
    return true
  }
  if (Object.getOwnPropertyDescriptor(prefs, pref.name)) {
    return !!prefs[pref.name]
  }
  if (!isNil(pref.defaultValue)) {
    return pref.defaultValue
  }
  return true
}

const PrefField = (props: PrefFieldProps) => {
  const toast = useToast()
  const queryClient = useQueryClient()
  const { applicationId, organizationId } = useOrganization()
  const { pref, currLang, params, onRemovePref, isDisabled } = props

  const { mutateAsync: updateSettingMutation } = useMutation<
    SettingModel[],
    Error,
    {
      organizationId: string
      applicationId: string
      input: SettingInput
    }
  >({
    mutationFn: ({ organizationId, applicationId, input }) =>
      updateSettings({ organizationId, applicationId, input }),
    onSuccess: () =>
      queryClient.invalidateQueries({ queryKey: ['GetSettings'] }),
  })

  const onSaveLabel = async (pref: PrefProps, value: string): Promise<void> => {
    if (!(applicationId && organizationId && pref?.name && currLang)) {
      return
    }

    const lang = currLang.value
    const name = prefName(pref.name)

    const input = [
      {
        label: `Custom Label for ${pref.label} (${currLang.label})`,
        name: `${name}_override_label@${lang}`,
        value,
        optional: 1,
        public: 1,
      },
    ] satisfies SettingInput

    try {
      await updateSettingMutation({
        organizationId,
        applicationId,
        input,
      })

      toast({
        status: 'success',
        msg: `${currLang.label} label for "${pref.label}" has been updated.`,
      })
    } catch (error: unknown) {
      Sentry.captureException(error)
      toast({
        status: 'error',
        msg: 'Unable to update label.',
      })
    }
  }

  const onSaveString = async (
    pref: PrefProps,
    value: string
  ): Promise<void> => {
    if (!(applicationId && organizationId && pref?.name)) {
      return
    }

    const name = prefName(pref.name)

    const input = [
      {
        label: `Custom value for ${pref.label}`,
        name: `${name}_string`,
        value,
        optional: 1,
        public: 1,
      },
    ] satisfies SettingInput

    try {
      await updateSettingMutation({
        organizationId,
        applicationId,
        input,
      })

      toast({
        status: 'success',
        msg: `Value for "${pref.label}" has been updated.`,
      })
    } catch (error: unknown) {
      Sentry.captureException(error)
      toast({
        status: 'error',
        msg: 'Unable to update label.',
      })
    }
  }

  const onSaveImage = async (pref: PrefProps, value: string): Promise<void> => {
    if (!(applicationId && organizationId && pref?.name && value)) {
      return
    }

    const name = prefName(pref.name)
    const parts = value.split(';')[0]?.split('/') || []

    if (parts.length < 2) {
      toast({
        status: 'error',
        msg: 'Invalid image format.',
      })
      return
    }

    const extension = parts[1]
    const contentType = `image/${extension}`
    const timestamp = Date.now()
    const fileName = `${name}_${timestamp}.${extension}`

    try {
      await deleteImage({
        fileName: name,
        applicationId,
      })
    } catch (error) {
      Sentry.captureException(error)
    }

    try {
      const s3Path = await uploadImage({
        base64Data: value,
        contentType,
        fileName,
        applicationId,
      })

      const input = [
        {
          label: `Image for ${pref.label}`,
          name: `${name}_file`,
          value: s3Path,
          optional: 1,
          public: 1,
        },
      ] satisfies SettingInput

      await updateSettingMutation({
        organizationId,
        applicationId,
        input,
      })

      toast({
        status: 'success',
        msg: `Image for "${pref.label}" has been updated.`,
      })
    } catch (error: unknown) {
      Sentry.captureException(error)
      toast({
        status: 'error',
        msg: 'Unable to update image.',
      })
    }
  }

  const onSaveOptions = async (
    pref: PrefProps,
    options: string[],
    action: string
  ): Promise<void> => {
    if (!(organizationId && applicationId && pref?.name && currLang)) {
      return
    }

    const lang = currLang.value
    const name = prefName(pref.name)

    try {
      const input = [
        {
          label: `Custom Options for ${pref.label} (${currLang.label})`,
          name: `${name}_options@${lang}`,
          value: JSON.stringify(options),
          optional: 1,
          public: 1,
        },
      ] satisfies SettingInput

      await updateSettingMutation({
        organizationId,
        applicationId,
        input,
      })

      toast({
        status: 'success',
        msg: `${currLang.label} options for "${pref.label}" has been ${action}.`,
      })
    } catch (error: unknown) {
      Sentry.captureException(error)
      toast({
        status: 'error',
        msg: 'Unable to update options.',
      })
    }
  }

  const onSaveList = async (
    pref: PrefProps,
    options: string[],
    action: string
  ): Promise<void> => {
    if (!(organizationId && applicationId && pref?.name && currLang)) {
      return
    }

    const name = prefName(pref.name)

    try {
      const input = [
        {
          label: `Custom Options for ${pref.label}`,
          name: `${name}_list`,
          value: JSON.stringify(options),
          optional: 1,
          public: 1,
        },
      ] satisfies SettingInput

      await updateSettingMutation({
        organizationId,
        applicationId,
        input,
      })

      toast({
        status: 'success',
        msg: `Options for "${pref.label}" has been ${action}.`,
      })
    } catch (error: unknown) {
      Sentry.captureException(error)
      toast({
        status: 'error',
        msg: 'Unable to update options.',
      })
    }
  }

  const onSaveColor = async (pref: PrefProps, value: string): Promise<void> => {
    if (!(organizationId && applicationId && pref?.name)) {
      return
    }

    const name = prefName(pref.name)

    try {
      const input = [
        {
          label: `Color for ${pref.label}`,
          name: `${name}_color`,
          value,
          optional: 1,
          public: 1,
        },
      ] satisfies SettingInput

      await updateSettingMutation({
        organizationId,
        applicationId,
        input,
      })

      toast({
        status: 'success',
        msg: `Color for "${pref.label}" has been updated.`,
      })
    } catch (error: unknown) {
      Sentry.captureException(error)
      toast({
        status: 'error',
        msg: 'Unable to update color.',
      })
    }
  }

  if (pref?.translatable)
    return (
      <PrefEditableField
        pref={pref}
        value={params.label?.value}
        onSave={(pref, value) => onSaveLabel(pref, value)}
        isDisabled={isDisabled}
      />
    )

  if (pref?.string)
    return (
      <PrefEditableField
        pref={pref}
        value={params.string?.value}
        onSave={(pref, value) => onSaveString(pref, value)}
        isMultiLine={true}
        isDisabled={isDisabled}
      />
    )

  if (pref?.options)
    return (
      <PrefCreatableField
        pref={pref}
        defaultValue={params.option?.value}
        onSave={(options, action) => onSaveOptions(pref, options, action)}
        isDisabled={isDisabled}
      />
    )

  if (pref?.list)
    return (
      <PrefCreatableField
        pref={pref}
        defaultValue={params.option?.value}
        onSave={(options, action) => onSaveList(pref, options, action)}
        isDisabled={isDisabled}
      />
    )

  if (pref?.color)
    return (
      <PrefColorPickerField
        pref={pref}
        value={params.color?.value}
        onSave={(pref, value) => onSaveColor(pref, value)}
        onRemove={(pref) => onRemovePref(pref)}
        isDisabled={isDisabled}
      />
    )

  if (pref?.image)
    return (
      <PrefImageField
        pref={pref}
        value={params.image?.value}
        onSave={(pref, value) => onSaveImage(pref, value)}
        onRemove={(pref) => onRemovePref(pref)}
        isDisabled={isDisabled}
      />
    )

  return null
}

const Preferences = memo(() => {
  const heading = 'Settings'
  const toast = useToast()
  const modal = useModal()
  const queryClient = useQueryClient()
  const { title, currentPath } = useRouter()
  const { isInternalAdmin } = useAuth()
  const { can } = useAbility()
  const { organizationId, applicationId } = useOrganization()
  const [isRestoring, setRestoring] = useState<boolean>(false)
  const [prefs, setPrefs] = useState<Prefs>({})
  const [prefsGroups, setPrefsGroups] = useState<PrefGroup[]>([])
  const [searchQuery, setSearchQuery] = useState<string>('')
  const deferredSearchQuery = useDeferredValue(searchQuery)
  const inputVariant = useColorModeValue('outline', 'filled')
  const unlocked = useSet<string>([])

  const [currLang, setCurrLang] = useState<BaseOption<string> | undefined>(() =>
    LANGUAGES.find(({ value }) => value === 'en-us')
  )

  const { mutateAsync: updateSettingMutation } = useMutation<
    SettingModel[],
    Error,
    {
      organizationId: string
      applicationId: string
      input: SettingInput
    }
  >({
    mutationFn: ({ organizationId, applicationId, input }) =>
      updateSettings({ organizationId, applicationId, input }),
    onSuccess: () =>
      queryClient.invalidateQueries({ queryKey: ['GetSettings'] }),
  })

  const { mutateAsync: deleteSettingMutation } = useMutation<
    boolean,
    Error,
    {
      organizationId: string
      applicationId: string
      name: string
    }
  >({
    mutationFn: ({ organizationId, applicationId, name }) =>
      deleteSetting({ organizationId, applicationId, name }),
    onSuccess: () =>
      queryClient.invalidateQueries({ queryKey: ['GetSettings'] }),
  })

  const { data } = useQuery<PaginatedQueryResponse<SettingModel> | null, Error>(
    {
      queryKey: ['GetSettings', applicationId, currentPath],
      queryFn: ({ signal }) =>
        applicationId
          ? getSettings({
              applicationId,
              signal,
            })
          : null,
      enabled: !!applicationId,
    }
  )

  const settings = useMemo<SettingModel[]>(
    () =>
      (data?.rows ?? []).filter(({ name }) =>
        name?.endsWith('_feature_enabled')
      ),
    [data]
  )

  const labels = useMemo<SettingModel[]>(
    () =>
      (data?.rows ?? []).filter(({ name }) =>
        name?.includes('_override_label@')
      ),
    [data]
  )

  const strings = useMemo<SettingModel[]>(
    () => (data?.rows ?? []).filter(({ name }) => name?.includes('_string')),
    [data]
  )

  const lists = useMemo<SettingModel[]>(
    () => (data?.rows ?? []).filter(({ name }) => name?.includes('_list')),
    [data]
  )

  const options = useMemo<SettingModel[]>(
    () => (data?.rows ?? []).filter(({ name }) => name?.includes('_options@')),
    [data]
  )

  const colors = useMemo<SettingModel[]>(
    () => (data?.rows ?? []).filter(({ name }) => name?.endsWith('_color')),
    [data]
  )

  const images = useMemo<SettingModel[]>(
    () => (data?.rows ?? []).filter(({ name }) => name?.endsWith('_file')),
    [data]
  )

  const findLabelSetting = useCallback(
    (pref: PrefProps): SettingModel | undefined =>
      labels.find(
        ({ name }) =>
          name ===
          `${prefName(pref.name ?? '')}_override_label@${currLang?.value}`
      ),
    [currLang?.value, labels]
  )

  const findStringSetting = useCallback(
    (pref: PrefProps): SettingModel | undefined =>
      strings.find(
        ({ name }) => name === `${prefName(pref.name ?? '')}_string`
      ),
    [strings]
  )

  const findOptionSetting = useCallback(
    (pref: PrefProps): SettingModel | undefined =>
      options.find(
        ({ name }) =>
          name === `${prefName(pref.name ?? '')}_options@${currLang?.value}`
      ),
    [currLang?.value, options]
  )

  const findListSetting = useCallback(
    (pref: PrefProps): SettingModel | undefined =>
      lists.find(({ name }) => name === `${prefName(pref.name ?? '')}_list`),
    [lists]
  )

  const findColorSetting = useCallback(
    (pref: PrefProps): SettingModel | undefined =>
      colors.find(({ name }) => name === `${prefName(pref.name ?? '')}_color`),
    [colors]
  )

  const findImageSetting = useCallback(
    (pref: PrefProps): SettingModel | undefined =>
      images.find(({ name }) => name === `${prefName(pref.name ?? '')}_file`),
    [images]
  )

  useEffect(() => {
    const dbSettings: Dict<boolean> = settings.reduce(
      (acc, { name, value }: SettingModel) => {
        if (name) {
          acc[name] = value === 'true'
        }
        return acc
      },
      {} as Dict<boolean>
    )

    const groups = PREFERENCES.map(
      ({ label, password, internal, preferences }: PrefGroup) => ({
        label,
        password: password ?? false,
        internal: internal === true && !isInternalAdmin,
        preferences: preferences
          .filter(({ name, restricted, internal }: PrefProps) => {
            const visible = (internal && isInternalAdmin) ?? true
            return !isNil(name) && (!restricted || isInternalAdmin) && visible
          })
          .map((preference: PrefProps) => ({
            ...preference,
            restricted: Boolean(preference.restricted && !isInternalAdmin),
          })),
      })
    )
      .filter(({ internal }: PrefGroup) => !internal)
      .filter(({ preferences }: PrefGroup) => preferences.length > 0)

    setPrefsGroups(groups)

    setPrefs(
      groups
        .flatMap(({ preferences }: PrefGroup) => preferences)
        .filter(({ name }: PrefProps) => !isNil(name))
        .reduce((acc: Prefs, { name }: any) => {
          if (Object.getOwnPropertyDescriptor(dbSettings, name)) {
            acc[name] = dbSettings[name] ?? false
          }
          return acc
        }, {})
    )
  }, [settings, isInternalAdmin])

  const canView = ({ name, restricted, required }: PrefProps): boolean =>
    !(isNil(name) || restricted || required)

  const canEdit = useCallback(
    (group: PrefGroup) => {
      if (can(['edit', 'applications'])) {
        return true
      }
      if (!group.password) {
        return false
      }
      if (unlocked.has(group.label)) {
        return true
      }
      return false
    },
    [can, unlocked]
  )

  const prefParams = (pref: PrefProps) => ({
    value: isEnabled(pref, prefs),
    label: findLabelSetting(pref),
    string: findStringSetting(pref),
    list: findListSetting(pref),
    option: findOptionSetting(pref),
    color: findColorSetting(pref),
    image: findImageSetting(pref),
    visible: canView(pref),
  })

  const onRestore = async (): Promise<void> => {
    if (!(organizationId && applicationId)) {
      return
    }

    setRestoring(true)

    const input = settings.reduce((acc: SettingInput, setting) => {
      acc.push(
        omit(['id', 'application_id'], {
          ...setting,
          value: 'true',
        })
      )
      return acc
    }, [])

    try {
      await updateSettingMutation({
        organizationId,
        applicationId,
        input,
      })
      toast({
        status: 'success',
        msg: 'Preferences has been restored.',
      })
    } catch (error: unknown) {
      Sentry.captureException(error)
      toast({
        status: 'error',
        msg: 'Unable to update preferences.',
      })
    } finally {
      setRestoring(false)
    }
  }

  const onRemovePref = async (pref: PrefProps): Promise<void> => {
    if (!(organizationId && applicationId && pref?.name)) {
      return
    }

    // If the preference is an image, delete the image first
    const name = prefName(pref.name)
    const imageSetting = findImageSetting(pref)

    if (imageSetting) {
      await deleteImage({
        fileName: name,
        applicationId,
      })
    }

    try {
      const names = data?.rows.reduce((acc: string[], row) => {
        if (!row.name.startsWith(name)) {
          return acc
        }
        acc.push(row.name)
        return acc
      }, [])

      if (!names) {
        return
      }

      for (const name of names) {
        await deleteSettingMutation({
          organizationId,
          applicationId,
          name,
        })
      }

      toast({
        status: 'success',
        msg: `"${pref.label}" has been removed.`,
      })
    } catch (error: unknown) {
      Sentry.captureException(error)
      toast({
        status: 'error',
        msg: 'Unable to remove preference.',
      })
    }
  }

  const onSaveSwitch = async (
    event: ChangeEvent<HTMLInputElement>
  ): Promise<void> => {
    if (!(organizationId && applicationId)) {
      return
    }

    const currentValue = event.target.checked
    const currentName = event.target.name
    const currentState = currentValue ? 'enabled' : 'disabled'

    const updatedPrefs = { ...prefs, [currentName]: currentValue }
    setPrefs(updatedPrefs)

    const settings: SettingInput = prefsGroups
      .flatMap(({ preferences }: PrefGroup) => preferences)
      .filter(
        (pref: PrefProps) =>
          !isNil(pref.name) &&
          pref.name === currentName &&
          !!Object.getOwnPropertyDescriptor(updatedPrefs, pref.name)
      )
      .map((pref: PrefProps) => ({
        label: pref.label,
        name: pref.name ?? '',
        value: isEnabled(pref, updatedPrefs).toString(),
        optional: 1, // not used - kept for legacy purpose
        public: 1, // setting must be public for PWA to retrieve it
      }))

    const setting = settings.find(({ name }) => name === currentName)

    // The preference is not defined.
    if (!setting) {
      toast({
        status: 'error',
        msg: `Preference "${currentName}" is not available, please contact support.`,
      })
      return
    }

    try {
      await updateSettingMutation({
        organizationId,
        applicationId,
        input: settings,
      })

      if (setting.name.startsWith('store_msrp_price_book')) {
        try {
          await postShopPriceBook({
            organizationId,
            applicationId,
            pricebook: currentValue ? 'msrp' : 'partner',
          })
        } catch (err: unknown) {
          Sentry.captureException(err)
        }
      }

      if (setting.name.startsWith('store_public')) {
        try {
          await postShopPublic({
            organizationId,
            applicationId,
            publicStore: currentValue,
          })
        } catch (err: unknown) {
          Sentry.captureException(err)
        }
      }

      toast({
        status: 'success',
        msg: `Preference for "${setting.label}" has been ${currentState}.`,
      })
    } catch (error: unknown) {
      Sentry.captureException(error)
      toast({
        status: 'error',
        msg: 'Unable to update preference.',
      })
    }
  }

  const onUnlock = useCallback(
    (section: string): void => {
      modal({
        size: 'md',
        component: <PasswordDialog />,
        config: {
          title: `Unlock "${section}"?`,
          confirmLabel: 'Unlock',
          onCallback: (ok: boolean) => {
            if (ok) {
              unlocked.add(section)
              toast({
                status: 'success',
                msg: `"${section}" has been unlocked.`,
              })
            } else {
              unlocked.delete(section)
              toast({
                status: 'error',
                msg: `Unable to unlock "${section}".`,
              })
            }
          },
        },
      })
    },
    [modal, toast, unlocked]
  )

  const filteredPrefsGroups = useMemo(() => {
    if (!deferredSearchQuery) return prefsGroups

    const fuse = new Fuse(
      prefsGroups.flatMap((group) =>
        group.preferences.map((pref) => ({
          ...pref,
          groupLabel: group.label,
        }))
      ),
      {
        keys: ['label', 'groupLabel'],
        threshold: 0.3,
      }
    )

    const searchResults = fuse.search(deferredSearchQuery)
    const matchedPrefNames = new Set(
      searchResults.map((result) => result.item.name)
    )

    return prefsGroups
      .map((group) => ({
        ...group,
        preferences: group.preferences.filter(
          (pref) => pref.name && matchedPrefNames.has(pref.name)
        ),
      }))
      .filter((group) => group.preferences.length > 0)
  }, [prefsGroups, deferredSearchQuery])

  return (
    <>
      <Helmet>
        <title>{metaTitle([title, heading])}</title>
      </Helmet>
      <Flex align="center" justify="space-between" p={4}>
        <Title title={heading} />
        <HStack spacing={4} minW="500px" justify="flex-end">
          <InputGroup maxW="250px">
            <InputLeftElement pointerEvents="none">
              <Icon
                as={SearchIcon}
                boxSize={4}
                color="gray.400"
                _dark={{ color: 'gray.700' }}
              />
            </InputLeftElement>
            <Input
              placeholder="Search preferences..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              variant={inputVariant}
            />
            {searchQuery && (
              <InputRightElement>
                <IconButton
                  aria-label="Clear search"
                  icon={<XIcon size={14} />}
                  size="sm"
                  variant="link"
                  colorScheme="secondary"
                  onClick={() => setSearchQuery('')}
                />
              </InputRightElement>
            )}
          </InputGroup>
          <Button
            colorScheme="primary"
            aria-label="Restore"
            isLoading={isRestoring}
            loadingText="Restoring"
            onClick={onRestore}
            whiteSpace="nowrap"
          >
            Restore Default UI Preferences
          </Button>
        </HStack>
      </Flex>
      <Box px={4} pb={10}>
        <Tabs current={currentPath}>
          <Suspense fallback={null}>
            <Container maxW="container.xl" m={0} p={0}>
              <Table
                sx={{
                  tr: {
                    _hover: {
                      bg: 'blackAlpha.50',
                    },
                  },
                  th: {
                    lineHeight: 6,
                    bg: 'whiteAlpha.800',
                    _dark: {
                      bg: 'blackAlpha.200',
                    },
                  },
                  'th, td': {
                    w: '33%',
                    borderColor: 'inherit',
                    _dark: {
                      borderColor: 'gray.900',
                    },
                  },
                }}
              >
                <Tbody>
                  {filteredPrefsGroups.map((group, key) => (
                    <Fragment key={key}>
                      <Tr>
                        <Th>{group.label}</Th>
                        <Th>
                          {key === 0 && (
                            <Select
                              value={currLang}
                              options={LANGUAGES}
                              onChange={(option: any) => setCurrLang(option)}
                            />
                          )}
                        </Th>
                        <Th
                          sx={{
                            textAlign: 'right',
                          }}
                        >
                          {!isInternalAdmin &&
                            group.password &&
                            (unlocked.has(group.label) ? (
                              <Button
                                type="button"
                                variant="solid"
                                colorScheme="gray"
                                size="sm"
                                leftIcon={<LockOpenIcon size={16} />}
                                isDisabled
                              >
                                Unlocked Section
                              </Button>
                            ) : (
                              <Button
                                type="button"
                                variant="solid"
                                colorScheme="red"
                                size="sm"
                                leftIcon={<LockIcon size={16} />}
                                onClick={() => onUnlock(group.label)}
                              >
                                Locked Section
                              </Button>
                            ))}
                        </Th>
                      </Tr>
                      {group.preferences.map((pref) => {
                        const params = prefParams(pref)
                        if (!(pref?.name && params.visible)) {
                          return null
                        }
                        return (
                          <Tr
                            key={pref.name}
                            _last={{ td: { borderBottom: 0 } }}
                          >
                            <Td py={1}>
                              <Flex h="inherit" align="center">
                                <HStack>
                                  <Box textTransform="capitalize">
                                    {pref.label}
                                  </Box>
                                  {pref?.tooltip && (
                                    <Tooltip
                                      hasArrow
                                      label={pref?.tooltip}
                                      placement="bottom"
                                    >
                                      <Box>
                                        <Icon
                                          as={InfoIcon}
                                          sx={{
                                            boxSize: 4,
                                            color: 'blue.500',
                                            _dark: {
                                              color: 'blue.200',
                                            },
                                          }}
                                        />
                                      </Box>
                                    </Tooltip>
                                  )}
                                </HStack>
                              </Flex>
                            </Td>
                            <Td py={0}>
                              <PrefField
                                pref={pref}
                                currLang={currLang}
                                params={params}
                                onRemovePref={onRemovePref}
                                isDisabled={!canEdit(group)}
                              />
                            </Td>
                            <Td>
                              <Flex align="center" justify="flex-end">
                                <>
                                  <FormHeading pr={2}>
                                    {params.value
                                      ? (pref?.trueLabel ?? 'Shown')
                                      : (pref?.falseLabel ?? 'Hidden')}
                                  </FormHeading>
                                  <Switch
                                    size="md"
                                    colorScheme="secondary"
                                    id={pref.name}
                                    name={pref.name}
                                    isChecked={params.value}
                                    isDisabled={!canEdit(group)}
                                    onChange={onSaveSwitch}
                                  />
                                </>
                              </Flex>
                            </Td>
                          </Tr>
                        )
                      })}
                    </Fragment>
                  ))}
                </Tbody>
              </Table>
            </Container>
          </Suspense>
        </Tabs>
      </Box>
    </>
  )
})

export async function loader({ params }: LoaderFunctionArgs) {
  const scopes = ['view:applications']

  const { checkLoaderPermissions } = await import('@/utils/loader-auth')
  await checkLoaderPermissions(scopes)

  const { getFilteredTabs } = await import('@/utils/tab-loader')
  const tabs = await getFilteredTabs('settings-tabs', params)

  return {
    title: 'Show / Hide UI',
    scopes,
    tabs,
  }
}

export function Component() {
  return <Preferences />
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}
