import * as Sentry from '@sentry/react'
import { memo, Suspense, useEffect, useMemo, useState } from 'react'
import type { LoaderFunctionArgs } from 'react-router'
import { Helmet } from 'react-helmet-async'
import { pick } from 'lodash'
import { isEmpty } from 'ramda'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { useForm, useWatch } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import {
  Box,
  Button,
  Container,
  Divider,
  Flex,
  Grid,
  HStack,
} from '@chakra-ui/react'
import { Title } from '@/components/title'
import { Tabs } from '@/components/tabs'
import { CheckboxField } from '@/components/checkbox-field'
import { InputField } from '@/components/input-field'
import { NumberField } from '@/components/number-field'
import { RadioField } from '@/components/radio-field'
import { PasswordField } from '@/components/password-field'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { SmtpStatus, type SmtpStatusType } from '@/components/smtp-status'
import { useRouter } from '@/hooks/use-router'
import { useAbility } from '@/hooks/use-ability'
import { useOrganization } from '@/hooks/use-organization'
import { useToast } from '@/hooks/use-toast'
import { getSettings, updateSettings } from '@/api/settings'
import { verify } from '@/api/notifications'
import { metaTitle } from '@/utils/meta-title'
import type { SettingModel } from '@/types/models/setting'
import type {
  SettingInput,
  VerifyResponse,
  PaginatedQueryResponse,
} from '@/types/api'

type Dict = Record<string, any>

const DEFAULT_EMAIL = '<EMAIL>'

const providerOptions = [
  { label: 'myDevices', value: 'mydevices' },
  { label: 'SMTP', value: 'custom' },
  { label: 'Microsoft oAuth', value: 'microsoft' },
]

const schema = z
  .object({
    brand_name: z.string(),
    smtp_custom: z.boolean(),
    smtp_provider: z.enum(['mydevices', 'custom', 'microsoft']),
    smtp_from: z.string().email({ message: 'Invalid email address.' }),
    smtp_from_display_name: z.string().min(1, 'Display Name is required.'),
    smtp_host: z.string(),
    smtp_port: z.number().int().positive().min(0).max(65_353),
    smtp_user: z.string(),
    smtp_pass: z.string(),
    smtp_secure: z.enum(['true', 'false']),
    smtp_require_tls: z.enum(['true', 'false']),
    smtp_use_fallback_on_failure: z.boolean(),
    // Microsoft OAuth fields
    smtp_authority_url: z.string(),
    smtp_client_id: z.string(),
    smtp_client_secret: z.string(),
    smtp_protocol: z.string(),
    smtp_scopes_url: z.string(),
    smtp_tenant_id: z.string(),
  })
  .superRefine((arg, ctx) => {
    if (!arg.smtp_custom) {
      return z.NEVER
    }

    if (arg.smtp_provider === 'custom') {
      if (arg.smtp_host.length < 4) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'SMTP Host is required.',
          path: ['smtp_host'],
        })
      }

      if (arg.smtp_user.length < 2) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'SMTP User is required.',
          path: ['smtp_user'],
        })
      }

      if (arg.smtp_pass.length < 2) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'SMTP Password is required.',
          path: ['smtp_pass'],
        })
      }
    }

    if (arg.smtp_provider === 'microsoft') {
      if (arg.smtp_host.length < 4) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'SMTP Host is required.',
          path: ['smtp_host'],
        })
      }

      if (arg.smtp_client_id.length < 2) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'SMTP Client ID is required.',
          path: ['smtp_client_id'],
        })
      }

      if (arg.smtp_client_secret.length < 2) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'SMTP Client Secret is required.',
          path: ['smtp_client_secret'],
        })
      }

      if (arg.smtp_tenant_id.length < 2) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'SMTP Tenant ID is required.',
          path: ['smtp_tenant_id'],
        })
      }
    }

    return z.NEVER
  })

type ProviderEnum = 'mydevices' | 'custom' | 'microsoft'
type BooleanEnum = 'true' | 'false'

type FormInputProps = z.infer<typeof schema> & {
  [key: string]: any
}

const defaultValues = {
  brand_name: '',
  smtp_custom: false,
  smtp_provider: 'mydevices' as ProviderEnum,
  smtp_from: DEFAULT_EMAIL,
  smtp_from_display_name: '',
  smtp_host: '',
  smtp_port: 25,
  smtp_user: '',
  smtp_pass: '',
  smtp_secure: 'false' as BooleanEnum,
  smtp_require_tls: 'false' as BooleanEnum,
  smtp_use_fallback_on_failure: true,
  // Microsoft OAuth defaults
  smtp_authority_url: 'https://login.microsoftonline.com',
  smtp_client_id: '',
  smtp_client_secret: '',
  smtp_protocol: 'oauth2',
  smtp_scopes_url: 'https://outlook.office365.com/.default',
  smtp_tenant_id: '',
}

const Email = memo(() => {
  const heading = 'Settings'
  const toast = useToast()
  const queryClient = useQueryClient()
  const { title, currentPath } = useRouter()
  const { organizationId, applicationId } = useOrganization()
  const { can } = useAbility()
  const [status, setStatus] = useState<SmtpStatusType>('ok')
  const [error, setError] = useState<string>('')
  const [isVerifying, setIsVerifying] = useState<boolean>(false)

  const canEdit = can(['edit', 'applications'])

  const {
    reset,
    control,
    setValue,
    handleSubmit,
    formState: { isValid, isSubmitting },
  } = useForm({
    mode: 'onTouched',
    resolver: zodResolver(schema),
    defaultValues,
  })

  const smtpCustom = useWatch({
    name: 'smtp_custom',
    control,
  })

  const smtpProvider = useWatch({
    name: 'smtp_provider',
    control,
  })

  const { mutateAsync: verifyMutation } = useMutation<
    VerifyResponse,
    Error,
    string
  >({
    mutationFn: (applicationId: string) => verify(applicationId),
  })

  const { mutateAsync: updateSettingMutation } = useMutation<
    SettingModel[],
    Error,
    {
      organizationId: string
      applicationId: string
      input: SettingInput
    }
  >({
    mutationFn: ({ organizationId, applicationId, input }) =>
      updateSettings({ organizationId, applicationId, input }),
    onSuccess: () =>
      queryClient.invalidateQueries({ queryKey: ['GetSettings'] }),
  })

  const { data, refetch } = useQuery<
    PaginatedQueryResponse<SettingModel> | null,
    Error
  >({
    queryKey: ['GetSettings', applicationId, currentPath],
    queryFn: ({ signal }) =>
      applicationId
        ? getSettings({
            applicationId,
            signal,
          })
        : null,
    enabled: !!applicationId,
  })

  const settings = useMemo<SettingModel[]>(
    () => (data ? data.rows : []),
    [data]
  )

  const brandName = useMemo<string>(
    () => settings.find(({ name }) => name === 'brand_name')?.value ?? '',
    [settings]
  )

  useEffect(() => {
    if (!data) {
      return
    }

    const params = pick(
      data.rows.reduce((acc: Dict, { name, value }) => {
        if (name) {
          acc[name] = value
        }
        return acc
      }, {}),
      Object.keys(defaultValues)
    )

    const smtpFrom: string = [params.smtp_from, DEFAULT_EMAIL].filter(
      Boolean
    )?.[0]

    const smtpFromName: string = [
      params.smtp_from_display_name,
      brandName,
      organizationId,
    ].filter(Boolean)?.[0]

    reset({
      brand_name: params?.brand_name ?? '',
      smtp_custom: params?.smtp_custom === 'true',
      smtp_provider: params?.smtp_provider ?? 'mydevices',
      smtp_from: smtpFrom,
      smtp_from_display_name: smtpFromName,
      smtp_host: params?.smtp_host ?? '',
      smtp_port: isEmpty(params?.smtp_port) ? 25 : Number(params?.smtp_port),
      smtp_user: params?.smtp_user ?? '',
      smtp_pass: params?.smtp_pass ?? '',
      smtp_secure: params?.smtp_secure === 'true' ? 'true' : 'false',
      smtp_require_tls: params?.smtp_require_tls === 'true' ? 'true' : 'false',
      smtp_use_fallback_on_failure:
        params?.smtp_use_fallback_on_failure === 'true',
      // Microsoft OAuth fields
      smtp_authority_url:
        params?.smtp_authority_url ?? 'https://login.microsoftonline.com',
      smtp_client_id: params?.smtp_client_id ?? '',
      smtp_client_secret: params?.smtp_client_secret ?? '',
      smtp_protocol: params?.smtp_protocol ?? 'oauth2',
      smtp_scopes_url:
        params?.smtp_scopes_url ?? 'https://outlook.office365.com/.default',
      smtp_tenant_id: params?.smtp_tenant_id ?? '',
    })
  }, [reset, data, organizationId])

  const onVerifySmtp = async (): Promise<void> => {
    if (!(applicationId && smtpCustom)) {
      return
    }

    setIsVerifying(true)
    setStatus('verifying')

    try {
      const { error_code, message, success } =
        await verifyMutation(applicationId)

      if (success) {
        setStatus('ok')
        setError('')
        toast({
          status: 'success',
          msg: 'SMTP settings are valid.',
        })
      } else {
        const errorMessage = `(${error_code}) ${message}`
        setStatus('error')
        setError(errorMessage)
        toast({
          status: 'error',
          msg: errorMessage,
        })
      }
    } catch (error: unknown) {
      Sentry.captureException(error)
      setStatus('error')
      setError('Internal server error!')
      if (error instanceof Error) {
        toast({
          status: 'error',
          msg: error.message,
        })
      } else {
        toast({
          status: 'error',
          msg: 'Internal server error!',
        })
      }
    } finally {
      setIsVerifying(false)
    }
  }

  const canVerify = useMemo<boolean>(
    () => can(['edit', 'applications']) && isValid && smtpCustom === true,
    [can, smtpCustom, isValid]
  )

  const onSubmit = async (values: FormInputProps): Promise<boolean> => {
    if (!(organizationId && applicationId)) {
      return false
    }

    const input = Object.keys(defaultValues).reduce(
      (acc: SettingInput, key) => {
        // Skip brand_name as it's not an email setting
        if (key === 'brand_name') return acc

        // Find existing setting if it exists
        const existingSetting = settings.find((s) => s.name === key)
        const value = values[key]?.toString() || ''

        acc.push({
          label:
            existingSetting?.label ||
            key
              .split('_')
              .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
              .join(' '),
          name: key,
          value,
          optional: 1,
          public: 0,
        })

        return acc
      },
      []
    )

    try {
      await updateSettingMutation({
        organizationId,
        applicationId,
        input,
      })

      toast({
        status: 'success',
        msg: 'Settings has been updated.',
      })

      refetch()

      return true
    } catch (error: unknown) {
      Sentry.captureException(error)
      toast({
        status: 'error',
        msg: 'Unable to update setting.',
      })
      return false
    }
  }

  return (
    <>
      <Helmet>
        <title>{metaTitle([title, heading])}</title>
      </Helmet>
      <Flex align="center" justify="space-between" p={4}>
        <Title title={heading} />
      </Flex>
      <Box px={4} pb={10}>
        <Tabs current={currentPath}>
          <Suspense fallback={null}>
            <Box as="form" noValidate onSubmit={handleSubmit(onSubmit)}>
              <Container maxW="container.xl" m={0} pt={4}>
                <Grid
                  gap={4}
                  templateColumns={{ base: '1fr', lg: 'repeat(2, 1fr)' }}
                >
                  <Grid
                    templateColumns="1fr 1fr"
                    alignItems="center"
                    justifyContent="space-between"
                    gridColumn="1 / -1"
                  >
                    <RadioField
                      name="smtp_provider"
                      label="SMTP Provider"
                      control={control}
                      options={providerOptions}
                      isDisabled={!canEdit}
                      isInline
                      isRequired
                      onChange={(e) => {
                        const value = e.target.value as ProviderEnum

                        reset({
                          ...defaultValues,
                          smtp_from_display_name:
                            defaultValues.smtp_from_display_name || brandName,
                        })

                        setValue('smtp_custom', value !== 'mydevices')
                        setValue('smtp_provider', value)

                        // Set protocol based on provider
                        if (value === 'microsoft') {
                          setValue('smtp_from', '')
                          setValue('smtp_protocol', 'oauth2')
                          setValue('smtp_host', 'smtp.office365.com')
                          setValue('smtp_port', 587)
                          setValue(
                            'smtp_authority_url',
                            'https://login.microsoftonline.com'
                          )
                          setValue(
                            'smtp_scopes_url',
                            'https://outlook.office365.com/.default'
                          )
                        } else if (value === 'custom') {
                          setValue('smtp_from', '')
                          setValue('smtp_protocol', 'basic')
                        } else {
                          // mydevices provider
                          setValue('smtp_protocol', '')
                        }
                      }}
                    />
                    <Flex justify="flex-end" align="center" w="100%">
                      <SmtpStatus status={status} error={error} />
                    </Flex>
                  </Grid>

                  <InputField
                    name="smtp_from"
                    label="From Email Address"
                    type="email"
                    control={control}
                    isDisabled={!(smtpCustom && canEdit)}
                    isRequired
                    autoFocus
                  />
                  <InputField
                    name="smtp_from_display_name"
                    label="From Display Name"
                    control={control}
                    isDisabled={!canEdit}
                    isRequired
                  />

                  {smtpCustom === true && smtpProvider === 'custom' && (
                    <>
                      <InputField
                        name="smtp_host"
                        label="SMTP Host"
                        control={control}
                        isDisabled={!canEdit}
                        isRequired
                      />
                      <NumberField
                        name="smtp_port"
                        label="SMTP Port"
                        control={control}
                        isDisabled={!canEdit}
                        isRequired
                      />
                      <InputField
                        name="smtp_user"
                        label="SMTP Username"
                        control={control}
                        isDisabled={!canEdit}
                        isRequired
                      />
                      <PasswordField
                        name="smtp_pass"
                        label="SMTP Password"
                        control={control}
                        isDisabled={!canEdit}
                        isRequired
                      />
                      <RadioField
                        name="smtp_secure"
                        label="SMTP Secure"
                        control={control}
                        isDisabled={!canEdit}
                        isRequired
                      />
                      <RadioField
                        name="smtp_require_tls"
                        label="SMTP Require TLS"
                        control={control}
                        isDisabled={!canEdit}
                        isRequired
                      />
                      <CheckboxField
                        name="smtp_use_fallback_on_failure"
                        label="Use Fallback On Failure"
                        control={control}
                        isDisabled={!canEdit}
                        isRequired
                        sx={{
                          gridColumn: '1 / -1',
                        }}
                      >
                        If Email SMTP fails, use myDevices email "
                        {DEFAULT_EMAIL}" to send product notifications.
                      </CheckboxField>
                    </>
                  )}

                  {smtpCustom === true && smtpProvider === 'microsoft' && (
                    <>
                      <InputField
                        name="smtp_host"
                        label="SMTP Host"
                        control={control}
                        isDisabled={!canEdit}
                        isRequired
                      />
                      <NumberField
                        name="smtp_port"
                        label="SMTP Port"
                        control={control}
                        isDisabled={!canEdit}
                        isRequired
                      />
                      <InputField
                        name="smtp_user"
                        label="SMTP Username"
                        control={control}
                        isDisabled={!canEdit}
                        isRequired
                      />
                      <InputField
                        name="smtp_authority_url"
                        label="SMTP Authority URL"
                        control={control}
                        isDisabled={!canEdit}
                        isRequired
                      />
                      <InputField
                        name="smtp_client_id"
                        label="SMTP Client ID"
                        control={control}
                        isDisabled={!canEdit}
                        isRequired
                      />
                      <PasswordField
                        name="smtp_client_secret"
                        label="SMTP Client Secret"
                        control={control}
                        isDisabled={!canEdit}
                        isRequired
                      />
                      <InputField
                        name="smtp_protocol"
                        label="SMTP Protocol"
                        control={control}
                        isDisabled={!canEdit}
                        isRequired
                      />
                      <InputField
                        name="smtp_scopes_url"
                        label="SMTP Scopes URL"
                        control={control}
                        isDisabled={!canEdit}
                        isRequired
                      />
                      <InputField
                        name="smtp_tenant_id"
                        label="SMTP Tenant ID"
                        control={control}
                        isDisabled={!canEdit}
                        isRequired
                      />
                      <CheckboxField
                        name="smtp_use_fallback_on_failure"
                        label="Use Fallback On Failure"
                        control={control}
                        isDisabled={!canEdit}
                        isRequired
                        sx={{
                          gridColumn: '1 / -1',
                        }}
                      >
                        If Email SMTP fails, use myDevices email "
                        {DEFAULT_EMAIL}" to send product notifications.
                      </CheckboxField>
                    </>
                  )}

                  <Divider gridColumn="1 / -1" />

                  <Box gridColumn="1 / -1">
                    <HStack mt={2} justify="space-between">
                      {canEdit && (
                        <HStack>
                          <Button
                            type="submit"
                            aria-label="Save"
                            colorScheme="green"
                            isLoading={isSubmitting}
                            isDisabled={!isValid || isSubmitting}
                            loadingText="Saving"
                          >
                            Save
                          </Button>
                          <Button
                            type="button"
                            aria-label="Verify"
                            variant="ghost"
                            colorScheme="secondary"
                            isLoading={isVerifying}
                            isDisabled={!canVerify}
                            loadingText="Verifying"
                            onClick={onVerifySmtp}
                          >
                            Quick Verify
                          </Button>
                        </HStack>
                      )}
                    </HStack>
                  </Box>
                </Grid>
              </Container>
            </Box>
          </Suspense>
        </Tabs>
      </Box>
    </>
  )
})

export async function loader({ params }: LoaderFunctionArgs) {
  const scopes = ['view:applications']

  const { checkLoaderPermissions } = await import('@/utils/loader-auth')
  await checkLoaderPermissions(scopes)

  const { getFilteredTabs } = await import('@/utils/tab-loader')
  const tabs = await getFilteredTabs('settings-tabs', params)

  return {
    title: 'Email',
    scopes,
    tabs,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <Email />
}
