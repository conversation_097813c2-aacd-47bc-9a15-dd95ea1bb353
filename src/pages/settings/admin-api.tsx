import { memo, useEffect, useMemo } from 'react'
import { <PERSON><PERSON><PERSON> } from 'react-helmet-async'
import type { LoaderFunctionArgs } from 'react-router'
import { useForm, FormProvider } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { useSetAtom } from 'jotai'
import { useQuery } from '@tanstack/react-query'
import { Box, Container, Divider, Flex, Grid } from '@chakra-ui/react'
import { Title } from '@/components/title'
import { Tabs } from '@/components/tabs'
import { InputField } from '@/components/input-field'
import { PasswordField } from '@/components/password-field'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { Curl } from '@/features/admin-api/curl'
import { UsersCard } from '@/features/admin-api/users-card'
import { LocationsCard } from '@/features/admin-api/locations-card'
import { ThingsCard } from '@/features/admin-api/things-card'
import { useRouter } from '@/hooks/use-router'
import { useAbility } from '@/hooks/use-ability'
import { useOrganization } from '@/hooks/use-organization'
import { metaTitle } from '@/utils/meta-title'
import { toggleAtom } from '@/utils/stores/toggle'
import { getClientSecret } from '@/api/users'
import { getApplication } from '@/api/applications'
import type { ApplicationModel } from '@/types/models/application'

const schema = z.object({
  applicationId: z.string().min(2, 'Application ID is required.'),
  appName: z.string().min(2, 'Application Name is required.'),
  clientSecret: z.string().email(),
})

const AdminApi = memo(() => {
  const heading = 'Settings'
  const { title, currentPath } = useRouter()
  const passwordName = 'clientSecret'
  const { can } = useAbility()
  const { organizationId, applicationId } = useOrganization()
  const setToggle = useSetAtom(toggleAtom(passwordName))

  const methods = useForm({
    mode: 'onTouched',
    resolver: zodResolver(schema),
    defaultValues: {
      applicationId: applicationId ?? '',
      appName: '',
      clientSecret: '',
    },
  })

  const { control, setValue, handleSubmit } = methods

  const { data: application } = useQuery<ApplicationModel | null, Error>({
    queryKey: ['GetApplication', organizationId, applicationId],
    queryFn: ({ signal }) =>
      !!organizationId && !!applicationId
        ? getApplication({
            organizationId,
            applicationId,
            signal,
          })
        : null,
    enabled: !!organizationId || !!applicationId,
  })

  const { data: clientSecret } = useQuery<string | null, Error>({
    queryKey: ['GetClientSecret', organizationId, applicationId],
    queryFn: ({ signal }) =>
      organizationId && applicationId
        ? getClientSecret({
            organizationId,
            applicationId,
            signal,
          })
        : null,
    enabled: !!organizationId && !!applicationId,
  })

  useEffect(() => {
    if (!application) {
      return
    }
    setValue('appName', application.name)
    setValue('applicationId', application.id)
  }, [application, setValue])

  useEffect(() => {
    if (!clientSecret) {
      return
    }

    setToggle(false)
    setValue('clientSecret', clientSecret)

    return () => {
      setToggle(false)
    }
  }, [clientSecret, setToggle, setValue])

  const canViewApplications = useMemo<boolean>(
    () => can(['view', 'applications']),
    [can]
  )

  const canManageApplications = useMemo<boolean>(
    () => can(['edit', 'applications']),
    [can]
  )

  /** No endpoint yet to submit this ... */
  const onSubmit = async (): Promise<boolean> => false

  return (
    <>
      <Helmet>
        <title>{metaTitle([title, heading])}</title>
      </Helmet>
      <Flex align="center" justify="space-between" p={4}>
        <Title title={heading} />
      </Flex>
      <Box px={4} pb={10}>
        <Tabs current={currentPath}>
          <Container maxW="container.xl" m={0} pt={8}>
            {canViewApplications && (
              <Box>
                <Grid
                  gap={4}
                  templateColumns={{ base: '1fr', lg: 'repeat(3, 1fr)' }}
                >
                  <UsersCard />
                  <LocationsCard />
                  <ThingsCard />
                </Grid>
                <Divider mt={8} />
              </Box>
            )}
            {canManageApplications && (
              <Box py={4}>
                <FormProvider {...methods}>
                  <Box as="form" noValidate onSubmit={handleSubmit(onSubmit)}>
                    <Grid
                      gap={4}
                      templateColumns={{ base: '1fr', lg: 'repeat(2, 1fr)' }}
                    >
                      <InputField
                        name="appName"
                        label="Application ID"
                        control={control}
                        isReadOnly
                        isDisabled
                      />
                      <InputField
                        name="applicationId"
                        label="Client ID"
                        control={control}
                        isReadOnly
                        isDisabled
                      />
                      <PasswordField
                        name="clientSecret"
                        label="Client Secret"
                        groupName="clientSecret"
                        control={control}
                        isReadOnly
                        sx={{
                          gridColumn: '1 / -1',
                        }}
                      />
                      <Curl
                        passwordName="clientSecret"
                        control={control}
                        sx={{
                          gridColumn: '1 / -1',
                        }}
                      />
                    </Grid>
                  </Box>
                </FormProvider>
              </Box>
            )}
          </Container>
        </Tabs>
      </Box>
    </>
  )
})

export async function loader({ params }: LoaderFunctionArgs) {
  const scopes = ['view:applications']

  const { checkLoaderPermissions } = await import('@/utils/loader-auth')
  await checkLoaderPermissions(scopes)

  const { getFilteredTabs } = await import('@/utils/tab-loader')
  const tabs = await getFilteredTabs('settings-tabs', params)

  return {
    title: 'Admin API',
    scopes,
    tabs,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <AdminApi />
}
