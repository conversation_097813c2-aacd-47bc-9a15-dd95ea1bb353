import * as Sentry from '@sentry/react'
import { memo, Suspense, useEffect, useMemo } from 'react'
import type { LoaderFunctionArgs } from 'react-router'
import { Helmet } from 'react-helmet-async'
import { useForm, FormProvider } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { omit, isEmpty } from 'ramda'
import { pick } from 'lodash'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  Alert,
  AlertIcon,
  Box,
  Button,
  Container,
  Divider,
  Flex,
  Grid,
} from '@chakra-ui/react'
import { Title } from '@/components/title'
import { Tabs } from '@/components/tabs'
import { ColorPickerField } from '@/components/color-picker-field/color-picker-field'
import { DropZoneField } from '@/components/drop-zone-field'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { useRouter } from '@/hooks/use-router'
import { useOrganization } from '@/hooks/use-organization'
import { useToast } from '@/hooks/use-toast'
import { getSettings, updateSettings } from '@/api/settings'
import { metaTitle } from '@/utils/meta-title'
import { hex } from '@/utils/zod-plus'
import type { SettingModel } from '@/types/models/setting'
import type { SettingInput } from '@/types/api'
import type { PaginatedQueryResponse } from '@/types/api'

const schema = z.object({
  logo: z.string().min(1).default(''),
  icon: z.string().min(1).default(''),
  background_graphic: z.string().min(1).default(''),
  primary_color: hex.default('#000'),
  secondary_color: hex.default('#999'),
  background_color: hex.default('#fff'),
})

type FormInputProps = z.infer<typeof schema> & {
  [key: string]: any
}

const permittedParams = [
  'logo',
  'icon',
  'background_graphic',
  'primary_color',
  'secondary_color',
  'background_color',
]

const CustomizeLegacy = memo(() => {
  const heading = 'Settings'
  const toast = useToast()
  const { title, currentPath } = useRouter()
  const { organizationId, applicationId } = useOrganization()
  const queryClient = useQueryClient()

  const methods = useForm({
    mode: 'onTouched',
    resolver: zodResolver(schema),
    defaultValues: {
      logo: '',
      icon: '',
      background_graphic: '',
      primary_color: '#000',
      secondary_color: '#999',
      background_color: '#fff',
    },
  })

  const {
    reset,
    control,
    handleSubmit,
    formState: { isSubmitting },
  } = methods

  const { mutateAsync: updateSettingMutation } = useMutation<
    SettingModel[],
    Error,
    {
      organizationId: string
      applicationId: string
      input: SettingInput
    }
  >({
    mutationFn: ({ organizationId, applicationId, input }) =>
      updateSettings({ organizationId, applicationId, input }),
    onSuccess: () =>
      queryClient.invalidateQueries({ queryKey: ['GetSettings'] }),
  })

  const { data } = useQuery<PaginatedQueryResponse<SettingModel> | null, Error>(
    {
      queryKey: ['GetSettings', applicationId, currentPath],
      queryFn: ({ signal }) =>
        applicationId
          ? getSettings({
              applicationId,
              signal,
            })
          : null,
      enabled: !!applicationId,
    }
  )

  const settings = useMemo<SettingModel[]>(
    () => (data ? data.rows : []),
    [data]
  )

  const themeId = useMemo(() => {
    if (!(data && applicationId)) {
      return
    }

    if (applicationId !== 'iotinabox') {
      const match = (data?.rows ?? []).find(
        ({ name }) => name === 'theme_id'
      )?.value

      if (match && match.length > 1) {
        return match
      }
    }

    return 'iotinabox'
  }, [data, applicationId])

  useEffect(() => {
    if (!themeId) {
      return
    }

    queryClient
      .fetchQuery({
        queryKey: ['GetSettings', themeId, currentPath],
        queryFn: ({ signal }) =>
          getSettings({
            applicationId: themeId,
            signal,
          }),
      })
      .then((result) => {
        if (!result) {
          return
        }
        reset(
          pick(
            result.rows.reduce((acc: Dict, { name, value }) => {
              if (name && value) {
                acc[name] = value
              }
              return acc
            }, {}),
            permittedParams
          )
        )
      })
  }, [themeId, queryClient, currentPath, reset])

  // Permissions temporary off until edit features applied.
  const canEdit = useMemo<boolean>(() => false, [])

  const inheritedTheme = useMemo<boolean>(
    () => !!themeId && !isEmpty(themeId) && themeId !== applicationId,
    [themeId, applicationId]
  )

  const onSubmit = async (values: FormInputProps): Promise<boolean> => {
    if (!(organizationId && applicationId)) {
      return false
    }

    const input = settings.reduce((acc: SettingInput, setting) => {
      if (permittedParams.includes(setting.name)) {
        acc.push(
          omit(['id', 'application_id'], {
            ...setting,
            value: values[setting.name],
          })
        )
      }

      return acc
    }, [])

    try {
      await updateSettingMutation({
        organizationId,
        applicationId,
        input,
      })
      toast({
        status: 'success',
        msg: 'Settings has been updated.',
      })
      return true
    } catch (error: unknown) {
      Sentry.captureException(error)
      toast({
        status: 'error',
        msg: 'Unable to update setting.',
      })
      return false
    }
  }

  return (
    <>
      <Helmet>
        <title>{metaTitle([title, heading])}</title>
      </Helmet>
      <Flex align="center" justify="space-between" p={4}>
        <Title title={heading} />
      </Flex>
      <Box px={4} pb={10}>
        <Tabs current={currentPath}>
          <Suspense fallback={null}>
            <FormProvider {...methods}>
              <Box as="form" noValidate onSubmit={handleSubmit(onSubmit)}>
                <Container maxW="container.xl" m={0} pt={4}>
                  {inheritedTheme && (
                    <Alert status="info" mb={6}>
                      <AlertIcon />
                      {`Application inherits theme from “${themeId}”.`}
                    </Alert>
                  )}
                  <Grid
                    gap={4}
                    templateColumns={{ base: '1fr', lg: 'repeat(3, 1fr)' }}
                  >
                    <DropZoneField
                      name="logo"
                      label="Logo"
                      control={control}
                      isRequired
                      isDisabled={!canEdit}
                      height={100}
                      shortInfo="Supported files; SVG. Make sure your logo is vector."
                      sx={{
                        gridColumn: '1 / span 3',
                      }}
                    />
                    <DropZoneField
                      name="icon"
                      label="Icon"
                      control={control}
                      isDisabled={!canEdit}
                      height={140}
                      shortInfo="Supported files; SVG. Aspect ratio: 1:1 (Square). Make sure your icon is vector."
                      sx={{
                        gridColumn: '1 / span 3',
                      }}
                    />
                    <DropZoneField
                      name="background_graphic"
                      label="Background Graphic"
                      control={control}
                      isDisabled={!canEdit}
                      height={280}
                      shortInfo="Supported files; SVG/PNG. Aspect ratio: 375:812 e.g. w: 1125px h: 2436px."
                      accept={{
                        'image/svg+xml': ['.svg'],
                        'image/png': ['.png'],
                      }}
                      sx={{
                        gridColumn: '1 / span 3',
                      }}
                    />
                    <ColorPickerField
                      name="primary_color"
                      label="Primary Brand Color"
                      control={control}
                      isDisabled={!canEdit}
                    />
                    <ColorPickerField
                      name="secondary_color"
                      label="Secondary Brand Color"
                      control={control}
                      isDisabled={!canEdit}
                    />
                    <ColorPickerField
                      name="background_color"
                      label="Background Color"
                      control={control}
                      isDisabled={!canEdit}
                    />
                    {canEdit && (
                      <>
                        <Divider gridColumn="1 / span 3" />
                        <Box gridColumn="1 / span 3">
                          <Button
                            mt={4}
                            type="submit"
                            aria-label="Save"
                            colorScheme="green"
                            minW={120}
                            isLoading={isSubmitting}
                            loadingText="Saving"
                          >
                            Save
                          </Button>
                        </Box>
                      </>
                    )}
                  </Grid>
                </Container>
              </Box>
            </FormProvider>
          </Suspense>
        </Tabs>
      </Box>
    </>
  )
})

export async function loader({ params }: LoaderFunctionArgs) {
  const scopes = ['view:applications']

  const { checkLoaderPermissions } = await import('@/utils/loader-auth')
  await checkLoaderPermissions(scopes)

  const { getFilteredTabs } = await import('@/utils/tab-loader')
  const tabs = await getFilteredTabs('settings-tabs', params)

  return {
    title: 'Customize',
    scopes,
    tabs,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <CustomizeLegacy />
}
