import * as Sentry from '@sentry/react'
import {
  lazy,
  Suspense,
  useCallback,
  useState,
  useMemo,
  useRef,
  memo,
} from 'react'
import type { LoaderFunctionArgs } from 'react-router'
import { useCustomCompareEffect, useMountEffect } from '@react-hookz/web'
import type { EventSourceMessage } from '@microsoft/fetch-event-source'
import { Helmet } from 'react-helmet-async'
import { jsonrepair } from 'jsonrepair'
import { useForm, useWatch, FormProvider } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { is, isNil, isEmpty, mergeDeepRight } from 'ramda'
import { useAtom, useSetAtom } from 'jotai'
import { useResetAtom } from 'jotai/utils'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import {
  Box,
  Flex,
  Grid,
  GridItem,
  Alert,
  AlertIcon,
  Button,
  HStack,
} from '@chakra-ui/react'
import {
  Panel,
  PanelGroup,
  type ImperativePanelHandle,
  type ImperativePanelGroupHandle,
} from 'react-resizable-panels'
import { Title } from '@/components/title'
import { Tabs } from '@/components/tabs'
import { ColorPickerField } from '@/components/color-picker-field/color-picker-field'
import { DropZoneField } from '@/components/drop-zone-field'
import { InputField } from '@/components/input-field'
import { NumberField } from '@/components/number-field'
import { SelectField } from '@/components/select-field'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { useRouter } from '@/hooks/use-router'
import { useOrganization } from '@/hooks/use-organization'
import { useAbility } from '@/hooks/use-ability'
import { useToast } from '@/hooks/use-toast'
import { metaTitle } from '@/utils/meta-title'
import { fallbackColors } from '@/utils/theme/fallback-theme'
import { getSettings } from '@/api/settings'
import { logAtom } from '@/features/builder/utils/atoms'
import type { SettingModel } from '@/types/models/setting'
import type { PaginatedQueryResponse } from '@/types/api'

import {
  resetAtom,
  isFetchingAtom,
  jsonThemeAtom,
  panelSizeAtom,
} from '@/features/builder/utils/atoms'
import { jsonThemeSchema } from '@/features/builder/utils/schema/json-theme'
import { useStream } from '@/hooks/use-stream'
import { jsonThemePolyfill } from '@/features/builder/utils/json-theme-polyfill'
import { extractCssUrls } from '@/features/builder/utils/extract-css-urls'
import { PanelResize } from '@/features/builder/components/panel-resize'
import { ColorSwatches } from '@/features/builder/components/color-swatches'
import { substituteFont } from '@/features/builder/utils/substitute-font'
import { convertColor } from '@/features/builder/utils/convert-color'
import { composeTheme } from '@/features/builder/utils/compose-theme'
import {
  colorOptions,
  backgroundOptions,
} from '@/features/builder/utils/options'
import { getSvgIcon, getFontOptions, getFontObject } from '@/api/builder'

import { defaultValues } from '@/features/customize/default-values'
import { validPostParamSchema } from '@/features/customize/valid-post-params'
import { schema, type FormInputProps } from '@/features/customize/schema'

const BuilderPreviewSimple = lazy(() =>
  import('@/features/builder/components/builder-preview-simple').then(
    ({ BuilderPreviewSimple }) => ({ default: BuilderPreviewSimple })
  )
)

const JobIndicator = lazy(() =>
  import('@/features/builder/components/job-indicator').then(
    ({ JobIndicator }) => ({ default: JobIndicator })
  )
)

const htmlRegex = /"([^"]*)"/

const Customize = memo(() => {
  const heading = 'Settings'
  const toast = useToast()
  const queryClient = useQueryClient()
  const { title, currentPath } = useRouter()
  const { organizationId, applicationId } = useOrganization()
  const { can } = useAbility()
  const resetLog = useResetAtom(resetAtom)
  const [jsonTheme, setJsonTheme] = useAtom(jsonThemeAtom)
  const [isFetching, setFetching] = useAtom(isFetchingAtom)
  const [themeId, setThemeId] = useState<string | null>(null)
  const setLog = useSetAtom(logAtom)

  const [panelSize, setPanelSize] = useAtom(panelSizeAtom)
  const panelGroupRef = useRef<ImperativePanelGroupHandle>(null)
  const panelRef = useRef<ImperativePanelHandle>(null)

  const { readyState, connect, disconnect } = useStream({
    onMessage: ({ id, data }: EventSourceMessage) => {
      if (id.length > 0) {
        const log = JSON.parse(data)
        setLog((prev) => [...prev, log])
      }
    },
  })

  useMountEffect(() => {
    const panelGroup = panelGroupRef.current
    if (panelGroup) {
      panelGroup.setLayout([50, 50])
      setPanelSize(50)
    }
  })

  const methods = useForm({
    mode: 'onTouched',
    resolver: zodResolver(schema),
    defaultValues,
  })

  const {
    reset,
    trigger,
    control,
    setValue,
    getValues,
    handleSubmit,
    formState: { isValid, isValidating, isSubmitting },
  } = methods

  const onBackgroundImageUpload = useCallback(
    (file: File) => {
      if (!file) {
        return
      }
      const isProd = import.meta.env.VITE_DB_ENV === 'production'
      const fileExt = file.name.split('.').pop()?.toLowerCase() || ''
      const env = isProd ? 'web/assets' : 'staging/web/assets'
      const url = `https://s3.amazonaws.com/mydevicescdn.com/${env}/${applicationId}/background.${fileExt}`
      setValue('background.backgroundImage', url)
    },
    [applicationId, setValue]
  )

  const formValues = useWatch({
    control,
  })

  const logo = useWatch({
    name: 'logo',
    control,
  })

  const useBackground = useWatch({
    name: 'useBackground',
    defaultValue: false,
    control,
  })

  const { data: fontOptions } = useQuery({
    queryKey: ['getFontOptions'],
    queryFn: ({ signal }) => getFontOptions({ signal }),
  })

  const { data, refetch } = useQuery<
    PaginatedQueryResponse<SettingModel> | null,
    Error,
    Dict<string> | null
  >({
    queryKey: ['GetSettings', applicationId, currentPath],
    queryFn: ({ signal }) =>
      applicationId
        ? getSettings({
            applicationId,
            signal,
          })
        : null,
    enabled: !!applicationId,
    select: (data) =>
      (data?.rows ?? []).reduce((acc: Dict<string>, { name, value }) => {
        acc[name] = value
        return acc
      }, {}),
  })

  const svgIcon = useCallback(
    async ({ applicationId, url }: { applicationId: string; url: string }) => {
      const fileName = url.split('/').pop()
      if (!fileName) {
        return ''
      }
      return await queryClient.fetchQuery({
        queryKey: ['GetSvgIcon', applicationId, fileName],
        queryFn: ({ signal }) =>
          getSvgIcon({
            appId: applicationId,
            fileName,
            signal,
          }),
      })
    },
    [queryClient]
  )

  const isInheritedTheme = useMemo<boolean>(() => {
    if (!(themeId && applicationId)) {
      return false
    }
    return themeId !== applicationId
  }, [themeId, applicationId])

  const canEdit = useMemo(
    () => can(['manage', 'all']) && !isInheritedTheme,
    [can, isInheritedTheme]
  )

  const isFieldDisabled = useMemo(
    () => isFetching || !canEdit || isSubmitting,
    [isFetching, canEdit, isSubmitting]
  )

  const loadData = async (setting: Dict<string>) => {
    if (!(setting && organizationId && applicationId)) {
      return
    }

    let effectiveSetting = setting

    try {
      // collect all data
      let collector = {
        ...defaultValues,
        orgId: organizationId,
        appId: applicationId,
        themeId: effectiveSetting.theme_id,
        url: isEmpty(effectiveSetting.custom_url)
          ? (effectiveSetting.mydevices_url ?? '')
          : effectiveSetting.custom_url,
      } satisfies FormInputProps

      const themeId = effectiveSetting.theme_id ?? applicationId
      setThemeId(themeId)

      if (themeId && themeId !== applicationId) {
        const data = await queryClient.fetchQuery({
          queryKey: ['GetSettings', themeId],
          queryFn: ({ signal }) =>
            getSettings({
              applicationId: themeId,
              signal,
            }),
        })

        effectiveSetting = (data?.rows ?? []).reduce(
          (acc: Dict<string>, { name, value }) => {
            acc[name] = value
            return acc
          },
          {}
        )
      }

      const logo = await svgIcon({
        applicationId: themeId,
        url: effectiveSetting?.logo ?? 'logo.svg',
      })

      const emailLogo = await svgIcon({
        applicationId: themeId,
        url: 'logo-email.svg',
      })

      const dashboardLogo = await svgIcon({
        applicationId: themeId,
        url: 'logo-dashboard.svg',
      })

      const icon = await svgIcon({
        applicationId: themeId,
        url: effectiveSetting?.icon ?? 'app-icon.svg',
      })

      // repair theme and add polyfill due to legacy inconsistencies
      const parsedJson = jsonrepair(effectiveSetting.theme ?? '{}')
      const theme = jsonThemeSchema.parse(
        mergeDeepRight(jsonThemePolyfill, JSON.parse(parsedJson))
      )

      const heading = theme.fonts.heading.match(htmlRegex)
      const headingFont = heading?.[1] ? substituteFont(heading[1]) : ''

      const body = theme.fonts.body.match(htmlRegex)
      const bodyFont = body?.[1] ? substituteFont(body[1]) : ''

      const backgroundUrls = extractCssUrls(theme.start_page.background_image)

      const colors = Object.entries(theme.colors).reduce(
        (acc: Array<{ name: string; value: string }>, [name, value]) => {
          if (
            name === 'primary' ||
            name === 'secondary' ||
            isEmpty(name?.trim())
          ) {
            return acc
          }
          acc.push({
            name,
            value,
          })
          return acc
        },
        []
      )

      // collect all data
      collector = {
        ...collector,
        logo: logo,
        emailLogo: emailLogo,
        dashboardLogo: dashboardLogo,
        icon: icon,
        iconPadding: theme.meta?.icon_padding ?? 20,
        iconBackground: theme.meta?.icon_background ?? '',
        headingFont,
        bodyFont,
        primaryColor: effectiveSetting.primary_color ?? '',
        secondaryColor: effectiveSetting.secondary_color ?? '',
        backgroundColor: effectiveSetting.background_color ?? '',
        primaryColorMap: theme.meta?.primary_color_map ?? '',
        secondaryColorMap: theme.meta?.secondary_color_map ?? '',
        customButtonColor: convertColor(
          theme.custom_colors.start_page_button_background_color
        ),
        customLinkColor: convertColor(theme.meta?.custom_link_color ?? ''),
        customTextColor: convertColor(theme.meta?.custom_text_color ?? ''),
        appIconSize: theme.start_page.app_icon_size,
        dashboardLogoHeight: theme.dashboard_header.logo_height,
        startLogoHeight: theme.start_page.logo_height,
        buttonRoundness: Number.parseInt(theme.start_page.button_roundness),
        linkTextDecoration: theme.link_text_decoration,
        useBackground: !isEmpty(theme.start_page.background_image),
        background: {
          backgroundImageFile: backgroundUrls?.[0] ?? '',
          backgroundImage: theme.start_page.background_image,
          backgroundPosition: theme.start_page.background_position,
          backgroundAttachment: theme.start_page.background_attachment,
          backgroundRepeat: theme.start_page.background_repeat,
          backgroundSize: theme.start_page.background_size,
        },
        colors,
      } satisfies FormInputProps

      setJsonTheme(JSON.stringify(theme, null, 2))
      reset(collector)
    } catch (error: unknown) {
      setJsonTheme(null)
      if (error instanceof z.ZodError) {
        console.error(error.issues)
      } else if (error instanceof Error) {
        console.error(error.message)
      }
    } finally {
      trigger()
      setFetching(false)
    }
  }

  // Build the theme.json and set the jsonTheme atom
  const buildTheme = async (): Promise<void> => {
    if (isFetching || !isValid || isSubmitting) {
      return
    }

    try {
      const values = getValues()

      const colors = {
        ...fallbackColors,
        ...values.colors.reduce((acc: any, { name, value }) => {
          acc[name] = value
          return acc
        }, {}),
        primary: values?.primaryColor,
        secondary: values?.secondaryColor,
      }

      const fontData = await queryClient.fetchQuery({
        queryKey: ['getFontObject', values.headingFont, values.bodyFont],
        queryFn: ({ signal }) =>
          getFontObject({
            headingFont: values.headingFont,
            bodyFont: values.bodyFont,
            signal,
          }),
      })

      const font = {
        font_url: fontData?.url,
        fonts: {
          body: fontData?.body,
          heading: fontData?.heading,
          mono: 'Menlo, monospace',
        },
      }

      if (!(themeId && font)) {
        return
      }

      const buttonRoundness =
        typeof values.buttonRoundness === 'string'
          ? Number(values.buttonRoundness)
          : (values.buttonRoundness ?? 6)

      const dashboardLogoHeight =
        typeof values.dashboardLogoHeight === 'string'
          ? Number(values.dashboardLogoHeight)
          : (values.dashboardLogoHeight ?? 40)

      const startLogoHeight =
        typeof values.startLogoHeight === 'string'
          ? Number(values.startLogoHeight)
          : (values.startLogoHeight ?? 120)

      const appIconSize =
        typeof values.appIconSize === 'string'
          ? Number(values.appIconSize)
          : (values.appIconSize ?? 100)

      const iconPadding =
        typeof values.iconPadding === 'string'
          ? Number(values.iconPadding)
          : (values.iconPadding ?? 20)

      const composed = composeTheme({
        ...values,
        themeId,
        hasDashboardLogo:
          !!values.dashboardLogo?.startsWith('data:image/svg+xml'),
        font,
        colors,
        buttonRoundness,
        dashboardLogoHeight,
        startLogoHeight,
        appIconSize,
        iconPadding,
      })

      setJsonTheme(JSON.stringify(composed))
    } catch (error: unknown) {
      setJsonTheme(null)
      if (error instanceof Error) {
        console.error(error)
      }
    }
  }

  const onSubmit = async (values: FormInputProps): Promise<boolean> => {
    if (!(organizationId && applicationId)) {
      return false
    }

    try {
      if (isNil(jsonTheme)) {
        throw new Error('Theme is not built')
      }

      resetLog()

      const payload = validPostParamSchema.parse({
        ...values,
        orgId: organizationId,
        appId: applicationId,
        backgroundImage: values.background.backgroundImageFile,
        theme: JSON.stringify(JSON.parse(jsonTheme)),
      })

      await connect('api/builder/update-application', payload)

      toast({
        status: 'success',
        msg: 'Settings has been updated.',
      })

      await queryClient.invalidateQueries({
        queryKey: ['GetSettings', applicationId, currentPath],
      })

      const result = await refetch()

      if (result.data) {
        setFetching(true)
        await loadData(result.data)
        await buildTheme()
      }

      return true
    } catch (error: unknown) {
      disconnect()
      Sentry.captureException(error)
      toast({
        status: 'error',
        msg: 'Unable to update setting.',
      })

      return false
    }
  }

  useCustomCompareEffect(() => {
    if (data) {
      setFetching(true)
      loadData(data)
    }

    return () => {
      setThemeId(null)
      setFetching(false)
      setJsonTheme(null)
      disconnect()
    }
  }, [data])

  useCustomCompareEffect(() => {
    buildTheme()
  }, [isFetching, isValidating, isValid, formValues])

  const panelOverlayStyle = useMemo(
    () => ({
      w: '100%',
      h: 'calc(100vh - 240px)',
      overflow: 'auto',
      opacity: isFieldDisabled ? 0.5 : 1,
    }),
    [isFieldDisabled]
  )

  const panelGridStyle = useMemo(
    () => ({
      p: 4,
      pr: 8,
      gap: 4,
      gridTemplateColumns:
        panelSize < 30
          ? '1fr'
          : {
              base: '1fr',
              lg: 'repeat(2, 1fr)',
            },
    }),
    [panelSize]
  )

  return (
    <>
      <Helmet>
        <title>{metaTitle([title, heading])}</title>
      </Helmet>
      <Flex align="center" justify="space-between" p={4}>
        <Title title={heading} />
      </Flex>
      <Box px={4}>
        <Tabs current={currentPath}>
          <Suspense fallback={null}>
            <FormProvider {...methods}>
              <Box as="form" noValidate onSubmit={handleSubmit(onSubmit)}>
                {isInheritedTheme && (
                  <Alert
                    status="info"
                    sx={{
                      border: '2px solid',
                      borderTop: 0,
                      borderColor: 'gray.100',
                    }}
                  >
                    <AlertIcon />
                    {`Application inherits theme from "${themeId}".`}
                  </Alert>
                )}

                <Grid
                  sx={{
                    gap: 0,
                    h: 'calc(100vh - 179px)',
                    gridTemplateRows: 'auto 60px',
                    borderLeft: '2px solid',
                    borderRight: '2px solid',
                    borderColor: 'gray.100',
                    _dark: {
                      borderColor: 'transparent',
                    },
                  }}
                >
                  <GridItem>
                    <PanelGroup
                      className="panel-group"
                      direction="horizontal"
                      ref={panelGroupRef}
                    >
                      <Panel ref={panelRef}>
                        <Box sx={panelOverlayStyle}>
                          <Grid sx={panelGridStyle}>
                            <DropZoneField
                              name="logo"
                              label="Logo"
                              control={control}
                              isDisabled={isFieldDisabled}
                              isRequired
                              height={100}
                              shortInfo="Supported files; SVG. Make sure your logo is vector."
                              longInfo="Upload your main application logo in SVG format. This logo will be displayed on the start page and in various parts of the application. Using SVG ensures your logo remains crisp at any size. The logo should be properly optimized and follow vector design best practices."
                              accept={{
                                'image/svg+xml': ['.svg'],
                              }}
                              sx={{
                                gridColumn: '1 / -1',
                              }}
                            />

                            <ColorSwatches dataUri={logo} />

                            <DropZoneField
                              name="dashboardLogo"
                              label="Logo (Dashboard)"
                              control={control}
                              isDisabled={isFieldDisabled}
                              height={100}
                              shortInfo="Alternative logo for dashboard. Supported files; SVG. Make sure your logo is vector."
                              longInfo="Upload a specialized version of your logo for the dashboard header. This logo can be optimized for horizontal display or have specific adjustments for the dashboard context. Using SVG ensures perfect scaling at any size. If not specified, the main logo will be used."
                              accept={{
                                'image/svg+xml': ['.svg'],
                              }}
                              sx={{
                                gridColumn: '1 / -1',
                              }}
                            />

                            <DropZoneField
                              name="emailLogo"
                              label="Logo (Email)"
                              control={control}
                              isDisabled={isFieldDisabled}
                              height={100}
                              shortInfo="Alternative logo for emails. Supported files; SVG. Make sure your logo is vector."
                              longInfo="Upload a specialized version of your logo for email communications. This logo will be used in system-generated emails and should be optimized for white backgrounds. The logo should have good contrast and be clearly visible in email clients. If not specified, the main logo will be used."
                              accept={{
                                'image/svg+xml': ['.svg'],
                              }}
                              sx={{
                                gridColumn: '1 / -1',
                              }}
                            />

                            <DropZoneField
                              name="icon"
                              label="App Icon"
                              control={control}
                              isDisabled={isFieldDisabled}
                              isRequired
                              height={100}
                              shortInfo="Supported files; SVG. Make sure your logo is vector."
                              longInfo="Upload your application icon in SVG format. This icon will be used for the app launcher, home screen, and other system-level displays. The icon should be simple, recognizable, and work well at small sizes. Using SVG ensures perfect scaling across all devices and resolutions."
                              accept={{
                                'image/svg+xml': ['.svg'],
                              }}
                              sx={{
                                gridColumn: '1 / -1',
                              }}
                            />

                            <InputField
                              name="iconPadding"
                              label="App Icon Padding (in %)"
                              type="number"
                              control={control}
                              isDisabled={isFieldDisabled}
                              isRequired
                              longInfo="Controls the spacing around the app icon. A higher percentage adds more padding around the icon, while 0 removes padding completely. Use 0 if your icon has its own background or if you want it to fill the entire space."
                              placeholder="20"
                            />

                            <ColorPickerField
                              name="iconBackground"
                              label="Icon Background Color"
                              control={control}
                              isDisabled={isFieldDisabled}
                              longInfo="Sets the background color for the app icon. This color will be visible when the icon is displayed in various contexts like the device home screen or app launcher. Leave empty for transparent background."
                              placeholder="#ffffff"
                            />

                            <SelectField
                              name="headingFont"
                              label="Heading Font"
                              longInfo="Select the font family for all headings and titles throughout the application. This font should be used for main titles, section headers, and important text elements. Choose a font that complements your brand identity and ensures good readability."
                              control={control}
                              options={fontOptions}
                              isLoading={fontOptions?.length === 0}
                              isDisabled={isFieldDisabled}
                              isRequired
                              isSearchable
                              menuPosition="fixed"
                            />

                            <SelectField
                              name="bodyFont"
                              label="Body Font"
                              control={control}
                              longInfo="Select the font family for the main body text of your application. This font will be used for paragraphs, lists, and general content. Choose a font that offers excellent readability for longer text passages."
                              options={fontOptions}
                              isLoading={fontOptions?.length === 0}
                              isDisabled={isFieldDisabled}
                              isRequired
                              isSearchable
                              menuPosition="fixed"
                            />

                            <ColorPickerField
                              name="primaryColor"
                              label="Primary Color"
                              control={control}
                              isDisabled={isFieldDisabled}
                              isRequired
                              longInfo="The main brand color used throughout the application. This color is typically used for primary buttons, important UI elements, and key interactive components. Choose a color that represents your brand identity."
                            />

                            <ColorPickerField
                              name="secondaryColor"
                              label="Secondary Color"
                              control={control}
                              isDisabled={isFieldDisabled}
                              isRequired
                              longInfo="A complementary color to your primary color, used for secondary elements, accents, and supporting UI components. This color should harmonize with your primary color while providing visual hierarchy."
                            />

                            <SelectField
                              name="primaryColorMap"
                              label="Primary Color Map"
                              control={control}
                              isDisabled={isFieldDisabled}
                              longInfo="Defines how the primary color is applied across different UI elements. This mapping determines which components use the primary color and how it's distributed throughout the interface."
                              options={colorOptions}
                              menuPosition="fixed"
                            />

                            <SelectField
                              name="secondaryColorMap"
                              label="Secondary Color Map"
                              control={control}
                              isDisabled={isFieldDisabled}
                              longInfo="Defines how the secondary color is applied across different UI elements. This mapping determines which components use the secondary color and how it complements the primary color usage."
                              options={colorOptions}
                              menuPosition="fixed"
                            />

                            <ColorPickerField
                              name="backgroundColor"
                              label="Background Color"
                              control={control}
                              isDisabled={isFieldDisabled}
                              isRequired
                              longInfo="The main background color used on the start page and dashboard header. This color sets the foundation for your application's visual hierarchy and should provide good contrast with text and other UI elements."
                            />

                            <ColorPickerField
                              name="textColor"
                              label="Text Color"
                              control={control}
                              isDisabled={isFieldDisabled}
                              isRequired
                              longInfo="The default text color used throughout the application, excluding the start page. This color should provide good contrast against the background color for optimal readability."
                            />

                            <ColorPickerField
                              name="customButtonColor"
                              label="Custom Button Color"
                              control={control}
                              isDisabled={isFieldDisabled}
                              longInfo="Override the default button color with a custom color. This allows for specific button styling that may differ from the primary color scheme. Leave empty to use the default button styling."
                            />

                            <ColorPickerField
                              name="customLinkColor"
                              label="Custom Link Color"
                              control={control}
                              isDisabled={isFieldDisabled}
                              longInfo="Override the default link color with a custom color. This allows for specific link styling that may differ from the primary color scheme. Leave empty to use the default link styling."
                            />

                            <ColorPickerField
                              name="customTextColor"
                              label="Custom Text Color"
                              control={control}
                              isDisabled={isFieldDisabled}
                              longInfo="Override the default text color with a custom color. This allows for specific text styling that may differ from the primary color scheme. Leave empty to use the default text color."
                            />

                            <NumberField
                              name="startLogoHeight"
                              label="Start Logo Height"
                              control={control}
                              isDisabled={isFieldDisabled}
                              isRequired
                              longInfo="Controls the height of the logo displayed on the start page. This value determines how large the logo appears when users first visit your application. Adjust based on your logo's proportions and design needs."
                              placeholder="120"
                            />

                            <NumberField
                              name="dashboardLogoHeight"
                              label="Dashboard Logo Height"
                              type="number"
                              control={control}
                              isDisabled={isFieldDisabled}
                              isRequired
                              longInfo="Controls the height of the logo displayed in the dashboard header. This value determines how large the logo appears in the main application interface. Adjust based on your logo's proportions and design needs."
                              placeholder="40"
                            />

                            <NumberField
                              name="appIconSize"
                              label="App Icon Size"
                              type="number"
                              control={control}
                              isDisabled={isFieldDisabled}
                              isRequired
                              longInfo="Controls the size of the application icon displayed on devices. This value determines how large the icon appears on home screens and app launchers. Adjust based on your icon's design and visibility needs."
                              placeholder="100"
                            />

                            <SelectField
                              name="useBackground"
                              label="Background"
                              control={control}
                              options={backgroundOptions}
                              isDisabled={isFieldDisabled}
                              longInfo="Determines whether to use a custom background (image or gradient) on the start page. When enabled, you can specify background image properties and styling options."
                              menuPosition="fixed"
                            />

                            <Box
                              sx={{
                                display: useBackground ? 'grid' : 'none',
                                gridTemplateColumns: 'repeat(2, 1fr)',
                                gridColumn: '1 / -1',
                                gap: 4,
                              }}
                            >
                              <DropZoneField
                                name="background.backgroundImageFile"
                                label="Background Image File"
                                control={control}
                                isDisabled={isFieldDisabled}
                                height={100}
                                shortInfo="Supported files; PNG, JPG."
                                longInfo="Upload a background image file for the start page. The image should be high resolution and optimized for web use. Supported formats are PNG and JPG. The image will be displayed according to the background position, size, and repeat settings below."
                                accept={{
                                  'image/jpg': ['.jpg'],
                                  'image/jpeg': ['.jpeg'],
                                  'image/png': ['.png'],
                                }}
                                sx={{
                                  gridColumn: '1 / -1',
                                }}
                                onCallback={onBackgroundImageUpload}
                              />

                              <InputField
                                name="background.backgroundImage"
                                label="Background Image"
                                control={control}
                                isDisabled={isFieldDisabled}
                                isRequired={useBackground}
                                shortInfo="Gradient and/or image URL."
                                longInfo="Specify a background image URL or CSS gradient. You can use a single image URL, a gradient, or combine both. Example: linear-gradient(to bottom, rgba(0,0,0,0.6), rgba(0,0,0,0.5)), url('https://example.com/background.jpg')"
                                placeholder="linear-gradient(to bottom, rgba(0,0,0,0.6), rgba(0,0,0,0.5)), url('https://example.com/background.jpg')"
                                sx={{
                                  gridColumn: '1 / -1',
                                }}
                              />

                              <InputField
                                name="background.backgroundPosition"
                                label="Background Position"
                                control={control}
                                isDisabled={isFieldDisabled}
                                longInfo="Controls how the background image is positioned within its container. Common values include 'center center', 'top left', '50% 50%', or specific pixel values like '0 0'. This setting is crucial for how the image aligns with the content."
                                placeholder="center center"
                              />

                              <InputField
                                name="background.backgroundAttachment"
                                label="Background Attachment"
                                control={control}
                                isDisabled={isFieldDisabled}
                                longInfo="Determines how the background image behaves when scrolling. 'fixed' keeps the image fixed while content scrolls over it, 'scroll' moves with the content, and 'local' scrolls with the element's content. Choose based on your desired parallax or scrolling effect."
                                placeholder="fixed"
                              />

                              <InputField
                                name="background.backgroundRepeat"
                                label="Background Repeat"
                                control={control}
                                isDisabled={isFieldDisabled}
                                longInfo="Controls how the background image repeats to fill the container. 'no-repeat' shows the image once, 'repeat' tiles both horizontally and vertically, 'repeat-x' tiles horizontally only, and 'repeat-y' tiles vertically only. Use 'no-repeat' for full-screen images."
                                placeholder="no-repeat"
                              />

                              <InputField
                                name="background.backgroundSize"
                                label="Background Size"
                                control={control}
                                isDisabled={isFieldDisabled}
                                longInfo="Determines how the background image is sized within its container. 'cover' scales the image to cover the entire container while maintaining aspect ratio, 'contain' fits the entire image within the container, or use specific dimensions like '100% 100%' for exact sizing."
                                placeholder="cover"
                              />
                            </Box>
                          </Grid>
                        </Box>
                      </Panel>
                      <PanelResize
                        onDragging={() => {
                          const size = panelRef.current?.getSize()
                          if (is(Number, size)) {
                            setPanelSize(size)
                          }
                        }}
                      />
                      <Panel>
                        <Box sx={panelOverlayStyle}>
                          <Suspense fallback={null}>
                            <BuilderPreviewSimple control={control} />
                          </Suspense>
                        </Box>
                      </Panel>
                    </PanelGroup>
                  </GridItem>
                  <GridItem
                    sx={{
                      px: 2,
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      borderTop: '2px solid',
                      borderColor: 'gray.100',
                      bg: 'white',
                      _dark: {
                        borderColor: 'transparent',
                        bg: 'transparent',
                      },
                    }}
                  >
                    <HStack spacing={2}>
                      <Suspense fallback={null}>
                        <JobIndicator />
                      </Suspense>
                    </HStack>
                    <HStack spacing={2}>
                      <Button
                        type="submit"
                        aria-label="Update"
                        colorScheme="green"
                        minW="120px"
                        isDisabled={
                          !isValid ||
                          isValidating ||
                          isFieldDisabled ||
                          readyState === 1
                        }
                        loadingText="Updating"
                        isLoading={isSubmitting || readyState === 1}
                      >
                        Update
                      </Button>
                    </HStack>
                  </GridItem>
                </Grid>
              </Box>
            </FormProvider>
          </Suspense>
        </Tabs>
      </Box>
    </>
  )
})

export async function loader({ params }: LoaderFunctionArgs) {
  const scopes = ['manage:all']

  const { checkLoaderPermissions } = await import('@/utils/loader-auth')
  await checkLoaderPermissions(scopes)

  const { getFilteredTabs } = await import('@/utils/tab-loader')
  const tabs = await getFilteredTabs('settings-tabs', params)

  return {
    title: 'Customize',
    scopes,
    tabs,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <Customize />
}
