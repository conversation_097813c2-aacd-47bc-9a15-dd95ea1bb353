import {
  lazy,
  Suspense,
  useMemo,
  useCallback,
  useDeferredValue,
  memo,
} from 'react'
import { Helm<PERSON> } from 'react-helmet-async'
import { useAtomValue } from 'jotai'
import { useNavigate, type LoaderFunctionArgs } from 'react-router'
import {
  useQuery,
  useMutation,
  useQueryClient,
  keepPreviousData,
} from '@tanstack/react-query'
import { Box, Badge, Flex, HStack } from '@chakra-ui/react'
import { DataTable } from '@/components/data-table/data-table'
import { useOrganization } from '@/hooks/use-organization'
import { useModal } from '@/hooks/use-modal'
import { useToast } from '@/hooks/use-toast'
import { useAbility } from '@/hooks/use-ability'
import { useColorMap } from '@/hooks/use-color-map'
import { formatDate } from '@/utils/date/format-date'
import { Title } from '@/components/title'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { Tabs } from '@/components/tabs'
import { useRouter } from '@/hooks/use-router'
import { metaTitle } from '@/utils/meta-title'
import { tableAtom } from '@/utils/stores/table'
import { GlobalSearch } from '@/features/integrations/global-search'
import {
  getIntegrationSources,
  deleteIntegrationSource,
} from '@/api/integrations'
import type { GetCursorResponse } from '@/types/api'
import type { IntegrationSourceModel } from '@/types/models/integration'
import type { ColumnProps, ActionProps } from '@/types/table'

const ConfirmDialog = lazy(() =>
  import('@/components/confirm-dialog').then(({ ConfirmDialog }) => ({
    default: ConfirmDialog,
  }))
)

const Sources = memo(() => {
  const heading = 'Integrations'
  const tableName = 'my-integrations'
  const modal = useModal()
  const toast = useToast()
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  const { mapColor } = useColorMap()
  const { title, currentPath } = useRouter()
  const { applicationId, organizationId } = useOrganization()
  const { limit, currPage, filter } = useAtomValue(tableAtom(tableName))
  const deferredFilter = useDeferredValue(filter)
  const { can } = useAbility()

  const canEdit = can(['edit', 'applications'])

  const { mutateAsync: deleteIntegrationSourceMutation } = useMutation<
    number,
    Error,
    {
      organizationId: string
      applicationId: string
      integrationId: string
    }
  >({
    mutationFn: ({ organizationId, applicationId, integrationId }) =>
      deleteIntegrationSource(organizationId, applicationId, integrationId),
    onSuccess: () => {
      const cache = ['GetIntegrationSources', 'GetIntegrationSource']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const { data } = useQuery<
    GetCursorResponse<IntegrationSourceModel> | null,
    Error
  >({
    queryKey: [
      'GetIntegrationSources',
      organizationId,
      applicationId,
      limit,
      currPage,
      deferredFilter,
    ],
    queryFn: ({ signal }) =>
      organizationId && applicationId
        ? getIntegrationSources({
            organizationId,
            applicationId,
            limit,
            page: currPage,
            filter: deferredFilter,
            signal,
          })
        : null,
    placeholderData: keepPreviousData,
    enabled: !!organizationId && !!applicationId,
  })

  const onEdit = useCallback(
    (source?: IntegrationSourceModel) => {
      navigate(
        `/manage/integrations/connect/${source?.integration_id}/edit/${source?.id}`
      )
    },
    [navigate]
  )

  const onRemove = useCallback(
    ({ id, name }: IntegrationSourceModel): void => {
      if (!(organizationId && applicationId)) {
        return
      }

      modal({
        size: 'md',
        component: <ConfirmDialog />,
        config: {
          title: 'Remove Integration Source?',
          description: `Are you sure you want to remove “${name}”?`,
          confirmLabel: 'Remove',
          onCallback: async () => {
            try {
              await deleteIntegrationSourceMutation({
                organizationId,
                applicationId,
                integrationId: id,
              })
              toast({
                status: 'success',
                msg: `“${name}” has been removed.`,
              })
            } catch {
              toast({
                status: 'error',
                msg: `Unable to delete “${name}”.`,
              })
            }
          },
        },
      })
    },
    [
      modal,
      toast,
      organizationId,
      applicationId,
      deleteIntegrationSourceMutation,
    ]
  )

  const columns = useMemo<ColumnProps<IntegrationSourceModel>[]>(
    () => [
      {
        name: 'Name',
        id: 'name',
        cell: ({ name }) => <Box noOfLines={1}>{name}</Box>,
      },
      {
        name: 'Type',
        id: 'alias',
        canHide: true,
        cell: ({ integration }) =>
          integration && <Box noOfLines={1}>{integration.name}</Box>,
      },
      {
        name: 'Direction',
        id: 'type',
        canHide: true,
        cell: ({ integration }) =>
          integration && <Box noOfLines={1}>{integration.type}</Box>,
      },
      {
        name: 'Created',
        id: 'created_at',
        canHide: true,
        cell: ({ created_at }) =>
          created_at && (
            <Box noOfLines={1}>{formatDate(created_at, 'P pp')}</Box>
          ),
      },
      {
        name: 'Events',
        id: 'events',
        cell: ({ integration }) =>
          (integration?.events ?? '').split(',').map((v: string, i: number) => (
            <Badge mr={1} key={i} colorScheme={mapColor(v)}>
              {v}
            </Badge>
          )),
      },
      {
        name: 'Last Data',
        id: 'updated_at',
        canHide: true,
        cell: ({ updated_at }) =>
          updated_at && (
            <Box noOfLines={1}>{formatDate(updated_at, 'P pp')}</Box>
          ),
      },
    ],
    [mapColor]
  )

  const actions = useMemo<ActionProps<IntegrationSourceModel>[]>(
    () => [
      {
        label: 'Edit Source',
        onClick: onEdit,
        canView: () => canEdit,
      },
      {
        label: 'Remove Source',
        onClick: onRemove,
        canView: () => canEdit,
      },
    ],
    [canEdit, onEdit, onRemove]
  )

  return (
    <>
      <Helmet>
        <title>{metaTitle([title, heading])}</title>
      </Helmet>
      <Flex align="center" justify="space-between" p={4}>
        <Title title={heading} count={data?.count} suffix="Record" />
        <HStack>
          <GlobalSearch tableName={tableName} />
        </HStack>
      </Flex>
      <Box px={4}>
        <Tabs current={currentPath}>
          <Suspense fallback={null}>
            <DataTable
              tableName={tableName}
              data={data ?? { count: 0, rows: [] }}
              columns={columns}
              actions={actions}
            />
          </Suspense>
        </Tabs>
      </Box>
    </>
  )
})

export async function loader({ params }: LoaderFunctionArgs) {
  const scopes = ['manage:all']

  const { checkLoaderPermissions } = await import('@/utils/loader-auth')
  await checkLoaderPermissions(scopes)

  const { getFilteredTabs } = await import('@/utils/tab-loader')
  const tabs = await getFilteredTabs('integration-tabs', params)

  return {
    title: 'My Integrations',
    scopes,
    tabs,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <Sources />
}
