import { lazy, Suspense, memo } from 'react'
import { useMountEffect } from '@react-hookz/web'
import { Helmet } from 'react-helmet-async'
import type { LoaderFunctionArgs } from 'react-router'
import { useAtom, useAtomValue, useSet<PERSON>tom } from 'jotai'
import {
  Box,
  Flex,
  HStack,
  ButtonGroup,
  IconButton,
  Alert,
  AlertDescription,
  AlertTitle,
  AlertIcon,
} from '@chakra-ui/react'
import { LayoutGridIcon, LayoutListIcon } from 'lucide-react'
import { Title } from '@/components/title'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { Tabs } from '@/components/tabs'
import { useRouter } from '@/hooks/use-router'
import { metaTitle } from '@/utils/meta-title'
import { transparentize } from '@/utils/transparentize'
import { searchCriteria } from '@/utils/search-criteria'
import { tableAtom, gridAtom, recordCountAtom } from '@/utils/stores/table'
import { GlobalSearch } from '@/features/integrations/global-search'

const GridView = lazy(() =>
  import('@/features/integrations/grid-view').then(({ GridView }) => ({
    default: GridView,
  }))
)

const TableView = lazy(() =>
  import('@/features/integrations/table-view').then(({ TableView }) => ({
    default: TableView,
  }))
)

const Sources = memo(() => {
  const heading = 'Integrations'
  const tableName = 'integrations'
  const { title, currentPath } = useRouter()
  const setTable = useSetAtom(tableAtom(tableName))
  const [isGrid, setGrid] = useAtom(gridAtom)
  const count = useAtomValue(recordCountAtom)

  useMountEffect(() => {
    const stack = searchCriteria({
      id: 'type',
      condition: 'eq',
      value: 'inbound',
      prev: [],
    })

    setTable((prev) => ({
      ...prev,
      currPage: 0,
      filter: stack?.[0]?.query,
      filters: [...stack],
    }))
  })

  const styles = {
    borderRadius: 'base',
    color: 'primary.500',
    bg: 'gray.100',
    _dark: {
      color: 'whiteAlpha.400',
      bg: 'whiteAlpha.50',
    },
    _active: {
      color: 'gray.100',
      bg: 'primary.500',
      _dark: {
        bg: transparentize('primary.500', 0.1),
      },
    },
  }

  return (
    <>
      <Helmet>
        <title>{metaTitle([title, heading])}</title>
      </Helmet>
      <Flex align="center" justify="space-between" p={4}>
        <Title title={heading} count={count} suffix="Record" />
        <HStack>
          <GlobalSearch tableName={tableName} />
          <ButtonGroup isAttached colorScheme="primary">
            <IconButton
              aria-label="List View"
              icon={<LayoutListIcon size={16} />}
              onClick={() => setGrid(false)}
              isActive={!isGrid}
              sx={styles}
            />
            <IconButton
              aria-label="Grid View"
              icon={<LayoutGridIcon size={16} />}
              onClick={() => setGrid(true)}
              isActive={isGrid}
              sx={styles}
            />
          </ButtonGroup>
        </HStack>
      </Flex>
      <Box px={4}>
        <Alert
          status="info"
          variant="ghost"
          colorScheme="blue"
          sx={{
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            textAlign: 'center',
            height: '180px',
            borderRadius: 'base',
            border: '1px solid',
            borderColor: 'blue.200',
            bg: 'blue.100',
            color: 'blue.500',
            _dark: {
              borderColor: 'transparent',
              bg: transparentize('blue.500', 0.1),
              color: 'blue.200',
            },
          }}
        >
          <AlertIcon boxSize="40px" mr={0} />
          <AlertTitle mt={4} mb={1} fontSize="lg">
            Integration Guidelines
          </AlertTitle>
          <AlertDescription maxWidth="3xl">
            Sensor connected to gateways purchased from myDevices or registered
            <br />
            on myDevices’s LNS (LoRaWAN Network Server) does not require an
            integration endpoint url.
            <br />
            Only add an integration if your device sends data via HTTP over
            Cellular or using your own LNS.
          </AlertDescription>
        </Alert>
      </Box>
      <Box px={4}>
        <Tabs current={currentPath}>
          <Suspense fallback={null}>
            {isGrid ? <GridView /> : <TableView />}
          </Suspense>
        </Tabs>
      </Box>
    </>
  )
})

export async function loader({ params }: LoaderFunctionArgs) {
  const scopes = ['view:applications']

  const { checkLoaderPermissions } = await import('@/utils/loader-auth')
  await checkLoaderPermissions(scopes)

  const { getFilteredTabs } = await import('@/utils/tab-loader')
  const tabs = await getFilteredTabs('integration-tabs', params)

  return {
    title: 'Integrations',
    scopes,
    tabs,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <Sources />
}
