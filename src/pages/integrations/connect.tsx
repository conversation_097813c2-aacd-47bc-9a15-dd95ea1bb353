import { Suspense, useCallback, useEffect, useMemo, memo } from 'react'
import { Helmet } from 'react-helmet-async'
import { useNavigate, useParams } from 'react-router'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import {
  useForm,
  useWatch,
  FormProvider,
  useFieldArray,
  type FieldArrayWithId,
} from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import {
  Box,
  Grid,
  Code,
  Button,
  IconButton,
  Flex,
  HStack,
  Image,
  Heading,
  Link,
  Container,
  Divider,
  VStack,
} from '@chakra-ui/react'
import { CopyIcon } from 'lucide-react'
import { upperFirst } from 'lodash'
import { isEmpty, isNil } from 'ramda'
import { Title } from '@/components/title'
import { BackButton } from '@/components/back-button'
import { InputField } from '@/components/input-field'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { useRouter } from '@/hooks/use-router'
import { useToast } from '@/hooks/use-toast'
import { useOrganization } from '@/hooks/use-organization'
import { metaTitle } from '@/utils/meta-title'
import { onImageError } from '@/utils/on-image-error'
import {
  getIntegration,
  getIntegrationSource,
  createIntegrationSource,
  updateIntegrationSource,
} from '@/api/integrations'
import { SelectField } from '@/components/select-field'
import { TextareaField } from '@/components/textarea-field'
import { SwitchField } from '@/components/switch-field'
import { sortByOrder } from '@/utils/sort-by-order'
import type { VariableModel, VariableType } from '@/types/models/variable'
import type {
  IntegrationModel,
  IntegrationSourceModel,
} from '@/types/models/integration'
import type {
  IntegrationSourceSettingInput,
  CreateIntegrationSourceResponse,
  CreateIntegrationSourceInput,
} from '@/types/api'

const isProd = import.meta.env.VITE_DB_ENV === 'production'

const baseUrl = isProd
  ? 'https://hub.m2c.io/v3/ingress/'
  : 'https://lora.staging.mydevices.com/ingress/'

const schema = z.object({
  integration_id: z.string().nullish(),
  name: z.string(),
  type: z.string(),
  key: z.string(),
  mask_id: z.string(),
  endpoint: z.string().url().or(z.literal('')),
  variables: z.array(
    z
      .object({
        name: z.string(),
        label: z.string(),
        isRequired: z.boolean(),
        description: z.string(),
        options: z.array(z.record(z.string())),
        datatype: z.enum([
          'string',
          'number',
          'text',
          'password',
          'email',
          'url',
          'select',
          'code',
          'switch',
        ]),
        value: z.string(),
      })
      .superRefine((arg, ctx) => {
        if (arg.datatype === 'url' && !arg.value) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Invalid URL.',
            path: ['variables', 'value'],
          })
        }

        if (arg.datatype === 'email' && !arg.value) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Invalid email address.',
            path: ['variables', 'value'],
          })
        }

        if (arg.datatype === 'number' && /^\d+$/g.test(arg.value)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Only numbers.',
            path: ['variables', 'value'],
          })
        }

        if (arg.datatype === 'switch' && !arg.options) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Switch requires options.',
            path: ['variables', 'options'],
          })
        }

        return z.NEVER
      })
  ),
})

type FormInputProps = z.infer<typeof schema>

interface FormVariable extends Omit<VariableModel, 'options' | 'description'> {
  isRequired: boolean
  description: string
  value: string
  options: BaseOption<string>[]
}

interface FormIntegration extends Omit<IntegrationModel, 'variables'> {
  variables: FormVariable[]
}

const Connect = memo(() => {
  const navigate = useNavigate()
  const { title } = useRouter()
  const params = useParams()
  const toast = useToast()
  const queryClient = useQueryClient()
  const { integrationId, sourceId } = params
  const { organizationId, applicationId } = useOrganization()

  const videoUrl = null

  const { mutateAsync: createIntegrationSourceMutation } = useMutation<
    CreateIntegrationSourceResponse,
    Error,
    {
      organizationId: string
      applicationId: string
      input: CreateIntegrationSourceInput
    }
  >({
    mutationFn: ({ organizationId, applicationId, input }) =>
      createIntegrationSource(organizationId, applicationId, input),
    onSuccess: () => {
      const cache = ['GetIntegrationSources', 'GetIntegrationSource']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const { mutateAsync: updateIntegrationSourceMutation } = useMutation<
    CreateIntegrationSourceResponse,
    Error,
    {
      organizationId: string
      applicationId: string
      id: Maybe<string>
      input: CreateIntegrationSourceInput
    }
  >({
    mutationFn: ({ id, organizationId, applicationId, input }) =>
      updateIntegrationSource(organizationId, applicationId, id, input),
    onSuccess: () => {
      const cache = ['GetIntegrationSources', 'GetIntegrationSource']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const { data: integration } = useQuery<
    IntegrationModel,
    Error,
    FormIntegration
  >({
    queryKey: ['GetIntegration', organizationId, applicationId, integrationId],
    queryFn: ({ signal }) =>
      getIntegration({
        organizationId,
        applicationId,
        integrationId,
        signal,
      }),

    enabled: !!integrationId,
    select: useCallback(
      (data: IntegrationModel) => ({
        ...data,
        variables: sortByOrder(
          (data?.variables ?? []).reduce(
            (acc: FormVariable[], variable: VariableModel) => {
              acc.push({
                ...variable,
                isRequired: variable.is_required === 1,
                value: variable.default_value ?? '',
                description: variable.description ?? '',
                options: (variable.options ?? '')
                  .split(' ')
                  .filter(Boolean)
                  .map((value: string) => ({
                    value,
                    label: upperFirst(value),
                  })),
              })
              return acc
            },
            []
          )
        ),
      }),
      []
    ),
  })

  const { data: source } = useQuery<IntegrationSourceModel | null, Error>({
    queryKey: ['GetIntegrationSource', organizationId, applicationId, sourceId],
    queryFn: ({ signal }) =>
      organizationId && applicationId && sourceId
        ? getIntegrationSource({
            organizationId,
            applicationId,
            sourceId,
            signal,
          })
        : null,
    enabled: !!organizationId && !!applicationId && !!sourceId,
  })

  const inputType = (datatype: VariableType): string => {
    switch (datatype) {
      case 'string':
        return 'text'
      default:
        return datatype
    }
  }

  const controlType = ({
    options,
    datatype,
  }: FieldArrayWithId<
    {
      name: string
      organization_id: string
      application_id: string
      account_id: string
      variables?: Array<{
        name: string
        label: string
        value: string
        datatype: string
        isRequired: boolean
        options: Dict[]
        description: string
      }>
    },
    'variables',
    'id'
  >): string => {
    // Legacy fallback when it has options but not a select.
    if (options?.length > 1 && !['select', 'switch'].includes(datatype)) {
      return 'select'
    }

    switch (datatype) {
      case 'text':
        return 'textarea'
      case 'code':
        return 'code'
      case 'switch':
        return 'switch'
      case 'select':
        return 'select'
      default:
        return 'input'
    }
  }

  const defaultValues = {
    integration_id: integrationId ?? null,
    mask_id: '',
    name: '',
    type: '',
    key: '',
    endpoint: '',
    variables: [] as FormVariable[],
  } satisfies FormInputProps

  const methods = useForm({
    mode: 'onTouched',
    resolver: zodResolver(schema),
    defaultValues,
  })

  const {
    control,
    setValue,
    handleSubmit,
    formState: { isValid, isSubmitting },
  } = methods

  const endpoint = useWatch({
    name: 'endpoint',
    control,
  })

  const { fields } = useFieldArray({
    control,
    name: 'variables',
  })

  useEffect(() => {
    setValue('name', source?.name ?? integration?.name ?? '')
    setValue('key', source?.key ?? '')
    setValue('type', source?.type ?? integration?.type ?? '')
    setValue('mask_id', source?.mask_id ?? '')
    setValue(
      'endpoint',
      source ? `${baseUrl}${source?.mask_id}?apiKey=${source?.key}` : ''
    )
    setValue(
      'variables',
      integration?.variables?.map((variable) => ({
        ...variable,
        value:
          source?.settings?.find(({ name }) => name === variable.name)?.value ??
          variable.value,
      })) ??
        integration?.variables ??
        []
    )
  }, [integration, source, setValue])

  const curl = useMemo<string>(
    () =>
      [
        'curl --request POST \\',
        `--url ${endpoint} \\`,
        '--header "Content-Type: application/json" \\',
        `--data '{`,
        `  "eui": "sim-5241-6e82-ac46",`,
        `  "format": "json",`,
        `  "ignore_codec": true,`,
        `  "data": [{"channel":4,"type":"temp","unit":"c","value":23.62},{"channel":5,"type":"batt","unit":"v","value":99}]`,
        `}'`,
      ].join('\n'),
    [endpoint]
  )

  const onGoBack = () => {
    navigate('/manage/integrations/sources', {
      viewTransition: true,
    })
  }

  const onCopy = async (): Promise<void> => {
    try {
      await navigator.clipboard.writeText(curl)
      toast({
        status: 'copy',
        msg: 'cURL request has been copied to clipboard.',
      })
    } catch {
      // void
    }
  }

  const onSubmit = async (values: FormInputProps): Promise<boolean> => {
    if (!(organizationId && applicationId && integrationId)) {
      return false
    }

    const input = {
      integration_id: integrationId,
      name: values.name,
      type: values.type,
      settings: (values?.variables ?? []).reduce(
        (acc: IntegrationSourceSettingInput[], { name, value }) => {
          if (isNil(value) || isEmpty(value)) {
            return acc
          }
          acc.push({ name, value })
          return acc
        },
        []
      ),
    }

    try {
      const response = source?.id
        ? await updateIntegrationSourceMutation({
            organizationId,
            applicationId,
            id: source?.id,
            input,
          })
        : await createIntegrationSourceMutation({
            organizationId,
            applicationId,
            input,
          })

      if (!response?.id) {
        throw new Error('Integration source ID is missing.')
      }

      toast({
        msg: 'Integration source has been saved.',
        status: 'success',
      })

      navigate(
        `/manage/integrations/connect/${integrationId}/edit/${response.id}`,
        {
          viewTransition: true,
        }
      )

      return true
    } catch (error: unknown) {
      if (error instanceof Error) {
        console.error(error.message)
      }

      toast({
        msg: 'Unable to save integration source.',
        status: 'error',
      })

      return false
    }
  }

  return (
    <>
      <Helmet>
        <title>{metaTitle([title])}</title>
      </Helmet>
      <Suspense fallback={null}>
        <Flex align="center" justify="space-between" p={4}>
          <HStack align="center" justify="space-between">
            <BackButton onClick={onGoBack} />
            <Title title={title} />
          </HStack>
        </Flex>
        <Box mx={4} pb={8}>
          <FormProvider {...methods}>
            <Box as="form" noValidate onSubmit={handleSubmit(onSubmit)}>
              <Container maxW="container.xl" m={0} pt={4}>
                <Flex
                  sx={{
                    pb: 4,
                    mb: 8,
                    gap: 4,
                    position: 'relative',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    borderBottom: '2px solid',
                    borderColor: 'gray.100',
                    _dark: {
                      borderColor: 'blackAlpha.200',
                    },
                  }}
                >
                  <Flex
                    sx={{
                      justifyContent: 'flex-start',
                      alignItems: 'center',
                    }}
                  >
                    <Flex
                      sx={{
                        boxSize: '100px',
                        justifyContent: 'center',
                        alignItems: 'center',
                        borderRadius: '50%',
                        borderColor: 'gray.200',
                        bg: 'blackAlpha.50',
                        _dark: {
                          bg: 'blackAlpha.100',
                        },
                      }}
                    >
                      <Image src={integration?.icon} onError={onImageError} />
                    </Flex>
                    <Flex
                      sx={{
                        flexDirection: 'column',
                        ml: 4,
                      }}
                    >
                      <Heading
                        sx={{
                          textTransform: 'uppercase',
                        }}
                      >
                        {integration?.name}
                      </Heading>
                      <Link
                        href={integration?.documentation ?? undefined}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        Integration Docs
                      </Link>
                    </Flex>
                  </Flex>
                  {videoUrl && (
                    <Flex
                      sx={{
                        justifyContent: 'center',
                        alignItems: 'center',
                        fontSize: 'sm',
                        borderRadius: 'base',
                        w: 'calc(560px / 2)',
                        h: 'calc(315px / 2)',
                        bg: 'blackAlpha.50',
                        _dark: {
                          bg: 'blackAlpha.100',
                        },
                      }}
                    >
                      <Box
                        as="iframe"
                        title="video"
                        src={''}
                        sx={{
                          position: 'relative',
                          borderRadius: 'base',
                          border: 0,
                          w: 'inherit',
                          h: 'inherit',
                        }}
                      />
                    </Flex>
                  )}
                </Flex>
                <Suspense fallback={null}>
                  <Grid
                    gap={4}
                    templateColumns={{ base: '1fr', lg: 'repeat(2, 1fr)' }}
                  >
                    <InputField
                      name="name"
                      label="Friendly Name"
                      control={control}
                      isRequired
                      autoFocus
                      sx={{ gridColumn: '1 / span 2' }}
                    />

                    {source && (
                      <>
                        <InputField
                          name="mask_id"
                          label="Client ID"
                          control={control}
                          isReadOnly
                          isCopyable
                        />
                        <InputField
                          name="key"
                          label="API Key"
                          control={control}
                          isReadOnly
                          isCopyable
                        />
                        <InputField
                          name="endpoint"
                          type="url"
                          label="Endpoint URL"
                          control={control}
                          isReadOnly
                          isCopyable
                          shortInfo="Use API Key as query parameter `apiKey=` or as header `x-apikey`"
                          sx={{ gridColumn: '1 / span 2' }}
                        />

                        {integration?.alias === 'http' && (
                          <Box
                            sx={{
                              p: 4,
                              mb: 2,
                              pos: 'relative',
                              borderRadius: 'base',
                              gridColumn: '1 / span 2',
                              bg: 'blackAlpha.50',
                              _dark: {
                                bg: 'blackAlpha.100',
                              },
                            }}
                          >
                            <IconButton
                              aria-label="Copy"
                              variant="link"
                              colorScheme="secondary"
                              icon={<CopyIcon size={16} />}
                              onClick={onCopy}
                              sx={{
                                pos: 'absolute',
                                top: 3,
                                right: 0,
                                borderTopLeftRadius: 0,
                                borderBottomRightRadius: 0,
                              }}
                            />
                            <Code
                              sx={{
                                whiteSpace: 'break-spaces',
                                bg: 'transparent',
                              }}
                            >
                              {curl}
                            </Code>
                          </Box>
                        )}
                      </>
                    )}
                    {fields.length > 0 && (
                      <>
                        <Divider
                          sx={{
                            mb: 4,
                            gridColumn: '1 / span 2',
                          }}
                        />
                        <Box
                          py={4}
                          fontWeight="semibold"
                          fontSize="lg"
                          sx={{ gridColumn: '1 / span 2' }}
                        >
                          Settings
                        </Box>
                        {fields.map((variable, index) => {
                          const type = controlType(variable)
                          return (
                            <VStack
                              key={variable.id}
                              sx={{
                                mb: 5,
                                alignItems: 'stretch',
                                borderRadius: 'base',
                                borderColor: 'blackAlpha.200',
                                bg: 'gray.50',
                                _dark: {
                                  bg: 'blackAlpha.100',
                                },
                              }}
                            >
                              <Grid gap={4} p={4} templateColumns="1fr">
                                {type === 'input' && (
                                  <InputField
                                    name={`variables.${index}.value`}
                                    label={variable.label}
                                    type={inputType(
                                      variable.datatype as VariableType
                                    )}
                                    placeholder={variable.description}
                                    control={control}
                                    isRequired={variable.isRequired}
                                    longInfo={variable?.description}
                                  />
                                )}
                                {type === 'select' && (
                                  <SelectField
                                    name={`variables.${index}.value`}
                                    label={variable.label}
                                    placeholder={variable.description}
                                    options={variable.options}
                                    control={control}
                                    isRequired={variable.isRequired}
                                    longInfo={variable?.description}
                                  />
                                )}
                                {type === 'textarea' && (
                                  <TextareaField
                                    name={`variables.${index}.value`}
                                    label={variable.label}
                                    placeholder={variable.description}
                                    control={control}
                                    isRequired={variable.isRequired}
                                    longInfo={variable?.description}
                                  />
                                )}

                                {type === 'switch' && (
                                  <SwitchField
                                    name={`variables.${index}.value`}
                                    label={variable.label}
                                    control={control}
                                    isRequired={variable.isRequired}
                                    onChange={(event) => {
                                      setValue(
                                        `variables.${index}.value`,
                                        String(event.target.checked)
                                      )
                                    }}
                                  />
                                )}
                              </Grid>
                            </VStack>
                          )
                        })}
                      </>
                    )}

                    <Box gridColumn="1 / span 2">
                      <Divider
                        sx={{
                          mb: 4,
                          gridColumn: '1 / span 2',
                        }}
                      />
                      <Button
                        type="submit"
                        aria-label="Submit"
                        colorScheme="green"
                        minW={120}
                        isLoading={isSubmitting}
                        isDisabled={!isValid || isSubmitting}
                        loadingText={source ? 'Updating' : 'Generating'}
                      >
                        {source ? 'Update' : 'Generate'}
                      </Button>
                    </Box>
                  </Grid>
                </Suspense>
              </Container>
            </Box>
          </FormProvider>
        </Box>
      </Suspense>
    </>
  )
})

export async function loader() {
  const scopes = ['manage:all']

  const { checkLoaderPermissions } = await import('@/utils/loader-auth')
  await checkLoaderPermissions(scopes)

  return {
    title: 'Connect',
    scopes,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <Connect />
}
