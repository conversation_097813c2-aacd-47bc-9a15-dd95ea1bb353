import { Suspense, useMemo, useCallback, memo } from 'react'
import { <PERSON><PERSON><PERSON> } from 'react-helmet-async'
import { useNavigate } from 'react-router'
import { useQuery } from '@tanstack/react-query'
import {
  Box,
  Button,
  Flex,
  Grid,
  Image,
  SimpleGrid,
  Text,
  useColorModeValue,
} from '@chakra-ui/react'
import { Title } from '@/components/title'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { useRouter } from '@/hooks/use-router'
import { metaTitle } from '@/utils/meta-title'
import { onImageError } from '@/utils/on-image-error'
import { getIntegrations } from '@/api/integrations'
import { useDarkerBg } from '@/hooks/use-darker-bg'
import { useOrganization } from '@/hooks/use-organization'
import type { IntegrationModel } from '@/types/models/integration'
import type { GetResponse } from '@/types/api'

const Setup = memo(() => {
  const navigate = useNavigate()
  const { title } = useRouter()
  const darkerBg = useDarkerBg()
  const variant = useColorModeValue('ghost', 'solid')
  const { applicationId, organizationId } = useOrganization()

  const { data } = useQuery<GetResponse<IntegrationModel>, Error>({
    queryKey: ['GetIntegrations', 100, 0, organizationId, applicationId],
    queryFn: ({ signal }) =>
      getIntegrations({
        organizationId,
        applicationId,
        limit: 100,
        page: 0,
        signal,
      }),
  })

  const records = useMemo<GetResponse<IntegrationModel>>(
    () => data ?? { page: 0, count: 0, rows: [] },
    [data]
  )

  const onSetup = useCallback(
    (integrationId: string): void => {
      navigate(`/manage/setup-integration/${integrationId}/add`, {
        viewTransition: true,
      })
    },
    [navigate]
  )

  return (
    <>
      <Helmet>
        <title>{metaTitle([title])}</title>
      </Helmet>
      <Suspense fallback={null}>
        <Flex align="center" justify="space-between" p={4}>
          <Title title={title} count={records.count} suffix="Record" />
          <Text fontSize="lg" fontWeight="medium">
            Please select an integration from the list below and follow the
            instructions.
          </Text>
        </Flex>
        <SimpleGrid minChildWidth="350px" spacing={4} px={4} pb={20}>
          {records.rows.map((row: IntegrationModel) => {
            const { id, icon, name, description } = row
            return (
              <Grid
                key={id}
                sx={{
                  p: 4,
                  gap: 2,
                  gridTemplateRows: '70px 40px 1fr 40px',
                  borderRadius: 'mb',
                  shadow: 'md',
                  h: '260px',
                  bg: 'white',
                  _dark: {
                    bg: darkerBg,
                  },
                }}
              >
                <Flex justify="center" align="center">
                  <Image
                    src={icon}
                    maxH="70px"
                    alignSelf="center"
                    onError={onImageError}
                  />
                </Flex>
                <Flex justify="center" align="center" fontWeight="bold">
                  {name}
                </Flex>
                <Box
                  lineHeight="md"
                  fontSize="sm"
                  textAlign="center"
                  overflow="hidden"
                  textOverflow="ellipsis"
                >
                  {description}
                </Box>
                <Flex justify="center" align="center">
                  <Button
                    type="button"
                    aria-label="Setup"
                    variant={variant}
                    colorScheme="primary"
                    onClick={() => onSetup(row.id)}
                  >
                    Setup
                  </Button>
                </Flex>
              </Grid>
            )
          })}
        </SimpleGrid>
      </Suspense>
    </>
  )
})

export async function loader() {
  const scopes = ['manage:all']

  const { checkLoaderPermissions } = await import('@/utils/loader-auth')
  await checkLoaderPermissions(scopes)

  return {
    title: 'Setup Integration',
    scopes,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <Setup />
}
