import * as Sentry from '@sentry/react'
import { lazy, useMemo, useEffect, useCallback, Suspense, memo } from 'react'
import { Helmet } from 'react-helmet-async'
import { useParams, useNavigate } from 'react-router'
import {
  useForm,
  useWatch,
  useFieldArray,
  FormProvider,
  type FieldArrayWithId,
} from 'react-hook-form'
import { isNil, isEmpty } from 'ramda'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  Box,
  Button,
  Divider,
  Flex,
  Grid,
  IconButton,
  Link,
  HStack,
  VStack,
  Container,
} from '@chakra-ui/react'
import { InfoIcon } from 'lucide-react'
import { Title } from '@/components/title'
import { BackButton } from '@/components/back-button'
import { InputField } from '@/components/input-field'
import { SelectField } from '@/components/select-field'
import { TextareaField } from '@/components/textarea-field'
import { SwitchField } from '@/components/switch-field'
import { SelectOrganizationField } from '@/components/select-organization-field'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { useRouter } from '@/hooks/use-router'
import { useToast } from '@/hooks/use-toast'
import { metaTitle } from '@/utils/meta-title'
import { upperFirst } from '@/utils/upper-first'
import { sortByOrder } from '@/utils/sort-by-order'
import { getApplications } from '@/api/applications'
import { getIntegration } from '@/api/integrations'
import { createFuse } from '@/api/fuses'
import { useOrganization } from '@/hooks/use-organization'
import type { IntegrationModel } from '@/types/models/integration'
import type { VariableModel, VariableType } from '@/types/models/variable'
import type { FuseModel } from '@/types/models/fuse'
import type { FuseInput, FuseSettingInput } from '@/types/api'

const CodeEditorField = lazy(() =>
  import('@/components/code-editor-field').then(({ CodeEditorField }) => ({
    default: CodeEditorField,
  }))
)

const TRANSFORM_SCRIPT_DEFAULT = `
/** Transform webhook payload to non-standard format
 *  - 'event' contains the standard JSON schema
 *      https://github.com/myDevicesIoT/integrations-docs/blob/master/docs/transform/event.json
 *  - 'metadata' contains fuseData/metadata such as company and location.
 *      https://github.com/myDevicesIoT/integrations-docs/blob/master/docs/transform/metadata.json
 * The function Transform must return an object, e.g. {"temperature": 22.5}
 */
function Transform(event, metadata) {
    return event;
}
`

const schema = z.object({
  name: z.string().min(2, 'Name is required.'),
  organization_id: z.string(),
  application_id: z.string(),
  account_id: z.string().min(1, 'Account ID is required.'),
  variables: z.array(
    z
      .object({
        name: z.string(),
        label: z.string(),
        isRequired: z.boolean(),
        description: z.string(),
        options: z.array(z.record(z.string())),
        datatype: z.enum([
          'string',
          'number',
          'text',
          'password',
          'email',
          'url',
          'select',
          'code',
          'switch',
        ]),
        value: z.string(),
      })
      .superRefine((arg, ctx) => {
        if (arg.datatype === 'url' && !arg.value) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Invalid URL.',
            path: ['variables', 'value'],
          })
        }

        if (arg.datatype === 'email' && !arg.value) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Invalid email address.',
            path: ['variables', 'value'],
          })
        }

        if (arg.datatype === 'number' && /^\d+$/g.test(arg.value)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Only numbers.',
            path: ['variables', 'value'],
          })
        }

        if (arg.datatype === 'switch' && !arg.options) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Switch requires options.',
            path: ['variables', 'options'],
          })
        }

        return z.NEVER
      })
  ),
})

const defaultValues = {
  name: '',
  organization_id: '',
  application_id: '',
  account_id: '',
  variables: [] as FormVariable[],
}

type FormInputProps = z.infer<typeof schema>

interface FormVariable extends Omit<VariableModel, 'options' | 'description'> {
  isRequired: boolean
  description: string
  value: string
  options: BaseOption<string>[]
}

interface FormIntegration extends Omit<IntegrationModel, 'variables'> {
  variables: FormVariable[]
}

const AddIntegration = memo(() => {
  const toast = useToast()
  const navigate = useNavigate()
  const { title } = useRouter()
  const queryClient = useQueryClient()
  const params = useParams<Dict<string>>()
  const { organizationId, applicationId } = useOrganization()

  const integrationId = useMemo<string>(
    () => params.integrationId ?? '',
    [params]
  )

  const methods = useForm({
    mode: 'onTouched',
    resolver: zodResolver(schema),
    defaultValues,
  })

  const {
    reset,
    control,
    setValue,
    handleSubmit,
    formState: { isValid, isSubmitting },
  } = methods

  const variables = useWatch({
    name: 'variables',
    control,
  })

  const { fields } = useFieldArray({
    control,
    name: 'variables',
  })

  const { mutateAsync: createFuseMutation } = useMutation<
    FuseModel,
    Error,
    FuseInput
  >({
    mutationFn: (input) => createFuse(input),
    onSuccess: () => {
      const cache = ['GetIntegrations', 'GetFuses']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const { data: integration } = useQuery<
    IntegrationModel,
    Error,
    FormIntegration
  >({
    queryKey: ['getIntegration', integrationId, organizationId, applicationId],
    queryFn: ({ signal }) =>
      getIntegration({
        organizationId,
        applicationId,
        integrationId,
        signal,
      }),
    enabled: !!integrationId,
    select: useCallback(
      (data: IntegrationModel) => ({
        ...data,
        variables: sortByOrder(
          (data?.variables ?? []).reduce(
            (acc: FormVariable[], variable: VariableModel) =>
              acc.concat({
                ...variable,
                isRequired: variable.is_required === 1,
                value:
                  variable.name === 'webhook_transform_script'
                    ? TRANSFORM_SCRIPT_DEFAULT.trim()
                    : (variable.default_value ?? ''),
                description: variable.description ?? '',
                options: (variable.options ?? '')
                  .split(' ')
                  .filter(Boolean)
                  .map((value: string) => ({
                    value,
                    label: upperFirst(value),
                  })),
              }),
            []
          )
        ),
      }),
      []
    ),
  })

  useEffect(() => {
    if (!integration) {
      return
    }

    reset({
      name: integration.name,
      variables: integration.variables,
    })
  }, [reset, integration])

  const hasEnableTransform = useMemo<boolean>(
    () => fields.some(({ name }) => name === 'webhook_enable_transform'),
    [fields]
  )

  const isTransformEnabled = useCallback((): boolean => {
    const match = (variables ?? []).find(
      ({ name }) => name === 'webhook_enable_transform'
    )
    return match?.value === 'true'
  }, [variables])

  const canShowCodeControl = useCallback((): boolean => {
    return isTransformEnabled() || !hasEnableTransform
  }, [hasEnableTransform, isTransformEnabled])

  const inputType = (datatype: VariableType): string => {
    switch (datatype) {
      case 'string':
        return 'text'
      default:
        return datatype
    }
  }

  const controlType = ({
    options,
    datatype,
  }: FieldArrayWithId<
    {
      name: string
      organization_id: string
      application_id: string
      account_id: string
      variables?: Array<{
        name: string
        label: string
        value: string
        datatype: string
        isRequired: boolean
        options: Dict[]
        description: string
      }>
    },
    'variables',
    'id'
  >): string => {
    // Legacy fallback when it has options but not a select.
    if (options.length > 1 && !['select', 'switch'].includes(datatype)) {
      return 'select'
    }

    switch (datatype) {
      case 'text':
        return 'textarea'
      case 'code':
        return 'code'
      case 'switch':
        return 'switch'
      case 'select':
        return 'select'
      default:
        return 'input'
    }
  }

  const onChangeOrganization = async (value: string): Promise<void> => {
    if (isEmpty(value)) {
      setValue('organization_id', '')
      setValue('application_id', '')
      return
    }

    const applications = await queryClient.fetchQuery({
      queryKey: ['GetApplications', value],
      queryFn: ({ signal }) =>
        getApplications({
          organizationId: value,
          signal,
        }),
    })

    const apps = (applications?.rows ?? []).reduce(
      (acc: BaseOption<string>[], { id, name }) =>
        acc.concat({
          label: name,
          value: id,
        }),
      []
    )

    const firstId = apps?.[0]?.value

    if (!firstId) {
      setValue('application_id', '')
      toast({
        status: 'warning',
        msg: 'Unable to fetch applications.',
      })
      return
    }

    setValue('organization_id', value)
    setValue('application_id', firstId)
  }

  const onGoBack = (): void => {
    navigate('/manage/setup-integration', {
      viewTransition: true,
    })
  }

  const onSubmit = async (values: FormInputProps): Promise<boolean> => {
    if (!integration) {
      return false
    }

    const input = {
      name: values.name.trim(),
      integration_id: integration.id,
      account_id: values.account_id,
      active: integration.active,
      settings: (values?.variables ?? []).reduce(
        (acc: FuseSettingInput[], { name, value }) => {
          if (isNil(value) || isEmpty(value)) {
            return acc
          }
          if (value === TRANSFORM_SCRIPT_DEFAULT.trim()) {
            return acc
          }
          acc.push({ name, value })
          return acc
        },
        []
      ),
    } satisfies FuseInput

    try {
      await createFuseMutation(input)
      toast({
        status: 'success',
        msg: 'Integration has been created.',
      })
      return true
    } catch (error: unknown) {
      Sentry.captureException(error)
      toast({
        status: 'error',
        msg: 'Unable to create integration.',
      })
      return false
    }
  }

  return (
    <>
      <Helmet>
        <title>{metaTitle([title])}</title>
      </Helmet>
      <Suspense fallback={null}>
        <Flex align="center" justify="space-between" p={4}>
          <HStack align="center" justify="space-between">
            <BackButton onClick={onGoBack} />
            <Title title={title} />
            {integration?.documentation && (
              <Link
                alignSelf="start"
                href={integration?.documentation}
                target="_blank"
                rel="noopener noreferrer"
              >
                <IconButton
                  isRound
                  size="xs"
                  aria-label="Documentation"
                  colorScheme="secondary"
                  variant="ghost"
                  icon={<InfoIcon size={16} />}
                  sx={{
                    userSelect: 'none',
                    bg: 'blackAlpha.50',
                    _dark: {
                      bg: 'whiteAlpha.50',
                    },
                  }}
                />
              </Link>
            )}
          </HStack>
        </Flex>
        <Box mx={4} borderTop="2px" borderColor="inherit">
          <FormProvider {...methods}>
            <Box as="form" noValidate onSubmit={handleSubmit(onSubmit)}>
              <Container maxW="container.xl" m={0} pt={4}>
                <Grid
                  gap={4}
                  templateColumns={{ base: '1fr', lg: 'repeat(2, 1fr)' }}
                >
                  <InputField
                    name="name"
                    label="Name"
                    control={control}
                    isRequired
                    autoFocus
                    sx={{
                      gridColumn: '1 / -1',
                    }}
                  />
                  <InputField
                    name="account_id"
                    label="Account ID"
                    control={control}
                    isRequired
                  />
                  <SelectOrganizationField
                    name="organization_id"
                    label="Organization"
                    control={control}
                    isClearable
                    onChange={(option: any) =>
                      onChangeOrganization(option?.value ?? '')
                    }
                  />
                </Grid>
                <Suspense fallback={null}>
                  {fields.length > 0 && (
                    <>
                      <Divider my={2} />
                      <Box py={4} fontWeight="semibold" fontSize="lg">
                        Settings
                      </Box>
                      {fields.map((variable, index) => {
                        const type = controlType(variable)
                        return (
                          <VStack
                            key={variable.id}
                            sx={{
                              mb: 5,
                              alignItems: 'stretch',
                              borderRadius: 'base',
                              borderWidth: '1px',
                              borderColor: 'blackAlpha.200',
                              bg: 'gray.50',
                              _dark: {
                                bg: 'blackAlpha.100',
                              },
                            }}
                          >
                            <Grid gap={4} p={4} templateColumns="1fr">
                              {type === 'input' && (
                                <InputField
                                  name={`variables.${index}.value`}
                                  label={variable.label}
                                  type={inputType(
                                    variable.datatype as VariableType
                                  )}
                                  placeholder={variable.description}
                                  control={control}
                                  isRequired={variable.isRequired}
                                  longInfo={variable?.description}
                                />
                              )}
                              {type === 'select' && (
                                <SelectField
                                  name={`variables.${index}.value`}
                                  label={variable.label}
                                  placeholder={variable.description}
                                  options={variable.options}
                                  control={control}
                                  isRequired={variable.isRequired}
                                  longInfo={variable?.description}
                                />
                              )}
                              {type === 'textarea' && (
                                <TextareaField
                                  name={`variables.${index}.value`}
                                  label={variable.label}
                                  placeholder={variable.description}
                                  control={control}
                                  isRequired={variable.isRequired}
                                  longInfo={variable?.description}
                                />
                              )}
                              {type === 'code' &&
                                (canShowCodeControl() ? (
                                  <CodeEditorField
                                    name={`variables.${index}.value`}
                                    label={variable.label}
                                    control={control}
                                    isRequired={variable.isRequired}
                                    longInfo={variable?.description}
                                  />
                                ) : (
                                  <Box>{variable.label} is disabled.</Box>
                                ))}
                              {type === 'switch' && (
                                <SwitchField
                                  name={`variables.${index}.value`}
                                  label={variable.label}
                                  control={control}
                                  isRequired={variable.isRequired}
                                  onChange={(event) => {
                                    setValue(
                                      `variables.${index}.value`,
                                      String(event.target.checked)
                                    )
                                  }}
                                />
                              )}
                            </Grid>
                          </VStack>
                        )
                      })}
                    </>
                  )}
                </Suspense>
                <Flex justifyContent="space-between">
                  <Button
                    type="button"
                    aria-label="Cancel"
                    variant="ghost"
                    colorScheme="gray"
                    onClick={onGoBack}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    aria-label="Save"
                    colorScheme="green"
                    minW={120}
                    isLoading={isSubmitting}
                    isDisabled={!isValid || isSubmitting}
                    loadingText="Saving"
                  >
                    Save
                  </Button>
                </Flex>
              </Container>
            </Box>
          </FormProvider>
        </Box>
      </Suspense>
    </>
  )
})

export async function loader() {
  const scopes = ['manage:all']

  const { checkLoaderPermissions } = await import('@/utils/loader-auth')
  await checkLoaderPermissions(scopes)

  return {
    title: 'Setup Integration',
    scopes,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <AddIntegration />
}
