import { useState, useMemo, useEffect, memo } from 'react'
import { Helm<PERSON> } from 'react-helmet-async'
import { useQuery } from '@tanstack/react-query'
import { Flex, SimpleGrid } from '@chakra-ui/react'
import {
  HeartHandshakeIcon,
  BookOpenTextIcon,
  GlobeLockIcon,
} from 'lucide-react'
import { Title } from '@/components/title'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { Card, type CardProps } from '@/components/card'
import { useAuth } from '@/contexts/use-auth'
import { useRouter } from '@/hooks/use-router'
import { useShop } from '@/hooks/use-shop'
import { useOrganization } from '@/hooks/use-organization'
import { metaTitle } from '@/utils/meta-title'
import { getSettings } from '@/api/settings'
import type { SettingModel } from '@/types/models/setting'
import type { PaginatedQueryResponse } from '@/types/api'

const Help = memo(() => {
  const { title } = useRouter()
  const { getUrl } = useShop()
  const [url, setUrl] = useState<string>('')
  const { isInternalAdmin } = useAuth()
  const { applicationId } = useOrganization()

  const { data: qs } = useQuery<
    PaginatedQueryResponse<SettingModel> | null,
    Error,
    string | undefined
  >({
    queryKey: ['GetSettings', applicationId],
    queryFn: ({ signal }) =>
      applicationId
        ? getSettings({
            applicationId,
            signal,
          })
        : null,
    enabled: !!applicationId,
    select: (data) => {
      if (!data) {
        return
      }
      const match = data.rows.find(({ name }) => name === 'company')
      return match
        ? new URLSearchParams({
            customfield_10981: match.value,
          }).toString()
        : undefined
    },
  })

  useEffect(() => {
    getUrl().then((u) => u && setUrl(u))
  }, [getUrl])

  const lists = useMemo<CardProps[]>(
    () => [
      {
        title: 'Help',
        icon: HeartHandshakeIcon,
        btn: {
          title: 'Submit Ticket',
          onClick: () => {
            window.open('https://help.mydevices.com', '_blank')
            return null
          },
        },
        data: [
          {
            name: 'Provide Feedback',
            href: 'https://mydevices.atlassian.net/servicedesk/customer/portal/9/group/23/create/141',
          },
          {
            name: 'Need Sensor Advice',
            href: `https://mydevices.atlassian.net/servicedesk/customer/portal/19/group/38/create/250?${qs}`,
          },
          {
            name: 'System Status',
            href: 'https://status.mydevices.com',
          },
          {
            name: 'Trust Center',
            href: 'https://trust.mydevices.com',
          },
          {
            name: 'Feature Roadmap',
            href: 'https://app-rm.roadmunk.com/publish/b18660ff26e45deeb59a3b7f5e02e851fb163de0',
          },
          {
            name: 'Developer Roadmap',
            href: 'https://app-rm.roadmunk.com/publish/3cb287e35db785f904cf76a8917215697f5d1673',
            canView: isInternalAdmin,
          },
          {
            name: 'DevOps Roadmap',
            href: 'https://app-rm.roadmunk.com/publish/0a25458700b7fcb815671ea765d89ff6ccaf8408',
            canView: isInternalAdmin,
          },
          {
            name: 'API Docs',
            href: 'https://docs.mydevices.com',
          },
          {
            name: 'Console Docs',
            href: 'https://iot-help.scrollhelp.site/partners',
          },
          {
            name: 'App Docs',
            href: 'https://iot-help.scrollhelp.site/iotkb',
          },
          {
            name: 'Quotes and Orders',
            href: url,
          },
        ],
      },
      {
        title: 'Sales Enablement Material',
        icon: BookOpenTextIcon,
        data: [
          {
            name: 'DocSend Site',
            href: 'https://docsend.com/view/s/pebm3pgjsenb7zf2',
            subText: 'Marketing, Platform, Pricing, Referrals',
          },
        ],
      },
      {
        title: 'Legal',
        icon: GlobeLockIcon,
        data: [
          {
            name: 'Terms & Conditions',
            href: 'https://mydevices.com/terms-of-service',
          },
          {
            name: 'Privacy Policy',
            href: 'https://mydevices.com/privacy-policy',
          },
        ],
      },
    ],
    [url, isInternalAdmin, qs]
  )

  return (
    <>
      <Helmet>
        <title>{metaTitle([title])}</title>
      </Helmet>
      <Flex align="center" justify="space-between" p={4}>
        <Title title={title} />
      </Flex>
      <SimpleGrid minChildWidth={500} spacing={4} px={4} mb={20}>
        {Object.values(lists).map((list, key) => (
          <Card key={key} {...list} />
        ))}
      </SimpleGrid>
    </>
  )
})

export async function loader() {
  const scopes = [] as const

  return {
    title: 'Help',
    scopes,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <Help />
}
