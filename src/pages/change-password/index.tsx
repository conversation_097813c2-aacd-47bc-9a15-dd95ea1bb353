import * as Sentry from '@sentry/react'
import { memo, useEffect, useMemo } from 'react'
import { Helm<PERSON> } from 'react-helmet-async'
import { useNavigate, Navigate } from 'react-router'
import { useMountEffect } from '@react-hookz/web'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { Button, Flex, Heading, Box } from '@chakra-ui/react'
import { InputField } from '@/components/input-field'
import { PasswordField } from '@/components/password-field'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { ErrorMessage } from '@/components/error-message'
import { useAuth } from '@/contexts/use-auth'
import { useRouter } from '@/hooks/use-router'
import { useAuthRoute } from '@/hooks/use-auth-route'
import { useToast } from '@/hooks/use-toast'
import { getCurrentUser, updateCurrentUser } from '@/api/users'
import { metaTitle } from '@/utils/meta-title'
import { checkPasswordStrength } from '@/utils/check-password-strength'
import type { CurrentUserModel } from '@/types/models/user'
import type { UserInput } from '@/types/api'

const schema = z
  .object({
    org: z.string().min(2, 'Organization is required.'),
    email: z.string().email(),
    password: z.string().min(8, 'Password must be at least 8 characters.'),
    passwordConfirmation: z.string(),
  })
  .superRefine(({ password }, ctx) => {
    const result = checkPasswordStrength(password)
    if (result.id < 2) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['password'],
        message:
          'Password must contain upper/lowercase letters, numbers, & special chars.',
      })
    }
  })
  .superRefine((values, ctx) => {
    if (values.password !== values.passwordConfirmation) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Passwords do not match.',
        path: ['passwordConfirmation'],
      })
    }

    return z.NEVER
  })

type FormInputProps = z.infer<typeof schema>

const ChangePassword = memo(() => {
  const toast = useToast()
  const navigate = useNavigate()
  const { title } = useRouter()
  const { user, isInternalAdmin } = useAuth()
  const queryClient = useQueryClient()
  const { verified } = useAuthRoute()

  const {
    control,
    setValue,
    handleSubmit,
    formState: { isValid, isDirty, isSubmitting, errors },
  } = useForm({
    mode: 'onTouched',
    resolver: zodResolver(schema),
    defaultValues: {
      org: user?.organization ?? '',
      email: '',
      password: '',
      passwordConfirmation: '',
    },
  })

  useMountEffect(() => {
    if (isInternalAdmin) {
      navigate('/signin', { replace: true, viewTransition: true })
      return
    }
  })

  const { mutateAsync: updateCurrentUserMutation } = useMutation<
    CurrentUserModel,
    Error,
    Partial<UserInput>
  >({
    mutationFn: (input) => updateCurrentUser(input),
    onSuccess: () =>
      queryClient.invalidateQueries({ queryKey: ['GetCurrentUser'] }),
  })

  const { data: currentUser } = useQuery<CurrentUserModel | null, Error>({
    queryKey: ['GetCurrentUser', user?.token],
    queryFn: ({ signal }) =>
      getCurrentUser({
        signal,
      }),
  })

  useEffect(() => {
    if (!currentUser) {
      return
    }
    setValue('email', currentUser?.email ?? '')
  }, [setValue, currentUser])

  const onSubmit = async (values: FormInputProps): Promise<boolean> => {
    if (!user?.organization) {
      return false
    }

    const input = {
      password: values.password,
    } satisfies Partial<UserInput>

    try {
      await updateCurrentUserMutation(input)
      toast({
        status: 'success',
        msg: 'Password has been reset.',
      })

      navigate(`/signin?realm=${user?.organization}`, {
        replace: true,
        viewTransition: true,
      })
      return true
    } catch (error: unknown) {
      Sentry.captureException(error)
      if (error instanceof Error) {
        toast({
          status: 'error',
          msg: error?.message,
        })
      }
      return false
    }
  }

  const error = useMemo(() => {
    if (isValid || !isDirty || Object.keys(errors).length === 0) {
      return
    }
    return Object.values(errors).find((error) => error?.message)?.message
  }, [errors, isValid, isDirty])

  if (!user?.access_token) return <Navigate to="/signin" />
  if (!verified) return null

  return (
    <>
      <Helmet>
        <title>{metaTitle([title])}</title>
      </Helmet>
      <Flex
        sx={{
          flex: 1,
          w: '100%',
          flexDirection: 'column',
          p: { base: '1rem 2rem', md: '2rem' },
        }}
      >
        <Heading
          sx={{
            w: '100%',
            textAlign: 'center',
            fontSize: '1.5rem',
            mb: '1.5rem',
            fontWeight: 'medium',
          }}
        >
          Change Password
        </Heading>

        {error ? <ErrorMessage>{error}</ErrorMessage> : null}

        <Box as="form" noValidate onSubmit={handleSubmit(onSubmit)}>
          <InputField
            name="org"
            size="lg"
            label="Organization"
            placeholder="Organization"
            control={control}
            isInline={true}
            isRequired
            isReadOnly
          />
          <InputField
            name="email"
            label="Email"
            size="lg"
            placeholder="Email"
            type="email"
            autoComplete="email"
            control={control}
            isInline={true}
            isRequired
            isReadOnly
          />
          <PasswordField
            name="password"
            label="Password"
            size="lg"
            placeholder="Password"
            autoComplete="new-password"
            control={control}
            isInline={true}
            passwordHint
            isRequired
            autoFocus
          />
          <PasswordField
            name="passwordConfirmation"
            label="Confirm Password"
            size="lg"
            placeholder="Confirm Password"
            autoComplete="new-password"
            control={control}
            isInline={true}
            isRequired
          />
          <Button
            type="submit"
            size="lg"
            aria-label="Change Password"
            colorScheme="primary"
            isLoading={isSubmitting}
            isDisabled={!isValid || isSubmitting}
            sx={{
              w: '100%',
              fontWeight: 'semibold',
              _disabled: {
                cursor: 'not-allowed',
                userSelect: 'none',
              },
            }}
          >
            Change Password
          </Button>
        </Box>
      </Flex>
    </>
  )
})

export async function loader() {
  const scopes = [] as string[]

  return {
    title: 'Change Password',
    scopes,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <ChangePassword />
}
