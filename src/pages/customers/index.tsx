import * as Sentry from '@sentry/react'
import {
  lazy,
  memo,
  Suspense,
  useCallback,
  useEffect,
  useMemo,
  useState,
  useDeferredValue,
  type ReactElement,
} from 'react'
import { Helmet } from 'react-helmet-async'
import { useSearchParams } from 'react-router'
import { useAtom } from 'jotai'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { Box, Flex, Td, Tr } from '@chakra-ui/react'
import { Title } from '@/components/title'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { Clipboard } from '@/components/data-table/clipboard'
import { ToggleRow } from '@/components/data-table/toggle-row'
import { DataTable } from '@/components/data-table/data-table'
import { useAuth } from '@/contexts/use-auth'
import { useModal } from '@/hooks/use-modal'
import { useAbility } from '@/hooks/use-ability'
import { useRemoteAssistance } from '@/hooks/use-remote-assistance'
import { useOrganization } from '@/hooks/use-organization'
import { useToast } from '@/hooks/use-toast'
import { getUsers, activateUser, deleteUser } from '@/api/users'
import { fullName } from '@/utils/full-name'
import { metaTitle } from '@/utils/meta-title'
import { formatTimestamp } from '@/utils/date/format-timestamp'
import { tableAtom } from '@/utils/stores/table'
import type { UserModel } from '@/types/models/user'
import type { CustomerModel } from '@/types/models/customer'
import type {
  DeleteUserInput,
  ActivateUserInput,
  PaginatedQueryResponse,
} from '@/types/api'
import type { ColumnProps, ActionProps } from '@/types/table'

const PhoneNumber = lazy(() =>
  import('@/pages/customers/phone-number').then(({ PhoneNumber }) => ({
    default: PhoneNumber,
  }))
)

const ConfirmDialog = lazy(() =>
  import('@/components/confirm-dialog').then(({ ConfirmDialog }) => ({
    default: ConfirmDialog,
  }))
)

const DeleteDialog = lazy(() =>
  import('@/components/delete-dialog').then(({ DeleteDialog }) => ({
    default: DeleteDialog,
  }))
)

const LocationList = lazy(() =>
  import('@/features/customers/location-list').then(({ LocationList }) => ({
    default: LocationList,
  }))
)

const UploadDevices = lazy(() =>
  import('@/pages/customers/upload-devices').then(({ UploadDevices }) => ({
    default: UploadDevices,
  }))
)

const UserDetails = lazy(() =>
  import('@/pages/customers/user-details').then(({ UserDetails }) => ({
    default: UserDetails,
  }))
)

const ResetPassword = lazy(() =>
  import('@/pages/customers/reset-password').then(({ ResetPassword }) => ({
    default: ResetPassword,
  }))
)

const Customers = memo(() => {
  const title = 'Customers'
  const tableName = 'customers'
  const modal = useModal()
  const toast = useToast()
  const { organizationId, applicationId } = useOrganization()
  const queryClient = useQueryClient()
  const impersonate = useRemoteAssistance()
  const { isInternalAdmin } = useAuth()
  const { can } = useAbility()
  const [searchParams, setSearchParams] = useState({})
  const deferredSearchParams = useDeferredValue(searchParams)

  const [{ limit, currPage, filters, hiddenColumns, visibleIds }, setTable] =
    useAtom(tableAtom(tableName))

  const [qs] = useSearchParams()
  const externalFilter = qs.get('email')

  useEffect(() => {
    if (!externalFilter) {
      return
    }

    const filter = [
      {
        id: 'email',
        condition: 'like',
        value: externalFilter,
        query: `email like %${externalFilter}%`,
      },
    ]

    setTable((prev) => ({
      ...prev,
      currPage: 0,
      filter: filter.map(({ query }: any) => query).join(','),
      filters: [...filter],
    }))
  }, [externalFilter, setTable])

  const { mutateAsync: deleteUserMutation } = useMutation<
    boolean,
    Error,
    DeleteUserInput
  >({
    mutationFn: (input) => deleteUser(input),
    onSuccess: () => {
      const cache = ['GetRoleUsers', 'GetUsers', 'GetUser']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const { mutateAsync: activateUserMutation } = useMutation<
    UserModel,
    Error,
    ActivateUserInput
  >({
    mutationFn: (input) => activateUser(input),
    onSuccess: () => {
      const cache = ['GetRoleUsers', 'GetUsers', 'GetUser']
      for (const cacheName of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheName] })
      }
    },
  })

  useEffect(() => {
    setSearchParams(
      filters.reduce((acc: Dict, { id, value }: any) => {
        if (['phoneNumber'].includes(id)) {
          acc.attributes = JSON.stringify({ [id]: value })
        } else {
          acc[id] = value
        }
        return acc
      }, {})
    )
  }, [filters])

  const { data: customers } = useQuery<
    PaginatedQueryResponse<UserModel> | null,
    Error,
    {
      count: number
      rows: CustomerModel[]
    }
  >({
    queryKey: [
      'GetUsers',
      organizationId,
      applicationId,
      limit,
      currPage,
      deferredSearchParams,
    ],
    queryFn: ({ signal }) =>
      organizationId && applicationId
        ? getUsers({
            organizationId,
            applicationId,
            limit,
            page: currPage,
            ...deferredSearchParams,
            signal,
          })
        : null,
    enabled: !!organizationId && !!applicationId,
    select: useCallback((data: PaginatedQueryResponse<UserModel> | null) => {
      const count = data?.count || 0
      const rows = (data?.rows || []).reduce((acc: CustomerModel[], row) => {
        acc.push({
          ...row,
          hasChildren: row?.totalLocations > 0,
          phoneNumber: row?.attributes?.phoneNumber?.[0] || '',
          createdAt: formatTimestamp(row?.createdTimestamp),
        })
        return acc
      }, [])
      return { count, rows }
    }, []),
  })

  const records = useMemo<{
    count: number
    rows: CustomerModel[]
  }>(
    () =>
      customers ?? {
        count: 0,
        rows: [],
      },
    [customers]
  )

  const onHideRow = useCallback(
    (id: string): void => {
      setTable((prev) => ({
        ...prev,
        visibleIds:
          prev.visibleIds.indexOf(id) > -1
            ? prev.visibleIds.filter((v: string | number) => v !== id)
            : prev.visibleIds.concat(id),
      }))
    },
    [setTable]
  )

  const onDelete = useCallback(
    (row?: CustomerModel): void => {
      if (!row?.tina_user_id) {
        return
      }
      modal({
        size: 'md',
        component: <DeleteDialog />,
        config: {
          title: 'Delete user?',
          description: `Are you sure you want to delete “${row.email}”?`,
          confirmLabel: 'Delete',
          inputLabel: 'DELETE',
          onCallback: async () => {
            if (!(organizationId && applicationId)) {
              return
            }
            try {
              await deleteUserMutation({
                organizationId,
                applicationId,
                userId: row.tina_user_id,
              })
              toast({
                status: 'success',
                msg: `“${row.email}” has been deleted.`,
              })
            } catch {
              toast({
                status: 'error',
                msg: `Unable to delete “${row.email}”.`,
              })
            }
          },
        },
      })
    },
    [modal, toast, organizationId, applicationId, deleteUserMutation]
  )

  const onUploadDevices = useCallback(
    (row: CustomerModel): void => {
      modal({
        component: <UploadDevices />,
        config: {
          data: row,
          onCallback: () => ({}),
        },
      })
    },
    [modal]
  )

  const onImpersonate = useCallback(
    (user: CustomerModel): void => {
      if (!(organizationId && applicationId && user?.tina_user_id)) {
        return
      }
      impersonate({
        organizationId,
        applicationId,
        user,
      })
    },
    [organizationId, applicationId, impersonate]
  )

  const onUserDetails = useCallback(
    (row: CustomerModel): void => {
      modal({
        component: <UserDetails />,
        config: {
          data: row,
          onCallback: () => ({}),
        },
      })
    },
    [modal]
  )

  const onResetPassword = useCallback(
    (row: CustomerModel): void => {
      modal({
        size: 'md',
        component: <ResetPassword />,
        config: {
          data: row,
          onCallback: () => ({}),
        },
      })
    },
    [modal]
  )

  const onPhoneNumber = useCallback(
    (row: CustomerModel): void => {
      modal({
        size: '2xl',
        component: <PhoneNumber />,
        config: {
          data: {
            phoneNumber: row.phoneNumber,
          },
          onCallback: () => ({}),
        },
      })
    },
    [modal]
  )

  const onEnableAccount = useCallback(
    ({ tina_user_id, firstName, lastName }: CustomerModel): void => {
      const name = fullName(firstName, lastName)
      modal({
        size: 'md',
        component: <ConfirmDialog />,
        config: {
          title: 'Enable account',
          description: `Are you sure you want to enable account for “${name}”?`,
          confirmLabel: 'Enable',
          onCallback: async () => {
            if (!(organizationId && applicationId)) {
              return
            }

            const input = {
              organizationId,
              applicationId,
              userId: tina_user_id,
              enabled: true,
            } satisfies ActivateUserInput

            try {
              await activateUserMutation(input)
              toast({
                status: 'success',
                msg: `Account for “${name}” has been enabled.`,
              })
            } catch (error: unknown) {
              Sentry.captureException(error)
              toast({
                status: 'error',
                msg: `Unable to enable account for “${name}”.`,
              })
            }
          },
        },
      })
    },
    [modal, toast, activateUserMutation, organizationId, applicationId]
  )

  const onDisableAccount = useCallback(
    ({ tina_user_id, firstName, lastName }: CustomerModel): void => {
      const name = fullName(firstName, lastName)
      modal({
        size: 'md',
        component: <ConfirmDialog />,
        config: {
          title: 'Disable account',
          description: `Are you sure you want to disable account for “${name}”?`,
          confirmLabel: 'Disable',
          onCallback: async () => {
            if (!(organizationId && applicationId)) {
              return
            }

            const input = {
              organizationId,
              applicationId,
              userId: tina_user_id,
              enabled: false,
            } satisfies ActivateUserInput

            try {
              await activateUserMutation(input)
              toast({
                status: 'success',
                msg: `Account for “${name}” has been disabled.`,
              })
            } catch (error: unknown) {
              Sentry.captureException(error)
              toast({
                status: 'error',
                msg: `Unable to disable account for “${name}”.`,
              })
            }
          },
        },
      })
    },
    [modal, toast, activateUserMutation, organizationId, applicationId]
  )

  const isPending = useCallback(
    (row?: CustomerModel): boolean =>
      row?.attributes?.inviteStatus?.[0] === 'PENDING' ||
      row?.attributes?.invite_status?.[0] === 'PENDING',
    []
  )

  const canUploadDevices = useCallback(
    (row?: CustomerModel): boolean => {
      const count = row?.totalLocations ?? 0
      return count > 0 && !!row?.enabled && can(['edit', 'organizations'])
    },
    [can]
  )

  const canImpersonate = useCallback(
    (row?: CustomerModel): boolean =>
      !!row?.enabled && !!row?.tina_user_id && can(['edit', 'applications']),
    [can]
  )

  const canViewUserDetails = useCallback(
    (row?: CustomerModel): boolean => !isPending(row) && isInternalAdmin,
    [isInternalAdmin, isPending]
  )

  const canResetUserPassword = can(['edit', 'applications'])

  const canActivateAccount = can(['edit', 'applications'])

  const canViewDevices = useCallback(
    (row?: CustomerModel): boolean =>
      !!row?.hasChildren && can(['view', 'applications']),
    [can]
  )

  const columns = useMemo<ColumnProps<CustomerModel>[]>(
    () => [
      {
        name: 'Email',
        id: 'email',
        w: '350px',
        filter: {
          type: 'input',
          condition: 'like',
        },
        cell: (row) =>
          row.hasChildren ? (
            <ToggleRow
              id={row.id}
              tableName={tableName}
              hasChildren={row.hasChildren}
              onClick={() => onHideRow(row.id)}
            >
              <Box
                sx={{
                  color: 'secondary.500',
                  _dark: {
                    color: 'secondary.200',
                  },
                }}
              >
                <Clipboard>{row.email}</Clipboard>
              </Box>
            </ToggleRow>
          ) : (
            <Box pl={8}>
              <Clipboard>{row.email}</Clipboard>
            </Box>
          ),
      },
      {
        name: 'First Name',
        id: 'firstName',
        canHide: true,
        filter: {
          type: 'input',
          condition: 'like',
        },
        cell: ({ firstName }) =>
          firstName && <Clipboard>{firstName}</Clipboard>,
      },
      {
        name: 'Last Name',
        id: 'lastName',
        canHide: true,
        filter: {
          type: 'input',
          condition: 'like',
        },
        cell: ({ lastName }) => lastName && <Clipboard>{lastName}</Clipboard>,
      },
      {
        name: 'ID',
        id: 'id',
        canHide: false,
        filter: {
          type: 'input',
          condition: 'eq',
        },
        cell: ({ id }) => <Clipboard>{id}</Clipboard>,
      },
      {
        name: 'Phone',
        id: 'phoneNumber',
        canHide: true,
        cell: ({ phoneNumber }) =>
          phoneNumber && <Clipboard>{phoneNumber}</Clipboard>,
      },
      {
        name: 'Created',
        id: 'createdTimestamp',
        canHide: false,
        cell: ({ createdTimestamp }) => (
          <Clipboard>{formatTimestamp(createdTimestamp)}</Clipboard>
        ),
      },
    ],
    [onHideRow]
  )

  const actions = useMemo<ActionProps<CustomerModel>[]>(
    () => [
      {
        label: 'Upload Devices',
        onClick: onUploadDevices,
        canView: (row) => canUploadDevices(row),
      },
      {
        label: 'Remote Assistance',
        onClick: onImpersonate,
        canView: (row) => canImpersonate(row),
      },
      {
        label: 'User Details',
        onClick: onUserDetails,
        canView: (row) => canViewUserDetails(row),
      },
      {
        label: 'Reset Password',
        onClick: onResetPassword,
        canView: () => canResetUserPassword,
      },
      {
        label: 'Enable Account',
        onClick: onEnableAccount,
        canView: (row) => !row?.enabled && canActivateAccount,
      },
      {
        label: 'Disable Account',
        onClick: onDisableAccount,
        canView: (row) => !!row?.enabled && canActivateAccount,
      },
      {
        label: 'Manage Phone Number',
        onClick: onPhoneNumber,
        canView: () => isInternalAdmin,
      },
      {
        label: 'Delete User',
        onClick: (row) => onDelete(row),
        canView: () => isInternalAdmin,
      },
    ],
    [
      isInternalAdmin,
      onDelete,
      onPhoneNumber,
      onUploadDevices,
      canUploadDevices,
      onUserDetails,
      canViewUserDetails,
      onImpersonate,
      canImpersonate,
      onResetPassword,
      canResetUserPassword,
      onEnableAccount,
      onDisableAccount,
      canActivateAccount,
    ]
  )

  const expandable = ({ row }: { row: CustomerModel }): ReactElement | null =>
    canViewDevices(row) ? (
      <Tr display={visibleIds.includes(row.id) ? 'table-row' : 'none'}>
        <Td pr={4} pl={8} colSpan={7 - hiddenColumns.length}>
          <LocationList row={row} />
        </Td>
      </Tr>
    ) : null

  return (
    <>
      <Helmet>
        <title>{metaTitle([title])}</title>
      </Helmet>
      <Flex align="center" justify="space-between" p={4}>
        <Title title={title} count={records.count} suffix="Record" />
      </Flex>
      <Flex px={4}>
        <Suspense fallback={null}>
          <DataTable
            tableName={tableName}
            data={records}
            columns={columns}
            actions={actions}
            isExpandable={true}
            hasHover={false}
            expandable={expandable}
            disabledRow={({ enabled }) => !enabled}
          />
        </Suspense>
      </Flex>
    </>
  )
})

export async function loader() {
  const scopes = ['view:applications']

  const { checkLoaderPermissions } = await import('@/utils/loader-auth')
  await checkLoaderPermissions(scopes)

  return {
    title: 'Customers',
    scopes,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <Customers />
}
