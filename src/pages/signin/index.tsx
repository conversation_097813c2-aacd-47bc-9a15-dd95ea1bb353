import * as Sentry from '@sentry/react'
import {
  memo,
  useEffect,
  useState,
  useMemo,
  useCallback,
  type BaseSyntheticEvent,
} from 'react'
import { Helm<PERSON> } from 'react-helmet-async'
import { useSearchParams, useNavigate } from 'react-router'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { useAtomValue } from 'jotai'
import { zodResolver } from '@hookform/resolvers/zod'
import { Box, Button } from '@chakra-ui/react'
import { InputField } from '@/components/input-field'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { PublicWrapper, PublicHeading } from '@/components/public-wrapper'
import { ErrorMessage } from '@/components/error-message'
import { useAuth } from '@/contexts/use-auth'
import { metaTitle } from '@/utils/meta-title'
import { idpAuthorize } from '@/api/keycloak'
import { lastLocationAtom } from '@/utils/stores/last-location'

const schema = z.object({
  org: z.string().min(2, 'Organization is required.'),
})

type FormInputProps = z.infer<typeof schema>

const Signin = memo(() => {
  const [qs] = useSearchParams()
  const navigate = useNavigate()
  const { user, login, logout, verify } = useAuth()
  const [process, setProcess] = useState<boolean>(true)
  const lastLocation = useAtomValue(lastLocationAtom)

  const realm = useMemo<string>(() => {
    const qr = qs.get('realm')
    const sr = user?.realm
    if (qr) {
      return qr.toLocaleLowerCase()
    }
    return sr === 'master' ? '' : (sr ?? '')
  }, [qs, user?.realm])

  const state = useMemo(() => qs.get('state'), [qs])
  const code = useMemo(() => qs.get('code'), [qs])

  const {
    control,
    handleSubmit,
    formState: { isValid, isSubmitting, isDirty, errors },
  } = useForm({
    mode: 'onTouched',
    resolver: zodResolver(schema),
    defaultValues: {
      org: realm ?? '',
    },
  })

  const onSubmit = useCallback(
    (values: FormInputProps, event?: BaseSyntheticEvent): boolean => {
      event?.preventDefault()

      setProcess(true)

      try {
        const idpUrl = idpAuthorize(
          values.org.toLocaleLowerCase().trim(),
          false
        )
        window.location.assign(decodeURIComponent(idpUrl))
        return true
      } catch (error: unknown) {
        Sentry.captureException(error, {
          fingerprint: ['signin'],
          level: 'fatal',
        })
        setProcess(false)
        return false
      }
    },
    []
  )

  useEffect(() => {
    if (realm && state && code) {
      login({ isInternal: false, realm, state, code }).catch((err) => {
        Sentry.captureException(err, {
          fingerprint: ['signin'],
          level: 'fatal',
        })
        setProcess(true)
        logout()
      })
    } else if (user?.access_token) {
      // Maybe we are already logged in ...
      setProcess(true)
      verify().then((ok) => {
        if (ok) {
          if (user.role === 'report') {
            navigate('/manage/reports', {
              replace: true,
              viewTransition: true,
            })
            return
          }
          navigate(lastLocation?.pathname ?? '/manage/customers', {
            replace: true,
            viewTransition: true,
          })
        } else {
          logout() // clear
        }
      })
    } else {
      setProcess(false)
    }
  }, [realm, state, code, login, logout, navigate, verify, user, lastLocation])

  const error = useMemo(() => {
    if (isValid || !isDirty || Object.keys(errors).length === 0) {
      return
    }
    return Object.values(errors).find((error) => error?.message)?.message
  }, [errors, isValid, isDirty])

  useEffect(() => {
    const timeout = setTimeout(() => {
      setProcess(false)
    }, 5000)
    return () => clearTimeout(timeout)
  }, [])

  return (
    <>
      <Helmet>
        <title>{metaTitle(['Sign In'])}</title>
      </Helmet>
      {!process ? (
        <PublicWrapper>
          <PublicHeading>Sign In</PublicHeading>
          {error ? <ErrorMessage>{error}</ErrorMessage> : null}
          <Box as="form" noValidate onSubmit={handleSubmit(onSubmit)}>
            <InputField
              name="org"
              size="lg"
              label="Organization"
              placeholder="Organization"
              control={control}
              isInline={true}
              isRequired
            />
            <Button
              size="lg"
              type="submit"
              aria-label="Next"
              colorScheme="primary"
              isLoading={isSubmitting}
              isDisabled={!isValid || isSubmitting}
              sx={{
                w: '100%',
                fontWeight: 'semibold',
                _disabled: {
                  cursor: 'not-allowed',
                  userSelect: 'none',
                },
              }}
            >
              Next
            </Button>
          </Box>
        </PublicWrapper>
      ) : null}
    </>
  )
})

export async function loader() {
  const scopes = [] as string[]

  return {
    title: 'Signin',
    scopes,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <Signin />
}
