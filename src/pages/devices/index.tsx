import * as Sentry from '@sentry/react'
import {
  memo,
  lazy,
  Suspense,
  useCallback,
  useMemo,
  useState,
  useDeferredValue,
} from 'react'
import { useCustomCompareEffect } from '@react-hookz/web'
import { Helmet } from 'react-helmet-async'
import { Badge, Box, Flex } from '@chakra-ui/react'
import { useAtom } from 'jotai'
import { equals } from 'ramda'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { Title } from '@/components/title'
import { AddButton } from '@/components/add-button'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { Clipboard } from '@/components/data-table/clipboard'
import { DataTable } from '@/components/data-table/data-table'
import { metaTitle } from '@/utils/meta-title'
import { formatDate } from '@/utils/date/format-date'
import { tableAtom } from '@/utils/stores/table'
import { useRouter } from '@/hooks/use-router'
import { useModal } from '@/hooks/use-modal'
import { useOrganization } from '@/hooks/use-organization'
import { useColorMap } from '@/hooks/use-color-map'
import { useToast } from '@/hooks/use-toast'
import { deactivateThing } from '@/api/things/things'
import { getDevices } from '@/api/devices'
import { useAuth } from '@/contexts/use-auth'
import type { DeviceModel } from '@/types/models/device'
import type { GetResponse, DeactivateThingInput } from '@/types/api'
import type { ColumnProps, ActionProps } from '@/types/table'

const ConfirmDialog = lazy(() =>
  import('@/components/confirm-dialog').then(({ ConfirmDialog }) => ({
    default: ConfirmDialog,
  }))
)

const Edit = lazy(() =>
  import('@/pages/devices/edit').then(({ Edit }) => ({
    default: Edit,
  }))
)

const RuleNotification = lazy(() =>
  import('@/pages/devices/rule-notification').then(({ RuleNotification }) => ({
    default: RuleNotification,
  }))
)

const statusOptions = [
  { label: 'Pending', value: 'PENDING' },
  { label: 'Activated', value: 'ACTIVATED' },
  { label: 'Deactivated', value: 'DEACTIVATED' },
]

const Devices = memo(() => {
  const tableName = 'devices'
  const modal = useModal()
  const toast = useToast()
  const { isInternalAdmin } = useAuth()
  const { title } = useRouter()
  const { organizationId, applicationId } = useOrganization()
  const queryClient = useQueryClient()
  const { mapColor } = useColorMap()
  const [{ limit, currPage, filter }, setTable] = useAtom(tableAtom(tableName))
  const deferredFilter = useDeferredValue(filter)
  const [initialFilter, setInitialFilter] = useState<boolean>(false)

  useCustomCompareEffect(
    () => {
      if (!applicationId) {
        return
      }

      setTable((prev) => ({
        ...prev,
        filter: `application_id eq ${applicationId}, status eq ACTIVATED`,
        filters: [
          {
            id: 'application_id',
            value: `${applicationId}`,
            condition: 'eq',
            query: `application_id eq ${applicationId}`,
          },
          {
            id: 'status',
            value: 'ACTIVATED',
            condition: 'eq',
            query: 'status eq ACTIVATED',
          },
        ],
        hiddenColumns: ['user_id', 'hardware_id', 'application_id'],
      }))
      setInitialFilter(true)
    },
    [applicationId],
    (a, b) => equals(a, b)
  )

  const { mutateAsync: deactivateThingMutation } = useMutation<
    boolean,
    Error,
    DeactivateThingInput
  >({
    mutationFn: (input) => deactivateThing(input),
    onSuccess: () => {
      const cache = ['GetDevices', 'GetRegistries', 'GetThings']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const { data } = useQuery<Omit<GetResponse<DeviceModel>, 'page'>, Error>({
    queryKey: ['GetDevices', limit, currPage, deferredFilter],
    queryFn: ({ signal }) =>
      getDevices({
        limit,
        offset: currPage * limit,
        filter: deferredFilter,
        signal,
      }),
    enabled: !!applicationId && initialFilter,
  })

  const records = useMemo<Omit<GetResponse<DeviceModel>, 'page'>>(
    () => data ?? { count: 0, rows: [] },
    [data]
  )

  const onAdd = useCallback((): void => {
    modal({
      component: <Edit />,
      config: {
        title: 'Add Device',
        data: {},
        onCallback: () => ({}),
      },
    })
  }, [modal])

  const onEdit = useCallback(
    (row: DeviceModel): void => {
      modal({
        component: <Edit />,
        config: {
          title: 'Edit Device',
          data: row,
          onCallback: () => ({}),
        },
      })
    },
    [modal]
  )

  const onRuleNotification = useCallback(
    (row: DeviceModel): void => {
      modal({
        size: '3xl',
        component: <RuleNotification />,
        config: {
          data: row,
          onCallback: () => ({}),
        },
      })
    },
    [modal]
  )

  const onDeactivate = useCallback(
    ({ name, hardware_id }: DeviceModel): void => {
      if (!(organizationId && applicationId)) {
        return
      }

      modal({
        size: 'md',
        component: <ConfirmDialog />,
        config: {
          title: 'Device deactivation',
          description: `Are you sure you want to deactivate “${name}”?`,
          confirmLabel: 'Deactivate',
          onCallback: async () => {
            const input = {
              organizationId,
              applicationId,
              hardwareId: hardware_id,
            } satisfies DeactivateThingInput

            try {
              await deactivateThingMutation(input)
              toast({
                status: 'success',
                msg: `“${name}” has been deactivated.`,
              })
            } catch (error: unknown) {
              Sentry.captureException(error)
              toast({
                status: 'error',
                msg: `Unable to deactivate “${name}”.`,
              })
            }
          },
        },
      })
    },
    [modal, toast, deactivateThingMutation, organizationId, applicationId]
  )

  const columns = useMemo<ColumnProps<DeviceModel>[]>(
    () => [
      {
        name: 'ID',
        id: 'id',
        filter: {
          type: 'input',
          condition: 'eq',
        },
        cell: ({ id }) => <Clipboard>{id}</Clipboard>,
      },
      {
        name: 'User ID',
        id: 'user_id',
        canHide: true,
        filter: {
          type: 'input',
          condition: 'eq',
        },
        cell: ({ user_id }) => <Clipboard>{user_id}</Clipboard>,
      },
      {
        name: 'Hardware ID',
        id: 'hardware_id',
        canHide: true,
        filter: {
          type: 'input',
          condition: 'like',
        },
        cell: ({ hardware_id }) =>
          hardware_id && <Clipboard>{hardware_id}</Clipboard>,
      },
      {
        name: 'Parent ID',
        id: 'parent_id',
        canHide: true,
        filter: {
          type: 'input',
          condition: 'eq',
        },
        cell: ({ parent_id }) =>
          parent_id && <Clipboard>{parent_id}</Clipboard>,
      },
      {
        name: 'Status',
        id: 'status',
        w: '10%',
        filter: {
          type: 'select',
          condition: 'eq',
          options: statusOptions,
        },
        cell: ({ status }) => (
          <Badge colorScheme={mapColor(status)}>{status}</Badge>
        ),
      },
      {
        name: 'Application ID',
        id: 'application_id',
        w: '10%',
        canHide: true,
        filter: {
          type: 'input',
          condition: 'eq',
        },
        cell: ({ application_id }) =>
          application_id && <Clipboard>{application_id}</Clipboard>,
      },
      {
        name: 'Device Template',
        id: 'device_type',
        w: '10%',
        canHide: true,
        cell: ({ device_type }) =>
          device_type?.name && <Clipboard>{device_type?.name}</Clipboard>,
      },
      {
        name: 'Created',
        id: 'created_at',
        w: '10%',
        canHide: true,
        cell: ({ created_at }) => (
          <Box noOfLines={1}>{formatDate(created_at)}</Box>
        ),
      },
    ],
    [mapColor]
  )

  const actions = useMemo<ActionProps<DeviceModel>[]>(
    () => [
      {
        label: 'Edit Device',
        onClick: onEdit,
        canView: () => true,
      },
      {
        label: 'Deactivate Device',
        onClick: onDeactivate,
        canView: (row) => row?.status === 'ACTIVATED',
      },
      {
        label: 'Rule Notification Behavior',
        onClick: onRuleNotification,
        canView: (row) => isInternalAdmin && !!row?.hardware_id,
      },
    ],
    [onEdit, onDeactivate, onRuleNotification, isInternalAdmin]
  )

  return (
    <>
      <Helmet>
        <title>{metaTitle([title])}</title>
      </Helmet>
      <Box px={4}>
        <Suspense fallback={null}>
          <DataTable
            tableName={tableName}
            columns={columns}
            data={records}
            actions={actions}
            disabledRow={({ status }) => status !== 'ACTIVATED'}
            header={() => (
              <Flex pl={2} pr={3} align="center" justify="space-between">
                <Title title={title} count={records.count} suffix="Record" />
                <AddButton label="Add Device" onClick={onAdd} />
              </Flex>
            )}
          />
        </Suspense>
      </Box>
    </>
  )
})

export async function loader() {
  const scopes = ['manage:all']

  const { checkLoaderPermissions } = await import('@/utils/loader-auth')
  await checkLoaderPermissions(scopes)

  return {
    title: 'Devices',
    scopes,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <Devices />
}
