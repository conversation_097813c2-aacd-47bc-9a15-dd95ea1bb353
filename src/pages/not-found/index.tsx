import { memo } from 'react'
import { Helm<PERSON> } from 'react-helmet-async'
import { ChakraProvider } from '@chakra-ui/react'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { PublicWrapper, PublicHeading } from '@/components/public-wrapper'
import { metaTitle } from '@/utils/meta-title'
import { theme } from '@/utils/theme/theme'

const NotFound = memo(() => {
  const title = 'Page not found'

  return (
    <>
      <Helmet>
        <title>{metaTitle([title])}</title>
      </Helmet>
      <ChakraProvider resetCSS theme={theme}>
        <PublicWrapper>
          <PublicHeading>{title}</PublicHeading>
        </PublicWrapper>
      </ChakraProvider>
    </>
  )
})

export async function loader() {
  const scopes = [] as string[]

  return {
    title: 'Not Found',
    scopes,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <NotFound />
}
