import { memo, Suspense, useState, useMemo, useEffect } from 'react'
import { Helmet } from 'react-helmet-async'
import { useParams, useNavigate } from 'react-router'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { useUpdateEffect } from '@react-hookz/web'
import { Box, Button, Flex, HStack } from '@chakra-ui/react'
import { models, type Report, type Embed } from 'powerbi-client'
import { PowerBIEmbed } from '@/features/powerbi/powerbi-react'
import { Title } from '@/components/title'
import { BackButton } from '@/components/back-button'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { useOrganization } from '@/hooks/use-organization'
import { useToast } from '@/hooks/use-toast'
import { getReportTemplate, getReportEmbed } from '@/api/reports'
import { metaTitle } from '@/utils/meta-title'
import { useAuth } from '@/contexts/use-auth'
import type { ReportEmbedResponse } from '@/types/api'
import type { ReportTemplateModel } from '@/types/models/report'

const View = memo(() => {
  const toast = useToast()
  const { id }: any = useParams()
  const queryClient = useQueryClient()
  const navigate = useNavigate()
  const { applicationId } = useOrganization()
  const { isInternalAdmin } = useAuth()
  const [report, setReport] = useState<Report>()

  const { data } = useQuery<ReportTemplateModel | null, Error>({
    queryKey: ['GetReportTemplate', id, applicationId],
    queryFn: ({ signal }) =>
      applicationId
        ? getReportTemplate({
            id,
            applicationId,
            signal,
          })
        : null,
    enabled: !!id && !!applicationId,
  })

  const { data: embed, refetch } = useQuery<ReportEmbedResponse | null, Error>({
    queryKey: ['GetReportEmbed', id, applicationId],
    queryFn: ({ signal }) =>
      applicationId
        ? getReportEmbed({
            id,
            applicationId,
            signal,
          })
        : null,
    enabled: !!id && !!applicationId && !!data?.embed,
  })

  const onGoBack = (): void => {
    navigate('/manage/reports', {
      viewTransition: true,
    })
  }

  const title = useMemo<string>(
    () => embed?.embedUrl[0]?.reportName ?? '',
    [embed]
  )

  useUpdateEffect(() => {
    if (!applicationId) {
      return
    }

    const cache = ['GetReportTemplates', 'GetReportTemplate', 'GetReportEmbed']
    cache.map((cacheKey) =>
      queryClient.invalidateQueries({ queryKey: [cacheKey] })
    )

    refetch()
  }, [refetch, queryClient, applicationId])

  useEffect(() => {
    report?.on('loaded', () => {
      toast({
        status: 'info',
        msg: 'Your report has been loaded.',
      })
    })

    report?.on('saved', () => {
      toast({
        status: 'success',
        msg: 'Your report has been saved.',
      })
    })

    return () => {
      report?.off('loaded')
      report?.off('saved')
    }
  }, [toast, report])

  return (
    <>
      <Helmet>
        <title>{metaTitle([title])}</title>
      </Helmet>
      <Suspense fallback={null}>
        <Flex align="center" justify="space-between" p={4}>
          <HStack>
            <BackButton onClick={onGoBack} />
            <Title title={title} />
          </HStack>
          {isInternalAdmin && (
            <Button
              type="button"
              aria-label="Edit Report"
              variant="solid"
              colorScheme="blue"
              onClick={() => navigate(`/manage/reports/${id}/edit`)}
            >
              Edit Report
            </Button>
          )}
        </Flex>
        <Box px={4} borderRadius="base">
          {embed?.embedUrl[0]?.embedUrl && (
            <PowerBIEmbed
              __css={{
                position: 'relative',
                width: '100%',
                height: '80vh',
                iframe: {
                  margin: 0,
                  border: 'none',
                },
              }}
              embedConfig={{
                type: 'report',
                id: embed?.embedUrl[0]?.reportId,
                embedUrl: embed?.embedUrl[0]?.embedUrl,
                accessToken: embed.accessToken,
                tokenType: models.TokenType.Embed,
                permissions: models.Permissions.Read,
                viewMode: models.ViewMode.View,
                settings: {
                  background: models.BackgroundType.Default,
                  filterPaneEnabled: true,
                  navContentPaneEnabled: true,
                  layoutType: models.LayoutType.Custom,
                  customLayout: {
                    displayOption: models.DisplayOption.FitToPage,
                  },
                  bars: {
                    actionBar: {
                      visible: false,
                    },
                  },
                  panes: {
                    filters: {
                      expanded: true,
                      visible: true,
                    },
                    visualizations: {
                      expanded: false,
                      visible: false,
                    },
                  },
                },
              }}
              getEmbeddedComponent={(embeddedReport: Embed) => {
                setReport(embeddedReport as Report)
              }}
            />
          )}
        </Box>
      </Suspense>
    </>
  )
})

export async function loader() {
  const scopes = ['manage:all']

  const { checkLoaderPermissions } = await import('@/utils/loader-auth')
  await checkLoaderPermissions(scopes)

  return {
    title: 'View Report',
    scopes,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <View />
}
