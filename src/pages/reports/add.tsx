import { lazy, Suspense, useCallback, memo } from 'react'
import { <PERSON><PERSON><PERSON> } from 'react-helmet-async'
import { useNavigate } from 'react-router'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  Box,
  Button,
  HStack,
  Container,
  Divider,
  Flex,
  Grid,
} from '@chakra-ui/react'
import { AddButton } from '@/components/add-button'
import { BackButton } from '@/components/back-button'
import { InputField } from '@/components/input-field'
import { CheckboxField } from '@/components/checkbox-field'
import { TextareaField } from '@/components/textarea-field'
import { SelectField } from '@/components/select-field'
import { Title } from '@/components/title'
import { useModal } from '@/hooks/use-modal'
import { useToast } from '@/hooks/use-toast'
import { useRouter } from '@/hooks/use-router'
import { metaTitle } from '@/utils/meta-title'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { CategoryOption } from '@/features/reports/category-option'
import { getReportCategories, createReportTemplate } from '@/api/reports'
import type {
  ReportTemplateModel,
  ReportCategoryModel,
} from '@/types/models/report'
import type { ReportTemplateInput } from '@/types/api'

const EditCategory = lazy(() =>
  import('@/pages/reports/edit-category').then(({ EditCategory }) => ({
    default: EditCategory,
  }))
)

const schema = z.object({
  visible: z.boolean().default(true),
  name: z.string().min(1, 'Name is required.'),
  display_name: z.string().min(1, 'Display Name is required.'),
  description: z.string().min(1, 'Description is required.'),
  embed: z.boolean().default(false),
  thumbnail: z.string().min(1, 'Thumbnail URL is required.').url(),
  preview: z.string().min(1, 'PDF Preview URL is required.').url(),
  config: z.string(),
  report_categories: z.array(z.number()),
  ui_settings: z.object({
    daily_report: z.boolean(),
    weekly_report: z.boolean(),
    monthly_report: z.boolean(),
    custom_report: z.boolean(),
    select_devices: z.boolean(),
  }),
})

type FormInputProps = z.infer<typeof schema>

const Add = memo(() => {
  const toast = useToast()
  const modal = useModal()
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  const { title } = useRouter()

  const {
    control,
    handleSubmit,
    formState: { isValid, isSubmitting },
  } = useForm({
    mode: 'onTouched',
    resolver: zodResolver(schema),
    defaultValues: {
      visible: true,
      name: '',
      display_name: '',
      description: '',
      embed: false,
      thumbnail: '',
      preview: '',
      config: JSON.stringify({ is_location: false }, null, 2),
      report_categories: [] as number[],
      ui_settings: {
        daily_report: false,
        weekly_report: false,
        monthly_report: false,
        custom_report: false,
        select_devices: false,
      },
    },
  })

  const { mutateAsync: createReportTemplateMutation } = useMutation<
    ReportTemplateModel,
    Error,
    Partial<ReportTemplateInput>
  >({
    mutationFn: (input) => createReportTemplate(input),
    onSuccess: () => {
      const cache = [
        'GetReportTemplate',
        'GetReportTemplates',
        'GetReportCategories',
      ]
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const { data: categoryOptions, isFetching: isFetchingCategories } = useQuery<
    any,
    Error,
    BaseOption[]
  >({
    queryKey: ['GetReportCategories'],
    queryFn: ({ signal }) =>
      getReportCategories({
        signal,
      }),
    refetchOnWindowFocus: false,
    select: useCallback(
      (data: ReportCategoryModel[]) =>
        data.reduce(
          (acc: BaseOption[], { id, display_name }: any) =>
            acc.concat({
              label: display_name,
              value: id,
            }),
          []
        ),
      []
    ),
  })

  const onAddCategory = (): void => {
    modal({
      component: <EditCategory />,
      config: {
        title: 'Add Category',
        data: {},
        onCallback: () => ({}),
      },
    })
  }

  const onGoBack = (): void => {
    navigate('/manage/report-builder')
  }

  const onSubmit = async (values: FormInputProps): Promise<boolean> => {
    const input: Partial<ReportTemplateInput> = {
      ...values,
      config: JSON.parse(values.config),
    }

    try {
      await createReportTemplateMutation(input)
      toast({
        msg: 'Report has been created.',
        status: 'success',
      })
      return true
    } catch {
      toast({
        msg: 'Unable to create report.',
        status: 'error',
      })
      return false
    }
  }

  return (
    <>
      <Helmet>
        <title>{metaTitle(title)}</title>
      </Helmet>
      <Flex align="center" justify="space-between" p={4}>
        <HStack>
          <BackButton onClick={onGoBack} />
          <Title title={title} />
        </HStack>
      </Flex>
      <Box mx={4} pb={10} borderTop="2px" borderColor="inherit">
        <Suspense fallback={null}>
          <Box as="form" noValidate onSubmit={handleSubmit(onSubmit)}>
            <Container maxW="container.xl" m={0} pt={4}>
              <Grid
                gap={4}
                templateColumns={{ base: '1fr', lg: 'repeat(2, 1fr)' }}
              >
                <Box gridColumn="1 / -1">
                  <CheckboxField
                    name="visible"
                    label="Visible"
                    control={control}
                  />
                </Box>
                <Box gridColumn="1 / -1">
                  <CheckboxField
                    name="embed"
                    label="Embed (Power BI)"
                    control={control}
                  />
                </Box>
                <InputField
                  name="name"
                  label="Name"
                  control={control}
                  isRequired
                />
                <InputField
                  name="display_name"
                  label="Display Name"
                  control={control}
                  isRequired
                />
                <Box gridColumn="1 / -1">
                  <SelectField
                    name="report_categories"
                    label="Categories"
                    control={control}
                    options={categoryOptions}
                    components={{ Option: CategoryOption }}
                    isLoading={isFetchingCategories}
                    isRequired
                    isSearchable
                    isClearable
                    isMulti
                    link={
                      <AddButton
                        variant="link"
                        label="Add Category"
                        onClick={onAddCategory}
                        _hover={{ textDecoration: 'none' }}
                      />
                    }
                  />
                </Box>
                <Box gridColumn="1 / -1">
                  <TextareaField
                    name="description"
                    label="Description"
                    control={control}
                    isRequired
                  />
                </Box>
                <InputField
                  name="thumbnail"
                  label="Thumbnail URL"
                  type="url"
                  control={control}
                  isRequired
                />
                <InputField
                  name="preview"
                  label="Example URL"
                  type="url"
                  control={control}
                  isRequired
                />
                <Box gridColumn="1 / -1">
                  <TextareaField
                    name="config"
                    label="Config"
                    control={control}
                  />
                </Box>
                <Box gridColumn="1 / -1">
                  <CheckboxField
                    name="ui_settings.daily_report"
                    label="Show Daily Reports"
                    control={control}
                  />
                </Box>
                <Box gridColumn="1 / -1">
                  <CheckboxField
                    name="ui_settings.weekly_report"
                    label="Show Weekly Reports"
                    control={control}
                  />
                </Box>
                <Box gridColumn="1 / -1">
                  <CheckboxField
                    name="ui_settings.monthly_report"
                    label="Show Monthly Reports"
                    control={control}
                  />
                </Box>
                <Box gridColumn="1 / -1">
                  <CheckboxField
                    name="ui_settings.custom_report"
                    label="Show Custom Reports"
                    control={control}
                  />
                </Box>
                <Box gridColumn="1 / -1">
                  <CheckboxField
                    name="ui_settings.select_devices"
                    label="Show Select Devices"
                    control={control}
                  />
                </Box>
                <Divider gridColumn="1 / -1" />
                <Box gridColumn="1 / -1">
                  <Button
                    mt={4}
                    type="submit"
                    aria-label="Save"
                    colorScheme="teal"
                    minW={120}
                    isLoading={isSubmitting}
                    isDisabled={!isValid || isSubmitting}
                    loadingText="Saving"
                  >
                    Save
                  </Button>
                </Box>
              </Grid>
            </Container>
          </Box>
        </Suspense>
      </Box>
    </>
  )
})

export async function loader() {
  const scopes = ['manage:all']

  const { checkLoaderPermissions } = await import('@/utils/loader-auth')
  await checkLoaderPermissions(scopes)

  return {
    title: 'Add Report',
    scopes,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <Add />
}
