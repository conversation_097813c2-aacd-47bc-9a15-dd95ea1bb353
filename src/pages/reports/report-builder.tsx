import { lazy, Suspense, type CSSProperties, memo } from 'react'
import { Helmet } from 'react-helmet-async'
import { Box, Skeleton } from '@chakra-ui/react'
import { Panel, PanelGroup } from 'react-resizable-panels'
import { PanelResize } from '@/features/reports/panel-resize'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { useRouter } from '@/hooks/use-router'
import { metaTitle } from '@/utils/meta-title'

const panelStyle = {
  borderRadius: '4px',
  overflowX: 'hidden',
  overflowY: 'scroll',
  backgroundColor: 'var(--chakra-colors-blackAlpha-50)',
} satisfies CSSProperties

const ReportHeader = lazy(() =>
  import('@/features/reports/report-header').then(({ ReportHeader }) => ({
    default: ReportHeader,
  }))
)

const ReportMenu = lazy(() =>
  import('@/features/reports/report-menu').then(({ ReportMenu }) => ({
    default: ReportMenu,
  }))
)

const ReportDisplay = lazy(() =>
  import('@/features/reports/report-display').then(({ ReportDisplay }) => ({
    default: ReportDisplay,
  }))
)

const ReportForm = lazy(() =>
  import('@/features/reports/report-form').then(({ ReportForm }) => ({
    default: ReportForm,
  }))
)

const ReportBuilder = memo(() => {
  const { title } = useRouter()

  return (
    <>
      <Helmet>
        <title>{metaTitle([title])}</title>
      </Helmet>
      <Suspense
        fallback={
          <Skeleton
            sx={{
              w: '100%',
              h: '72px',
            }}
          />
        }
      >
        <ReportHeader />
      </Suspense>
      <Box
        sx={{
          px: 4,
          height: 'calc(100vh - 157px)',
        }}
      >
        <PanelGroup direction="horizontal">
          <Panel
            defaultSize={20}
            minSize={15}
            maxSize={30}
            order={1}
            style={{
              margin: '0 2px 0 0',
              ...panelStyle,
              overflowY: 'hidden',
            }}
          >
            <Suspense
              fallback={
                <Skeleton
                  sx={{
                    w: '100%',
                    h: 10,
                  }}
                />
              }
            >
              <ReportMenu />
            </Suspense>
          </Panel>
          <PanelResize />
          <Panel
            order={2}
            style={{
              margin: '0 2px 0 2px',
              ...panelStyle,
            }}
          >
            <Suspense
              fallback={
                <Skeleton
                  sx={{
                    w: '100%',
                    h: '100%',
                  }}
                />
              }
            >
              <ReportDisplay />
            </Suspense>
          </Panel>
          <PanelResize />
          <Panel
            defaultSize={25}
            minSize={25}
            maxSize={30}
            order={3}
            style={{
              margin: '0 0 0 2px',
              ...panelStyle,
            }}
          >
            <Suspense
              fallback={
                <Skeleton
                  sx={{
                    w: '100%',
                    h: '100%',
                  }}
                />
              }
            >
              <ReportForm />
            </Suspense>
          </Panel>
        </PanelGroup>
      </Box>
    </>
  )
})

export async function loader() {
  const scopes = ['manage:all']

  const { checkLoaderPermissions } = await import('@/utils/loader-auth')
  await checkLoaderPermissions(scopes)

  return {
    title: 'App Report Builder',
    scopes,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <ReportBuilder />
}
