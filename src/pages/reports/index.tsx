import { memo, Suspense, useMemo } from 'react'
import { Helm<PERSON> } from 'react-helmet-async'
import { useNavigate } from 'react-router'
import { useQuery } from '@tanstack/react-query'
import { Box, Flex, Button, Grid } from '@chakra-ui/react'
import { Title } from '@/components/title'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { useRouter } from '@/hooks/use-router'
import { useOrganization } from '@/hooks/use-organization'
import { getReportTemplates } from '@/api/reports'
import { metaTitle } from '@/utils/meta-title'
import { sortByPriority } from '@/utils/sort-by-priority'
import type { ReportTemplateModel } from '@/types/models/report'

const Reports = memo(() => {
  const navigate = useNavigate()
  const { title } = useRouter()
  const { applicationId } = useOrganization()

  const { data } = useQuery<ReportTemplateModel[], Error>({
    queryKey: ['GetReportTemplates', 'console', applicationId],
    queryFn: ({ signal }) =>
      getReportTemplates({
        type: 'console',
        signal,
      }),
    refetchOnWindowFocus: false,
    enabled: !!applicationId,
  })

  const records = useMemo<{
    count: number
    rows: ReportTemplateModel[]
  }>(
    () =>
      data
        ? {
            count: data.length,
            rows: sortByPriority(data),
          }
        : {
            count: 0,
            rows: [],
          },
    [data]
  )

  const onView = (id: number): void => {
    navigate(`/manage/reports/${id}/view`, {
      viewTransition: true,
    })
  }

  return (
    <>
      <Helmet>
        <title>{metaTitle([title])}</title>
      </Helmet>
      <Flex align="center" justify="space-between" p={4}>
        <Title title={title} count={records.count} suffix="Record" />
      </Flex>
      <Box px={4}>
        <Suspense fallback={null}>
          <Grid templateColumns="repeat(auto-fill, minmax(250px, 1fr))" gap={4}>
            {records.rows.map(
              ({ id, display_name, thumbnail }: ReportTemplateModel) => (
                <Grid
                  key={id}
                  templateRows="200px auto"
                  borderRadius="md"
                  gap={3}
                >
                  <Flex
                    onClick={() => onView(id)}
                    sx={{
                      w: 'full',
                      position: 'relative',
                      borderRadius: 'md',
                      overflow: 'hidden',
                      bgColor: 'white',
                      bgImage: thumbnail ?? '',
                      bgPos: 'center',
                      bgSize: 'cover',
                      bgRepeat: 'no-repeat',
                      cursor: 'pointer',
                      shadow: 'md',
                      _dark: {
                        shadow: 'md-dark',
                      },
                    }}
                  />
                  <Flex
                    sx={{
                      w: 'inherit',
                      alignItems: 'center',
                      overflow: 'hidden',
                    }}
                  >
                    <Button
                      w="full"
                      variant="link"
                      colorScheme="primary"
                      onClick={() => onView(id)}
                    >
                      <Box noOfLines={1}>{display_name}</Box>
                    </Button>
                  </Flex>
                </Grid>
              )
            )}
          </Grid>
        </Suspense>
      </Box>
    </>
  )
})

export async function loader() {
  const scopes = ['view:reports']

  const { checkLoaderPermissions } = await import('@/utils/loader-auth')
  await checkLoaderPermissions(scopes)

  return {
    title: 'Reports',
    scopes,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <Reports />
}
