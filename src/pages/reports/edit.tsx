import { lazy, Suspense, useCallback, useEffect, memo } from 'react'
import { He<PERSON><PERSON> } from 'react-helmet-async'
import { useNavigate, useParams } from 'react-router'
import { useForm } from 'react-hook-form'
import { mergeDeepLeft } from 'ramda'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  Box,
  Button,
  HStack,
  Container,
  Divider,
  Flex,
  Grid,
} from '@chakra-ui/react'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { AddButton } from '@/components/add-button'
import { BackButton } from '@/components/back-button'
import { InputField } from '@/components/input-field'
import { SelectField } from '@/components/select-field'
import { TextareaField } from '@/components/textarea-field'
import { Title } from '@/components/title'
import { CheckboxField } from '@/components/checkbox-field'
import { useModal } from '@/hooks/use-modal'
import { useToast } from '@/hooks/use-toast'
import { useOrganization } from '@/hooks/use-organization'
import { useRouter } from '@/hooks/use-router'
import { metaTitle } from '@/utils/meta-title'
import { CategoryOption } from '@/features/reports/category-option'
import {
  getReportCategories,
  getReportTemplate,
  updateReportTemplate,
} from '@/api/reports'
import type { ReportTemplateModel } from '@/types/models/report'
import type { ReportTemplateInput } from '@/types/api'

const TypeOptions = [
  { label: 'App', value: 'app' },
  { label: 'Console', value: 'console' },
  { label: 'Superadmin', value: 'superadmin' },
]

const EditCategory = lazy(() =>
  import('@/pages/reports/edit-category').then(({ EditCategory }) => ({
    default: EditCategory,
  }))
)

const schema = z.object({
  visible: z.boolean().default(true),
  name: z.string().min(1, 'Name is required.'),
  display_name: z.string().min(1, 'Display Name is required.'),
  description: z.string().min(1, 'Description is required.'),
  embed: z.boolean().default(false),
  thumbnail: z.string().min(1, 'Thumbnail URL is required.').url(),
  preview: z.string().min(1, 'PDF Preview URL is required.').url(),
  config: z.string(),
  type: z.string(),
  report_categories: z.array(z.number()),
  ui_settings: z.object({
    daily_report: z.boolean(),
    weekly_report: z.boolean(),
    monthly_report: z.boolean(),
    custom_report: z.boolean(),
    select_devices: z.boolean(),
  }),
})

type FormInputProps = z.infer<typeof schema>

const Edit = memo(() => {
  const toast = useToast()
  const modal = useModal()
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  const { title } = useRouter()
  const { id }: any = useParams<{ id: string }>()
  const { applicationId } = useOrganization()

  const {
    reset,
    control,
    handleSubmit,
    formState: { isValid, isSubmitting },
  } = useForm({
    mode: 'onTouched',
    resolver: zodResolver(schema),
    defaultValues: {
      visible: true,
      name: '',
      display_name: '',
      description: '',
      embed: false,
      thumbnail: '',
      preview: '',
      type: 'console',
      config: JSON.stringify({ is_location: false }, null, 2),
      report_categories: [] as number[],
      ui_settings: {
        daily_report: false,
        weekly_report: false,
        monthly_report: false,
        custom_report: false,
        select_devices: false,
      },
    },
  })

  const { mutateAsync: updateReportTemplateMutation } = useMutation<
    ReportTemplateModel,
    Error,
    Partial<ReportTemplateInput>
  >({
    mutationFn: (input) => updateReportTemplate(id, input),
    onSuccess: () => {
      const cache = [
        'GetReportTemplate',
        'GetReportTemplates',
        'GetReportCategories',
      ]
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const { data } = useQuery<ReportTemplateModel | null, Error>({
    queryKey: ['GetReportTemplate', id, applicationId],
    queryFn: ({ signal }) =>
      applicationId
        ? getReportTemplate({
            id,
            applicationId,
            signal,
          })
        : null,
    refetchOnWindowFocus: false,
    enabled: !!id && !!applicationId,
  })

  useEffect(() => {
    if (!data?.id) {
      return
    }

    reset({
      visible: data.visible ?? true,
      name: data.name ?? '',
      display_name: data.display_name ?? '',
      description: data.description ?? '',
      embed: data.embed ?? false,
      thumbnail: data.thumbnail ?? '',
      preview: data.preview ?? '',
      type: data.type ?? 'console',
      config: JSON.stringify(
        mergeDeepLeft(data?.config ?? {}, { is_location: false }),
        null,
        2
      ),
      report_categories:
        (data?.report_categories ?? []).map(({ id }) => id) ?? [],
      ui_settings: {
        daily_report: data.ui_settings?.daily_report ?? false,
        weekly_report: data.ui_settings?.weekly_report ?? false,
        monthly_report: data.ui_settings?.monthly_report ?? false,
        custom_report: data.ui_settings?.custom_report ?? false,
        select_devices: data.ui_settings?.select_devices ?? false,
      },
    })
  }, [reset, data])

  const { data: categoryOptions, isFetching: isFetchingCategories } = useQuery<
    any,
    Error,
    BaseOption[]
  >({
    queryKey: ['GetReportCategories'],
    queryFn: ({ signal }) =>
      getReportCategories({
        signal,
      }),
    refetchOnWindowFocus: false,
    select: useCallback(
      (data: any) =>
        data.reduce(
          (acc: BaseOption[], { id, display_name }: any) =>
            acc.concat({ label: display_name, value: id }),
          []
        ),
      []
    ),
  })

  const onAddCategory = useCallback((): void => {
    modal({
      component: <EditCategory />,
      config: {
        title: 'Add Category',
        data: {},
        onCallback: () => ({}),
      },
    })
  }, [modal])

  const onGoBack = (): void => {
    navigate(`/manage/reports/${data?.id}/view`, {
      viewTransition: true,
    })
  }

  const onSubmit = async (values: FormInputProps): Promise<boolean> => {
    const input: Partial<ReportTemplateInput> = {
      ...values,
      config: JSON.parse(values.config),
    }

    try {
      await updateReportTemplateMutation(input)
      toast({
        msg: 'Report has been updated.',
        status: 'success',
      })
      return true
    } catch {
      toast({
        msg: 'Unable to update report.',
        status: 'error',
      })
      return false
    }
  }

  return (
    <>
      <Helmet>
        <title>{metaTitle(title)}</title>
      </Helmet>
      <Flex align="center" justify="space-between" p={4}>
        <HStack>
          <BackButton onClick={onGoBack} />
          <Title title={title} />
        </HStack>
      </Flex>
      <Box mx={4} borderTop="2px" borderColor="inherit">
        <Suspense fallback={null}>
          <Box as="form" noValidate onSubmit={handleSubmit(onSubmit)}>
            <Container maxW="container.xl" m={0} pt={4}>
              <Grid
                gap={4}
                templateColumns={{ base: '1fr', lg: 'repeat(2, 1fr)' }}
              >
                <Box gridColumn="1 / -1">
                  <CheckboxField
                    name="visible"
                    label="Visible"
                    control={control}
                  />
                </Box>
                <Box gridColumn="1 / -1">
                  <CheckboxField
                    name="embed"
                    label="Embed (Power BI)"
                    control={control}
                  />
                </Box>
                <InputField
                  name="name"
                  label="Name"
                  control={control}
                  isRequired
                />
                <InputField
                  name="display_name"
                  label="Display Name"
                  control={control}
                  isRequired
                />
                <SelectField
                  name="type"
                  label="Type"
                  control={control}
                  options={TypeOptions}
                  isRequired
                />
                <SelectField
                  name="report_categories"
                  label="Category"
                  control={control}
                  options={categoryOptions}
                  components={{ Option: CategoryOption }}
                  isLoading={isFetchingCategories}
                  isSearchable
                  isClearable
                  isMulti
                  link={
                    <AddButton
                      variant="link"
                      label="Add Category"
                      onClick={onAddCategory}
                      _hover={{ textDecoration: 'none' }}
                    />
                  }
                />
                <Box gridColumn="1 / -1">
                  <TextareaField
                    name="description"
                    label="Description"
                    control={control}
                    isRequired
                  />
                </Box>
                <InputField
                  name="thumbnail"
                  label="Thumbnail URL"
                  type="url"
                  control={control}
                  isRequired
                />
                <InputField
                  name="preview"
                  label="Example URL"
                  type="url"
                  control={control}
                  isRequired
                />
                <Box gridColumn="1 / -1">
                  <TextareaField
                    name="config"
                    label="Config"
                    control={control}
                    rows={8}
                  />
                </Box>
                <Box gridColumn="1 / -1">
                  <CheckboxField
                    name="ui_settings.daily_report"
                    label="Show Daily Reports"
                    control={control}
                  />
                </Box>
                <Box gridColumn="1 / -1">
                  <CheckboxField
                    name="ui_settings.weekly_report"
                    label="Show Weekly Reports"
                    control={control}
                  />
                </Box>
                <Box gridColumn="1 / -1">
                  <CheckboxField
                    name="ui_settings.monthly_report"
                    label="Show Monthly Reports"
                    control={control}
                  />
                </Box>
                <Box gridColumn="1 / -1">
                  <CheckboxField
                    name="ui_settings.custom_report"
                    label="Show Custom Reports"
                    control={control}
                  />
                </Box>
                <Box gridColumn="1 / -1">
                  <CheckboxField
                    name="ui_settings.select_devices"
                    label="Show Select Devices"
                    control={control}
                  />
                </Box>
                <Divider gridColumn="1 / -1" />
                <Box gridColumn="1 / -1">
                  <Button
                    mt={4}
                    type="submit"
                    aria-label="Save"
                    colorScheme="teal"
                    minW={120}
                    isLoading={isSubmitting}
                    isDisabled={!isValid || isSubmitting}
                    loadingText="Saving"
                  >
                    Save
                  </Button>
                </Box>
              </Grid>
            </Container>
          </Box>
        </Suspense>
      </Box>
    </>
  )
})

export async function loader() {
  const scopes = ['manage:all']

  const { checkLoaderPermissions } = await import('@/utils/loader-auth')
  await checkLoaderPermissions(scopes)

  return {
    title: 'Edit Report',
    scopes,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <Edit />
}
