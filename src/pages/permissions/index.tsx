import { lazy, Suspense, useCallback, useMemo, memo } from 'react'
import { He<PERSON><PERSON> } from 'react-helmet-async'
import { useMountEffect } from '@react-hookz/web'
import { useNavigate } from 'react-router'
import {
  Box,
  Button,
  Container,
  Flex,
  Grid,
  Heading,
  Icon,
  StackDivider,
  Text,
  VStack,
  useColorModeValue,
} from '@chakra-ui/react'
import { CheckIcon } from 'lucide-react'
import { useQuery } from '@tanstack/react-query'
import { useAuth } from '@/contexts/use-auth'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { useRouter } from '@/hooks/use-router'
import { useAbility } from '@/hooks/use-ability'
import { metaTitle } from '@/utils/meta-title'
import { upperFirst } from '@/utils/upper-first'
import { getCurrentUser } from '@/api/users'
import { getApplications } from '@/api/applications'
import { useDarkerBg } from '@/hooks/use-darker-bg'
import type { CurrentUserModel } from '@/types/models/user'
import type { ApplicationModel } from '@/types/models/application'
import type { PaginatedQueryResponse } from '@/types/api'

const DomainUrl = lazy(() =>
  import('@/components/domain-url').then(({ DomainUrl }) => ({
    default: DomainUrl,
  }))
)

const Permissions = memo(() => {
  const navigate = useNavigate()
  const { title } = useRouter()
  const { user, isInternalAdmin } = useAuth()
  const { can } = useAbility()

  const darkerBg = useDarkerBg()
  const bg = useColorModeValue('white', darkerBg)

  const onNext = useCallback(() => {
    if (can(['view', 'reports'])) {
      navigate('/manage/reports', {
        replace: true,
        viewTransition: true,
      })
      return
    }

    const next = can(['view', 'applications'])
      ? '/manage/customers'
      : '/manage/admin-api'

    navigate(next, {
      replace: true,
      viewTransition: true,
    })
  }, [can, navigate])

  // Just ensure that internal admins does not try to access this page.
  useMountEffect(() => {
    if (isInternalAdmin) {
      onNext()
    }
  })

  const isEnabled = useMemo<boolean>(
    () => !!user?.organization || !isInternalAdmin,
    [user, isInternalAdmin]
  )

  const { data: account } = useQuery<CurrentUserModel | null, Error>({
    queryKey: ['GetCurrentUser', user?.token],
    queryFn: ({ signal }) =>
      getCurrentUser({
        signal,
      }),
    enabled: isEnabled,
  })

  const { data: apps } = useQuery<
    PaginatedQueryResponse<ApplicationModel> | null,
    Error,
    Pick<ApplicationModel, 'id' | 'name'>[]
  >({
    queryKey: ['GetApplications', user?.organization],
    queryFn: ({ signal }) =>
      user?.organization
        ? getApplications({
            organizationId: user?.organization,
            signal,
          })
        : null,
    enabled: isEnabled,
    select: useCallback(
      (data: PaginatedQueryResponse<ApplicationModel> | null) =>
        (data?.rows ?? []).reduce(
          (acc: Pick<ApplicationModel, 'id' | 'name'>[], { id, name }) => {
            acc.push({
              id,
              name,
            })
            return acc
          },
          []
        ),
      []
    ),
  })

  const roleName = useMemo<string>(() => {
    if (user?.role === 'report') {
      return 'Reports Viewer'
    }
    return [upperFirst(user?.role ?? ''), 'Admin'].filter(Boolean).join(' ')
  }, [user?.role])

  const roleDescription = useMemo<string>(() => {
    if (user?.role === 'report') {
      return upperFirst(
        [
          account?.firstName ?? '',
          'you now get access to the back office myDevices Console to view reports only.',
        ]
          .filter(Boolean)
          .join(' ')
      )
    }
    return upperFirst(
      [
        account?.firstName ?? '',
        'you now get access to the back office myDevices Console to manage, support, and bill your end customers for the following applications:',
      ]
        .filter(Boolean)
        .join(' ')
    )
  }, [user, account])

  return (
    <>
      <Helmet>
        <title>{metaTitle([title])}</title>
      </Helmet>
      <Suspense fallback={null}>
        <Grid
          templateColumns="1fr"
          templateRows="1fr 65px"
          gap={0}
          h="full"
          w="full"
        >
          <Container maxW="container.xl">
            <Grid
              my={8}
              gap={{ base: 4, xl: 8 }}
              templateColumns={{ base: '1fr', xl: '1fr 0.7fr' }}
            >
              <Box bg={bg} shadow="md" p={5} borderRadius="md">
                <Heading as="h1" size="md" fontWeight="medium">
                  {roleDescription}
                </Heading>
                <VStack
                  mt={8}
                  spacing={2}
                  align="stretch"
                  divider={
                    <StackDivider
                      sx={{
                        borderColor: 'gray.200',
                        _dark: {
                          borderColor: 'blackAlpha.300',
                        },
                      }}
                    />
                  }
                >
                  {(apps ?? []).map(({ id, name }) => (
                    <Flex key={id} align="center" justify="space-between">
                      <Flex align="center">
                        <Icon
                          as={CheckIcon}
                          sx={{
                            mr: 4,
                            color: 'secondary.500',
                            _dark: {
                              color: 'secondary.200',
                            },
                          }}
                        />
                        <Text>{name}</Text>
                      </Flex>
                      <Suspense fallback={null}>
                        <DomainUrl applicationId={id} />
                      </Suspense>
                    </Flex>
                  ))}
                </VStack>
              </Box>
              <Box bg={bg} shadow="md" p={5} borderRadius="md">
                <Heading
                  as="h2"
                  size="md"
                  fontWeight="medium"
                  maxW={{ base: '100%', lg: '80%' }}
                >
                  Role
                </Heading>
                <Box mt={4} mb={6}>
                  {roleName}
                </Box>
              </Box>
            </Grid>
          </Container>
          <Flex px={4} bg={bg} align="center" justify="flex-end">
            <Button
              type="button"
              aria-label="Next"
              colorScheme="green"
              minW="120px"
              onClick={() => onNext()}
            >
              Let&apos;s Go
            </Button>
          </Flex>
        </Grid>
      </Suspense>
    </>
  )
})

export async function loader() {
  const scopes = [] as string[]

  return {
    title: 'Permissions',
    scopes,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <Permissions />
}
