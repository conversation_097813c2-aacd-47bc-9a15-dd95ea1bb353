import * as Sentry from '@sentry/react'
import {
  lazy,
  Suspense,
  useCallback,
  useMemo,
  useState,
  useEffect,
} from 'react'
import { Helmet } from 'react-helmet-async'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { sortWith, ascend, prop } from 'ramda'
import { useAtomValue } from 'jotai'
import { Box, Flex, Tooltip } from '@chakra-ui/react'
import { Title } from '@/components/title'
import { AddButton } from '@/components/add-button'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { Clipboard } from '@/components/data-table/clipboard'
import { DataTable } from '@/components/data-table/data-table'
import { useModal } from '@/hooks/use-modal'
import { useAbility } from '@/hooks/use-ability'
import { useRouter } from '@/hooks/use-router'
import { useShop } from '@/hooks/use-shop'
import { useOrganization } from '@/hooks/use-organization'
import { useRemoteAssistance } from '@/hooks/use-remote-assistance'
import { useToast } from '@/hooks/use-toast'
import { metaTitle } from '@/utils/meta-title'
import { fullName } from '@/utils/full-name'
import { tableAtom } from '@/utils/stores/table'
import {
  getOrganizationUsers,
  getApplicationUsers,
  inviteUser,
  deleteUser,
} from '@/api/users'
import { useAuth } from '@/contexts/use-auth'
import { matchRole } from '@/utils/match-role'
import { pluralize } from '@/utils/pluralize'
import {
  hasOrgRole,
  hasRoleById,
  hasRoleForApp,
  extractAppNamesFromRules,
  extractAppNameFromScope,
} from '@/utils/scope-utils'
import type { UserRoleModel } from '@/types/models/user'
import type {
  InviteUserResponse,
  InviteUserInput,
  QueryResponse,
  DeleteUserInput,
} from '@/types/api'
import type { ColumnProps, ActionProps } from '@/types/table'

const DeleteDialog = lazy(() =>
  import('@/components/delete-dialog').then(({ DeleteDialog }) => ({
    default: DeleteDialog,
  }))
)

const Edit = lazy(() =>
  import('@/pages/users/edit').then(({ Edit }) => ({
    default: Edit,
  }))
)

const sortList = sortWith<UserRoleModel>([
  ascend(prop('firstName')),
  ascend(prop('lastName')),
  ascend(prop('email')),
])

const setActivated = (row: UserRoleModel): boolean =>
  row.attributes?.inviteStatus?.[0] === 'ACTIVATED' ||
  row.attributes?.invite_status?.[0] === 'ACTIVATED'

const setPending = (row: UserRoleModel): boolean =>
  row.attributes?.inviteStatus?.[0] === 'PENDING' ||
  row.attributes?.invite_status?.[0] === 'PENDING'

const roleName = (name: string): string => {
  switch (name) {
    case 'organization_admin':
      return 'Org Admin'
    case 'application_admin':
      return 'App Admin'
    case 'reports_viewer':
      return 'Report Viewer'
    default:
      return name
  }
}

const roleOptions = [
  {
    label: 'All',
    value: '',
  },
  {
    label: 'App Admin',
    value: 'App Admin',
  },
  {
    label: 'Org Admin',
    value: 'Org Admin',
  },
  {
    label: 'Report Viewer',
    value: 'Report Viewer',
  },
]

function Users() {
  const tableName = 'users'
  const modal = useModal()
  const toast = useToast()
  const { title } = useRouter()
  const queryClient = useQueryClient()
  const { organizationId, applicationId } = useOrganization()
  const { can } = useAbility()
  const impersonate = useRemoteAssistance()
  const { filters } = useAtomValue(tableAtom(tableName))
  const { isInternalAdmin, user } = useAuth()
  const { deleteShopUserMutation } = useShop()

  const [records, setRecords] = useState<QueryResponse<UserRoleModel>>(() => ({
    count: 0,
    rows: [],
  }))

  const { mutateAsync: inviteUserMutation } = useMutation<
    InviteUserResponse,
    Error,
    InviteUserInput
  >({ mutationFn: (input) => inviteUser(input) })

  const { mutateAsync: deleteUserMutation } = useMutation<
    boolean,
    Error,
    DeleteUserInput
  >({
    mutationFn: (input) => deleteUser(input),
    onSuccess: () => {
      const cache = ['GetRoleUsers', 'GetUsers', 'GetUser']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const { data } = useQuery<UserRoleModel[] | null, Error>({
    queryKey: ['GetRoleUsers', organizationId, applicationId],
    queryFn: ({ signal }) =>
      organizationId && applicationId
        ? can(['edit', 'organizations'])
          ? getOrganizationUsers({
              organizationId,
              signal,
            })
          : getApplicationUsers({
              organizationId,
              applicationId,
              signal,
            })
        : null,
    enabled: !!organizationId && !!applicationId,
    select: useCallback(
      (data: UserRoleModel[] | null) =>
        sortList(
          (data ?? []).map((row) => ({
            ...row,
            pending: setPending(row),
            activated: setActivated(row),
            locale: row.attributes?.locale?.[0] ?? 'en-us',
            name: fullName(row.firstName, row.lastName),
            phoneNumber: row.attributes?.phoneNumber?.[0] ?? '',
            // The below 3 rows are just for testing purposes
            orgAdmin: hasOrgRole(row.roles, organizationId),
            appAdmin: hasRoleById(row.roles, 2, applicationId),
            reportViewer: hasRoleById(row.roles, 3, applicationId),
            hasRole: hasRoleForApp(row.roles, applicationId),
            scopeNames: extractAppNamesFromRules(row.roles),
            scopes: row.roles.map(({ role, scope }) => ({
              role_id: role.id,
              name: roleName(role.name),
              application: extractAppNameFromScope(scope),
            })),
          }))
        ),
      [organizationId, applicationId]
    ),
  })

  const currentAppRoleName = useCallback(
    (row?: UserRoleModel): string | null => {
      const matchedRole = row?.scopes?.find(
        ({ application }) => application === applicationId
      )
      return matchedRole ? matchedRole.name : null
    },
    [applicationId]
  )

  useEffect(() => {
    if (!data) {
      return
    }

    setRecords({
      count: data.length,
      rows: data.filter((row) => {
        if (filters.length === 0) {
          return true
        }
        return filters.every(({ id, value, condition }) => {
          if (!value) {
            return true
          }
          if (id === 'role') {
            return currentAppRoleName(row) === value
          }
          if (condition === 'like') {
            return row[id]?.toLowerCase().includes(value.toLowerCase())
          }
          if (condition === 'eq') {
            return row[id] === value
          }
          return true
        })
      }),
    })
  }, [data, filters, currentAppRoleName])

  const canAdd = useMemo<boolean>(() => can(['edit', 'applications']), [can])

  const canEdit = useCallback(
    (row?: UserRoleModel): boolean => {
      if (can(['edit', 'organizations'])) {
        return true
      }
      // check if the user has higher or equal role
      const rowRoles = Array.from(
        new Set(
          (row?.roles ?? []).map(({ role }) =>
            role.name.replace('_admin', '').replace('s_viewer', '')
          )
        )
      )
      const userRole = user?.role ?? ''
      return matchRole(userRole, rowRoles)
    },
    [user, can]
  )

  const canImpersonate = useCallback(
    (row?: UserRoleModel): boolean =>
      !!row?.enabled && !!row?.tina_user_id && canEdit(row),
    [canEdit]
  )

  const canInvite = useCallback(
    (row?: UserRoleModel): boolean =>
      row?.pending && can(['edit', 'applications']),
    [can]
  )

  const onAdd = useCallback((): void => {
    modal({
      component: <Edit />,
      config: {
        title: 'Add User',
        data: null,
        onCallback: () => ({}),
      },
    })
  }, [modal])

  const onEdit = useCallback(
    (row: UserRoleModel): void => {
      modal({
        component: <Edit />,
        config: {
          title: `Edit “${row.name}”`,
          data: row,
          onCallback: () => ({}),
        },
      })
    },
    [modal]
  )

  const onImpersonate = useCallback(
    (row: UserRoleModel): void => {
      if (!(organizationId && applicationId && row?.tina_user_id)) {
        return
      }
      impersonate({
        organizationId,
        applicationId,
        user: row,
      })
    },
    [organizationId, applicationId, impersonate]
  )

  const onResend = useCallback(
    async ({ tina_user_id, email }: UserRoleModel): Promise<void> => {
      if (!(organizationId && applicationId)) {
        return
      }

      try {
        await inviteUserMutation({
          organizationId,
          applicationId,
          userId: tina_user_id,
        })
        toast({
          status: 'success',
          msg: `Invite sent to “${email}”.`,
        })
      } catch (error: unknown) {
        Sentry.captureException(error)
        toast({
          status: 'error',
          msg: 'Unable to send invitation.',
        })
      }
    },
    [toast, organizationId, applicationId, inviteUserMutation]
  )

  const onDelete = useCallback(
    (row?: UserRoleModel): void => {
      if (!row?.tina_user_id) {
        return
      }
      modal({
        size: 'md',
        component: <DeleteDialog />,
        config: {
          title: 'Delete user?',
          description: `Are you sure you want to delete “${row.email}”?`,
          confirmLabel: 'Delete',
          inputLabel: 'DELETE',
          onCallback: async () => {
            if (!(organizationId && applicationId)) {
              return
            }
            try {
              await deleteUserMutation({
                organizationId,
                applicationId,
                userId: row.tina_user_id,
              })
              await deleteShopUserMutation({
                organizationId,
                applicationId,
                email: row.email,
              })
              toast({
                status: 'success',
                msg: `“${row.email}” has been deleted.`,
              })
            } catch {
              toast({
                status: 'error',
                msg: `Unable to delete “${row.email}”.`,
              })
            }
          },
        },
      })
    },
    [
      modal,
      toast,
      organizationId,
      applicationId,
      deleteUserMutation,
      deleteShopUserMutation,
    ]
  )

  const columns = useMemo<ColumnProps<UserRoleModel>[]>(
    () => [
      {
        name: 'Name',
        id: 'name',
        filter: {
          type: 'input',
          condition: 'like',
        },
        canHide: true,
        cell: ({ name }) => {
          return name && <Clipboard>{name}</Clipboard>
        },
      },
      {
        name: 'Email',
        id: 'email',
        filter: {
          type: 'input',
          condition: 'like',
        },
        canHide: true,
        cell: ({ email }) => <Clipboard>{email}</Clipboard>,
      },
      {
        name: 'Phone Number',
        id: 'phoneNumber',
        filter: {
          type: 'input',
          condition: 'like',
        },
        canHide: true,
        cell: ({ attributes }) => {
          const phoneNumber = attributes?.phoneNumber?.[0]
          return phoneNumber && <Clipboard>{phoneNumber}</Clipboard>
        },
      },
      {
        name: 'Role',
        id: 'role',
        w: '15%',
        filter: {
          type: 'select',
          condition: 'eq',
          options: roleOptions,
        },
        canHide: true,
        cell: (row) => {
          const roleName = currentAppRoleName(row)
          const num = row.scopeNames.length
          return (
            <Box color="gray.600">
              <Tooltip
                label={`Access to ${num} ${pluralize(num, 'app')}`}
                hasArrow
              >
                <Box as="span">{roleName ?? '-'}</Box>
              </Tooltip>
            </Box>
          )
        },
      },
    ],
    [currentAppRoleName]
  )

  const actions = useMemo<ActionProps<UserRoleModel>[]>(
    () => [
      {
        label: 'Edit User',
        onClick: onEdit,
        canView: (row) => canEdit(row),
      },
      {
        label: 'Remote Assistance',
        onClick: onImpersonate,
        canView: (row) => canImpersonate(row),
      },
      {
        label: 'Resend Invite',
        onClick: onResend,
        canView: (row) => canInvite(row),
      },
      {
        label: 'Delete User',
        onClick: (row) => onDelete(row),
        canView: (row) => row?.hasRole && isInternalAdmin,
      },
    ],
    [
      onEdit,
      canEdit,
      canImpersonate,
      onImpersonate,
      onResend,
      onDelete,
      canInvite,
      isInternalAdmin,
    ]
  )

  return (
    <>
      <Helmet>
        <title>{metaTitle([title])}</title>
      </Helmet>
      <Flex align="center" justify="space-between" p={4}>
        <Title title={title} count={records.count} suffix="Record" />
        {canAdd && <AddButton label="Add User" onClick={onAdd} />}
      </Flex>
      <Box px={4}>
        <Suspense fallback={null}>
          <DataTable
            tableName={tableName}
            data={records}
            columns={columns}
            actions={actions}
            skipPagination={true}
          />
        </Suspense>
      </Box>
    </>
  )
}

export async function loader() {
  const scopes = ['view:applications']

  const { checkLoaderPermissions } = await import('@/utils/loader-auth')
  await checkLoaderPermissions(scopes)

  return {
    title: 'Users',
    scopes,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <Users />
}
