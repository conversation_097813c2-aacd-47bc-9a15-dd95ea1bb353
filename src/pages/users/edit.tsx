import * as Sentry from '@sentry/react'
import { memo, lazy, useMemo, useEffect, useCallback, useState } from 'react'
import { useForm, useWatch } from 'react-hook-form'
import { z } from 'zod'
import pMap from 'p-map'
import { zodResolver } from '@hookform/resolvers/zod'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { useAtomValue } from 'jotai'
import { head, is } from 'ramda'
import {
  Button,
  Box,
  Grid,
  ModalBody,
  ModalCloseButton,
  ModalFooter,
  ModalHeader,
  useModalContext,
  HStack,
} from '@chakra-ui/react'
import { ModalHeaderBadge } from '@/components/modal-header-badge'
import { InputField } from '@/components/input-field'
import { SelectField } from '@/components/select-field'
import { useAbility } from '@/hooks/use-ability'
import { useOrganization } from '@/hooks/use-organization'
import { useModal } from '@/hooks/use-modal'
import { useShop } from '@/hooks/use-shop'
import { useToast } from '@/hooks/use-toast'
import { upperFirst } from '@/utils/upper-first'
import { modalAtom } from '@/utils/stores/modal'
import { LANGUAGES } from '@/data/languages'
import {
  createUser,
  updateUserDataForAppAdmin,
  updateUserDataForOrgAdmin,
  assignUserToApplications,
  updateOrgAdminPermission,
  updateReportViewerPermission,
  revokeOrgAdminPermissions,
  revokePermissions,
} from '@/api/users'
import { getApplications, getUserApplications } from '@/api/applications'
import { getRoles } from '@/api/roles'
import type { UserModel } from '@/types/models/user'
import type { UserInput } from '@/types/api'
import type { UserRoleModel } from '@/types/models/user'
import type { ModalAtomProps } from '@/types/store'
import type { ApplicationModel } from '@/types/models/application'
import type { RoleModel } from '@/types/models/role'
import type {
  PaginatedQueryResponse,
  AssignUserToApplicationsInput,
  DeleteUserInput,
  UpdateAsReportUserInput,
} from '@/types/api'

const ConfirmDialog = lazy(() =>
  import('@/components/confirm-dialog').then(({ ConfirmDialog }) => ({
    default: ConfirmDialog,
  }))
)

const roleName = (roleId: number | null): string => {
  switch (roleId) {
    case 1:
      return 'org admin'
    case 2:
      return 'app admin'
    case 3:
      return 'report viewer'
    default:
      return 'unknown'
  }
}

const schema = z
  .object({
    firstName: z.string().min(1, 'First Name is required.'),
    lastName: z.string().min(1, 'Last Name is required.'),
    email: z.string().email({ message: 'Invalid email address.' }),
    phoneNumber: z.string(),
    activated: z.boolean(),
    pending: z.boolean(),
    locale: z.string(),
    role: z.number().nullable(),
    applications: z.array(z.string()).optional(),
  })
  .superRefine((arg, ctx) => {
    if (
      arg?.role &&
      arg?.role > 1 &&
      arg.applications &&
      arg.applications.length === 0
    ) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Applications are required.',
        path: ['applications'],
      })
    }

    return z.NEVER
  })

type FormInputProps = z.infer<typeof schema>

interface UserExtendedModel extends UserRoleModel {
  pending: boolean
  activated: boolean
  locale: string
  name: string
  phoneNumber: string
  orgAdmin: boolean
  hasRole: boolean
  scopeNames: string[]
  scopes: {
    role_id: number
    name: string
    application: string
  }[]
}

export const Edit = memo(() => {
  const modal = useModal()
  const toast = useToast()
  const queryClient = useQueryClient()
  const { onClose } = useModalContext()
  const { organizationId, applicationId } = useOrganization()
  const { can } = useAbility()
  const { postShopUserMutation, deleteShopUserMutation } = useShop()

  const [applicationOptions, setApplicationOptions] = useState<
    BaseOption<string>[]
  >([])

  const {
    config: { title, data },
  } = useAtomValue<ModalAtomProps<UserExtendedModel>>(modalAtom)

  const isNew = useMemo<boolean>(() => !data?.id, [data])

  const userId = useMemo<Maybe<string>>(
    () => data?.tina_user_id ?? null,
    [data]
  )

  const currentAppRoleName = useMemo<string>(() => {
    const matchedRole = data?.scopes?.find(
      ({ application }) => application === applicationId
    )
    return matchedRole ? matchedRole.name : 'No Permissions'
  }, [data, applicationId])

  const {
    reset,
    control,
    setValue,
    handleSubmit,
    formState: { isValid, isSubmitting },
  } = useForm({
    mode: 'onTouched',
    resolver: zodResolver(schema),
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      phoneNumber: '',
      activated: false,
      pending: false,
      locale: 'en-us',
      role: 2, // application admin
      applications: [] as string[],
    },
  })

  const isActivated = useWatch({
    name: 'activated',
    control,
  })

  const isPending = useWatch({
    name: 'pending',
    control,
  })

  const roleId = useWatch({
    name: 'role',
    control,
  })

  const { mutateAsync: createUserMutation } = useMutation<
    UserModel,
    Error,
    {
      organizationId: string
      input: Partial<UserInput>
    }
  >({
    mutationFn: ({ organizationId, input }) =>
      createUser({ organizationId, input }),
    onSuccess: () => {
      const cache = ['GetRoleUsers', 'GetUsers']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const { mutateAsync: updateOrgAdminPermissionMutation } = useMutation<
    UserModel,
    Error,
    {
      organizationId: string
      userId: string
      inviteNotification: boolean
    }
  >({
    mutationFn: ({ organizationId, userId, inviteNotification }) =>
      updateOrgAdminPermission({
        organizationId,
        userId,
        inviteNotification,
      }),
    onSuccess: () => {
      const cache = ['GetRoleUsers', 'GetUsers', 'GetUser']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const { mutateAsync: updateUserDataForAppAdminMutation } = useMutation<
    UserModel,
    Error,
    {
      organizationId: string
      applicationId: string
      userId: string
      input: Partial<UserInput>
    }
  >({
    mutationFn: ({ organizationId, applicationId, userId, input }) =>
      updateUserDataForAppAdmin({
        organizationId,
        applicationId,
        userId,
        input,
      }),
    onSuccess: () => {
      const cache = ['GetRoleUsers', 'GetUsers', 'GetUser']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const { mutateAsync: updateUserDataForOrgAdminMutation } = useMutation<
    UserModel,
    Error,
    {
      organizationId: string
      userId: string
      input: Partial<UserInput>
    }
  >({
    mutationFn: ({ organizationId, userId, input }) =>
      updateUserDataForOrgAdmin({ organizationId, userId, input }),
    onSuccess: () => {
      const cache = ['GetRoleUsers', 'GetUsers', 'GetUser']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const { mutateAsync: assignUserToApplicationsMutation } = useMutation<
    any,
    Error,
    AssignUserToApplicationsInput
  >({
    mutationFn: ({ organizationId, userId, inviteNotification, apps }) =>
      assignUserToApplications({
        organizationId,
        userId,
        inviteNotification,
        apps,
      }),
    onSuccess: () => {
      const cache = ['GetRoleUsers', 'GetUsers', 'GetApplications']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const { mutateAsync: revokeOrgAdminPermissionsMutation } = useMutation<
    boolean,
    Error,
    {
      organizationId: string
      userId: string
    }
  >({
    mutationFn: (input) => revokeOrgAdminPermissions(input),
    onSuccess: () => {
      const cache = ['GetRoleUsers', 'GetUsers', 'GetUser']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const { mutateAsync: revokePermissionsMutation } = useMutation<
    boolean,
    Error,
    DeleteUserInput
  >({
    mutationFn: (input) => revokePermissions(input),
    onSuccess: () => {
      const cache = ['GetRoleUsers', 'GetUsers', 'GetUser']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const { mutateAsync: updateReportViewerPermissionMutation } = useMutation<
    UserModel,
    Error,
    UpdateAsReportUserInput
  >({
    mutationFn: (input) => updateReportViewerPermission(input),
    onSuccess: () => {
      const cache = ['GetRoleUsers', 'GetUsers', 'GetUser']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const { data: applications } = useQuery<
    PaginatedQueryResponse<ApplicationModel> | null,
    Error,
    BaseOption<string>[]
  >({
    queryKey: ['GetApplications', organizationId],
    queryFn: ({ signal }) =>
      organizationId
        ? getApplications({
            organizationId,
            signal,
          })
        : null,
    enabled: !!organizationId,
    select: useCallback(
      (data: PaginatedQueryResponse<ApplicationModel> | null) =>
        (data?.rows ?? []).map(({ id, name }) => ({
          label: name,
          value: id,
        })),
      []
    ),
  })

  const { data: userApplications } = useQuery<
    PaginatedQueryResponse<ApplicationModel> | null,
    Error,
    BaseOption<string>[]
  >({
    queryKey: ['GetUserApplications', organizationId, userId],
    queryFn: ({ signal }) =>
      organizationId && userId
        ? getUserApplications({
            organizationId,
            userId,
            signal,
          })
        : null,
    enabled: !!organizationId && !!userId && can(['edit', 'applications']),
    select: useCallback(
      (data: PaginatedQueryResponse<ApplicationModel> | null) =>
        (data?.rows ?? []).map(({ id, name }) => ({
          label: name,
          value: id,
        })),
      []
    ),
  })

  useEffect(() => {
    if (!applications) {
      return
    }

    setApplicationOptions(applications)

    if (isNew || !userApplications) {
      const app = head(applications)
      if (app) {
        setValue('applications', [app.value])
      }
    } else {
      setValue(
        'applications',
        userApplications.map(({ value }) => value)
      )
    }
  }, [setValue, userApplications, applications, isNew])

  const { data: roles } = useQuery<RoleModel[], Error>({
    queryKey: ['GetRoles'],
    queryFn: ({ signal }) =>
      getRoles({
        signal,
      }),
  })

  useEffect(() => {
    if (!data?.id) {
      return
    }

    // Match the current role for the current application
    const currentRole = data.scopes.find(
      ({ application }) => application === applicationId
    )

    // If the user is an org admin, set the role to org admin
    const role = currentRole ? currentRole.role_id : data.orgAdmin ? 1 : 2

    reset((prev) => ({
      ...prev,
      firstName: data.firstName ?? '',
      lastName: data.lastName ?? '',
      email: data.email,
      phoneNumber: data.phoneNumber,
      activated: data.activated,
      pending: data.pending,
      locale: data.locale,
      role,
    }))
  }, [data, applicationId, reset])

  const canEditOrganization = useMemo(
    () => can(['edit', 'organizations']),
    [can]
  )

  const canEditApplication = useMemo(() => can(['edit', 'applications']), [can])

  const canRevokePermissions = useMemo<boolean>(
    () =>
      can(['edit', 'organizations']) && (!!data?.hasRole || !!data?.orgAdmin),
    [can, data]
  )

  const showApplications = useMemo<boolean>(() => {
    return can(['edit', 'applications']) && roleId !== 1
  }, [can, roleId])

  const roleOptions = useMemo(
    () =>
      (roles ?? [])
        .map(({ id, name }) => ({
          label: name.split('_').map(upperFirst).join(' '),
          value: id,
        }))
        .filter(({ value }) => canEditOrganization || value !== 1),
    [roles, canEditOrganization]
  )

  const onSelectAll = useCallback((): void => {
    if (applicationOptions?.length === 0) {
      return
    }
    setValue(
      'applications',
      applicationOptions.map(({ value }) => value) ?? [],
      { shouldValidate: true }
    )
  }, [applicationOptions, setValue])

  const onRemoveAll = (): void => {
    setValue('applications', [], { shouldValidate: true })
  }

  const revokeUserPermissions = async (userId: string): Promise<boolean> => {
    if (!(data && organizationId && applicationId)) {
      return false
    }

    try {
      if (data?.orgAdmin) {
        return await revokeOrgAdminPermissionsMutation({
          organizationId,
          userId,
        })
      }

      // never fails because it returns full axios response
      const result = await pMap(
        data.scopeNames, // revoke from user assigned apps
        async (appId) =>
          await revokePermissionsMutation({
            organizationId,
            applicationId: appId,
            userId,
          }),
        { concurrency: 1 }
      )

      return result.some((res) => res)
    } catch (error) {
      Sentry.captureException(error)
      toast({
        status: 'error',
        msg: 'Unable to downgrade user',
      })
      return false
    }
  }

  const onRevokePermissions = (): void => {
    if (!data?.tina_user_id) {
      return
    }
    modal({
      size: 'md',
      component: <ConfirmDialog />,
      config: {
        title: 'Revoke permissions?',
        description: `Are you sure you want to revoke admin permissions for "${data.email}"?`,
        confirmLabel: 'Revoke Permissions',
        onCallback: async () => {
          if (!(organizationId && applicationId)) {
            return
          }
          try {
            await revokeUserPermissions(data.tina_user_id)
            await deleteShopUserMutation({
              organizationId,
              applicationId,
              email: data.email,
            })
            toast({
              status: 'success',
              msg: `"${data.email}" has had its permissions revoked.`,
            })
          } catch (error: unknown) {
            Sentry.captureException(error)
            toast({
              status: 'error',
              msg: `Unable to revoke permissions for "${data.name}".`,
            })
          }
        },
      },
    })
  }

  const create = async (values: FormInputProps): Promise<boolean> => {
    if (!(organizationId && applicationId)) {
      return false
    }

    const isOrgAdminRole = values.role === 1
    const isAppAdminRole = values.role === 2
    const isReportViewerRole = values.role === 3

    const apps = Array.from(
      new Set(
        (values?.applications ?? []).filter((app): app is string =>
          is(String, app)
        )
      )
    )

    if (apps.length === 0 && !isOrgAdminRole) {
      toast({
        status: 'error',
        msg: 'You need to select an application.',
      })
      return false
    }

    const input = {
      firstName: values.firstName,
      lastName: values.lastName,
      email: values.email,
      attributes: {
        phoneNumber: values.phoneNumber,
        locale: values.locale,
      },
    } satisfies Partial<UserInput>

    try {
      const res = await createUserMutation({
        organizationId,
        input,
      })

      if (!res?.tina_user_id) {
        throw new Error('Unable to create user.')
      }

      if (isOrgAdminRole) {
        await updateOrgAdminPermissionMutation({
          organizationId,
          userId: res.tina_user_id,
          inviteNotification: false,
        })

        await assignUserToApplicationsMutation({
          organizationId,
          userId: res.tina_user_id,
          inviteNotification: false,
          apps: [],
        })
      }

      if (isAppAdminRole) {
        await assignUserToApplicationsMutation({
          organizationId,
          userId: res.tina_user_id,
          inviteNotification: false,
          apps,
        })
      }

      if (isReportViewerRole) {
        for (const app of apps) {
          await updateReportViewerPermissionMutation({
            organizationId,
            applicationId: app,
            userId: res.tina_user_id,
            inviteNotification: false,
          })
        }
      }

      await postShopUserMutation({
        organizationId,
        applicationId,
        userId: res.tina_user_id,
        email: values.email,
        firstName: values.firstName,
        lastName: values.lastName,
        phoneNumber: values.phoneNumber,
      })

      toast({
        status: 'success',
        msg: `User has been created as ${roleName(values.role)} for ${isOrgAdminRole ? 'all' : apps.length} application(s).`,
      })

      return true
    } catch (error: unknown) {
      Sentry.captureException(error)
      toast({
        status: 'error',
        msg: 'Unable to create user.',
      })
      return false
    } finally {
      onClose()
    }
  }

  const updateUserData = async ({
    userId,
    input,
  }: {
    userId: string
    input: Partial<UserInput>
  }): Promise<UserModel | undefined> => {
    try {
      if (!(organizationId && applicationId)) {
        return
      }
      if (canEditOrganization) {
        return await updateUserDataForOrgAdminMutation({
          organizationId,
          userId,
          input,
        })
      }
      return await updateUserDataForAppAdminMutation({
        organizationId,
        applicationId,
        userId,
        input,
      })
    } catch (error) {
      Sentry.captureException(error)
      return Promise.reject('Unable to update user.')
    }
  }

  const update = async (values: FormInputProps): Promise<boolean> => {
    if (!(organizationId && applicationId && data?.tina_user_id)) {
      return false
    }

    const userId = data.tina_user_id
    const isOrgAdminRole = values.role === 1
    const isAppAdminRole = values.role === 2
    const isReportViewerRole = values.role === 3

    const apps = Array.from(
      new Set(
        (values?.applications ?? []).filter((app): app is string =>
          is(String, app)
        )
      )
    )

    if (apps.length === 0 && !isOrgAdminRole) {
      toast({
        status: 'error',
        msg: 'You need to select an application.',
      })
      return false
    }

    const input = {
      firstName: values.firstName,
      lastName: values.lastName,
      attributes: {
        phoneNumber: values.phoneNumber,
        locale: values.locale,
      },
    } satisfies Partial<UserInput>

    try {
      await updateUserData({
        userId,
        input,
      })

      if (isOrgAdminRole) {
        await updateOrgAdminPermissionMutation({
          organizationId,
          userId,
          inviteNotification: false,
        })

        await assignUserToApplicationsMutation({
          organizationId,
          userId,
          inviteNotification: false,
          apps: [],
        })
      }

      try {
        // wild fix for the role not updating
        await revokeUserPermissions(userId)
      } catch {
        // ignore
      }

      if (isAppAdminRole) {
        await assignUserToApplicationsMutation({
          organizationId,
          userId,
          inviteNotification: false,
          apps,
        })
      }

      if (isReportViewerRole) {
        for (const app of apps) {
          await updateReportViewerPermissionMutation({
            organizationId,
            applicationId: app,
            userId,
            inviteNotification: false,
          })
        }
      }

      await postShopUserMutation({
        organizationId,
        applicationId,
        userId,
        email: values.email,
        firstName: values.firstName,
        lastName: values.lastName,
        phoneNumber: values.phoneNumber,
      })

      toast({
        status: 'success',
        msg: `User has been updated as ${roleName(values.role)} for ${isOrgAdminRole ? 'all' : apps.length} application(s).`,
      })

      return true
    } catch (error: unknown) {
      Sentry.captureException(error)
      toast({
        status: 'error',
        msg: 'Unable to update user.',
      })
      return false
    } finally {
      onClose()
    }
  }

  const onSubmit = async (values: FormInputProps): Promise<boolean> =>
    data?.id ? await update(values) : await create(values)

  return (
    <Box as="form" noValidate onSubmit={handleSubmit(onSubmit)}>
      <ModalHeader>
        <HStack>
          <Box>{title}</Box>
          {data?.id && <ModalHeaderBadge text={currentAppRoleName ?? ''} />}
        </HStack>
      </ModalHeader>
      <ModalCloseButton />
      <ModalBody
        sx={{
          maxH: '70vh',
          overflow: 'auto',
        }}
      >
        <Grid gap={4} templateColumns={{ base: '1fr', lg: 'repeat(2, 1fr)' }}>
          <InputField
            autoComplete="given-name"
            name="firstName"
            label="First Name"
            control={control}
            isRequired
            isDisabled={isActivated}
          />
          <InputField
            autoComplete="family-name"
            name="lastName"
            label="Last Name"
            control={control}
            isRequired
            isDisabled={isActivated}
          />
          <InputField
            autoComplete="email"
            name="email"
            type="email"
            label="Email"
            control={control}
            isRequired
            isDisabled={isActivated || isPending}
          />
          <InputField
            autoComplete="tel"
            name="phoneNumber"
            label="Phone Number"
            control={control}
            isDisabled={isActivated}
          />
          <SelectField
            name="locale"
            label="Language"
            options={LANGUAGES}
            control={control}
            isRequired
            menuPosition="fixed"
          />
          <SelectField
            name="role"
            label="Role"
            options={roleOptions}
            isLoading={roleOptions.length === 0}
            isOptionDisabled={(option: any) => option.disabled}
            control={control}
            isRequired
            menuPosition="fixed"
            onChange={({ value }: BaseOption<number>) => {
              setValue('role', value)
              onRemoveAll()
            }}
          />
          {showApplications ? (
            <Box gridColumn="1 / -1">
              <SelectField
                name="applications"
                label="Applications"
                control={control}
                options={applicationOptions}
                isLoading={applicationOptions.length === 0}
                isMulti
                menuPosition="fixed"
                render={
                  <HStack>
                    <Button
                      size="xs"
                      variant="ghost"
                      colorScheme="secondary"
                      onClick={onSelectAll}
                    >
                      Select All
                    </Button>
                    <Button
                      size="xs"
                      variant="ghost"
                      colorScheme="red"
                      onClick={onRemoveAll}
                    >
                      Remove All
                    </Button>
                  </HStack>
                }
              />
            </Box>
          ) : null}
        </Grid>
      </ModalBody>
      <ModalFooter justifyContent="space-between">
        <Box>
          {canRevokePermissions && (
            <Button
              type="button"
              aria-label="Revoke Permissions"
              colorScheme="red"
              variant="ghost"
              onClick={onRevokePermissions}
            >
              Revoke Permissions
            </Button>
          )}
        </Box>
        <HStack>
          <Button
            type="button"
            aria-label="Cancel"
            variant="ghost"
            colorScheme="gray"
            onClick={onClose}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            aria-label="Save"
            colorScheme="green"
            minW={120}
            isLoading={isSubmitting}
            isDisabled={!isValid || isSubmitting || !canEditApplication}
            loadingText="Saving"
          >
            Save
          </Button>
        </HStack>
      </ModalFooter>
    </Box>
  )
})
