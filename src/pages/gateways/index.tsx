import * as Sentry from '@sentry/react'
import {
  lazy,
  memo,
  Suspense,
  useCallback,
  useMemo,
  useDeferredValue,
} from 'react'
import { Outlet, useNavigate } from 'react-router'
import { Helmet } from 'react-helmet-async'
import { useQuery, useMutation } from '@tanstack/react-query'
import { useMountEffect } from '@react-hookz/web'
import { useAtom } from 'jotai'
import pMap from 'p-map'
import {
  Box,
  Button,
  ButtonGroup,
  Flex,
  HStack,
  Menu,
  MenuButton,
  MenuItem,
  MenuList,
  useColorModeValue,
} from '@chakra-ui/react'
import { ChevronDownIcon, SearchIcon } from 'lucide-react'
import { Title } from '@/components/title'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { Clipboard } from '@/components/data-table/clipboard'
import { DataTable } from '@/components/data-table/data-table'
import { useOrganization } from '@/hooks/use-organization'
import { useToast } from '@/hooks/use-toast'
import { useModal } from '@/hooks/use-modal'
import { pluralize } from '@/utils/pluralize'
import { metaTitle } from '@/utils/meta-title'
import { formatDate } from '@/utils/date/format-date'
import { tableAtom } from '@/utils/stores/table'
import { getNetworks } from '@/api/networks'
import { getGateways, createGatewayCommand } from '@/api/gateways'
import type { GatewayModel } from '@/types/models/gateway'
import type { GetResponse } from '@/types/api'
import type { ColumnProps, ActionProps } from '@/types/table'
import type { RegistryModel } from '@/types/models/registry'
import type { NetworkModel } from '@/types/models/network'

const ConfirmDialog = lazy(() =>
  import('@/components/confirm-dialog').then(({ ConfirmDialog }) => ({
    default: ConfirmDialog,
  }))
)

const BulkSearch = lazy(() =>
  import('@/pages/device-registry/bulk-search').then(({ BulkSearch }) => ({
    default: BulkSearch,
  }))
)

const CommandUpdateBulkModal = lazy(() =>
  import('@/pages/gateways/command-update-bulk').then(
    ({ CommandUpdateBulkModal }) => ({
      default: CommandUpdateBulkModal,
    })
  )
)

interface GatewayAttribute {
  name: string
  value: string
}

interface Props {
  attributes?: GatewayAttribute[]
}

const LastSeenCell = ({ attributes }: Props) => {
  const lastSeen = useMemo(() => {
    const match = (attributes ?? []).find(
      ({ name }: GatewayAttribute) => name === 'last_seen_at'
    )
    return match ? formatDate(match?.value, 'PPppp') : null
  }, [attributes])

  if (!lastSeen) {
    return '-'
  }

  return <Box noOfLines={1}>{lastSeen}</Box>
}

const collator = new Intl.Collator('en-US')
const numberFormat = new Intl.NumberFormat('en-US')

const Gateways = memo(() => {
  const heading = 'Gateways'
  const tableName = 'gateways'
  const toast = useToast()
  const modal = useModal()
  const navigate = useNavigate()
  const { applicationId, organizationId } = useOrganization()

  const countColor = useColorModeValue('gray.800', 'whiteAlpha.900')
  const countColorDisabled = useColorModeValue('gray.400', 'whiteAlpha.600')

  const [{ limit, currPage, filter, filters, checkedIds }, setTable] = useAtom(
    tableAtom(tableName)
  )

  const deferredFilter = useDeferredValue(filter)

  const checkedState = useMemo<string>(() => {
    const num = numberFormat.format(checkedIds.length)
    const str = pluralize(checkedIds.length, ' device')
    return [num, str].join(' ')
  }, [checkedIds.length])

  useMountEffect(() => {
    setTable((prev) => ({
      ...prev,
      hiddenColumns: ['sku', 'paired_at'],
    }))
  })

  const { data } = useQuery<
    Omit<GetResponse<GatewayModel>, 'page'> | null,
    Error
  >({
    queryKey: [
      'GetGateways',
      organizationId,
      applicationId,
      limit,
      currPage,
      deferredFilter,
    ],
    queryFn: ({ signal }) =>
      organizationId && applicationId
        ? getGateways({
            organizationId,
            applicationId,
            limit,
            page: currPage,
            filter: deferredFilter,
            signal,
          })
        : null,
    enabled: !!organizationId || !!applicationId,
  })

  const { mutateAsync: sendCommand } = useMutation<any, Error, any>({
    mutationFn: ({
      organizationId,
      applicationId,
      hardwareId,
      command,
      options,
    }) =>
      createGatewayCommand({
        organizationId,
        applicationId,
        hardwareId,
        command,
        options,
      }),
  })

  const records = useMemo<Omit<GetResponse<GatewayModel>, 'page'>>(
    () =>
      data ?? {
        count: 0,
        rows: [],
      },
    [data]
  )

  const { data: networkOptions } = useQuery<
    NetworkModel[],
    Error,
    BaseOption[]
  >({
    queryKey: ['GetNetworks'],
    queryFn: ({ signal }) =>
      getNetworks({
        signal,
      }),
    select: useCallback(
      (data: NetworkModel[]) =>
        Array.from(
          data.reduce(
            (acc: Set<BaseOption<string>>, { id, name }: NetworkModel) => {
              acc.add({
                label: name,
                value: id,
              })
              return acc
            },
            new Set<BaseOption<string>>()
          )
        ).sort((a, b) => collator.compare(a.label, b.label)),
      []
    ),
  })

  const onReboot = useCallback(async () => {
    modal({
      size: 'md',
      component: <ConfirmDialog />,
      config: {
        title: 'Bulk Reboot',
        description: `Are you sure you want to restart ${checkedIds.length} gateway(s)?`,
        confirmLabel: 'Confirm',
        onCallback: async () => {
          const result = await pMap(
            checkedIds,
            async (hardwareId) => {
              try {
                await sendCommand({
                  organizationId,
                  applicationId,
                  hardwareId,
                  command: 'reboot',
                })
                return true
              } catch (error: unknown) {
                Sentry.captureException(error)
                return false
              }
            },
            { concurrency: 1 }
          )

          const success = result.map((r) => r === true)
          const failure = result.map((r) => r === false)

          if (failure.length === 0) {
            toast({
              status: 'success',
              msg: `${success.length} reboot command(s) sent.`,
            })
          } else {
            toast({
              status: 'error',
              msg: `${failure.length} reboot commands failed.`,
            })
          }
        },
      },
    })
  }, [modal, toast, organizationId, applicationId, sendCommand, checkedIds])

  const onUpdate = useCallback(() => {
    const hardwareIds = (records.rows as unknown as RegistryModel[])
      .filter(({ id }) => checkedIds.includes(id))
      .map(({ hardware_id }) => hardware_id)

    modal({
      size: 'md',
      component: <CommandUpdateBulkModal />,
      config: {
        data: hardwareIds,
        onCallback: () => {},
      },
    })
  }, [checkedIds, modal, records.rows])

  const networkName = useCallback(
    (id: string): string => {
      const network = (networkOptions ?? []).find(({ value }) => id === value)
      return network?.label ?? id
    },
    [networkOptions]
  )

  const isGateway = useCallback((row?: RegistryModel): boolean => {
    if (!row) {
      return false
    }
    return (
      row?.device_type?.category.toLowerCase() === 'gateway' &&
      [
        'iotinabox.chirpstackio',
        'iotinabox.csiofrance',
        'iotinabox.csioaustralia',
      ].includes(row.network)
    )
  }, [])

  const onClear = useCallback((): void => {
    setTable((prev) => ({
      ...prev,
      currPage: 0,
      filter: undefined,
      filters: [],
      checkedIds: [],
    }))
  }, [setTable])

  const onDetails = useCallback(
    (id?: string): void => {
      if (!id) {
        return
      }
      navigate(`/manage/gateways/${id}/details`)
    },
    [navigate]
  )

  const onBulkSearch = useCallback((): void => {
    modal({
      size: '4xl',
      component: <BulkSearch />,
      config: {
        onCallback: (ids: string[]) => {
          if (ids.length === 0) {
            return
          }
          setTable((prev) => ({
            ...prev,
            currPage: 0,
            checkedIds: [],
            filter: `hardware_id in ${ids.join(' ')}`,
            filters: [],
          }))
        },
      },
    })
  }, [modal, setTable])

  const columns = useMemo<ColumnProps<RegistryModel>[]>(
    () => [
      {
        name: 'Hardware ID',
        id: 'hardware_id',
        filter: {
          type: 'input',
          condition: 'like',
        },
        canHide: false,
        cell: (row) => (
          <Clipboard
            link={
              isGateway(row)
                ? `/manage/gateways/${row.hardware_id}/details`
                : undefined
            }
          >
            {row.hardware_id}
          </Clipboard>
        ),
      },
      {
        name: 'Device Template',
        id: 'device_type_id',
        filter: {
          type: 'select-type',
          condition: 'eq',
          additionalQuery: 'category eq gateway',
        },
        canHide: true,
        cell: ({ device_type }) =>
          device_type?.name && <Clipboard>{device_type?.name}</Clipboard>,
      },
      {
        name: 'Network',
        id: 'network',
        filter: {
          type: 'select',
          condition: 'eq',
          options: networkOptions,
        },
        canHide: true,
        cell: ({ network }) => <Clipboard>{networkName(network)}</Clipboard>,
      },
      {
        name: 'SKU',
        id: 'sku',
        filter: {
          type: 'input',
          condition: 'like',
        },
        canHide: true,
        cell: ({ sku }) => sku && <Clipboard>{sku}</Clipboard>,
      },
      {
        name: 'Paired',
        id: 'paired_at',
        canHide: true,
        filter: {
          type: 'date',
          condition: 'between',
        },
        cell: ({ paired_at }) =>
          paired_at && <Box noOfLines={1}>{formatDate(paired_at)}</Box>,
      },
      {
        name: 'Last Seen',
        id: 'last_seen_at',
        canHide: true,
        cell: ({ attributes }) => <LastSeenCell attributes={attributes} />,
      },
    ],
    [networkName, isGateway, networkOptions]
  )

  const actions = useMemo<ActionProps<RegistryModel>[]>(
    () => [
      {
        label: 'Gateway Details',
        onClick: (row) => onDetails(row?.hardware_id),
        canView: (row) => isGateway(row),
      },
    ],
    [onDetails, isGateway]
  )

  return (
    <>
      <Helmet>
        <title>{metaTitle([heading])}</title>
      </Helmet>
      <Box px={4}>
        <Suspense fallback={null}>
          <DataTable
            isCheckable
            tableName={tableName}
            data={records}
            columns={columns}
            actions={actions}
            header={() => (
              <Flex pl={2} pr={3} align="center" justify="space-between">
                <Title title={heading} count={data?.count} suffix="Record" />
                <Suspense fallback={null}>
                  <HStack>
                    <ButtonGroup
                      isAttached
                      isDisabled={checkedIds.length === 0}
                      sx={{
                        border: 0,
                        borderRadius: 'base',
                        bg: 'gray.50',
                        _dark: {
                          bg: 'transparent',
                        },
                      }}
                    >
                      <Button
                        variant="unstyled"
                        isDisabled
                        sx={{
                          p: 0,
                          minW: 150,
                          border: 0,
                          fontWeight: 'medium',
                          color:
                            checkedIds.length === 0
                              ? countColorDisabled
                              : countColor,
                          bg: 'gray.50',
                          _dark: {
                            bg: 'transparent',
                          },
                          _disabled: {
                            opacity: 1,
                            cursor: 'default',
                          },
                        }}
                      >
                        {checkedState}
                      </Button>
                      <Button
                        variant="link"
                        colorScheme="primary"
                        onClick={onClear}
                        isDisabled={
                          checkedIds.length === 0 &&
                          filters.length === 0 &&
                          !filter
                        }
                        sx={{
                          px: 4,
                          bg: 'gray.50',
                          _dark: {
                            bg: 'transparent',
                          },
                          _hover: {
                            bg: 'gray.50',
                            _dark: {
                              bg: 'transparent',
                            },
                          },
                        }}
                      >
                        Clear
                      </Button>
                      <Menu>
                        <MenuButton
                          colorScheme="primary"
                          as={Button}
                          rightIcon={<ChevronDownIcon />}
                        >
                          Bulk Commands
                        </MenuButton>
                        <MenuList
                          sx={{
                            fontSize: 'md',
                            zIndex: 'dropdown',
                            p: 2,
                          }}
                        >
                          <MenuItem onClick={onReboot} sx={{ py: 3 }}>
                            {`Reboot Gateway${checkedIds.length > 1 ? 's' : ''}`}
                          </MenuItem>
                          <MenuItem onClick={onUpdate} sx={{ py: 3 }}>
                            {`Update Gateway${checkedIds.length > 1 ? 's' : ''}`}
                          </MenuItem>
                        </MenuList>
                      </Menu>
                    </ButtonGroup>
                    <Button
                      type="button"
                      aria-label="Bulk Search"
                      leftIcon={<SearchIcon size={16} />}
                      colorScheme="primary"
                      boxShadow="sm"
                      onClick={onBulkSearch}
                    >
                      Bulk Search
                    </Button>
                  </HStack>
                </Suspense>
              </Flex>
            )}
          />
        </Suspense>
      </Box>
      <Outlet />
    </>
  )
})

export async function loader() {
  const scopes = ['edit:applications']

  const { checkLoaderPermissions } = await import('@/utils/loader-auth')
  await checkLoaderPermissions(scopes)

  return {
    title: 'Gateways',
    scopes,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <Gateways />
}
