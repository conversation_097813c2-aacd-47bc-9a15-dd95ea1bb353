import * as Sentry from '@sentry/react'
import { lazy, useMemo, useC<PERSON>back, memo, Suspense } from 'react'
import { Helm<PERSON> } from 'react-helmet-async'
import { useParams, useNavigate, Outlet } from 'react-router'
import { useSuspenseQuery, useMutation } from '@tanstack/react-query'
import {
  Box,
  Button,
  ButtonGroup,
  Flex,
  HStack,
  Menu,
  MenuButton,
  MenuItem,
  MenuList,
  SimpleGrid,
  GridItem,
  Portal,
} from '@chakra-ui/react'
import { ChevronDownIcon } from 'lucide-react'
import { Title } from '@/components/title'
import { BackButton } from '@/components/back-button'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { useOrganization } from '@/hooks/use-organization'
import { metaTitle } from '@/utils/meta-title'
import { useToast } from '@/hooks/use-toast'
import { useModal } from '@/hooks/use-modal'
import { getGateway, createGatewayCommand } from '@/api/gateways'
import { NoMeta } from '@/features/gateway/no-meta'

const ConfirmDialog = lazy(() =>
  import('@/components/confirm-dialog').then(({ ConfirmDialog }) => ({
    default: ConfirmDialog,
  }))
)

const Overview = lazy(() =>
  import('@/features/gateway/overview').then(({ Overview }) => ({
    default: Overview,
  }))
)

const Metrics = lazy(() =>
  import('@/features/gateway/metrics').then(({ Metrics }) => ({
    default: Metrics,
  }))
)

const CommandUpdateModal = lazy(() =>
  import('@/pages/gateways/command-update').then(({ CommandUpdateModal }) => ({
    default: CommandUpdateModal,
  }))
)

const Details = memo(() => {
  const toast = useToast()
  const modal = useModal()
  const params = useParams()
  const navigate = useNavigate()
  const { organizationId, applicationId } = useOrganization()
  const hardwareId = useMemo<string | undefined>(() => params?.id, [params])
  const heading = useMemo(() => `Gateway: ${hardwareId}`, [hardwareId])

  const { data, isFetched } = useSuspenseQuery({
    queryKey: ['GetGateway', organizationId, applicationId, hardwareId],
    queryFn: ({ signal }) =>
      organizationId && applicationId && hardwareId
        ? getGateway({
            organizationId,
            applicationId,
            hardwareId,
            signal,
          })
        : null,
  })

  const { mutateAsync: sendCommand } = useMutation<any, Error, any>({
    mutationFn: ({
      organizationId,
      applicationId,
      hardwareId,
      command,
      options,
    }) =>
      createGatewayCommand({
        organizationId,
        applicationId,
        hardwareId,
        command,
        options,
      }),
  })

  const onGoBack = useCallback((): void => {
    navigate('/manage/gateways', {
      viewTransition: true,
    })
  }, [navigate])

  const hasMeta = useMemo(
    () => isFetched && Object.keys(data?.gateway?.metadata ?? {}).length > 0,
    [isFetched, data]
  )

  const onReboot = useCallback(() => {
    modal({
      size: 'md',
      component: <ConfirmDialog />,
      config: {
        title: 'Reboot Gateway',
        description: 'Are you sure you want to restart the gateway?',
        confirmLabel: 'Confirm',
        onCallback: async () => {
          try {
            await sendCommand({
              organizationId,
              applicationId,
              hardwareId,
              command: 'reboot',
            })
            toast({
              status: 'success',
              msg: 'Reboot command sent.',
            })
          } catch (error: unknown) {
            Sentry.captureException(error)
            toast({
              status: 'error',
              msg: 'Unable to send command.',
            })
          }
        },
      },
    })
  }, [modal, toast, organizationId, applicationId, hardwareId, sendCommand])

  const onUpdate = useCallback(async () => {
    modal({
      size: 'lg',
      component: <CommandUpdateModal />,
      config: {
        data: { hardwareId },
        onCallback: () => {},
      },
    })
  }, [modal, hardwareId])

  const onRemoteShell = useCallback(() => {
    navigate(`/manage/gateways/${hardwareId}/details/remote-shell`)
  }, [navigate, hardwareId])

  return (
    <>
      <Helmet>
        <title>{metaTitle([heading])}</title>
      </Helmet>
      <Flex align="center" justify="space-between" p={4}>
        <HStack>
          <BackButton onClick={onGoBack} />
          <Title title={heading} />
        </HStack>
        <ButtonGroup
          isAttached
          sx={{
            position: 'relative',
            border: 0,
            borderRadius: 'base',
            bg: 'gray.50',
            _dark: {
              bg: 'transparent',
            },
          }}
        >
          <Menu>
            <MenuButton
              colorScheme="primary"
              as={Button}
              rightIcon={<ChevronDownIcon />}
            >
              Commands
            </MenuButton>
            <Portal>
              <MenuList sx={{ p: 2 }}>
                <MenuItem onClick={onReboot}>Reboot Gateway</MenuItem>
                <MenuItem onClick={onUpdate}>Update Gateway</MenuItem>
                <MenuItem onClick={onRemoteShell}>Remote Shell</MenuItem>
              </MenuList>
            </Portal>
          </Menu>
        </ButtonGroup>
      </Flex>
      <Box px={4} pb={10}>
        <Box
          borderTop="1px solid"
          borderColor="gray.100"
          _dark={{
            borderColor: 'gray.700',
          }}
        >
          {hasMeta ? (
            <SimpleGrid
              columns={{ base: 1, lg: 2 }}
              gap={{ base: 4, lg: 8 }}
              minChildWidth="500px"
              p={2}
            >
              <GridItem>
                <Overview />
              </GridItem>
              <GridItem>
                <Metrics />
              </GridItem>
            </SimpleGrid>
          ) : (
            <NoMeta />
          )}
        </Box>
      </Box>
      <Suspense fallback={null}>
        <Outlet />
      </Suspense>
    </>
  )
})

export async function loader() {
  const scopes = ['edit:applications']

  const { checkLoaderPermissions } = await import('@/utils/loader-auth')
  await checkLoaderPermissions(scopes)

  return {
    title: 'Gateway Details',
    scopes,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <Details />
}
