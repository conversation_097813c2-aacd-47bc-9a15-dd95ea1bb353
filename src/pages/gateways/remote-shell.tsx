import { memo, useState, useRef, useEffect, useCallback } from 'react'
import { useNavigate, useParams } from 'react-router'
import { useQuery, useMutation } from '@tanstack/react-query'
import { base64ToString } from 'uint8array-extras'
import { isEmpty } from 'ramda'
import {
  Box,
  Flex,
  Grid,
  HStack,
  Spinner,
  Input,
  InputGroup,
  InputLeftElement,
  Drawer,
  DrawerBody,
  DrawerContent,
  DrawerOverlay,
  keyframes,
  Popover,
  PopoverTrigger,
  PopoverContent,
  PopoverBody,
  IconButton,
} from '@chakra-ui/react'
import {
  TerminalIcon,
  AlertOctagonIcon,
  RefreshCwIcon,
  InfoIcon,
  XIcon,
} from 'lucide-react'
import { useOrganization } from '@/hooks/use-organization'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { useAuth } from '@/contexts/use-auth'
import { getCurrentUser } from '@/api/users'
import type { CurrentUserModel } from '@/types/models/user'
import {
  createGatewayCommand,
  getCommandResponse,
  type CommandResponse,
  type CreateGatewayCommandResponse,
} from '@/api/gateways'

interface Command {
  id: string
  text: string
  timestamp: number
  status: 'pending' | 'success' | 'error'
  output?: string
}

const inputStyle = {
  px: 10,
  display: 'flex',
  h: '100%',
  border: 0,
  borderRadius: 0,
  bg: 'transparent',
  color: 'gray.800',
  _focus: {
    border: 'none',
    boxShadow: 'none',
  },
  _dark: {
    color: 'gray.50',
  },
}

const spin = keyframes`  
  from {transform: rotate(0deg);}   
  to {transform: rotate(360deg)} 
`

const spinAnimation = `${spin} 0.5s linear infinite`

const RemoteShell = memo(() => {
  const navigate = useNavigate()
  const params = useParams()
  const hardwareId = params.id
  const { user } = useAuth()
  const { organizationId, applicationId } = useOrganization()

  const terminalRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)
  const currentCmdIdRef = useRef<string | null>(null)

  const [input, setInput] = useState('')
  const [history, setHistory] = useState<Command[]>([])
  const [historyIndex, setHistoryIndex] = useState(-1)
  const [isProcessing, setIsProcessing] = useState(false)
  const [currentExecId, setCurrentExecId] = useState<string | null>(null)
  const [promptId, setPromptId] = useState<string | null>('user@machine:~$')

  const [spinningCommands, setSpinningCommands] = useState<
    Map<string, boolean>
  >(new Map())

  const [retryCount, setRetryCount] = useState(0)

  const { data: currentUser } = useQuery<CurrentUserModel | null, Error>({
    queryKey: ['GetCurrentUser', user?.token],
    queryFn: ({ signal }) =>
      getCurrentUser({
        signal,
      }),
  })

  useEffect(() => {
    if (currentUser?.firstName) {
      setPromptId(`${currentUser.firstName.toLowerCase()}@machine:~$`)
    }
  }, [user, currentUser])

  const { mutateAsync: sendCommand } = useMutation<
    CreateGatewayCommandResponse,
    Error,
    {
      organizationId: string
      applicationId: string
      hardwareId: string
      command: string
      options?: Dict
    }
  >({
    mutationFn: ({
      organizationId,
      applicationId,
      hardwareId,
      command,
      options,
    }) =>
      createGatewayCommand({
        organizationId,
        applicationId,
        hardwareId,
        command,
        options,
      }),
  })

  const { data: commandResponse, refetch } = useQuery<CommandResponse, Error>({
    queryKey: ['GetCommandResponse', currentExecId],
    queryFn: () =>
      getCommandResponse({
        organizationId: organizationId!,
        applicationId: applicationId!,
        hardwareId: hardwareId!,
        cmdId: currentExecId!,
      }),
    enabled:
      !!currentExecId && !!organizationId && !!applicationId && !!hardwareId,
    refetchIntervalInBackground: true,
    refetchInterval: (query) => {
      // Stop polling if we have stdout/stderr or after 5 retries
      if (retryCount >= 5) {
        setRetryCount(0)
        return false
      }
      return isEmpty(query.state.data?.stdout) &&
        isEmpty(query.state.data?.stderr)
        ? 1_500
        : false
    },
  })

  useEffect(() => {
    if (!commandResponse) {
      return
    }

    if (currentCmdIdRef.current) {
      setSpinningCommands((prev) => {
        const newMap = new Map(prev)
        newMap.delete(currentCmdIdRef.current!)
        return newMap
      })
    }

    if (!isEmpty(commandResponse.stderr)) {
      if (commandResponse.stderr === 'Exec ID not found') {
        return
      }
      setHistory((prev) =>
        prev.map((cmd) =>
          cmd.id === currentCmdIdRef.current
            ? {
                ...cmd,
                status: 'error',
                output: base64ToString(commandResponse.stderr),
              }
            : cmd
        )
      )
      setIsProcessing(false)
      setCurrentExecId(null)
      setRetryCount(0)
      return
    }

    if (!isEmpty(commandResponse.stdout)) {
      setHistory((prev) =>
        prev.map((cmd) =>
          cmd.id === currentExecId
            ? {
                ...cmd,
                status: 'success',
                output: base64ToString(commandResponse.stdout),
              }
            : cmd
        )
      )
      setIsProcessing(false)
      setCurrentExecId(null)
      setRetryCount(0)
    } else {
      setRetryCount((prev) => prev + 1)
    }
  }, [commandResponse])

  const executeCommand = useCallback(
    async (value: string) => {
      if (!(organizationId && applicationId && hardwareId && value.trim())) {
        return
      }

      setIsProcessing(true)

      try {
        const response = await sendCommand({
          organizationId,
          applicationId,
          hardwareId,
          command: 'remote-shell',
          options: {
            command_line: value,
          },
        })

        if (!response.exec_id) {
          setIsProcessing(false)
          return
        }

        const newCommand = {
          id: response.exec_id,
          text: value,
          timestamp: Date.now(),
          status: 'pending',
        } satisfies Command

        setHistory((prev) => [...prev, newCommand])
        currentCmdIdRef.current = response.exec_id
        setCurrentExecId(response.exec_id)
      } catch {
        setIsProcessing(false)
      }
    },
    [organizationId, applicationId, hardwareId, sendCommand]
  )

  const onClose = useCallback(() => {
    navigate(`/manage/gateways/${hardwareId}/details`)
  }, [hardwareId, navigate])

  const scrollToBottom = useCallback(() => {
    if (terminalRef.current) {
      terminalRef.current.scrollTop = terminalRef.current.scrollHeight
    }
  }, [])

  const onKeyDown = useCallback(
    (event: React.KeyboardEvent) => {
      if (isProcessing) {
        event.preventDefault()
        return
      }

      switch (event.key) {
        // Send command
        case 'Enter': {
          event.preventDefault()
          executeCommand(input)
          setInput('')
          setHistoryIndex(-1)
          break
        }
        // Navigate up in history
        case 'ArrowUp': {
          event.preventDefault()
          if (historyIndex < history.length - 1) {
            const newIndex = historyIndex + 1
            setHistoryIndex(newIndex)
            const historyItem = history[history.length - 1 - newIndex]
            if (historyItem) {
              setInput(historyItem.text)
            }
          }
          break
        }
        // Navigate down in history
        case 'ArrowDown': {
          event.preventDefault()
          if (historyIndex > 0) {
            const newIndex = historyIndex - 1
            setHistoryIndex(newIndex)
            const historyItem = history[history.length - 1 - newIndex]
            if (historyItem) {
              setInput(historyItem.text)
            }
          } else if (historyIndex === 0) {
            setHistoryIndex(-1)
            setInput('')
          }
          break
        }
        default:
          break
      }
    },
    [isProcessing, historyIndex, input]
  )

  const onChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setInput(event.target.value)
  }, [])

  const onRefetch = useCallback(
    async (cmdId: string) => {
      setSpinningCommands((prev) => {
        const newMap = new Map(prev)
        newMap.set(cmdId, true)
        return newMap
      })

      const command = history.find((cmd) => cmd.id === cmdId)

      if (command?.id) {
        setCurrentExecId(command.id)
        await refetch()
      }

      setTimeout(() => {
        setSpinningCommands((prev) => {
          const newMap = new Map(prev)
          newMap.delete(cmdId)
          return newMap
        })
      }, 500)
    },
    [refetch, history]
  )

  useEffect(() => {
    scrollToBottom()
  }, [history, scrollToBottom])

  useEffect(() => {
    inputRef.current?.focus()
  }, [])

  return (
    <Drawer isOpen={true} placement="right" onClose={onClose} size="lg">
      <DrawerOverlay />
      <DrawerContent>
        <HStack
          spacing={0}
          position="absolute"
          right="4"
          top="4"
          h="40px"
          align="center"
          justify="center"
        >
          <Popover
            placement="bottom-end"
            trigger="hover"
            openDelay={200}
            closeDelay={200}
            offset={[0, 2]}
          >
            <PopoverTrigger>
              <IconButton
                isRound
                aria-label="Remote Shell Info"
                icon={<InfoIcon size={20} />}
                size="sm"
                variant="ghost"
              />
            </PopoverTrigger>
            <PopoverContent>
              <PopoverBody fontSize="sm">
                Our Remote Shell feature for gateways allows you to send Linux
                commands remotely. However, please note that this is not a live
                interactive shell session.
                <br />
                <br />
                The shell operates more like an RPC (Remote Procedure Call),
                meaning:
                <br />• Real-time interactions like editing files in vi or nano
                are not supported.
                <br />• Continuous commands like tail -f or interactive scripts
                won't behave as expected.
                <br />• For best results, use single-run commands (e.g., cat,
                ls, df, etc.).
              </PopoverBody>
            </PopoverContent>
          </Popover>
          <IconButton
            isRound
            aria-label="Close"
            icon={<XIcon size={20} />}
            size="sm"
            variant="ghost"
            onClick={onClose}
          />
        </HStack>
        <DrawerBody p={0} as={Grid} gridTemplateRows="1fr 30px 50px">
          <Flex
            ref={terminalRef}
            p={4}
            h="100%"
            overflowY="auto"
            flexDirection="column"
            fontSize="xs"
            fontFamily="mono"
          >
            {history.map((cmd) => (
              <Box key={cmd.id} py={1}>
                <HStack spacing={2}>
                  <Box color="gray.400">{promptId}</Box>
                  <Box>{cmd.text}</Box>
                  {['error', 'pending'].includes(cmd.status) && (
                    <Box
                      color="gray.400"
                      cursor="pointer"
                      onClick={() => onRefetch(cmd.id)}
                      animation={
                        spinningCommands.get(cmd.id) ? spinAnimation : 'none'
                      }
                    >
                      <RefreshCwIcon size={8} />
                    </Box>
                  )}
                </HStack>
                {cmd.status === 'pending' ? (
                  <HStack spacing={2}>
                    <Spinner
                      size="xs"
                      color="gray.400"
                      thickness="1px"
                      emptyColor="blackAlpha.50"
                    />
                    <Box color="gray.400">Connecting...</Box>
                  </HStack>
                ) : null}
                {cmd.output && (
                  <Box
                    py={1}
                    whiteSpace="pre-wrap"
                    color={cmd.status === 'error' ? 'red.400' : 'inherit'}
                  >
                    {cmd.output}
                  </Box>
                )}
              </Box>
            ))}
          </Flex>
          <Flex
            justify="center"
            align="center"
            h="100%"
            fontSize="xs"
            color="red.700"
            bg="red.100"
            _dark={{
              color: 'red.300',
              bg: 'red.900',
            }}
          >
            <HStack spacing={2}>
              <AlertOctagonIcon size={14} />
              <Box>
                Caution: Be sure you know what you are doing, a bad command
                could brick your gateway.
              </Box>
            </HStack>
          </Flex>
          <Flex justify="center" align="center">
            <InputGroup size="xs" h="100%">
              <InputLeftElement
                pointerEvents="none"
                color="gray.400"
                h="100%"
                w="40px"
              >
                <TerminalIcon size={16} />
              </InputLeftElement>
              <Input
                ref={inputRef}
                type="text"
                value={input}
                size="sm"
                variant="unstyled"
                placeholder="write a command - press enter to send"
                onKeyDown={onKeyDown}
                onChange={onChange}
                autoFocus
                sx={inputStyle}
              />
            </InputGroup>
          </Flex>
        </DrawerBody>
      </DrawerContent>
    </Drawer>
  )
})

export async function loader() {
  const scopes = ['edit:applications']

  const { checkLoaderPermissions } = await import('@/utils/loader-auth')
  await checkLoaderPermissions(scopes)

  return {
    title: 'Remote Shell',
    scopes,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <RemoteShell />
}
