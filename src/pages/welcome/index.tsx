import * as Sentry from '@sentry/react'
import { Suspense, useEffect, useCallback } from 'react'
import { useNavigate } from 'react-router'
import { useForm, useWatch, FormProvider } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { isEmpty } from 'ramda'
import { Box, Button, Flex, Text, VStack, StackDivider } from '@chakra-ui/react'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { Title } from '@/components/title'
import { EditableField } from '@/components/editable-field'
import { EditablePasswordField } from '@/components/editable-password-field/editable-password-field'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { useAuth } from '@/contexts/use-auth'
import { useToast } from '@/hooks/use-toast'
import { getCurrentUser, updateCurrentUser } from '@/api/users'
import { useDarkerBg } from '@/hooks/use-darker-bg'
import { checkPasswordStrength } from '@/utils/check-password-strength'
import type { CurrentUserModel } from '@/types/models/user'
import type { UserInput } from '@/types/api'

const schema = z
  .object({
    firstName: z.string().min(1, 'First Name is required'),
    lastName: z.string().min(1, 'Last Name is required.'),
    email: z.string().email(),
    phoneNumber: z.string().min(1, 'Mobile Number is required.'),
    locale: z.string().min(1, 'Language is required.'),
    forcePassword: z.boolean(),
    password: z.string().nullable(),
    passwordConfirmation: z.string().nullable(),
  })
  .superRefine((arg, ctx) => {
    if (arg.forcePassword) {
      if (!arg.password) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Please update your password.',
          path: ['password'],
        })
      }

      if (arg?.password && arg?.password?.length < 8) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Please update your password (at least 8 characters).',
          path: ['password'],
        })
      }

      if (arg?.password) {
        const result = checkPasswordStrength(arg?.password ?? '')
        if (result.id < 2) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: ['password'],
            message:
              'Password must contain upper/lowercase letters, numbers, & special chars.',
          })
        }
      }

      if (arg.password !== arg.passwordConfirmation) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Passwords must match.',
          path: ['passwordConfirmation'],
        })
      }
    }

    return z.NEVER
  })

type FormInputProps = z.infer<typeof schema>

interface AccountSelectorProps extends CurrentUserModel {
  locale: string
  phoneNumber: string
  password: string
  forcePassword: boolean
}

function Welcome() {
  const toast = useToast()
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  const { user } = useAuth()
  const darkerBg = useDarkerBg()

  const methods = useForm({
    mode: 'all',
    resolver: zodResolver(schema),
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      phoneNumber: '',
      locale: 'en-us',
      forcePassword: false,
      password: null,
      passwordConfirmation: null,
    },
  })

  const {
    reset,
    control,
    handleSubmit,
    formState: { isValid, isSubmitting },
  } = methods

  const firstName = useWatch({
    name: 'firstName',
    control,
  })

  const { mutateAsync: updateCurrentUserMutation } = useMutation<
    CurrentUserModel,
    Error,
    Partial<UserInput>
  >({
    mutationFn: (input) => updateCurrentUser(input),
    onSuccess: () =>
      queryClient.invalidateQueries({ queryKey: ['GetCurrentUser'] }),
  })

  const { data: account } = useQuery<
    CurrentUserModel | null,
    Error,
    AccountSelectorProps | null
  >({
    queryKey: ['GetCurrentUser', user?.token],
    queryFn: ({ signal }) =>
      getCurrentUser({
        signal,
      }),
    refetchOnWindowFocus: false,
    select: useCallback(
      (data: CurrentUserModel | null) =>
        data
          ? {
              ...data,
              phoneNumber: data?.attributes?.phoneNumber?.[0] ?? '',
              locale: data?.locale ?? 'en-us',
              password: '',
              forcePassword: true,
            }
          : null,
      []
    ),
  })

  useEffect(() => {
    if (!account) {
      return
    }
    reset({
      firstName: account.firstName,
      lastName: account.lastName,
      email: account.email,
      phoneNumber: account.phoneNumber,
      locale: account.locale,
      forcePassword: account.forcePassword ?? false,
      password: null,
      passwordConfirmation: null,
    })
  }, [reset, account])

  const onSubmit = async (values: FormInputProps): Promise<boolean> => {
    let input: Partial<UserInput> = {
      firstName: values.firstName,
      lastName: values.lastName,
      attributes: {
        phoneNumber: values.phoneNumber,
        locale: values.locale,
      },
    }

    if (values.password && !isEmpty(values.password)) {
      input = {
        ...input,
        password: values.password,
      }
    }

    try {
      await updateCurrentUserMutation(input)

      toast({
        status: 'success',
        msg: 'Account has been updated.',
      })

      navigate('/invite/permissions', {
        viewTransition: true,
      })

      return true
    } catch (error: unknown) {
      Sentry.captureException(error, {
        fingerprint: ['welcome'],
        level: 'fatal',
      })
      toast({
        status: 'error',
        msg: 'Unable to update account.',
      })
      return false
    }
  }

  return (
    <Suspense fallback={null}>
      <FormProvider {...methods}>
        <Box as="form" noValidate onSubmit={handleSubmit(onSubmit)}>
          <Box
            sx={{
              mt: 8,
              ml: 8,
              p: 5,
              borderRadius: 'md',
              shadow: 'md',
              bg: 'white',
              _dark: {
                bg: darkerBg,
              },
            }}
          >
            <Flex p={4} direction="column" justify="center">
              <Title title={`Welcome ${firstName}`} />
              <Text>
                Please confirm your account information and enter the password
                you would like to use.
              </Text>
            </Flex>
            <VStack
              align="stretch"
              divider={
                <StackDivider
                  sx={{
                    borderColor: 'gray.200',
                    _dark: {
                      borderColor: 'blackAlpha.300',
                    },
                  }}
                />
              }
              spacing={0}
              minW="container.xl"
            >
              <EditableField
                name="firstName"
                type="text"
                label="First Name"
                onSubmit={onSubmit}
                control={control}
              />
              <EditableField
                name="lastName"
                type="text"
                label="Last Name"
                onSubmit={onSubmit}
                control={control}
              />
              <EditableField
                name="email"
                type="email"
                label="Email"
                onSubmit={onSubmit}
                control={control}
                isDisabled
              />
              <EditableField
                name="phoneNumber"
                type="tel"
                label="Mobile Number"
                onSubmit={onSubmit}
                control={control}
              />
              <EditablePasswordField
                name="password"
                label="Password"
                control={control}
                onSubmit={onSubmit}
                startWithEditView={true}
              />
            </VStack>
            <Flex pt={4} align="center" justify="flex-end">
              <Button
                type="submit"
                aria-label="Next"
                colorScheme="green"
                minW="120px"
                isLoading={isSubmitting}
                isDisabled={!isValid || isSubmitting}
              >
                Next
              </Button>
            </Flex>
          </Box>
        </Box>
      </FormProvider>
    </Suspense>
  )
}

export async function loader() {
  const scopes = [] as string[]

  return {
    title: 'Welcome',
    scopes,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <Welcome />
}
