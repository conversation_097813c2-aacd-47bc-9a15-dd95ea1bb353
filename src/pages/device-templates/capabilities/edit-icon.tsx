import * as Sentry from '@sentry/react'
import { memo } from 'react'
import { useForm, FormProvider, useWatch } from 'react-hook-form'
import chroma from 'chroma-js'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { useAtomValue } from 'jotai'
import { useQueryClient, useQuery, useMutation } from '@tanstack/react-query'
import {
  Box,
  Button,
  Grid,
  ModalBody,
  ModalCloseButton,
  ModalFooter,
  ModalHeader,
  useModalContext,
} from '@chakra-ui/react'
import type { CreateIconOptions } from '@/data/dev-icons'
import { ColorPickerField } from '@/components/color-picker-field/color-picker-field'
import { InputField } from '@/components/input-field'
import { SelectField } from '@/components/select-field'
import { DropZoneField } from '@/components/drop-zone-field'
import { SelectRemoveIconOption } from '@/features/capabilities/select-remove-icon-option'
import { SelectIconOptionLabel } from '@/features/capabilities/select-icon-option-label'
import { useToast } from '@/hooks/use-toast'
import { getIcons } from '@/utils/get-icons'
import { svgUrl } from '@/utils/svg-url'
import { modalAtom } from '@/utils/stores/modal'
import { createProperty } from '@/api/things/properties'
import type { IconModel } from '@/types/models/icon'
import type { PropertyModel, IconDataProperty } from '@/types/models/property'
import type { PropertyInput } from '@/types/api'
import type { ModalAtomProps } from '@/types/store'
import type { IconOption } from '@/types/models/icon'

const typeOptions = [
  { label: 'Icon', value: 'icon' },
  { label: 'Custom Image', value: 'url' },
] satisfies BaseOption<TypeEnum>[]

const schema = z.object({
  id: z.string().optional(),
  label: z.string().min(1, 'Label is required.'),
  type: z.enum(['icon', 'url']),
  color: z
    .string()
    .min(1, 'Color is required.')
    .refine((value) => value && chroma.valid(value), {
      message: 'Invalid color.',
    }),
  value: z.string().min(1, 'Value is required.'),
})

type TypeEnum = 'icon' | 'url'
type FormInputProps = z.infer<typeof schema>

export const EditIcon = memo(() => {
  const toast = useToast()
  const { onClose } = useModalContext()
  const queryClient = useQueryClient()

  const {
    config: { title, data, onCallback },
  } =
    useAtomValue<
      ModalAtomProps<{
        id?: string
        dataTypesId: number
        label: string
        color?: string
        svg?: CreateIconOptions
      }>
    >(modalAtom)

  const { mutateAsync: createPropertyMutation } = useMutation<
    PropertyModel,
    Error,
    {
      dataTypeId: number
      input: PropertyInput<IconDataProperty>
    }
  >({
    mutationFn: ({ dataTypeId, input }) => createProperty(dataTypeId, input),
    onSuccess: () => {
      const cache = ['GetProperties', 'GetIcons']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const methods = useForm({
    mode: 'onTouched',
    resolver: zodResolver(schema),
    defaultValues: {
      id: data?.id,
      label: data?.label ?? '',
      color: data?.color ?? '#000',
      type: data?.svg ? 'url' : ('icon' as TypeEnum),
      value: data?.svg ? svgUrl(data.svg) : '',
    },
  })

  const {
    control,
    setValue,
    handleSubmit,
    formState: { isValid, isSubmitting },
  } = methods

  const type = useWatch({
    name: 'type',
    control,
  })

  const { data: iconOptions, isFetching: isFetchingIcons } = useQuery<
    IconModel[],
    Error
  >({
    queryKey: ['GetIcons'],
    queryFn: () => getIcons(),
  })

  const onSubmit = async (values: FormInputProps): Promise<boolean> => {
    if (!data?.dataTypesId) {
      return false
    }

    const input = {
      label: values.label,
      type: 'icon',
      data: {
        type: 'url',
        name: values.label,
        value: values.value,
        color: values.color,
      },
    } satisfies PropertyInput<IconDataProperty>

    try {
      const property = await createPropertyMutation({
        dataTypeId: data?.dataTypesId,
        input,
      })

      toast({
        status: 'success',
        msg: 'Icon has been created.',
      })

      const iconOption = {
        dataTypeId: property.data_types_id,
        propertyId: property.id,
        label: property.label,
        value: property.data.value,
        color: property.data.color,
        type: property.data.type,
      } satisfies IconOption

      onCallback(iconOption)
      onClose()
      return true
    } catch (error: unknown) {
      Sentry.captureException(error)
      if (error instanceof Error) {
        toast({
          status: 'error',
          msg: error?.message,
        })
      } else {
        toast({
          status: 'error',
          msg: 'Unable to create icon.',
        })
      }
      return false
    }
  }

  return (
    <FormProvider {...methods}>
      <Box as="form" noValidate onSubmit={handleSubmit(onSubmit)}>
        <ModalHeader>{title}</ModalHeader>
        <ModalCloseButton />
        <ModalBody
          sx={{
            maxH: '70vh',
            overflow: 'auto',
          }}
        >
          <Grid gap={4} templateColumns="1fr">
            <InputField
              name="label"
              label="Label"
              control={control}
              isRequired
            />
            <ColorPickerField
              name="color"
              label="Color"
              control={control}
              isRequired
            />
            <SelectField
              name="type"
              label="Type"
              control={control}
              options={typeOptions}
              isRequired
              onChange={(option: BaseOption<TypeEnum>) => {
                setValue('id', '')
                setValue('value', '')
                setValue('type', option.value, {
                  shouldValidate: true,
                })
              }}
            />
            {type === 'url' ? (
              <DropZoneField
                name="value"
                label="Image URL"
                isRequired
                height={100}
                control={control}
                accept={{
                  'image/svg+xml': ['.svg'],
                  'image/png': ['.png'],
                }}
                shortInfo="Supported files; SVG/PNG. Aspect ratio: 1:1 (retina @2x square e.g. 100x100px for png)."
              />
            ) : (
              <SelectField
                name="id"
                label="Custom Icon"
                isSearchable
                isRequired
                formatOptionLabel={SelectIconOptionLabel}
                components={{ Option: SelectRemoveIconOption }}
                control={control}
                options={iconOptions}
                isLoading={isFetchingIcons}
                menuPosition="fixed"
                onChange={(option: IconModel) => {
                  setValue('id', option.value)
                  if (option?.svg) {
                    setValue('value', svgUrl(option.svg), {
                      shouldValidate: true,
                    })
                  }
                }}
              />
            )}
          </Grid>
        </ModalBody>
        <ModalFooter justifyContent="space-between">
          <Button
            type="button"
            aria-label="Close"
            variant="ghost"
            onClick={onClose}
          >
            Close
          </Button>
          <Button
            mt={4}
            type="submit"
            aria-label="Save"
            colorScheme="green"
            minW={120}
            isLoading={isSubmitting}
            isDisabled={!isValid || isSubmitting}
            loadingText="Saving"
          >
            Save
          </Button>
        </ModalFooter>
      </Box>
    </FormProvider>
  )
})
