import * as Sentry from '@sentry/react'
import {
  lazy,
  Suspense,
  useCallback,
  useEffect,
  useMemo,
  useState,
  memo,
} from 'react'
import { Helmet } from 'react-helmet-async'
import { useNavigate, useParams, type LoaderFunctionArgs } from 'react-router'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import pMap from 'p-map'
import { useAtomValue, useSetAtom } from 'jotai'
import { omit } from 'ramda'
import { Box, Flex, HStack } from '@chakra-ui/react'
import { CloudAlertIcon } from 'lucide-react'
import { DndTable } from '@/components/dnd-table/dnd-table'
import { Title } from '@/components/title'
import { Tabs } from '@/components/tabs'
import { AddButton } from '@/components/add-button'
import { Alert } from '@/components/alert'
import { BackButton } from '@/components/back-button'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { useRouter } from '@/hooks/use-router'
import { useModal } from '@/hooks/use-modal'
import { useOrganization } from '@/hooks/use-organization'
import { useToast } from '@/hooks/use-toast'
import { metaTitle } from '@/utils/meta-title'
import { sortByOrder } from '@/utils/sort-by-order'
import { isMovingAtom, tableAtom } from '@/utils/stores/table'
import { TEMPLATES } from '@/data/templates'
import {
  createChannel,
  deleteChannel,
  getChannels,
  updateChannel,
} from '@/api/things/channels'
import { getDataType } from '@/api/things/datatypes'
import type { ChannelModel } from '@/types/models/channel'
import type { DataTypeModel } from '@/types/models/data-type'
import type { ColumnProps, ActionProps } from '@/types/table'
import type { PaginatedQueryResponse } from '@/types/api'

const ConfirmDialog = lazy(() =>
  import('@/components/confirm-dialog').then(({ ConfirmDialog }) => ({
    default: ConfirmDialog,
  }))
)

interface CapabilityModel extends Omit<ChannelModel, 'id'> {
  id: number // Ensure id is always a number
  sortable: boolean
  sensor?: DataTypeModel
  capabilityType?: string
}

const Capabilities = memo(() => {
  const heading = 'Edit Device Template'
  const tableName = 'capabilities'
  const modal = useModal()
  const toast = useToast()
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  const { organizationId, applicationId } = useOrganization()
  const { title, currentPath } = useRouter()
  const setIsMoving = useSetAtom(isMovingAtom)
  const [rows, setRows] = useState<CapabilityModel[]>([])
  const [isSupported, setIsSupported] = useState<boolean>(true)
  const { limit, currPage } = useAtomValue(tableAtom(tableName))
  const { id } = useParams<{ id: string }>()
  const typeId = useMemo<string | undefined>(() => id, [id])

  const { mutateAsync: createChannelMutation } = useMutation<
    ChannelModel,
    Error,
    {
      typeId: string
      input: ChannelModel
    }
  >({
    mutationFn: ({ typeId, input }) =>
      createChannel({ organizationId, applicationId, typeId, input }),
    onSuccess: () =>
      queryClient.invalidateQueries({ queryKey: ['GetChannels'] }),
  })

  const { mutateAsync: updateChannelMutation } = useMutation<
    ChannelModel,
    Error,
    {
      typeId: string
      channelId: number
      input: Partial<ChannelModel>
    }
  >({
    mutationFn: ({ typeId, channelId, input }) =>
      updateChannel({
        organizationId,
        applicationId,
        typeId,
        channelId,
        input,
      }),
    onSuccess: () => {
      const cache = ['GetChannels', 'GetChannel']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const { mutateAsync: deleteChannelMutation } = useMutation<
    boolean,
    Error,
    {
      typeId: string
      channelId: number
    }
  >({
    mutationFn: ({ typeId, channelId }) =>
      deleteChannel({ organizationId, applicationId, typeId, channelId }),
    onSuccess: () => {
      const cache = ['GetChannels', 'GetChannel']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const { data } = useQuery<PaginatedQueryResponse<ChannelModel> | null, Error>(
    {
      queryKey: [
        'GetChannels',
        organizationId,
        applicationId,
        typeId,
        limit,
        currPage,
      ],
      queryFn: ({ signal }) =>
        typeId
          ? getChannels({
              organizationId,
              applicationId,
              typeId,
              limit,
              page: currPage,
              signal,
            })
          : null,
      enabled: !!organizationId && !!applicationId && !!typeId,
    }
  )

  const getSensors = useCallback(
    async (ids: number[]): Promise<DataTypeModel[]> =>
      await pMap(ids, async (id) => getDataType({ id }), { concurrency: 1 }),
    []
  )

  useEffect(() => {
    if (!data) {
      return
    }

    // Exit if legacy device template with invalid structure.
    if (data.count > 0 && !data.rows.some((row) => row?.data?.template)) {
      setIsSupported(false)
      return
    }

    const ids = data.rows.flatMap(({ data_types_id }) => data_types_id ?? [])

    getSensors(ids).then((sensors) => {
      const transformedRows = sortByOrder(
        data.rows.reduce((acc: CapabilityModel[], row) => {
          const sensor = sensors.find(
            (sensor) => sensor.id === row.data_types_id
          )
          if (!row.id) {
            return acc
          }
          acc.push({
            ...row,
            id: row.id, // This is now guaranteed to be a number
            sensor,
            sortable: !['RSSI', 'Battery', 'Sensor'].includes(
              sensor?.label ?? ''
            ),
            capabilityType: TEMPLATES.find(
              ({ value }) => value === row.data.template
            )?.label,
          })
          return acc
        }, [])
      )
      setRows(transformedRows)
    })
  }, [data, getSensors])

  const onGoBack = (): void => {
    navigate('/manage/device-templates', {
      viewTransition: true,
    })
  }

  const onAdd = useCallback(() => {
    navigate(`/manage/device-templates/${typeId}/capabilities/add`, {
      viewTransition: true,
    })
  }, [navigate, typeId])

  const onEdit = useCallback(
    (row?: CapabilityModel): void => {
      if (!(row && typeId)) {
        return
      }
      navigate(`/manage/device-templates/${typeId}/capabilities/${row.id}`, {
        viewTransition: true,
      })
    },
    [navigate, typeId]
  )

  const onDuplicate = useCallback(
    async (row?: CapabilityModel): Promise<void> => {
      if (!(row && typeId)) {
        return
      }

      const input: ChannelModel = {
        ...omit(['id', 'sensor', 'sortable', 'capabilityType'], row),
        name: `${row.name} (copy)`,
        order: rows.length + 1,
      }

      try {
        await createChannelMutation({
          typeId,
          input,
        })
        toast({
          status: 'success',
          msg: `"${row.name}" has been duplicated.`,
        })
      } catch (error: unknown) {
        Sentry.captureException(error)
        toast({
          status: 'error',
          msg: `"Unable to duplicate ${row.name}".`,
        })
      }
    },
    [toast, typeId, rows, createChannelMutation]
  )

  const onRemove = useCallback(
    (row?: CapabilityModel): void => {
      if (!(row?.id && typeId)) {
        return
      }

      modal({
        size: 'md',
        component: <ConfirmDialog />,
        config: {
          title: 'Remove Capability?',
          description: `Are you sure you want to remove "${row.name}"?`,
          confirmLabel: 'Remove',
          onCallback: async () => {
            if (!(row?.id && typeId)) {
              return
            }

            try {
              await deleteChannelMutation({
                typeId,
                channelId: row.id,
              })
              toast({
                status: 'success',
                msg: `"${row.name}" has been removed.`,
              })
            } catch (error: unknown) {
              Sentry.captureException(error)
              toast({
                status: 'error',
                msg: `Unable to remove "${row.name}".`,
              })
            }
          },
        },
      })
    },
    [toast, modal, typeId, deleteChannelMutation]
  )

  const updateOrder = useCallback(
    async (row: CapabilityModel, index: number): Promise<boolean> => {
      if (!(typeId && row?.id)) {
        return false
      }

      const input = {
        ...omit(['id', 'capabilityType', 'sensor', 'sortable'], row),
        order: index,
      } satisfies Omit<ChannelModel, 'id'>

      try {
        await updateChannelMutation({
          typeId,
          channelId: row.id,
          input,
        })
        return true
      } catch (error: unknown) {
        Sentry.captureException(error)
        return false
      }
    },
    [typeId, updateChannelMutation]
  )

  const onUpdateOrder = useCallback(
    async (items: CapabilityModel[]) => {
      try {
        await pMap(
          items,
          async (row: any, key: number) => await updateOrder(row, key),
          { concurrency: 10 }
        )

        setRows(items)

        toast({
          status: 'success',
          msg: 'Capabilities have been reordered.',
        })
      } catch {
        // Revert to original order on error
        setRows(rows)
        toast({
          status: 'error',
          msg: 'Unable to reorder capabilities.',
        })
      }
    },
    [toast, updateOrder, rows]
  )

  const onMoveOrder = useCallback(
    async ({
      updated,
      event,
    }: {
      updated: any[]
      event: string
    }): Promise<boolean> => {
      if (updated.length === 0) {
        return false
      }

      try {
        await pMap(
          updated,
          async (row: any, key: number) => await updateOrder(row, key),
          { concurrency: 1 }
        )
        toast({
          status: 'success',
          msg: `Channel has been moved ${event}.`,
        })
        setIsMoving(false)
        return true
      } catch (error: unknown) {
        Sentry.captureException(error)
        toast({
          status: 'error',
          msg: `Unable to move channel ${event}.`,
        })
        return false
      }
    },
    [toast, setIsMoving, updateOrder]
  )

  const columns = useMemo<ColumnProps<CapabilityModel>[]>(
    () => [
      {
        id: 'name',
        name: 'Name',
        cell: ({ name }) => <Box noOfLines={1}>{name}</Box>,
      },
      {
        name: 'Capability Type',
        id: 'capabilityType',
        cell: ({ capabilityType }) => <Box noOfLines={1}>{capabilityType}</Box>,
      },
      {
        id: 'sensor',
        name: 'Data Type',
        cell: ({ sensor }) => <Box noOfLines={1}>{sensor?.label}</Box>,
      },
      {
        id: 'channel',
        name: 'Channel',
        cell: ({ channel }) => <Box noOfLines={1}>{channel}</Box>,
      },
    ],
    [rows, onMoveOrder]
  )

  const actions = useMemo<ActionProps<CapabilityModel>[]>(
    () => [
      {
        label: 'Edit Capability',
        onClick: onEdit,
        canView: () => true,
      },
      {
        label: 'Duplicate Capability',
        onClick: onDuplicate,
        canView: () => true,
      },
      {
        label: 'Remove Capability',
        onClick: onRemove,
        canView: () => true,
      },
    ],
    [onEdit, onDuplicate, onRemove]
  )

  return (
    <>
      <Helmet>
        <title>{metaTitle([title, heading])}</title>
      </Helmet>
      <Suspense fallback={null}>
        <Flex align="center" justify="space-between" p={4}>
          <HStack>
            <BackButton onClick={onGoBack} />
            <Title title={heading} />
          </HStack>
          <HStack>
            <AddButton
              label="Add Capability"
              onClick={onAdd}
              isDisabled={!isSupported}
            />
          </HStack>
        </Flex>
        <Box px={4}>
          <Tabs current={currentPath}>
            {!isSupported ? (
              <Alert
                title="Capabilities is not supported for this device."
                icon={CloudAlertIcon}
                color="red.600"
                sx={{
                  bg: 'whiteAlpha.50',
                  _dark: {
                    bg: 'blackAlpha.100',
                  },
                }}
              />
            ) : (
              <DndTable<CapabilityModel>
                tableName={tableName}
                data={{ rows, count: rows.length }}
                columns={columns}
                actions={actions}
                onSort={onUpdateOrder}
              />
            )}
          </Tabs>
        </Box>
      </Suspense>
    </>
  )
})

export async function loader({ params }: LoaderFunctionArgs) {
  const scopes = ['edit:applications']

  const { checkLoaderPermissions } = await import('@/utils/loader-auth')
  await checkLoaderPermissions(scopes)

  const { getFilteredTabs } = await import('@/utils/tab-loader')
  const tabs = await getFilteredTabs('template-tabs', params)

  return {
    title: 'Capabilities',
    scopes,
    tabs,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <Capabilities />
}
