import * as Sentry from '@sentry/react'
import {
  memo,
  lazy,
  Suspense,
  useCallback,
  useMemo,
  type ReactElement,
} from 'react'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useAtomValue, useSetAtom } from 'jotai'
import { Box, Button, Flex, Radio, Switch } from '@chakra-ui/react'
import { Title } from '@/components/title'
import { AddButton } from '@/components/add-button'
import { DataTable } from '@/components/data-table/data-table'
import { useModal } from '@/hooks/use-modal'
import { useOrganization } from '@/hooks/use-organization'
import { useToast } from '@/hooks/use-toast'
import { deleteProperty } from '@/api/things/properties'
import { updateChannel } from '@/api/things/channels'
import { channelAtom, channelUnitAtom } from '@/utils/stores/channel'
import type { ChannelModel, ChannelUnit } from '@/types/models/channel'
import type { ColumnProps, ActionProps } from '@/types/table'

const ConfirmDialog = lazy(() =>
  import('@/components/confirm-dialog').then(({ ConfirmDialog }) => ({
    default: ConfirmDialog,
  }))
)

const Edit = lazy(() =>
  import('@/pages/device-templates/capabilities/unit-list/edit').then(
    ({ Edit }) => ({
      default: Edit,
    })
  )
)

export const UnitList = memo(() => {
  const tableName = 'unit-list'
  const modal = useModal()
  const toast = useToast()
  const queryClient = useQueryClient()
  const { organizationId, applicationId } = useOrganization()
  const channel = useAtomValue(channelAtom)
  const setChannelUnits = useSetAtom(channelUnitAtom)

  const { mutateAsync: updateChannelMutation } = useMutation<
    ChannelModel,
    Error,
    {
      typeId: string
      channelId: number
      input: ChannelModel
    }
  >({
    mutationFn: ({ typeId, channelId, input }) =>
      updateChannel({
        organizationId,
        applicationId,
        typeId,
        channelId,
        input,
      }),
    onSuccess: () => {
      const cache = ['GetChannel', 'GetChannels']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const { mutateAsync: deletePropertyMutation } = useMutation<
    boolean,
    Error,
    {
      dataTypeId: number
      propertyId: number
    }
  >({
    mutationFn: ({ dataTypeId, propertyId }) =>
      deleteProperty(dataTypeId, propertyId),
    onSuccess: () => {
      const cache = ['GetProperties', 'GetProperty']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const onAdd = useCallback((): void => {
    modal({
      size: '4xl',
      scrollBehavior: 'inside',
      component: <Edit />,
      config: {
        title: 'Add Unit',
        data: null,
        onCallback: () => ({}),
      },
    })
  }, [modal])

  const onEdit = useCallback(
    (row: ChannelUnit): void => {
      modal({
        size: '4xl',
        scrollBehavior: 'inside',
        component: <Edit />,
        config: {
          title: 'Edit Unit',
          data: row,
          onCallback: () => ({}),
        },
      })
    },
    [modal]
  )

  const onChangeDefault = useCallback(
    async ({ id }: ChannelUnit): Promise<void> => {
      if (!(channel?.id && channel?.device_type_id)) {
        toast({
          status: 'error',
          msg: 'Unable to update unit.',
        })
        return
      }

      const index = channel.data.units.findIndex((row) => row.id === id)
      const mutated = channel.data.units.reduce(
        (acc: ChannelUnit[], unit, key: number) => {
          acc.push({
            ...unit,
            default: key === index,
          })
          return acc
        },
        []
      )

      const input = {
        ...channel,
        data: {
          ...channel.data,
          units: mutated,
        },
      } satisfies ChannelModel

      try {
        await updateChannelMutation({
          typeId: channel.device_type_id,
          channelId: channel.id,
          input,
        })

        setChannelUnits(mutated)

        toast({
          status: 'success',
          msg: 'Default unit has been updated.',
        })
      } catch (error: unknown) {
        Sentry.captureException(error)
        toast({
          status: 'error',
          msg: 'Unable to update default unit.',
        })
      }
    },
    [toast, channel, setChannelUnits, updateChannelMutation]
  )

  const onToggleEnabled = useCallback(
    async (checked: boolean, row: ChannelUnit): Promise<void> => {
      if (!(channel?.id && channel?.device_type_id)) {
        toast({
          status: 'error',
          msg: 'Unable to update unit.',
        })
        return
      }

      const index = channel.data.units.findIndex(({ id }) => row.id === id)
      const mutated = channel.data.units.reduce(
        (acc: ChannelUnit[], unit, key: number) => {
          acc.push({
            ...unit,
            enabled: key === index ? checked : unit.enabled,
          })
          return acc
        },
        []
      )

      const input = {
        ...channel,
        data: {
          ...channel.data,
          units: mutated,
        },
      } satisfies ChannelModel

      try {
        await updateChannelMutation({
          typeId: channel.device_type_id,
          channelId: channel.id,
          input,
        })

        setChannelUnits(mutated)

        toast({
          status: 'success',
          msg: `Unit has been ${checked ? 'enabled' : 'disabled'}.`,
        })
      } catch (error: unknown) {
        Sentry.captureException(error)
        toast({
          status: 'error',
          msg: 'Unable to update unit.',
        })
      }
    },
    [toast, channel, setChannelUnits, updateChannelMutation]
  )

  const onRemove = useCallback(
    ({ id, label, data_types_id }: ChannelUnit): void => {
      modal({
        size: 'md',
        component: <ConfirmDialog />,
        config: {
          title: 'Remove Unit',
          description: `Are you sure you want to remove “${label}”?`,
          confirmLabel: 'Remove',
          onCallback: async () => {
            try {
              await deletePropertyMutation({
                dataTypeId: data_types_id,
                propertyId: id,
              })

              if (channel?.id && channel?.device_type_id) {
                const mutated = channel.data.units.filter(
                  (unit: ChannelUnit) => unit.id !== id
                )

                const input = {
                  ...channel,
                  data: {
                    ...channel.data,
                    units: mutated,
                  },
                } satisfies ChannelModel

                await updateChannelMutation({
                  typeId: channel.device_type_id,
                  channelId: channel.id,
                  input,
                })

                setChannelUnits(mutated)
              }

              toast({
                status: 'success',
                msg: `“${label}” has been removed.`,
              })
            } catch (error: unknown) {
              Sentry.captureException(error)
              toast({
                status: 'error',
                msg: `Unable to remove “${label}”.`,
              })
            }
          },
        },
      })
    },
    [
      modal,
      toast,
      channel,
      setChannelUnits,
      deletePropertyMutation,
      updateChannelMutation,
    ]
  )

  const columns = useMemo<ColumnProps<ChannelUnit>[]>(
    () => [
      {
        name: 'Unit',
        id: 'label',
        cell: ({ label }) => <Box noOfLines={1}>{label}</Box>,
      },
      {
        name: 'Display',
        id: 'display',
        cell: ({ display }) => <Box noOfLines={1}>{display}</Box>,
      },
      {
        name: '# of Decimals',
        id: 'decimals',
        w: '10%',
        textAlign: 'center',
        cell: ({ decimals }) => (
          <Box noOfLines={1} textAlign="center">
            {decimals}
          </Box>
        ),
      },
      {
        name: 'Default Unit',
        id: 'default',
        w: '10%',
        textAlign: 'center',
        cell: (row) => (
          <Box textAlign="center">
            <Radio
              name="default"
              colorScheme="secondary"
              isChecked={row.default}
              onChange={() => onChangeDefault(row)}
              isDisabled={!channel?.id}
            />
          </Box>
        ),
      },
      {
        name: 'Show in UI',
        id: 'enabled',
        w: '10%',
        textAlign: 'center',
        cell: (row) => (
          <Box textAlign="center">
            <Switch
              id={`toggle-${row.id}`}
              defaultChecked={row.enabled}
              colorScheme="secondary"
              onChange={(event) => onToggleEnabled(event.target.checked, row)}
              isDisabled={!channel?.id}
            />
          </Box>
        ),
      },
    ],
    [channel, onChangeDefault, onToggleEnabled]
  )

  const actions = useMemo<ActionProps<ChannelUnit>[]>(
    () => [
      {
        label: 'Edit Unit',
        onClick: onEdit,
        canView: (): boolean => !!channel?.id,
      },
      {
        label: 'Remove Unit',
        onClick: onRemove,
        canView: (): boolean => true,
      },
    ],
    [channel, onEdit, onRemove]
  )

  const emptyRows = useCallback(
    (): ReactElement => (
      <Box>
        <Box as="span" pr={1}>
          No records exist,
        </Box>
        <Button
          type="button"
          aria-label="Add Unit"
          variant="link"
          colorScheme="secondary"
          fontWeight="normal"
          onClick={onAdd}
        >
          add unit
        </Button>
        .
      </Box>
    ),
    [onAdd]
  )

  return (
    <>
      <Flex align="center" justify="space-between" p={4}>
        <Title title="Units" />
        {channel?.data_types_id && (
          <AddButton label="Add Unit" onClick={onAdd} />
        )}
      </Flex>
      <Box px={4} mb={10}>
        <Suspense fallback={null}>
          <DataTable
            tableName={tableName}
            data={{
              rows: channel.data.units,
              count: channel.data.units.length,
            }}
            columns={columns}
            actions={actions}
            emptyRows={emptyRows}
            sx={{
              borderRadius: 'base',
              overflow: 'hidden',
              boxShadow: 'sm',
              bgColor: 'whiteAlpha.400',
              _dark: {
                bgColor: 'blackAlpha.50',
              },
              th: {
                bgColor: 'whiteAlpha.900',
                _dark: {
                  bgColor: 'blackAlpha.100',
                },
              },
            }}
          />
        </Suspense>
      </Box>
    </>
  )
})
