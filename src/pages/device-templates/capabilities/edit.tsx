import * as Sentry from '@sentry/react'
import {
  lazy,
  Suspense,
  useCallback,
  useMemo,
  useState,
  useEffect,
  memo,
  type BaseSyntheticEvent,
} from 'react'
import { useMountEffect } from '@react-hookz/web'
import { useNavigate, useParams } from 'react-router'
import { useForm, useWatch, type DefaultValues } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { isNil, isNotNil } from 'ramda'
import { v4 as uuid } from 'uuid'
import { useAtom, useAtomValue, useSetAtom } from 'jotai'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  Box,
  Button,
  Container,
  Flex,
  Grid,
  HStack,
  Portal,
} from '@chakra-ui/react'
import { DebugDrawer } from '@/components/debug-drawer'
import { InputField } from '@/components/input-field'
import { SelectDataTypeField } from '@/components/select-datatype-field'
import { SelectField } from '@/components/select-field'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { EditHeader } from '@/features/capabilities/edit-header'
import {
  SelectWidgetOptionLabel,
  type SelectWidgetOptionProps,
} from '@/features/capabilities/select-widget-option-label'
import { SelectIconOptionLabel } from '@/features/capabilities/select-icon-option-label'
import { SelectRemoveIconOption } from '@/features/capabilities/select-remove-icon-option'
import { useModal } from '@/hooks/use-modal'
import { useOrganization } from '@/hooks/use-organization'
import { useToast } from '@/hooks/use-toast'
import { TEMPLATES } from '@/data/templates'
import { createChannel, getChannel, updateChannel } from '@/api/things/channels'
import { getDataType } from '@/api/things/datatypes'
import { getProperties } from '@/api/things/properties'
import { getWidgets } from '@/api/widgets'
import { interpolate } from '@/utils/interpolate'
import { getIcons } from '@/utils/get-icons'
import { widgetMeta } from '@/utils/widget-meta'
import { navAtom } from '@/utils/stores/nav'
import {
  channelAtom,
  channelChartAtom,
  channelIconAtom,
  channelRuleAtom,
  channelTemplateAtom,
  channelWidgetAtom,
  resetChannelAtom,
} from '@/utils/stores/channel'
import type { ChannelModel } from '@/types/models/channel'
import type {
  ChannelChart,
  ChannelIcon,
  ChannelRuleTemplate,
} from '@/types/models/channel'
import type { PropertyModel } from '@/types/models/property'
import type { DataTypeModel } from '@/types/models/data-type'
import type { IconModel, IconOption } from '@/types/models/icon'
import type { GetResponse } from '@/types/api'
import type { WidgetModel } from '@/types/models/widget'

const EditIcon = lazy(() =>
  import('@/pages/device-templates/capabilities/edit-icon').then(
    ({ EditIcon }) => ({
      default: EditIcon,
    })
  )
)

const UnitList = lazy(() =>
  import('@/pages/device-templates/capabilities/unit-list').then(
    ({ UnitList }) => ({
      default: UnitList,
    })
  )
)

const StatusList = lazy(() =>
  import('@/pages/device-templates/capabilities/status-list').then(
    ({ StatusList }) => ({
      default: StatusList,
    })
  )
)

const CommandList = lazy(() =>
  import('@/pages/device-templates/capabilities/command-list').then(
    ({ CommandList }) => ({
      default: CommandList,
    })
  )
)

const templateOptions = TEMPLATES.reduce(
  (acc: BaseOption<string>[], { description, value }) => {
    acc.push({
      label: description,
      value,
    })
    return acc
  },
  []
)

const chartOptions = [
  { label: 'n/a', value: '' },
  { label: 'Bar Chart', value: 'bar' },
  { label: 'Button', value: 'button' },
  { label: 'Line Chart', value: 'line' },
  { label: 'List Chart', value: 'list' },
  { label: 'Pie Chart', value: 'pie' },
  { label: 'Toggle On/Off', value: 'toggle' },
]

const schema = z.object({
  template_id: z.string().min(1, 'Template is required.'),
  data_type_id: z.number().nullable(),
  name: z.string().min(1, 'Name is required.'),
  channel: z.string().min(1, 'Channel ID is required.'),
  icon: z.string().nullable(),
  chart: z.string().nullable(),
  widget: z.string().nullable(),
  properties: z
    .array(
      z.object({
        key: z.string(),
        type: z.string(),
        value: z.string(),
      })
    )
    .optional(),
})

const defaultValues = {
  template_id: '',
  data_type_id: null as Maybe<number>,
  name: '',
  channel: '',
  icon: null as Maybe<string>,
  chart: null as Maybe<string>,
  widget: null as Maybe<string>,
  properties: [] as Array<{
    key: string
    type: string
    value: string
  }>,
} satisfies DefaultValues<FormInputProps>

type FormInputProps = z.infer<typeof schema>

interface DataTypeObject extends DataTypeModel {
  template: string
  props: PropertyModel[]
}

const Edit = memo(() => {
  const modal = useModal()
  const toast = useToast()
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  const { organizationId, applicationId } = useOrganization()
  const { open } = useAtomValue(navAtom)
  const { typeId, id } = useParams<Dict<string>>()

  const isNew = useMemo<boolean>(() => !id || id === 'add', [id])

  const channelId = useMemo<number | undefined>(
    () =>
      !id || Number.isNaN(Number.parseInt(id))
        ? undefined
        : Number.parseInt(id),
    [id]
  )

  const [customIcons, setCustomIcons] = useState<IconOption[]>([])
  const [channel, setChannel] = useAtom(channelAtom)
  const resetChannel = useSetAtom(resetChannelAtom)

  // Optics
  const setTemplate = useSetAtom(channelTemplateAtom)
  const setWidget = useSetAtom(channelWidgetAtom)
  const setChart = useSetAtom(channelChartAtom)
  const setIcon = useSetAtom(channelIconAtom)
  const setRuleTemplate = useSetAtom(channelRuleAtom)

  const {
    reset,
    control,
    setValue,
    handleSubmit,
    formState: { isValid, isSubmitting },
  } = useForm({
    mode: 'onTouched',
    resolver: zodResolver(schema),
    defaultValues,
  })

  const dataTypeId = useWatch({
    name: 'data_type_id',
    control,
  })

  const { mutateAsync: createChannelMutation } = useMutation<
    ChannelModel,
    Error,
    {
      typeId: string
      input: ChannelModel
    }
  >({
    mutationFn: ({ typeId, input }) =>
      createChannel({ organizationId, applicationId, typeId, input }),
    onSuccess: () =>
      queryClient.invalidateQueries({ queryKey: ['GetChannels'] }),
  })

  const { mutateAsync: updateChannelMutation } = useMutation<
    ChannelModel,
    Error,
    {
      typeId: string
      channelId: number
      input: ChannelModel
    }
  >({
    mutationFn: ({ typeId, channelId, input }) =>
      updateChannel({
        organizationId,
        applicationId,
        typeId,
        channelId,
        input,
      }),
    onSuccess: () => {
      const cache = ['GetChannels', 'GetChannel']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const { data } = useQuery<ChannelModel | null, Error, ChannelModel>({
    queryKey: ['GetChannel', organizationId, applicationId, typeId, channelId],
    queryFn: ({ signal }) =>
      typeId && channelId
        ? getChannel({
            organizationId,
            applicationId,
            typeId,
            channelId,
            signal,
          })
        : null,
    enabled: !!organizationId && !!applicationId && !isNew,
  })

  const { data: icons } = useQuery<IconModel[], Error>({
    queryKey: ['GetIcons'],
    queryFn: () => getIcons(),
  })

  const { data: propertyIcons } = useQuery<
    GetResponse<PropertyModel> | null,
    Error,
    IconOption[]
  >({
    queryKey: ['GetProperties', dataTypeId, 'icon'],
    queryFn: ({ signal }) =>
      dataTypeId
        ? getProperties({
            dataTypeId,
            type: 'icon',
            signal,
          })
        : null,
    enabled: !!dataTypeId,
    select: useCallback((data: GetResponse<PropertyModel> | null) => {
      return (data?.rows ?? [])
        .filter((row) => row.data.type === 'url')
        .reduce((acc: IconOption[], { id, data_types_id, label, data }) => {
          acc.push({
            dataTypeId: data_types_id,
            propertyId: id,
            label,
            value: data.value,
            color: data.color,
            type: data.type,
          })
          return acc
        }, [])
    }, []),
  })

  useEffect(() => {
    if (!propertyIcons) {
      return
    }
    setCustomIcons(propertyIcons)
  }, [propertyIcons])

  const iconOptions = useMemo<IconOption[]>(() => {
    return icons ? [...new Set([...customIcons, ...icons])] : []
  }, [icons, customIcons])

  useMountEffect(() => {
    if (isNew) {
      resetChannel()
    }
  })

  useEffect(() => {
    if (!data) {
      return
    }

    // Use channel atom if matching ids.
    let source = data
    if (!isNil(channel.id) && channel.id === data.id) {
      source = channel
    } else {
      resetChannel()
      setChannel(data)
    }

    reset({
      template_id: source.data.template ?? '',
      data_type_id: source.data_types_id ?? null,
      name: source.name ?? '',
      channel: source.channel ?? '',
      chart: source.data.chart?.name ?? null,
      icon: source.data.icon?.id ?? null,
      widget: source.data.widget ?? null,
      properties: [
        ...(Array.isArray(source.data?.properties)
          ? source.data.properties
          : []),
      ],
    })
  }, [reset, data, channel, setChannel, resetChannel])

  const { data: widgets, isFetching: isFetchingWidgets } = useQuery<
    GetResponse<WidgetModel>,
    Error,
    SelectWidgetOptionProps[]
  >({
    queryKey: ['GetWidgets'],
    queryFn: ({ signal }) =>
      getWidgets({
        signal,
      }),
    select: useCallback((data: GetResponse<WidgetModel> | undefined) => {
      const rows = data?.rows
        .filter(({ id }) => id !== 'region')
        .sort((a, b) => a.order - b.order)
      const accumulator: any[] = []
      if (rows) {
        for (const { id, label } of rows) {
          accumulator.push({
            name: label,
            value: id,
            ...widgetMeta(id),
          })
        }
      }
      return accumulator
    }, []),
  })

  const widgetOptions = useMemo<SelectWidgetOptionProps[]>(
    () => widgets ?? [],
    [widgets]
  )

  const getDefaultIcon = (value: string): string => {
    switch (value) {
      case 'toggle':
      case 'status':
        return 'dev-icon dev-digital-2state'
      case 'list':
        return 'dev-icon dev-alerts'
      case 'tracking':
        return 'dev-icon dev-map'
      case 'button':
        return 'dev-icon dev-button'
      default:
        return 'dev-icon dev-value'
    }
  }

  const onAddIcon = (): void => {
    const defaultIconCss = getDefaultIcon(channel.data.template ?? 'value')
    const icon = iconOptions.find(({ css }) => css === defaultIconCss)

    modal({
      component: <EditIcon />,
      config: {
        title: 'Add Custom Icon',
        data: {
          id: icon?.value,
          dataTypesId: channel.data_types_id,
          label: `${channel.name} (custom)`,
          svg: icon?.svg,
        },
        onCallback: (iconOption: IconOption) => {
          setCustomIcons((prev) => [iconOption, ...prev])
        },
      },
    })
  }

  const hasTemplate = (keys: string[]): boolean => {
    return channel.data.template ? keys.includes(channel.data.template) : false
  }

  const asIconProperty = (props: IconOption): ChannelIcon => {
    const { value, color, css, label, type } = props
    return {
      id: value,
      name: label,
      value: css ?? '',
      color,
      type,
    }
  }

  const asChartProperty = (props: any): ChannelChart => {
    const { value, label } = props
    return {
      name: value,
      label,
    }
  }

  const asObject = ({ id, label, data_types_id, data }: any): any => ({
    ...data,
    id,
    label,
    data_types_id,
  })

  const getObjects = (props: PropertyModel<DataTypeObject>[]): any[] => {
    return (props ?? []).map((prop) => asObject(prop))
  }

  const getDataTypeObject = async (
    dataTypeId: number
  ): Promise<DataTypeObject> => {
    const dataType = await queryClient.fetchQuery({
      queryKey: ['GetDataType', dataTypeId],
      queryFn: ({ signal }) =>
        getDataType({
          id: dataTypeId,
          signal,
        }),
    })

    const { rows } = await queryClient.fetchQuery({
      queryKey: ['GetProperties', dataTypeId, 'template'],
      queryFn: ({ signal }) =>
        getProperties({
          dataTypeId,
          type: 'template',
          signal,
        }),
    })

    return {
      ...dataType,
      template: rows.find(({ type }) => type === 'template')?.data?.value ?? '',
      props: rows ?? [],
    }
  }

  const setDefaultChart = (template: string): void => {
    let value: Maybe<string> = null

    switch (template) {
      case 'status':
        value = 'bar'
        break
      case 'button':
      case 'toggle':
      case 'list':
      case 'image':
        value = null
        break
      default:
        value = 'line'
    }

    const chartObject = isNotNil(value)
      ? asChartProperty(chartOptions.find((option) => option.value === value))
      : null

    setChart(chartObject)
    setValue('chart', value, { shouldValidate: true })
  }

  const iconFallback = (): Maybe<ChannelIcon> => {
    const icon = iconOptions.find(
      ({ css }) => css === 'dev-icon dev-generic-analog'
    )
    return icon ? asIconProperty(icon) : null
  }

  const getIconObject = ({ props }: DataTypeObject): Maybe<ChannelIcon> => {
    const prop = props.find(({ type }) => type === 'icon')
    const option = iconOptions.find(({ css }) => css === prop?.data?.value)
    const object = option ? asIconProperty(option) : iconFallback()

    if (!option) {
      toast({
        status: 'warning',
        msg: `Icon library not synced with db entries. “${prop?.data?.value}” missing or deprecated icon name. Using fallback icon.`,
        duration: 5000,
      })
    }

    return object
  }

  const onChangeDataType = async (value: Maybe<number>): Promise<void> => {
    if (!value) {
      return
    }

    const datatype = await getDataTypeObject(value)

    if (!datatype) {
      throw new Error(`Data type for ID “${value}” not found.`)
    }

    const iconObject = getIconObject(datatype)
    const { template } = channel.data

    setValue('template_id', datatype.template, { shouldValidate: true })
    setValue('icon', iconObject?.id ?? null, { shouldValidate: true })

    setDefaultChart(datatype.template)

    const list: {
      [key: string]: any
    } = {
      units: [],
      statuses: [],
      commands: [],
    }

    switch (datatype.template) {
      case 'value':
      case 'tracking':
        list.units = getObjects(
          datatype.props.filter(
            ({ type }: PropertyModel<DataTypeObject>) => type === 'unit'
          )
        )
        break
      case 'status':
        list.statuses = getObjects(
          datatype.props.filter(
            ({ type }: PropertyModel<DataTypeObject>) => type === 'status'
          )
        )
        break
      case 'toggle': {
        list.statuses = getObjects(
          datatype.props.filter(
            ({ type }: PropertyModel<DataTypeObject>) => type === 'status'
          )
        )
        list.commands = getObjects(
          datatype.props.filter(
            ({ type }: PropertyModel<DataTypeObject>) => type === 'toggle'
          )
        )
        break
      }
      case 'button':
      case 'list':
        list.commands = getObjects(
          datatype.props.filter(
            ({ type }: PropertyModel<DataTypeObject>) => type === template
          )
        )
        break
      case 'image': // image has no data types.
        break
      default:
        toast({
          status: 'error',
          msg: `Template type “${datatype.template}” not implemented.`,
        })
    }

    setChannel((prev) => ({
      ...prev,
      name: datatype.name,
      data: {
        ...prev.data,
        template: datatype.template,
        icon: iconObject,
        ...list,
      },
    }))
  }

  const getDataTypeName = async (): Promise<string> => {
    if (isNil(channel.data_types_id)) {
      return ''
    }
    const datatype = await getDataTypeObject(channel.data_types_id)
    return datatype?.name ?? ''
  }

  const getChannelOrder = async (): Promise<number> => {
    const name = await getDataTypeName()
    const order = ['BATTERY', 'RSSI'].includes(name.toLocaleUpperCase())
      ? 200
      : channel.order

    setChannel((prev) => ({
      ...prev,
      order,
    }))

    return order
  }

  const getNotification = (template: string): string => {
    const match = TEMPLATES.find(({ value }) => value === template)
    return match?.notification ?? ''
  }

  const getAlertType = (value: string): string => {
    switch (value) {
      case 'value':
        return 'min_max'
      case 'status':
        return 'status'
      default:
        return value // ?
    }
  }

  const getValueRules = (): ChannelRuleTemplate[] => {
    const current = channel.data.rule_templates?.[0]
    const { template } = channel.data

    if (!template) {
      setRuleTemplate([])
      return []
    }

    const notification =
      current?.notification_template ?? getNotification(template)

    const rules: ChannelRuleTemplate[] = [
      {
        id: current?.id ?? uuid(),
        type: getAlertType(template),
        label: channel.name,
        notification_template: notification,
        order: current?.order ?? 0,
        enabled: current?.enabled ?? true,
      },
    ]

    setRuleTemplate(rules)
    return rules
  }

  const getStatusRules = (): ChannelRuleTemplate[] => {
    if (channel.data.statuses.length === 0) {
      throw new Error('Unable to set rules because “statuses” are empty.')
    }

    const rules = channel.data.statuses.reduce(
      (acc: ChannelRuleTemplate[], { label, value }, index) => {
        const current = channel.data.rule_templates[index]
        const { template } = channel.data

        if (!template) {
          return acc
        }

        const notification =
          current?.notification_template ??
          interpolate(getNotification(template), {
            label,
          })

        acc[index] = {
          id: current?.id ?? uuid(),
          type: getAlertType(template),
          label,
          value,
          notification_template: notification,
          order: current?.order || index,
          enabled: current?.enabled ?? true,
        }

        return acc
      },
      []
    )

    setRuleTemplate(rules)
    return rules
  }

  const getRules = (template: string): ChannelRuleTemplate[] => {
    switch (template) {
      case 'value':
        return getValueRules()
      case 'status':
      case 'toggle':
        return getStatusRules()
      default:
        return []
    }
  }

  const create = async (values: FormInputProps): Promise<boolean> => {
    if (!typeId) {
      return false
    }

    const order = await getChannelOrder()
    const rules = getRules(values.template_id ?? '')
    const input = {
      ...channel,
      device_type_id: typeId,
      order: order,
      data: {
        ...channel.data,
        rule_templates: rules,
      },
    } satisfies ChannelModel

    try {
      await createChannelMutation({
        typeId,
        input,
      })

      toast({
        status: 'success',
        msg: 'Capability has been created.',
      })

      navigate(`/manage/device-templates/${typeId}/capabilities`, {
        viewTransition: true,
      })
      return true
    } catch (error: unknown) {
      Sentry.captureException(error)
      toast({
        status: 'error',
        msg: 'Unable to create capability.',
      })
      return false
    }
  }

  const update = async (values: FormInputProps): Promise<boolean> => {
    if (!(typeId && channelId)) {
      return false
    }

    const order = await getChannelOrder()
    const rules = getRules(values.template_id ?? '')
    const input = {
      ...channel,
      order: order,
      data: {
        ...channel.data,
        rule_templates: rules,
      },
    } satisfies ChannelModel

    try {
      await updateChannelMutation({
        typeId,
        channelId,
        input,
      })

      toast({
        status: 'success',
        msg: 'Capability has been updated.',
      })
      return true
    } catch (error: unknown) {
      Sentry.captureException(error)
      toast({
        status: 'error',
        msg: 'Unable to update capability.',
      })
      return false
    }
  }

  const getJsonChannel = useCallback(
    (): string => JSON.stringify(channel, null, 2),
    [channel]
  )

  const onGoBack = (): void => {
    resetChannel()
    navigate(`/manage/device-templates/${typeId}/capabilities`, {
      viewTransition: true,
    })
  }

  const onSubmit = async (values: FormInputProps): Promise<boolean> =>
    isNew ? await create(values) : await update(values)

  return (
    <Suspense fallback={null}>
      <EditHeader />
      <Box>
        <Box as="form" noValidate onSubmit={handleSubmit(onSubmit)}>
          <Container maxW="container.xl" m={0} pt={4}>
            <Grid
              gap={4}
              templateColumns={{ base: '1fr', lg: 'repeat(2, 1fr)' }}
            >
              <SelectField
                name="template_id"
                label="Template"
                control={control}
                isRequired
                options={templateOptions}
                onChange={({ value }: BaseOption<string>) => {
                  setValue('template_id', value, { shouldValidate: true })
                  setTemplate(value)
                  setDefaultChart(value)
                }}
              />
              <SelectDataTypeField
                name="data_type_id"
                label="Data Type"
                control={control}
                isRequired
                defaultValue={channel.data_types_id ?? ''}
                onChange={({ value }: any) => {
                  setValue('data_type_id', value, { shouldValidate: true })
                  setChannel((prev) => ({
                    ...prev,
                    data_types_id: value,
                  }))
                  onChangeDataType(value)
                }}
              />
              <InputField
                name="name"
                label="Name"
                control={control}
                isRequired
                onChange={(event: BaseSyntheticEvent) => {
                  const { value } = event.currentTarget
                  setValue('name', value, { shouldValidate: true })
                  setChannel((prev) => ({
                    ...prev,
                    name: value,
                  }))
                }}
              />
              <InputField
                name="channel"
                label="Channel ID"
                control={control}
                isRequired
                onChange={(event: BaseSyntheticEvent) => {
                  const { value } = event.currentTarget
                  setValue('channel', value, { shouldValidate: true })
                  setChannel((prev) => ({
                    ...prev,
                    channel: value,
                  }))
                }}
              />
              <SelectField
                name="chart"
                label="Chart"
                control={control}
                isRequired
                options={chartOptions}
                onChange={(option: BaseOption<string>) => {
                  setValue('chart', option.value, { shouldValidate: true })
                  setChart({
                    label: option.label,
                    name: option.value,
                  })
                }}
              />
              <SelectField
                name="icon"
                label="Icon"
                control={control}
                isSearchable
                isRequired
                options={iconOptions}
                formatOptionLabel={SelectIconOptionLabel}
                components={{ Option: SelectRemoveIconOption }}
                onChange={(option: ChannelIcon) => {
                  setValue('icon', option.id, { shouldValidate: true })
                  setIcon(option)
                }}
                render={
                  <Button
                    type="button"
                    aria-label="Custom Icon"
                    variant="link"
                    colorScheme="secondary"
                    fontWeight="medium"
                    onClick={onAddIcon}
                  >
                    Custom Icon
                  </Button>
                }
              />
              <SelectField
                name="widget"
                label="Widget"
                control={control}
                isRequired
                options={widgetOptions}
                formatOptionLabel={SelectWidgetOptionLabel}
                isLoading={isFetchingWidgets}
                onChange={(option: SelectWidgetOptionProps) => {
                  setValue('widget', option.value, { shouldValidate: true })
                  setWidget(option.value)
                }}
              />
              <Portal>
                <Flex
                  sx={{
                    p: 4,
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    position: 'absolute',
                    bottom: 0,
                    left: open ? '250px' : '70px',
                    w: `calc(100% - ${open ? 250 : 70}px)`,
                    bgColor: 'white',
                    _dark: {
                      bgColor: 'gray.900',
                    },
                  }}
                >
                  <DebugDrawer values={getJsonChannel()} />
                  <HStack>
                    <Button
                      type="button"
                      aria-label="Cancel"
                      colorScheme="gray"
                      onClick={onGoBack}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      aria-label="Save"
                      colorScheme="green"
                      minW={120}
                      isLoading={isSubmitting}
                      isDisabled={!isValid || isSubmitting}
                      onClick={handleSubmit(onSubmit)}
                      loadingText="Saving"
                    >
                      Save
                    </Button>
                  </HStack>
                </Flex>
              </Portal>
            </Grid>
          </Container>
        </Box>
      </Box>
      <Box pb={20}>
        {hasTemplate(['value', 'tracking']) && <UnitList />}
        {hasTemplate(['status', 'toggle']) && <StatusList />}
        {hasTemplate(['button', 'toggle', 'list']) && <CommandList />}
      </Box>
    </Suspense>
  )
})

export async function loader() {
  const scopes = ['edit:applications']

  const { checkLoaderPermissions } = await import('@/utils/loader-auth')
  await checkLoaderPermissions(scopes)

  return {
    title: 'Edit Device Template',
    scopes,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <Edit />
}
