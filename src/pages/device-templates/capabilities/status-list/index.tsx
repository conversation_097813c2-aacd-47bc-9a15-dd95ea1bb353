import * as Sentry from '@sentry/react'
import {
  lazy,
  memo,
  Suspense,
  useCallback,
  useMemo,
  type BaseSyntheticEvent,
} from 'react'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useAtomValue, useSetAtom } from 'jotai'
import { Box, Button, Flex, Switch } from '@chakra-ui/react'
import { Title } from '@/components/title'
import { AddButton } from '@/components/add-button'
import { DataTable } from '@/components/data-table/data-table'
import { useModal } from '@/hooks/use-modal'
import { useOrganization } from '@/hooks/use-organization'
import { useToast } from '@/hooks/use-toast'
import { deleteProperty } from '@/api/things/properties'
import { updateChannel } from '@/api/things/channels'
import { channelAtom, channelStatusAtom } from '@/utils/stores/channel'
import type { ChannelModel, ChannelStatus } from '@/types/models/channel'
import type { ColumnProps, ActionProps } from '@/types/table'

const ConfirmDialog = lazy(() =>
  import('@/components/confirm-dialog').then(({ ConfirmDialog }) => ({
    default: ConfirmDialog,
  }))
)

const Edit = lazy(() =>
  import('@/pages/device-templates/capabilities/status-list/edit').then(
    ({ Edit }) => ({
      default: Edit,
    })
  )
)

export const StatusList = memo(() => {
  const tableName = 'status-list'
  const modal = useModal()
  const toast = useToast()
  const queryClient = useQueryClient()
  const { organizationId, applicationId } = useOrganization()
  const channel = useAtomValue(channelAtom)
  const setChannelStatuses = useSetAtom(channelStatusAtom)

  const { mutateAsync: updateChannelMutation } = useMutation<
    ChannelModel,
    Error,
    {
      typeId: string
      channelId: number
      input: ChannelModel
    }
  >({
    mutationFn: ({ typeId, channelId, input }) =>
      updateChannel({
        organizationId,
        applicationId,
        typeId,
        channelId,
        input,
      }),
    onSuccess: () => {
      const cache = ['GetChannels', 'GetChannel']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const { mutateAsync: deletePropertyMutation } = useMutation<
    boolean,
    Error,
    {
      dataTypeId: number
      propertyId: number
    }
  >({
    mutationFn: ({ dataTypeId, propertyId }) =>
      deleteProperty(dataTypeId, propertyId),
    onSuccess: () => {
      const cache = ['GetProperties', 'GetProperty']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const onAdd = useCallback((): void => {
    modal({
      size: 'md',
      component: <Edit />,
      config: {
        title: 'Add Status',
        data: null,
        onCallback: () => ({}),
      },
    })
  }, [modal])

  const onEdit = useCallback(
    (row: ChannelStatus): void => {
      modal({
        size: 'md',
        component: <Edit />,
        config: {
          title: 'Edit Status',
          data: row,
          onCallback: () => ({}),
        },
      })
    },
    [modal]
  )

  const onToggleEnabled = useCallback(
    async (checked: boolean, row: ChannelStatus): Promise<void> => {
      if (!(channel?.id && channel?.device_type_id)) {
        toast({
          status: 'error',
          msg: 'Unable to update status.',
        })
        return
      }

      const index = channel.data.statuses.findIndex(({ id }) => row.id === id)
      const mutated = channel.data.statuses.reduce(
        (acc: ChannelStatus[], status, key: number) => {
          acc.push({
            ...status,
            enabled: key === index ? checked : status.enabled,
          })
          return acc
        },
        []
      )

      const input = {
        ...channel,
        data: {
          ...channel.data,
          statuses: mutated,
        },
      } satisfies ChannelModel

      try {
        await updateChannelMutation({
          typeId: channel.device_type_id,
          channelId: channel.id,
          input,
        })

        setChannelStatuses(mutated)

        toast({
          status: 'success',
          msg: `Status has been ${checked ? 'enabled' : 'disabled'}.`,
        })
      } catch (error: unknown) {
        Sentry.captureException(error)
        toast({
          status: 'error',
          msg: 'Unable to update status.',
        })
      }
    },
    [toast, channel, setChannelStatuses, updateChannelMutation]
  )

  const onRemove = useCallback(
    ({ id, label, data_types_id }: ChannelStatus): void => {
      modal({
        size: 'md',
        component: <ConfirmDialog />,
        config: {
          title: 'Remove Status',
          description: `Are you sure you want to remove “${label}”?`,
          confirmLabel: 'Remove',
          onCallback: async () => {
            try {
              await deletePropertyMutation({
                dataTypeId: data_types_id,
                propertyId: id,
              })

              if (channel?.id && channel?.device_type_id) {
                const mutated = channel.data.statuses.filter(
                  (status: ChannelStatus) => status.id !== id
                )

                const input = {
                  ...channel,
                  data: {
                    ...channel.data,
                    statuses: mutated,
                  },
                } satisfies ChannelModel

                await updateChannelMutation({
                  typeId: channel.device_type_id,
                  channelId: channel.id,
                  input,
                })

                setChannelStatuses(mutated)
              }

              toast({
                status: 'success',
                msg: `"${label}" has been removed.`,
              })
            } catch (error: unknown) {
              Sentry.captureException(error)
              toast({
                status: 'error',
                msg: `Unable to remove "${label}".`,
              })
            }
          },
        },
      })
    },
    [
      modal,
      toast,
      channel,
      setChannelStatuses,
      deletePropertyMutation,
      updateChannelMutation,
    ]
  )

  const columns = useMemo<ColumnProps<ChannelStatus>[]>(
    () => [
      {
        name: 'Display',
        id: 'label',
        cell: ({ label }) => <Box noOfLines={1}>{label}</Box>,
      },
      {
        name: 'Value',
        id: 'value',
        cell: ({ value }) => <Box noOfLines={1}>{value}</Box>,
      },
      {
        name: 'Show in UI',
        id: 'enabled',
        w: '10%',
        textAlign: 'center',
        cell: (row) => (
          <Box textAlign="center">
            <Switch
              id={`toggle-${row.id}`}
              defaultChecked={row.enabled}
              colorScheme="secondary"
              onChange={(event: BaseSyntheticEvent) =>
                onToggleEnabled(event.target.checked, row)
              }
              isDisabled={!channel?.id}
            />
          </Box>
        ),
      },
    ],
    [channel, onToggleEnabled]
  )

  const actions = useMemo<ActionProps<ChannelStatus>[]>(
    () => [
      {
        label: 'Edit Status',
        onClick: onEdit,
        canView: (): boolean => !!channel?.id,
      },
      {
        label: 'Remove Status',
        onClick: onRemove,
        canView: (): boolean => true,
      },
    ],
    [channel, onEdit, onRemove]
  )

  const emptyRows = useCallback(
    () => (
      <Box>
        <Box as="span" pr={1}>
          No records exist,
        </Box>
        <Button
          type="button"
          aria-label="Add Status"
          variant="link"
          colorScheme="secondary"
          fontWeight="normal"
          onClick={onAdd}
        >
          add status
        </Button>
        .
      </Box>
    ),
    [onAdd]
  )

  return (
    <>
      <Flex align="center" justify="space-between" p={4}>
        <Title title="Statuses" />
        {channel?.data_types_id && (
          <AddButton label="Add Status" onClick={onAdd} />
        )}
      </Flex>
      <Box px={4} mb={10}>
        <Suspense fallback={null}>
          <DataTable
            sx={{
              borderRadius: 'base',
              overflow: 'hidden',
              boxShadow: 'sm',
              bgColor: 'whiteAlpha.400',
              _dark: {
                bgColor: 'blackAlpha.50',
              },
              th: {
                bgColor: 'whiteAlpha.900',
                _dark: {
                  bgColor: 'blackAlpha.100',
                },
              },
            }}
            tableName={tableName}
            data={{
              rows: channel.data.statuses,
              count: channel.data.statuses.length,
            }}
            columns={columns}
            actions={actions}
            emptyRows={emptyRows}
          />
        </Suspense>
      </Box>
    </>
  )
})
