import * as Sentry from '@sentry/react'
import {
  lazy,
  memo,
  Suspense,
  useCallback,
  useMemo,
  type BaseSyntheticEvent,
} from 'react'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useAtomValue, useSetAtom } from 'jotai'
import { Box, Button, Flex, Switch } from '@chakra-ui/react'
import { Title } from '@/components/title'
import { AddButton } from '@/components/add-button'
import { DataTable } from '@/components/data-table/data-table'
import { useModal } from '@/hooks/use-modal'
import { useOrganization } from '@/hooks/use-organization'
import { useToast } from '@/hooks/use-toast'
import { deleteProperty } from '@/api/things/properties'
import { updateChannel } from '@/api/things/channels'
import { channelAtom, channelCommandAtom } from '@/utils/stores/channel'
import type { ChannelModel, ChannelCommand } from '@/types/models/channel'
import type { ColumnProps, ActionProps } from '@/types/table'

const ConfirmDialog = lazy(() =>
  import('@/components/confirm-dialog').then(({ ConfirmDialog }) => ({
    default: ConfirmDialog,
  }))
)

const Edit = lazy(() =>
  import('@/pages/device-templates/capabilities/command-list/edit').then(
    ({ Edit }) => ({
      default: Edit,
    })
  )
)

export const CommandList = memo(() => {
  const tableName = 'command-list'
  const modal = useModal()
  const toast = useToast()
  const queryClient = useQueryClient()
  const { organizationId, applicationId } = useOrganization()
  const channel = useAtomValue(channelAtom)
  const setChannelCommands = useSetAtom(channelCommandAtom)

  const { mutateAsync: updateChannelMutation } = useMutation<
    ChannelModel,
    Error,
    {
      typeId: string
      channelId: number
      input: ChannelModel
    }
  >({
    mutationFn: ({ typeId, channelId, input }) =>
      updateChannel({
        organizationId,
        applicationId,
        typeId,
        channelId,
        input,
      }),
    onSuccess: () => {
      const cache = ['GetChannels', 'GetChannel']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const { mutateAsync: deletePropertyMutation } = useMutation<
    boolean,
    Error,
    {
      dataTypeId: number
      propertyId: number
    }
  >({
    mutationFn: ({ dataTypeId, propertyId }) =>
      deleteProperty(dataTypeId, propertyId),
    onSuccess: () => {
      const cache = ['GetProperties', 'GetProperty']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const onAdd = useCallback((): void => {
    modal({
      size: '4xl',
      component: <Edit />,
      config: {
        title: 'Add Command',
        data: null,
        onCallback: () => ({}),
      },
    })
  }, [modal])

  const onEdit = useCallback(
    (row: ChannelCommand): void => {
      modal({
        size: '4xl',
        component: <Edit />,
        config: {
          title: 'Edit Command',
          data: row,
          onCallback: () => ({}),
        },
      })
    },
    [modal]
  )

  const onToggleEnabled = useCallback(
    async (checked: boolean, row: ChannelCommand): Promise<void> => {
      if (!(channel.id && channel.device_type_id)) {
        toast({
          status: 'error',
          msg: 'Unable to update command.',
        })
        return
      }

      const index = channel.data.commands.findIndex(({ id }) => row.id === id)
      const mutated = channel.data.commands.reduce(
        (acc: ChannelCommand[], command, key: number) => {
          acc.push({
            ...command,
            enabled: key === index ? checked : command.enabled,
          })
          return acc
        },
        [] as ChannelCommand[]
      )

      const input = {
        ...channel,
        data: {
          ...channel.data,
          commands: mutated,
        },
      } satisfies ChannelModel

      try {
        await updateChannelMutation({
          typeId: channel.device_type_id,
          channelId: channel.id,
          input,
        })

        setChannelCommands(mutated)

        toast({
          status: 'success',
          msg: `Command has been ${checked ? 'enabled' : 'disabled'}.`,
        })
      } catch (error: unknown) {
        Sentry.captureException(error)
        toast({
          status: 'error',
          msg: 'Unable to update command.',
        })
      }
    },
    [toast, channel, setChannelCommands, updateChannelMutation]
  )

  const onRemove = useCallback(
    ({ id, label, data_types_id }: ChannelCommand): void => {
      modal({
        size: 'md',
        component: <ConfirmDialog />,
        config: {
          title: 'Remove Command',
          description: `Are you sure you want to remove “${label}”?`,
          confirmLabel: 'Remove',
          onCallback: async () => {
            try {
              await deletePropertyMutation({
                dataTypeId: data_types_id,
                propertyId: id,
              })

              if (channel?.id && channel?.device_type_id) {
                const mutated = channel.data.commands.filter(
                  (command: ChannelCommand) => command.id !== id
                )

                const input = {
                  ...channel,
                  data: {
                    ...channel.data,
                    commands: mutated,
                  },
                } satisfies ChannelModel

                await updateChannelMutation({
                  typeId: channel.device_type_id,
                  channelId: channel.id,
                  input,
                })

                setChannelCommands(mutated)
              }

              toast({
                status: 'success',
                msg: `“${label}” has been removed.`,
              })
            } catch (error: unknown) {
              Sentry.captureException(error)
              toast({
                status: 'error',
                msg: `Unable to remove “${label}”.}`,
              })
            }
          },
        },
      })
    },
    [
      toast,
      modal,
      channel,
      setChannelCommands,
      deletePropertyMutation,
      updateChannelMutation,
    ]
  )

  const columns = useMemo<ColumnProps<ChannelCommand>[]>(
    () => [
      {
        name: 'Display',
        id: 'label',
        cell: ({ label }) => <Box noOfLines={1}>{label}</Box>,
      },
      {
        name: 'Port',
        id: 'port',
        w: '10%',
        textAlign: 'center',
        cell: ({ port }) => (
          <Box noOfLines={1} textAlign="center">
            {port}
          </Box>
        ),
      },
      {
        name: 'Command',
        id: 'command',
        cell: ({ payload }) => <Box noOfLines={1}>{payload}</Box>,
      },
      {
        name: 'Message Box',
        id: 'message',
        cell: ({ message }) => <Box noOfLines={1}>{message}</Box>,
      },
      {
        name: 'Show in UI',
        id: 'enabled',
        w: '10%',
        textAlign: 'center',
        cell: (row) => (
          <Box textAlign="center">
            <Switch
              id={`toggle-${row.id}`}
              defaultChecked={row.enabled}
              colorScheme="secondary"
              onChange={(event: BaseSyntheticEvent) =>
                onToggleEnabled(event.target.checked, row)
              }
              isDisabled={!channel?.id}
            />
          </Box>
        ),
      },
    ],
    [channel, onToggleEnabled]
  )

  const actions = useMemo<ActionProps<ChannelCommand>[]>(
    () => [
      {
        label: 'Edit Command',
        onClick: onEdit,
        canView: (): boolean => !!channel?.id,
      },
      {
        label: 'Remove Command',
        onClick: onRemove,
        canView: (): boolean => true,
      },
    ],
    [channel, onEdit, onRemove]
  )

  const emptyRows = () => (
    <Box>
      <Box as="span" pr={1}>
        No records exist,
      </Box>
      <Button
        type="button"
        aria-label="Add Command"
        variant="link"
        colorScheme="secondary"
        fontWeight="normal"
        onClick={onAdd}
      >
        add command
      </Button>
      .
    </Box>
  )

  return (
    <>
      <Flex align="center" justify="space-between" p={4}>
        <Title title="Commands" />
        {channel?.data_types_id && (
          <AddButton label="Add Command" onClick={onAdd} />
        )}
      </Flex>
      <Box px={4} mb={10}>
        <Suspense fallback={null}>
          <DataTable
            sx={{
              borderRadius: 'base',
              overflow: 'hidden',
              boxShadow: 'sm',
              bgColor: 'whiteAlpha.400',
              _dark: {
                bgColor: 'blackAlpha.50',
              },
              th: {
                bgColor: 'whiteAlpha.900',
                _dark: {
                  bgColor: 'blackAlpha.100',
                },
              },
            }}
            tableName={tableName}
            data={{
              rows: channel.data.commands,
              count: channel.data.commands.length,
            }}
            columns={columns}
            actions={actions}
            emptyRows={emptyRows}
          />
        </Suspense>
      </Box>
    </>
  )
})
