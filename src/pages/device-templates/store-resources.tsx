import { memo, Suspense } from 'react'
import { useNavigate, type LoaderFunctionArgs } from 'react-router'
import { Helm<PERSON> } from 'react-helmet-async'
import { Box, Flex, HStack } from '@chakra-ui/react'
import { Title } from '@/components/title'
import { BackButton } from '@/components/back-button'
import { Tabs } from '@/components/tabs'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { useRouter } from '@/hooks/use-router'
import { metaTitle } from '@/utils/meta-title'

const StoreResources = memo(() => {
  const heading = 'Edit Device Template'
  const navigate = useNavigate()
  const { title, currentPath } = useRouter()

  const link = 'https://mydevices.my.salesforce-sites.com/devices/AddDevice'

  const onGoBack = (): void => {
    navigate('/manage/device-templates', {
      viewTransition: true,
    })
  }

  return (
    <>
      <Helmet>
        <title>{metaTitle([title, heading])}</title>
      </Helmet>
      <Suspense fallback={null}>
        <Flex align="center" justify="space-between" p={4}>
          <HStack>
            <BackButton onClick={onGoBack} />
            <Title title={heading} />
          </HStack>
        </Flex>
        <Box px={4}>
          <Tabs current={currentPath}>
            <Box
              as="iframe"
              id="store-resources"
              title="Store Resources"
              src={link}
              allowFullScreen={true}
              style={{
                width: '100%',
                height: '100vh',
              }}
            />
          </Tabs>
        </Box>
      </Suspense>
    </>
  )
})

export async function loader({ params }: LoaderFunctionArgs) {
  const scopes = ['edit:applications']

  const { checkLoaderPermissions } = await import('@/utils/loader-auth')
  await checkLoaderPermissions(scopes)

  const { getFilteredTabs } = await import('@/utils/tab-loader')
  const tabs = await getFilteredTabs('template-tabs', params)

  return {
    title: 'Store Resources',
    scopes,
    tabs,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <StoreResources />
}
