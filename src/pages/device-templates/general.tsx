import * as Sentry from '@sentry/react'
import { lazy, Suspense, useCallback, useEffect, useMemo, memo } from 'react'
import { Helmet } from 'react-helmet-async'
import { useNavigate, useParams, type LoaderFunctionArgs } from 'react-router'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { useAtomValue } from 'jotai'
import { isEmpty, omit, pick, pickBy } from 'ramda'
import {
  Box,
  Button,
  Container,
  Flex,
  Grid,
  HStack,
  Portal,
} from '@chakra-ui/react'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { Title } from '@/components/title'
import { Tabs } from '@/components/tabs'
import { BackButton } from '@/components/back-button'
import { InputField } from '@/components/input-field'
import { SelectField } from '@/components/select-field'
import { TextareaField } from '@/components/textarea-field'
import { DebugDrawer } from '@/components/debug-drawer'
import { CheckboxField } from '@/components/checkbox-field'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { SelectIconOptionLabel } from '@/features/capabilities/select-icon-option-label'
import { SelectRemoveIconOption } from '@/features/capabilities/select-remove-icon-option'
import { SelectCodecOption } from '@/features/capabilities/select-codec-option'
import { useAuth } from '@/contexts/use-auth'
import { useRouter } from '@/hooks/use-router'
import { useModal } from '@/hooks/use-modal'
import { useOrganization } from '@/hooks/use-organization'
import { useToast } from '@/hooks/use-toast'
import { metaTitle } from '@/utils/meta-title'
import { isUUID } from '@/utils/is-uuid'
import { isJson } from '@/utils/is-json'
import { getIcons } from '@/utils/get-icons'
import { navAtom } from '@/utils/stores/nav'
import { createType, deleteType, getType, updateType } from '@/api/things/types'
import { createMeta, getMetas, updateMeta } from '@/api/things/metas'
import { getCodecs } from '@/api/codecs'
import { getDevices } from '@/api/devices'
import type { TypeModel } from '@/types/models/type'
import type { GetResponse, TypeInput, MetaInput } from '@/types/api'
import type { MetaModel } from '@/types/models/meta'
import type { CodecModel } from '@/types/models/codec'
import type { DeviceModel } from '@/types/models/device'
import type { IconModel } from '@/types/models/icon'

const ConfirmDialog = lazy(() =>
  import('@/components/confirm-dialog').then(({ ConfirmDialog }) => ({
    default: ConfirmDialog,
  }))
)

const categoryOptions = [
  // { label: 'Actuator', value: 'actuator' },
  // { label: 'Computer', value: 'computer' },
  { label: 'Gateway', value: 'gateway' },
  { label: 'End Device', value: 'module' },
  // { label: 'Sensor', value: 'sensor' },
]

const subcategoryOptions = [
  { label: 'Bluetooth', value: 'bluetooth' },
  { label: 'LoRa', value: 'lora' },
  { label: 'MQTT', value: 'mqtt' },
]

const schema = z.object({
  name: z.string().min(1, 'Name is required.'),
  application_id: z.string().min(1, 'Application is required.').default('*'),
  manufacturer: z.string(),
  model: z.string(),
  category: z.string().min(1, 'Category is required.'),
  subcategory: z.string().min(1, 'Subcategory is required.'),
  codec: z.string(),
  general_icon: z.string(),
  ip_rating: z.string(),
  certifications: z.string(),
  description: z.string().min(3, 'Description is required.'),
  keywords: z.string(),
  is_public: z.boolean(),
  is_example: z.boolean(),
  version: z.string(),
})

type FormInputProps = z.infer<typeof schema>

const General = memo(() => {
  const { title, currentPath } = useRouter()
  const { id } = useParams()
  const typeId = useMemo<string | undefined>(() => id, [id])
  const modal = useModal()
  const toast = useToast()
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  const { organizationId, applicationId } = useOrganization()
  const { isInternalAdmin } = useAuth()
  const { open } = useAtomValue(navAtom)

  const isNew = useMemo<boolean>(() => !typeId || typeId === 'add', [typeId])

  const heading = useMemo<string>(
    () => (isNew ? 'Add Device Template' : 'Edit Device Template'),
    [isNew]
  )

  const {
    reset,
    control,
    handleSubmit,
    formState: { isValid, isSubmitting },
  } = useForm({
    mode: 'onTouched',
    resolver: zodResolver(schema),
    defaultValues: {
      name: '',
      application_id: '*',
      manufacturer: '',
      model: '',
      category: '',
      subcategory: '',
      codec: '',
      general_icon: '',
      ip_rating: '',
      certifications: '',
      description: '',
      keywords: '',
      is_public: false,
      is_example: false,
      version: '',
    },
  })

  const { mutateAsync: createTypeMutation } = useMutation<
    TypeModel,
    Error,
    TypeInput
  >({
    mutationFn: (input) => createType({ organizationId, applicationId, input }),
    onSuccess: () => queryClient.invalidateQueries({ queryKey: ['GetTypes'] }),
  })

  const { mutateAsync: updateTypeMutation } = useMutation<
    TypeModel,
    Error,
    TypeInput
  >({
    mutationFn: (input) => updateType({ organizationId, applicationId, input }),
    onSuccess: () => {
      const cache = ['GetTypes', 'GetType']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const { mutateAsync: createMetaMutation } = useMutation<
    MetaModel,
    Error,
    {
      typeId: string
      input: MetaInput
    }
  >({
    mutationFn: ({ typeId, input }) =>
      createMeta({ organizationId, applicationId, typeId, input }),
    onSuccess: () => queryClient.invalidateQueries({ queryKey: ['GetMetas'] }),
  })

  const { mutateAsync: updateMetaMutation } = useMutation<
    MetaModel,
    Error,
    {
      typeId: string
      metaId: number
      input: MetaInput
    }
  >({
    mutationFn: ({ typeId, metaId, input }) =>
      updateMeta({ organizationId, applicationId, typeId, metaId, input }),
    onSuccess: () => {
      const cache = ['GetMetas', 'GetMeta']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const { mutateAsync: deleteTypeMutation } = useMutation<
    boolean,
    Error,
    string
  >({
    mutationFn: (typeId) =>
      deleteType({ organizationId, applicationId, typeId }),
    onSuccess: () => {
      const cache = [
        'GetTypes',
        'GetType',
        'GetInfinityTypes',
        'GetChannels',
        'GetMetas',
        'GetUses',
      ]
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const { data: codecOptions, isFetching: isFetchingCodecs } = useQuery<
    Omit<GetResponse<CodecModel>, 'page'> | null,
    Error,
    BaseOption[]
  >({
    queryKey: ['GetCodecs', 2000, 0, organizationId, applicationId],
    queryFn: ({ signal }) =>
      organizationId && applicationId
        ? getCodecs({
            organizationId,
            applicationId,
            limit: 2000,
            offset: 0,
            publicCodec: true,
            signal,
          })
        : null,
    select: useCallback(
      (data: Omit<GetResponse<CodecModel>, 'page'> | null) => {
        return (data?.rows ?? []).reduce(
          (acc: BaseOption<string>[], { id, name }: CodecModel) => {
            acc.push({
              label: name,
              value: id,
            })
            return acc
          },
          []
        )
      },
      []
    ),
  })

  const { data: iconOptions, isFetching: isFetchingIcons } = useQuery<
    IconModel[],
    Error
  >({
    queryKey: ['GetIcons'],
    queryFn: () => getIcons(),
  })

  const { data } = useQuery<TypeModel | null, Error>({
    queryKey: ['GetType', organizationId, applicationId, typeId],
    queryFn: ({ signal }) =>
      typeId
        ? getType({
            organizationId,
            applicationId,
            typeId,
            signal,
          })
        : null,
    refetchOnWindowFocus: false,
    enabled: !!organizationId && !!applicationId && !isNew,
  })

  const getIcon = useCallback((meta: any): Maybe<IconModel> => {
    const match: any = meta.find(
      ({ key }: any) => key === 'general_device_icon'
    )
    if (!isJson(match?.value)) {
      return null
    }

    const icon = JSON.parse(match.value)

    // fallback for old stored icon objects in meta data.
    if (isUUID(icon?.value)) {
      return icon
    }
    if (isUUID(icon?.id)) {
      return {
        label: icon?.name,
        value: icon?.id,
        css: icon?.value,
        color: icon?.color ?? '#000',
        type: 'svg',
      }
    }

    return null
  }, [])

  useEffect(() => {
    if (!data?.id) {
      return
    }

    reset({
      name: data.name ?? '',
      application_id: data.application_id ?? '*',
      manufacturer: data.manufacturer ?? '',
      model: data.model ?? '',
      category: data.category ?? '',
      subcategory: data.subcategory ?? '',
      codec: data.codec ?? '',
      general_icon: getIcon(data.meta)?.value ?? '',
      ip_rating: data.ip_rating ?? '',
      certifications: data.certifications ?? '',
      description: data.description ?? '',
      keywords: data.keywords ?? '',
      is_public: data?.is_public ?? false,
      is_example: data?.is_example ?? false,
      version: data?.version ?? '',
    })
  }, [reset, data, getIcon])

  const { data: devices } = useQuery<GetResponse<DeviceModel>, Error>({
    queryKey: ['GetDevices', typeId, 0, 1],
    queryFn: ({ signal }) =>
      getDevices({
        offset: 0,
        limit: 1,
        filter: `device_type_id eq ${typeId}`,
        signal,
      }),
    enabled: !!typeId,
  })

  const canRemove = useMemo<boolean>(
    () => (devices ? devices.count === 0 && !isNew : false),
    [devices, isNew]
  )

  const onGoBack = (): void => {
    navigate('/manage/device-templates', {
      viewTransition: true,
    })
  }

  const onRemove = useCallback((): void => {
    if (!data?.id) {
      return
    }

    modal({
      size: 'md',
      component: <ConfirmDialog />,
      config: {
        title: 'Remove Device Template?',
        description: `Are you sure you want to remove “${data.name}”?`,
        confirmLabel: 'Remove',
        onCallback: async () => {
          try {
            await deleteTypeMutation(data.id)
            toast({
              status: 'success',
              msg: `“${data.name}” has been removed.`,
            })

            navigate('/manage/device-templates', {
              viewTransition: true,
            })
          } catch (error: unknown) {
            Sentry.captureException(error)
            toast({
              status: 'error',
              msg: `Unable to remove “${data.name}”.`,
            })
          }
        },
      },
    })
  }, [toast, data, modal, navigate, deleteTypeMutation])

  const getIconReference = async (): Promise<MetaModel | undefined> => {
    if (!typeId) {
      return
    }

    try {
      const { rows } = await queryClient.fetchQuery({
        queryKey: ['GetMetas', typeId, organizationId, applicationId],
        queryFn: ({ signal }) =>
          getMetas({
            organizationId,
            applicationId,
            typeId,
            signal,
          }),
      })
      return rows.find(({ key }) => key === 'general_device_icon')
    } catch (error: unknown) {
      Sentry.captureException(error)
      toast({
        status: 'error',
        msg: 'Icon reference not found.',
      })
      return
    }
  }

  const createIcon = async ({
    typeId,
    uuid,
  }: {
    typeId: string
    uuid: string
  }): Promise<MetaModel | undefined> => {
    const currOption = (iconOptions ?? []).find(({ value }) => value === uuid)

    if (!currOption) {
      toast({
        status: 'error',
        msg: 'Unable to create icon reference.',
      })
      return
    }

    const input = {
      key: 'general_device_icon',
      value: JSON.stringify(omit(['icon', 'svg'], currOption)),
    } satisfies MetaInput

    try {
      return await createMetaMutation({
        typeId,
        input,
      })
    } catch (error: unknown) {
      Sentry.captureException(error)
      toast({
        status: 'error',
        msg: 'Unable to create icon reference.',
      })
      return
    }
  }

  const updateIcon = async (uuid: string): Promise<MetaModel | undefined> => {
    if (!(iconOptions && typeId)) {
      return
    }

    const currOption: any = iconOptions.find(({ value }) => value === uuid)
    const iconRef = await getIconReference()

    if (!iconRef?.id) {
      return
    }

    const input = {
      id: iconRef?.id,
      key: iconRef?.key,
      value: JSON.stringify(omit(['icon', 'svg'], currOption)),
    } satisfies MetaInput

    try {
      return await updateMetaMutation({
        typeId,
        metaId: iconRef?.id,
        input,
      })
    } catch (error: unknown) {
      Sentry.captureException(error)
      toast({
        status: 'error',
        msg: 'Unable to update icon reference.',
      })
      return
    }
  }

  const create = async (values: FormInputProps): Promise<boolean> => {
    const input = {
      ...omit<Omit<TypeInput, 'id'>, any>(
        ['application_id', 'organization_id', 'general_icon'],
        pickBy((values) => !isEmpty(values), values)
      ),
    } satisfies Omit<TypeInput, 'id'>

    try {
      const result = await createTypeMutation(input)

      if (values?.general_icon) {
        await createIcon({
          typeId: result.id,
          uuid: values.general_icon,
        })
      }

      toast({
        status: 'success',
        msg: 'Device Template has been created.',
      })

      navigate(`/manage/device-templates/${result?.id}/general`, {
        viewTransition: true,
      })

      return true
    } catch (error: unknown) {
      Sentry.captureException(error)
      toast({
        status: 'error',
        msg: 'Unable to create device template.',
      })
      return false
    }
  }

  const update = async (values: FormInputProps): Promise<boolean> => {
    if (!typeId) {
      return false
    }

    const input = {
      ...pick(
        [
          'name',
          'category',
          'subcategory',
          'description',
          'is_public',
          'is_example',
          'certifications',
          'ip_rating',
          'keywords',
          'version',
        ],
        values
      ),
      id: typeId,
      manufacturer: values.manufacturer ?? '',
      model: values.model ?? '',
      codec: values.codec,
    } satisfies Partial<TypeInput>

    try {
      await updateTypeMutation(input)

      if (values?.general_icon) {
        const iconRef = await getIconReference()

        const response = iconRef?.id
          ? await updateIcon(values.general_icon)
          : await createIcon({ typeId, uuid: values.general_icon })

        if (!response) {
          toast({
            status: 'error',
            msg: 'Unable to update device icon.',
          })
          return false
        }
      }

      toast({
        status: 'success',
        msg: 'Devices template has been updated.',
      })

      return true
    } catch (error: unknown) {
      Sentry.captureException(error)
      toast({
        status: 'error',
        msg: 'Unable to update device template.',
      })
      return false
    }
  }

  const getJsonData = useCallback(
    (): string => JSON.stringify(data, null, 2),
    [data]
  )

  const onSubmit = async (values: FormInputProps): Promise<boolean> =>
    isNew ? await create(values) : await update(values)

  return (
    <>
      <Helmet>
        <title>{metaTitle([title, heading])}</title>
      </Helmet>
      <Flex align="center" justify="space-between" p={4}>
        <HStack>
          <BackButton onClick={onGoBack} />
          <Title title={heading} />
        </HStack>
      </Flex>
      <Box px={4} pb={20}>
        <Tabs current={currentPath} isDisabled={isNew}>
          <Suspense fallback={null}>
            <Box as="form" noValidate onSubmit={handleSubmit(onSubmit)}>
              <Container maxW="container.xl" m={0} pt={4}>
                <Grid
                  gap={4}
                  templateColumns={{ base: '1fr', lg: 'repeat(2, 1fr)' }}
                >
                  <InputField
                    name="name"
                    label="Name"
                    placeholder="Displayed name"
                    control={control}
                    isRequired
                    autoFocus
                  />
                  <Box>
                    <Box
                      sx={{
                        mb: 4,
                        fontWeight: 'medium',
                      }}
                    >
                      Template Catalog
                    </Box>
                    <Flex>
                      <HStack minW="320px">
                        <CheckboxField
                          name="is_public"
                          label="Public Template"
                          control={control}
                          isDisabled={!isInternalAdmin}
                        />
                        <CheckboxField
                          name="is_example"
                          label="Example Template"
                          control={control}
                          isDisabled={!isInternalAdmin}
                        />
                      </HStack>
                    </Flex>
                  </Box>
                  <InputField
                    name="manufacturer"
                    label="Manufacturer"
                    placeholder="myDevices, Inc."
                    control={control}
                  />
                  <InputField
                    name="model"
                    label="Model"
                    placeholder="ABC-XYC"
                    control={control}
                  />
                  <SelectField
                    name="category"
                    label="Category"
                    control={control}
                    options={categoryOptions}
                    isRequired
                  />
                  <SelectField
                    name="subcategory"
                    label="Subcategory"
                    control={control}
                    options={subcategoryOptions}
                    isRequired
                  />
                  <SelectField
                    name="codec"
                    label="Codec"
                    control={control}
                    options={codecOptions}
                    components={{ Option: SelectCodecOption }}
                    isDisabled={codecOptions?.length === 0}
                    isLoading={isFetchingCodecs}
                    isSearchable
                  />
                  <SelectField
                    name="general_icon"
                    label="General Device Icon"
                    control={control}
                    formatOptionLabel={SelectIconOptionLabel}
                    components={{ Option: SelectRemoveIconOption }}
                    options={iconOptions}
                    isLoading={isFetchingIcons}
                    isSearchable
                  />
                  <InputField
                    name="certifications"
                    label="Certifications"
                    placeholder="FCC/CE/..."
                    control={control}
                  />
                  <InputField
                    name="ip_rating"
                    label="IP Rating"
                    placeholder="IP65"
                    control={control}
                  />
                  <TextareaField
                    name="description"
                    label="Description"
                    placeholder="Displayed description"
                    control={control}
                    isRequired
                  />
                  <TextareaField
                    name="keywords"
                    label="Keywords"
                    placeholder="Searchable keywords"
                    control={control}
                  />
                  <InputField
                    name="version"
                    label="Version"
                    placeholder="Version"
                    control={control}
                  />

                  <Portal>
                    <Flex
                      w={`calc(100% - ${open ? 250 : 70}px)`}
                      sx={{
                        p: 4,
                        position: 'absolute',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        bottom: 0,
                        left: open ? '250px' : '70px',
                        w: `calc(100% - ${open ? 250 : 70}px)`,
                        bgColor: 'white',
                        _dark: {
                          bgColor: 'gray.900',
                        },
                      }}
                    >
                      <HStack>
                        {data?.id && <DebugDrawer values={getJsonData()} />}
                        {canRemove && (
                          <Button
                            type="button"
                            aria-label="Remove"
                            colorScheme="red"
                            variant="ghost"
                            minW={120}
                            onClick={onRemove}
                          >
                            Remove
                          </Button>
                        )}
                      </HStack>
                      <HStack>
                        <Button
                          type="button"
                          aria-label="Cancel"
                          variant="ghost"
                          colorScheme="gray"
                          minW={120}
                          onClick={onGoBack}
                        >
                          Cancel
                        </Button>
                        <Button
                          type="submit"
                          aria-label="Save"
                          colorScheme="green"
                          minW={120}
                          isLoading={isSubmitting}
                          isDisabled={
                            !isValid ||
                            isSubmitting ||
                            (!isNew && applicationId !== data?.application_id)
                          }
                          loadingText="Saving"
                          onClick={handleSubmit(onSubmit)}
                        >
                          Save
                        </Button>
                      </HStack>
                    </Flex>
                  </Portal>
                </Grid>
              </Container>
            </Box>
          </Suspense>
        </Tabs>
      </Box>
    </>
  )
})

export async function loader({ params }: LoaderFunctionArgs) {
  const scopes = ['edit:applications']

  const { checkLoaderPermissions } = await import('@/utils/loader-auth')
  await checkLoaderPermissions(scopes)

  const { getFilteredTabs } = await import('@/utils/tab-loader')
  const tabs = await getFilteredTabs('template-tabs', params)

  return {
    title: 'Device Templates',
    scopes,
    tabs,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <General />
}
