import * as Sentry from '@sentry/react'
import { lazy, Suspense, useCallback, useMemo, memo } from 'react'
import { Helm<PERSON> } from 'react-helmet-async'
import { useNavigate, useParams, type LoaderFunctionArgs } from 'react-router'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { useAtomValue } from 'jotai'
import { Box, Flex, HStack, Switch } from '@chakra-ui/react'
import { Title } from '@/components/title'
import { BackButton } from '@/components/back-button'
import { Tabs } from '@/components/tabs'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { DataTable } from '@/components/data-table/data-table'
import { useRouter } from '@/hooks/use-router'
import { useModal } from '@/hooks/use-modal'
import { useOrganization } from '@/hooks/use-organization'
import { useToast } from '@/hooks/use-toast'
import { metaTitle } from '@/utils/meta-title'
import { tableAtom } from '@/utils/stores/table'
import { getChannels, updateChannel } from '@/api/things/channels'
import type { ChannelModel } from '@/types/models/channel'
import type { ChannelRuleTemplate } from '@/types/models/channel'
import type { PaginatedQueryResponse } from '@/types/api'
import type { ColumnProps, ActionProps } from '@/types/table'

interface AlertProps extends ChannelRuleTemplate {
  channel_id: Maybe<number>
}

const Edit = lazy(() =>
  import('@/pages/device-templates/alert-types/edit').then(({ Edit }) => ({
    default: Edit,
  }))
)

const getAlertType = (type: string): string => {
  switch (type) {
    case 'min_max':
    case 'value':
      return 'Min / Max Threshold'
    case 'status':
    case 'toggle':
      return 'Status'
    default:
      return type
  }
}

const AlertTypes = memo(() => {
  const heading = 'Edit Device Template'
  const tableName = 'alert-types'
  const modal = useModal()
  const toast = useToast()
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  const { organizationId, applicationId } = useOrganization()
  const { title, currentPath } = useRouter()
  const { id } = useParams()
  const typeId = useMemo<string | undefined>(() => id, [id])
  const { limit, currPage } = useAtomValue(tableAtom(tableName))

  const { mutateAsync: updateChannelMutation } = useMutation<
    ChannelModel,
    Error,
    {
      typeId: string
      channelId: number
      input: Partial<ChannelModel>
    }
  >({
    mutationFn: ({ typeId, channelId, input }) =>
      updateChannel({
        organizationId,
        applicationId,
        typeId,
        channelId,
        input,
      }),
    onSuccess: () => {
      const cache = ['GetChannels', 'GetChannel']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const { data: channels } = useQuery<
    PaginatedQueryResponse<ChannelModel> | null,
    Error
  >({
    queryKey: [
      'GetChannels',
      organizationId,
      applicationId,
      typeId,
      limit,
      currPage,
    ],
    queryFn: ({ signal }) =>
      typeId
        ? getChannels({
            organizationId,
            applicationId,
            typeId,
            limit,
            page: currPage,
            signal,
          })
        : null,
    enabled: !!organizationId && !!applicationId && !!typeId,
  })

  const alerts = useMemo<AlertProps[]>(
    () =>
      channels
        ? channels.rows.reduce(
            (acc: AlertProps[], channel: ChannelModel) =>
              acc.concat(
                channel.data?.rule_templates?.length > 0
                  ? channel.data.rule_templates.reduce(
                      (ac: any[], rule) =>
                        ac.concat({
                          ...rule,
                          channel_id: channel.id,
                        }),
                      []
                    )
                  : []
              ),
            []
          )
        : [],
    [channels]
  )

  const onGoBack = (): void => {
    navigate('/manage/device-templates', {
      viewTransition: true,
    })
  }

  const onEdit = useCallback(
    (row: AlertProps) => {
      if (!channels) {
        return
      }
      modal({
        component: <Edit />,
        config: {
          data: {
            id: row.id,
            label: row.label,
            notification: row.notification_template,
            channel: channels.rows.find(({ id }) => id === row.channel_id),
          },
          onCallback: () => ({}),
        },
      })
    },
    [modal, channels]
  )

  const onToggle = useCallback(
    async (checked: boolean, row: AlertProps) => {
      if (!(typeId && row.channel_id)) {
        return
      }

      const state = checked ? 'enabled' : 'disabled'
      const channel = (channels?.rows ?? []).find(
        ({ id }) => id === row.channel_id
      )

      if (!channel) {
        return
      }

      const rules = channel.data.rule_templates.reduce(
        (
          acc: ChannelRuleTemplate[],
          rule: ChannelRuleTemplate,
          key: number
        ) => {
          if (rule.id === row.id) {
            rule.enabled = checked
          }
          acc[key] = rule
          return acc
        },
        []
      )

      channel.data.rule_templates = rules

      try {
        await updateChannelMutation({
          typeId,
          channelId: row.channel_id,
          input: channel,
        })
        toast({
          status: 'success',
          msg: `“${row.label}” has been ${state}.`,
        })
      } catch (error: unknown) {
        Sentry.captureException(error)
        toast({
          status: 'error',
          msg: `“${row.label}” has been ${state}.`,
        })
      }
    },
    [toast, typeId, channels, updateChannelMutation]
  )

  const columns = useMemo<ColumnProps<AlertProps>[]>(
    () => [
      {
        name: 'Alert',
        id: 'label',
        w: '15%',
        cell: ({ label }) => <Box noOfLines={1}>{label}</Box>,
      },
      {
        name: 'Alert Type',
        id: 'type',
        w: '15%',
        cell: ({ type }) => <Box noOfLines={1}>{getAlertType(type)}</Box>,
      },
      {
        name: 'Text Message',
        id: 'notification_template',
        cell: ({ notification_template }) => (
          <Box noOfLines={1}>{notification_template}</Box>
        ),
      },
      {
        name: 'Show in UI',
        id: 'enabled',
        w: '10%',
        textAlign: 'center',
        cell: (row) => (
          <Box textAlign="center">
            <Switch
              id={`toggle-${row.id}`}
              defaultChecked={row.enabled}
              colorScheme="secondary"
              onChange={(event) => onToggle(event.target.checked, row)}
            />
          </Box>
        ),
      },
    ],
    [onToggle]
  )

  const actions = useMemo<ActionProps<AlertProps>[]>(
    () => [
      {
        label: 'Edit Alert Notification',
        onClick: onEdit,
        canView: () => true,
      },
    ],
    [onEdit]
  )

  return (
    <>
      <Helmet>
        <title>{metaTitle([title, heading])}</title>
      </Helmet>
      <Suspense fallback={null}>
        <Flex align="center" justify="space-between" p={4}>
          <HStack>
            <BackButton onClick={onGoBack} />
            <Title title={heading} />
          </HStack>
        </Flex>
        <Box px={4}>
          <Tabs current={currentPath}>
            <DataTable
              tableName={tableName}
              data={{ rows: alerts, count: alerts.length }}
              columns={columns}
              actions={actions}
            />
          </Tabs>
        </Box>
      </Suspense>
    </>
  )
})

export async function loader({ params }: LoaderFunctionArgs) {
  const scopes = ['edit:applications']

  const { checkLoaderPermissions } = await import('@/utils/loader-auth')
  await checkLoaderPermissions(scopes)

  const { getFilteredTabs } = await import('@/utils/tab-loader')
  const tabs = await getFilteredTabs('template-tabs', params)

  return {
    title: 'Alert Types',
    scopes,
    tabs,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <AlertTypes />
}
