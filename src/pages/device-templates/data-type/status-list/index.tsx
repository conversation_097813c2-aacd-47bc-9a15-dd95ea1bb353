import * as Sentry from '@sentry/react'
import {
  lazy,
  Suspense,
  useCallback,
  useEffect,
  useMemo,
  useState,
  type ReactElement,
} from 'react'
import { omit } from 'ramda'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { Box, Button, Flex, Switch } from '@chakra-ui/react'
import { Title } from '@/components/title'
import { AddButton } from '@/components/add-button'
import { DataTable } from '@/components/data-table/data-table'
import { useModal } from '@/hooks/use-modal'
import { useToast } from '@/hooks/use-toast'
import { deleteProperty, updateProperty } from '@/api/things/properties'
import type { ChannelStatus } from '@/types/models/channel'
import type { PropertyModel, StatusDataProperty } from '@/types/models/property'
import type { PropertyInput } from '@/types/api'
import type { ColumnProps, ActionProps } from '@/types/table'

const ConfirmDialog = lazy(() =>
  import('@/components/confirm-dialog').then(({ ConfirmDialog }) => ({
    default: ConfirmDialog,
  }))
)

const Edit = lazy(() =>
  import('@/pages/device-templates/data-type/status-list/edit').then(
    ({ Edit }) => ({
      default: Edit,
    })
  )
)

const asStatusProperty = ({
  label,
  ...rest
}: ChannelStatus): PropertyInput<StatusDataProperty> => {
  return {
    label,
    type: 'status',
    data: {
      ...omit(['id', 'data_types_id'], rest),
    },
  }
}

interface StatusPropertyListProps {
  dataTypeId: Maybe<number>
  records: ChannelStatus[]
}

export const StatusPropertyList = ({
  dataTypeId,
  records,
}: StatusPropertyListProps) => {
  const tableName = 'status-property-list'
  const modal = useModal()
  const toast = useToast()
  const queryClient = useQueryClient()
  const [statuses, setStatuses] = useState<ChannelStatus[]>([])

  useEffect(() => {
    if (!records) {
      return
    }
    setStatuses(records)
  }, [records])

  const { mutateAsync: updatePropertyMutation } = useMutation<
    PropertyModel,
    Error,
    {
      dataTypeId: number
      propertyId: number
      input: PropertyInput<StatusDataProperty>
    }
  >({
    mutationFn: ({ dataTypeId, propertyId, input }) =>
      updateProperty(dataTypeId, propertyId, input),
    onSuccess: () => {
      const cache = ['GetProperties', 'GetProperty']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const { mutateAsync: deletePropertyMutation } = useMutation<
    boolean,
    Error,
    {
      dataTypeId: number
      propertyId: number
    }
  >({
    mutationFn: ({ dataTypeId, propertyId }) =>
      deleteProperty(dataTypeId, propertyId),
    onSuccess: () => {
      const cache = ['GetProperties', 'GetProperty']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const onAdd = useCallback((): void => {
    modal({
      size: 'md',
      component: <Edit />,
      config: {
        title: 'Add Status',
        data: {
          dataTypeId,
          row: null,
        },
        onCallback: () => ({}),
      },
    })
  }, [modal, dataTypeId])

  const onEdit = useCallback(
    (row: ChannelStatus): void => {
      modal({
        size: 'md',
        component: <Edit />,
        config: {
          title: 'Edit Status',
          data: {
            dataTypeId,
            row,
          },
          onCallback: () => ({}),
        },
      })
    },
    [modal, dataTypeId]
  )

  const onToggleEnabled = useCallback(
    async (checked: boolean, row: ChannelStatus) => {
      const index = statuses.findIndex(({ id }) => row.id === id)
      const mutated = statuses.reduce(
        (acc: ChannelStatus[], status, key: number) => {
          acc.push({
            ...status,
            enabled: key === index ? checked : status.enabled,
          })
          return acc
        },
        [] as ChannelStatus[]
      )

      const input: PropertyInput<StatusDataProperty> = asStatusProperty({
        ...row,
        enabled: checked,
      })

      try {
        await updatePropertyMutation({
          dataTypeId: row.data_types_id,
          propertyId: row.id,
          input: input,
        })

        setStatuses(mutated)

        toast({
          status: 'success',
          msg: `Status has been ${checked ? 'enabled' : 'disabled'}.`,
        })
      } catch (error: unknown) {
        Sentry.captureException(error)
        toast({
          status: 'error',
          msg: 'Unable to update status.',
        })
      }
    },
    [toast, statuses, updatePropertyMutation]
  )

  const onRemove = useCallback(
    ({ id, label, data_types_id }: ChannelStatus): void => {
      modal({
        size: 'md',
        component: <ConfirmDialog />,
        config: {
          title: 'Remove Status',
          description: `Are you sure you want to remove “${label}”?`,
          confirmLabel: 'Remove',
          onCallback: async () => {
            try {
              const mutated = statuses.filter(
                (status: ChannelStatus) => status.id !== id
              )

              await deletePropertyMutation({
                dataTypeId: data_types_id,
                propertyId: id,
              })

              setStatuses(mutated)

              toast({
                status: 'success',
                msg: `“${label}” has been removed.`,
              })
            } catch (error: unknown) {
              Sentry.captureException(error)
              toast({
                status: 'error',
                msg: `Unable to remove “${label}”.`,
              })
            }
          },
        },
      })
    },
    [modal, toast, statuses, deletePropertyMutation]
  )

  const columns = useMemo<ColumnProps<ChannelStatus>[]>(
    () => [
      {
        name: 'Display',
        id: 'label',
        cell: ({ label }) => <Box noOfLines={1}>{label}</Box>,
      },
      {
        name: 'Value',
        id: 'value',
        cell: ({ value }) => <Box noOfLines={1}>{value}</Box>,
      },
      {
        name: 'Show in UI',
        id: 'enabled',
        w: '10%',
        textAlign: 'center',
        cell: (row) => (
          <Box textAlign="center">
            <Switch
              id={`toggle-${row.id}`}
              defaultChecked={row.enabled}
              colorScheme="secondary"
              onChange={(event) => onToggleEnabled(event.target.checked, row)}
            />
          </Box>
        ),
      },
    ],
    [onToggleEnabled]
  )

  const actions = useMemo<ActionProps<ChannelStatus>[]>(
    () => [
      {
        label: 'Edit Status',
        onClick: onEdit,
        canView: (): boolean => true,
      },
      {
        label: 'Remove Status',
        onClick: onRemove,
        canView: (): boolean => true,
      },
    ],
    [onEdit, onRemove]
  )

  const emptyRows = useCallback(
    (): ReactElement => (
      <Box>
        <Box as="span" pr={1}>
          No records exist,
        </Box>
        <Button
          type="button"
          aria-label="Add Status"
          variant="link"
          colorScheme="secondary"
          fontWeight="normal"
          onClick={onAdd}
        >
          add status
        </Button>
        .
      </Box>
    ),
    [onAdd]
  )

  return (
    <>
      <Flex align="center" justify="space-between" p={4}>
        <Title title="Statuses" />
        <AddButton label="Add Status" onClick={onAdd} />
      </Flex>
      <Box px={4} mb={10}>
        <Suspense fallback={null}>
          <DataTable
            tableName={tableName}
            data={{
              rows: statuses,
              count: statuses.length,
            }}
            columns={columns}
            actions={actions}
            emptyRows={emptyRows}
            sx={{
              borderRadius: 'base',
              overflow: 'hidden',
              boxShadow: 'sm',
              bgColor: 'whiteAlpha.400',
              _dark: {
                bgColor: 'blackAlpha.50',
              },
              th: {
                bgColor: 'whiteAlpha.900',
                _dark: {
                  bgColor: 'blackAlpha.100',
                },
              },
            }}
          />
        </Suspense>
      </Box>
    </>
  )
}
