import * as Sentry from '@sentry/react'
import {
  lazy,
  Suspense,
  useCallback,
  useEffect,
  useMemo,
  useState,
  type ReactElement,
} from 'react'
import pMap from 'p-map'
import { omit } from 'ramda'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { Box, Button, Flex, Radio, Switch } from '@chakra-ui/react'
import { Title } from '@/components/title'
import { AddButton } from '@/components/add-button'
import { DataTable } from '@/components/data-table/data-table'
import { useModal } from '@/hooks/use-modal'
import { useToast } from '@/hooks/use-toast'
import { deleteProperty, updateProperty } from '@/api/things/properties'
import type { ChannelUnit } from '@/types/models/channel'
import type { PropertyModel, UnitDataProperty } from '@/types/models/property'
import type { PropertyInput } from '@/types/api'
import type { ColumnProps, ActionProps } from '@/types/table'

const ConfirmDialog = lazy(() =>
  import('@/components/confirm-dialog').then(({ ConfirmDialog }) => ({
    default: ConfirmDialog,
  }))
)

const Edit = lazy(() =>
  import('@/pages/device-templates/data-type/unit-list/edit').then(
    ({ Edit }) => ({
      default: Edit,
    })
  )
)

const asUnitProperty = ({
  label,
  ...rest
}: ChannelUnit): PropertyInput<UnitDataProperty> => ({
  label,
  type: 'unit',
  data: {
    ...omit(['id', 'data_types_id'], rest),
  },
})

interface UnitPropertyListProps {
  dataTypeId: Maybe<number>
  records: ChannelUnit[]
}

export const UnitPropertyList = ({
  dataTypeId,
  records,
}: UnitPropertyListProps) => {
  const tableName = 'unit-property-list'
  const modal = useModal()
  const toast = useToast()
  const queryClient = useQueryClient()
  const [units, setUnits] = useState<ChannelUnit[]>([])

  useEffect(() => records && setUnits(records), [records])

  const { mutateAsync: updatePropertyMutation } = useMutation<
    PropertyModel,
    Error,
    {
      dataTypeId: number
      propertyId: number
      input: PropertyInput<UnitDataProperty>
    }
  >({
    mutationFn: ({ dataTypeId, propertyId, input }) =>
      updateProperty(dataTypeId, propertyId, input),
    onSuccess: () => {
      const cache = ['GetProperties', 'GetProperty']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const { mutateAsync: deletePropertyMutation } = useMutation<
    boolean,
    Error,
    {
      dataTypeId: number
      propertyId: number
    }
  >({
    mutationFn: ({ dataTypeId, propertyId }) =>
      deleteProperty(dataTypeId, propertyId),
    onSuccess: () => {
      const cache = ['GetProperties', 'GetProperty']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const onAdd = useCallback((): void => {
    modal({
      size: '4xl',
      scrollBehavior: 'inside',
      component: <Edit />,
      config: {
        title: 'Add Unit',
        data: {
          dataTypeId,
          row: null,
        },
        onCallback: () => ({}),
      },
    })
  }, [modal, dataTypeId])

  const onEdit = useCallback(
    (row: ChannelUnit): void => {
      modal({
        size: '4xl',
        scrollBehavior: 'inside',
        component: <Edit />,
        config: {
          title: 'Edit Unit',
          data: {
            dataTypeId,
            row,
          },
          onCallback: () => ({}),
        },
      })
    },
    [modal, dataTypeId]
  )

  const onChangeDefault = useCallback(
    async (row: ChannelUnit) => {
      if (units.length === 1) {
        return
      }

      const index = units.findIndex(({ id }) => id === row.id)
      const mutated = units.reduce((acc: ChannelUnit[], unit, key: number) => {
        acc.push({
          ...unit,
          default: key === index,
        })
        return acc
      }, [])

      try {
        await pMap(
          mutated,
          async (unit) => {
            const input = asUnitProperty(row) as PropertyInput<UnitDataProperty>
            await updatePropertyMutation({
              dataTypeId: unit.data_types_id,
              propertyId: unit.id,
              input,
            })
          },
          {
            concurrency: 1,
          }
        )

        setUnits(mutated)

        toast({
          status: 'success',
          msg: 'Default unit has been updated.',
        })
      } catch (error: unknown) {
        Sentry.captureException(error)
        toast({
          status: 'error',
          msg: 'Unable to update default unit.',
        })
      }
    },
    [toast, units, updatePropertyMutation]
  )

  const onToggleEnabled = useCallback(
    async (checked: boolean, row: ChannelUnit) => {
      const index = units.findIndex(({ id }) => row.id === id)
      const mutated = units.reduce((acc: ChannelUnit[], unit, key: number) => {
        acc.push({
          ...unit,
          enabled: key === index ? checked : unit.enabled,
        })
        return acc
      }, [])

      const input = asUnitProperty({
        ...row,
        enabled: checked,
      }) as PropertyInput<UnitDataProperty>

      try {
        await updatePropertyMutation({
          dataTypeId: row.data_types_id,
          propertyId: row.id,
          input: input,
        })

        setUnits(mutated)

        toast({
          status: 'success',
          msg: `Unit has been ${checked ? 'enabled' : 'disabled'}.`,
        })
      } catch (error: unknown) {
        Sentry.captureException(error)
        toast({
          status: 'error',
          msg: 'Unable to update unit.',
        })
      }
    },
    [toast, units, updatePropertyMutation]
  )

  const onRemove = useCallback(
    ({ id, label, data_types_id }: ChannelUnit): void =>
      modal({
        size: 'md',
        component: <ConfirmDialog />,
        config: {
          title: 'Remove Unit',
          description: `Are you sure you want to remove “${label}”?`,
          confirmLabel: 'Remove',
          onCallback: async () => {
            try {
              const mutated = units.filter(
                (unit: ChannelUnit) => unit.id !== id
              )

              await deletePropertyMutation({
                dataTypeId: data_types_id,
                propertyId: id,
              })

              setUnits(mutated)

              toast({
                status: 'success',
                msg: `“${label}” has been removed.`,
              })
            } catch (error: unknown) {
              Sentry.captureException(error)
              toast({
                status: 'error',
                msg: `Unable to remove “${label}”.`,
              })
            }
          },
        },
      }),
    [modal, toast, units, deletePropertyMutation]
  )

  const columns = useMemo<ColumnProps<ChannelUnit>[]>(
    () => [
      {
        name: 'Unit',
        id: 'label',
        cell: ({ label }) => <Box noOfLines={1}>{label}</Box>,
      },
      {
        name: 'Display',
        id: 'display',
        cell: ({ display }) => <Box noOfLines={1}>{display}</Box>,
      },
      {
        name: '# of Decimals',
        id: 'decimals',
        w: '10%',
        textAlign: 'center',
        cell: ({ decimals }) => (
          <Box noOfLines={1} textAlign="center">
            {decimals}
          </Box>
        ),
      },
      {
        name: 'Default Unit',
        id: 'default',
        w: '10%',
        textAlign: 'center',
        cell: (row) => (
          <Box textAlign="center">
            <Radio
              name="default"
              colorScheme="secondary"
              isChecked={row.default}
              onChange={() => onChangeDefault(row)}
            />
          </Box>
        ),
      },
      {
        name: 'Show in UI',
        id: 'enabled',
        w: '10%',
        textAlign: 'center',
        cell: (row) => (
          <Box textAlign="center">
            <Switch
              id={`toggle-${row.id}`}
              defaultChecked={row.enabled}
              colorScheme="secondary"
              onChange={(event) => onToggleEnabled(event.target.checked, row)}
            />
          </Box>
        ),
      },
    ],
    [onChangeDefault, onToggleEnabled]
  )

  const actions = useMemo<ActionProps<ChannelUnit>[]>(
    () => [
      {
        label: 'Edit Unit',
        onClick: onEdit,
        canView: (): boolean => true,
      },
      {
        label: 'Remove Unit',
        onClick: onRemove,
        canView: (): boolean => true,
      },
    ],
    [onEdit, onRemove]
  )

  const emptyRows = useCallback(
    (): ReactElement => (
      <Box>
        <Box as="span" pr={1}>
          No records exist,
        </Box>
        <Button
          type="button"
          aria-label="Add Unit"
          variant="link"
          colorScheme="secondary"
          fontWeight="normal"
          onClick={onAdd}
        >
          add unit
        </Button>
        .
      </Box>
    ),
    [onAdd]
  )

  return (
    <>
      <Flex align="center" justify="space-between" p={4}>
        <Title title="Units" />
        <AddButton label="Add Unit" onClick={onAdd} />
      </Flex>
      <Box px={4} mb={10}>
        <Suspense fallback={null}>
          <DataTable
            tableName={tableName}
            data={{
              rows: units,
              count: units.length,
            }}
            columns={columns}
            actions={actions}
            emptyRows={emptyRows}
            sx={{
              borderRadius: 'base',
              overflow: 'hidden',
              boxShadow: 'sm',
              bgColor: 'whiteAlpha.400',
              _dark: {
                bgColor: 'blackAlpha.50',
              },
              th: {
                bgColor: 'whiteAlpha.900',
                _dark: {
                  bgColor: 'blackAlpha.100',
                },
              },
            }}
          />
        </Suspense>
      </Box>
    </>
  )
}
