import * as Sentry from '@sentry/react'
import { lazy, Suspense, useCallback, useMemo, useEffect, memo } from 'react'
import { Helmet } from 'react-helmet-async'
import { useNavigate, useParams } from 'react-router'
import { useForm, useWatch } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { omit, isNil } from 'ramda'
import { useAtomValue } from 'jotai'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  Box,
  Button,
  Container,
  Flex,
  Grid,
  HStack,
  Portal,
} from '@chakra-ui/react'
import { Title } from '@/components/title'
import { BackButton } from '@/components/back-button'
import { InputField } from '@/components/input-field'
import { SelectField } from '@/components/select-field'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { SelectIconOptionLabel } from '@/features/capabilities/select-icon-option-label'
import { SelectRemoveIconOption } from '@/features/capabilities/select-remove-icon-option'
import { useRouter } from '@/hooks/use-router'
import { useModal } from '@/hooks/use-modal'
import { useToast } from '@/hooks/use-toast'
import { TEMPLATES } from '@/data/templates'
import { metaTitle } from '@/utils/meta-title'
import { getIcons } from '@/utils/get-icons'
import { channelAtom } from '@/utils/stores/channel'
import { navAtom } from '@/utils/stores/nav'
import {
  getDataType,
  createDataType,
  updateDataType,
  deleteDataType,
} from '@/api/things/datatypes'
import {
  getProperties,
  createProperty,
  updateProperty,
} from '@/api/things/properties'
import type { ChannelUnit, ChannelStatus } from '@/types/models/channel'
import type {
  PropertyModel,
  UnitDataProperty,
  StatusDataProperty,
  CommandDataProperty,
  IconDataProperty,
  TemplateDataProperty,
} from '@/types/models/property'
import type { GetResponse, PropertyInput, DataTypeInput } from '@/types/api'
import type { IconModel } from '@/types/models/icon'
import type { DataTypeModel } from '@/types/models/data-type'

const ConfirmDialog = lazy(() =>
  import('@/components/confirm-dialog').then(({ ConfirmDialog }) => ({
    default: ConfirmDialog,
  }))
)

const UnitPropertyList = lazy(() =>
  import('@/pages/device-templates/data-type/unit-list').then(
    ({ UnitPropertyList }) => ({
      default: UnitPropertyList,
    })
  )
)

const StatusPropertyList = lazy(() =>
  import('@/pages/device-templates/data-type/status-list').then(
    ({ StatusPropertyList }) => ({
      default: StatusPropertyList,
    })
  )
)

const CommandPropertyList = lazy(() =>
  import('@/pages/device-templates/data-type/command-list').then(
    ({ CommandPropertyList }) => ({
      default: CommandPropertyList,
    })
  )
)

const templateOptions = TEMPLATES.reduce(
  (acc: BaseOption<string>[], { description, value }) => {
    acc.push({
      label: description,
      value,
    })
    return acc
  },
  []
)

const schema = z.object({
  template: z.string().min(1, 'Template is required.').or(z.null()),
  data_type: z.string().min(1, 'Data Type is required.'),
  name: z.string().min(1, 'Name is required.'),
  payload: z.string().min(1, 'Payload is required.'),
  icon: z.string().min(1, 'Icon is required.'),
})

type FormInputProps = z.infer<typeof schema>

const DataType = memo(() => {
  const modal = useModal()
  const toast = useToast()
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  const { title } = useRouter()
  const { open } = useAtomValue(navAtom)

  const { typeId, id } = useParams<Dict<string>>()
  const dataTypeId = useMemo<number | undefined>(
    () =>
      !id || Number.isNaN(Number.parseInt(id))
        ? undefined
        : Number.parseInt(id),
    [id]
  )

  const isNew = useMemo<boolean>(() => !id, [id])
  const channel = useAtomValue(channelAtom)

  const {
    reset,
    control,
    handleSubmit,
    formState: { isValid, isSubmitting },
  } = useForm({
    mode: 'onTouched',
    resolver: zodResolver(schema),
    defaultValues: {
      template: null,
      data_type: '',
      name: '',
      payload: '',
      icon: '',
    },
  })

  const template = useWatch({
    name: 'template',
    control,
  })

  const { mutateAsync: createDataTypeMutation } = useMutation<
    DataTypeModel,
    Error,
    {
      input: DataTypeInput
    }
  >({
    mutationFn: ({ input }) => createDataType(input),
    onSuccess: () =>
      queryClient.invalidateQueries({ queryKey: ['GetDataTypes'] }),
  })

  const { mutateAsync: updateDataTypeMutation } = useMutation<
    DataTypeModel,
    Error,
    {
      dataTypeId: number
      input: DataTypeInput
    }
  >({
    mutationFn: ({ dataTypeId, input }) => updateDataType(dataTypeId, input),
    onSuccess: () => {
      const cache = ['GetDataTypes', 'GetDataType']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const { mutateAsync: createPropertyMutation } = useMutation<
    PropertyModel,
    Error,
    {
      dataTypeId: number
      input: PropertyInput
    }
  >({
    mutationFn: ({ dataTypeId, input }) => createProperty(dataTypeId, input),
    onSuccess: () =>
      queryClient.invalidateQueries({ queryKey: ['GetProperties'] }),
  })

  const { mutateAsync: updatePropertyMutation } = useMutation<
    PropertyModel,
    Error,
    {
      dataTypeId: number
      propertyId: number
      input: PropertyInput
    }
  >({
    mutationFn: ({ dataTypeId, propertyId, input }) =>
      updateProperty(dataTypeId, propertyId, input),
    onSuccess: () =>
      queryClient.invalidateQueries({ queryKey: ['GetProperties'] }),
  })

  const { mutateAsync: deleteDataTypeMutation } = useMutation<
    boolean,
    Error,
    {
      dataTypeId: number
    }
  >({
    mutationFn: ({ dataTypeId }) => deleteDataType(dataTypeId),
    onSuccess: () => {
      const cache = ['GetDataTypes', 'GetDataType']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const { data: dataType } = useQuery<DataTypeModel | null, Error>({
    queryKey: ['GetDataType', dataTypeId],
    queryFn: ({ signal }) =>
      dataTypeId
        ? getDataType({
            id: dataTypeId,
            signal,
          })
        : null,
    enabled: !!dataTypeId,
  })

  const { data: properties } = useQuery<
    GetResponse<PropertyModel> | null,
    Error,
    PropertyModel[]
  >({
    queryKey: ['GetProperties', dataTypeId],
    queryFn: ({ signal }) =>
      dataTypeId
        ? getProperties({
            dataTypeId,
            signal,
          })
        : null,
    enabled: !isNew,
    select: useCallback(
      (data: GetResponse<PropertyModel> | null) => data?.rows ?? [],
      []
    ),
  })

  const units = useMemo<ChannelUnit[]>(
    () =>
      (properties ?? [])
        .filter(({ type }) => type === 'unit')
        .reduce(
          (acc: ChannelUnit[], property: PropertyModel<UnitDataProperty>) => {
            acc.push({
              id: property.id,
              label: property.label,
              data_types_id: property.data_types_id,
              ...property.data,
            })
            return acc
          },
          []
        ),
    [properties]
  )

  const statuses = useMemo<ChannelStatus[]>(
    () =>
      (properties ?? [])
        .filter(({ type }) => type === 'status')
        .reduce(
          (
            acc: ChannelStatus[],
            property: PropertyModel<StatusDataProperty>
          ) => {
            acc.push({
              id: property.id,
              label: property.label,
              data_types_id: property.data_types_id,
              ...property.data,
            })
            return acc
          },
          [] as ChannelStatus[]
        ),
    [properties]
  )

  const commands = (properties ?? [])
    .filter(({ type }) => ['button', 'toggle', 'list'].includes(type))
    .map((property: PropertyModel<CommandDataProperty>) => ({
      id: property.id,
      label: property.label,
      data_types_id: property.data_types_id,
      ...property.data,
    }))

  const { data: icons } = useQuery<IconModel[], Error>({
    queryKey: ['GetIcons'],
    queryFn: () => getIcons(),
  })

  const iconOptions = useMemo<IconModel[]>(() => icons ?? [], [icons])

  const loadDataType = useCallback(async (): Promise<void> => {
    if (!(dataType?.id && properties && icons)) {
      return
    }

    const templateProp = properties.find(({ type }) => type === 'template')
    const iconProp = properties.find(({ type }) => type === 'icon')
    const icon = icons.find(({ label }) => label === iconProp?.label)

    reset({
      template: templateProp?.data.value ?? channel.data.template,
      data_type: dataType.label,
      name: dataType.name,
      payload: dataType.payload,
      icon: icon?.value ?? '',
    })
  }, [channel.data.template, dataType, icons, properties, reset])

  useEffect(() => {
    loadDataType()
  }, [loadDataType])

  const onGoBack = useCallback((): void => {
    const channelId = channel?.id ?? 'add'
    navigate(`/manage/device-templates/${typeId}/capabilities/${channelId}`, {
      viewTransition: true,
    })
  }, [navigate, channel, typeId])

  const onRemove = useCallback(() => {
    if (!dataType?.id) {
      return
    }

    modal({
      size: 'md',
      component: <ConfirmDialog />,
      config: {
        title: 'Remove Data Type?',
        description: `Are you sure you want to remove “${dataType.name}”?`,
        confirmLabel: 'Remove',
        onCallback: async () => {
          try {
            await deleteDataTypeMutation({
              dataTypeId: dataType.id,
            })
            toast({
              status: 'success',
              msg: `“${dataType.name}” has been removed.`,
            })
            onGoBack()
          } catch (error: unknown) {
            Sentry.captureException(error)
            toast({
              status: 'error',
              msg: `Unable to remove “${dataType.name}”.`,
            })
          }
        },
      },
    })
  }, [toast, modal, onGoBack, dataType, deleteDataTypeMutation])

  const hasTemplate = useCallback(
    (keys: string[]): boolean =>
      isNil(template) ? false : keys.includes(template),
    [template]
  )

  const asIconProperty = (
    dataTypesId: number,
    { label, css, ...rest }: IconModel
  ): Omit<PropertyModel<IconDataProperty>, 'id'> => ({
    label,
    data_types_id: dataTypesId,
    type: 'icon',
    data: {
      ...omit(['icon', 'svg'], rest),
      name: label,
      value: css,
      color: '#000',
    },
  })

  const asTemplateProperty = (
    value: string
  ): Omit<PropertyInput<TemplateDataProperty>, 'id'> => ({
    type: 'template',
    label: 'TEMPLATE',
    data: {
      value: value,
    },
  })

  const getProperty = (type: string): PropertyModel<any> | undefined => {
    return (properties ?? []).find((property) => property.type === type)
  }

  const create = async (values: FormInputProps): Promise<boolean> => {
    if (!values.template) {
      return false
    }

    const input = {
      label: values.data_type,
      name: values.name,
      payload: values.payload,
    } satisfies DataTypeInput

    try {
      // Create data type.
      const result = await createDataTypeMutation({
        input,
      })

      const templateInput: PropertyInput<TemplateDataProperty> =
        asTemplateProperty(values.template)

      // Create template property.
      await createPropertyMutation({
        dataTypeId: result.id,
        input: templateInput,
      })

      const icon = iconOptions.find(({ value }) => value === values.icon)

      if (icon) {
        const iconInput: PropertyInput<IconDataProperty> = {
          ...omit(['data_types_id'], asIconProperty(result.id, icon)),
        }

        // Create icon property.
        await createPropertyMutation({
          dataTypeId: result.id,
          input: iconInput,
        })
      }

      toast({
        status: 'success',
        msg: `“${values.name}” has been created.`,
      })

      // Redirect to update mode with proper url path.
      navigate(`/manage/device-templates/${typeId}/data-type/${result.id}`, {
        viewTransition: true,
      })

      return true
    } catch (error: unknown) {
      Sentry.captureException(error)
      toast({
        status: 'error',
        msg: `Unable to create “${values.name}”.}`,
      })
      return false
    }
  }

  const update = async (values: FormInputProps): Promise<boolean> => {
    if (!(dataTypeId && values.template)) {
      return false
    }

    const input = {
      label: values.data_type,
      name: values.name,
      payload: values.payload,
    } satisfies DataTypeInput

    try {
      const result = await updateDataTypeMutation({
        dataTypeId,
        input,
      })

      if (result.id) {
        const templateProperty = getProperty('template')
        const iconProperty = getProperty('icon')
        const icon = iconOptions.find(({ value }) => value === values.icon)

        const templateInput: PropertyInput<TemplateDataProperty> =
          asTemplateProperty(values.template)

        if (templateProperty) {
          // Update template property.
          await updatePropertyMutation({
            dataTypeId,
            propertyId: templateProperty.id,
            input: templateInput,
          })
        } else {
          // Create/patch template property.
          await createPropertyMutation({
            dataTypeId: result.id,
            input: templateInput,
          })
        }

        if (icon) {
          const iconInput: PropertyInput<IconDataProperty> = {
            ...omit(['data_types_id'], asIconProperty(result.id, icon)),
          }

          if (iconProperty?.id) {
            // Update icon property.
            await updatePropertyMutation({
              dataTypeId: result.id,
              propertyId: iconProperty.id,
              input: iconInput,
            })
          } else {
            // Create/patch icon property.
            await createPropertyMutation({
              dataTypeId: result.id,
              input: iconInput,
            })
          }
        }
      }

      toast({
        status: 'success',
        msg: `“${values.name}” has been updated.`,
      })
      return true
    } catch (error: unknown) {
      Sentry.captureException(error)
      toast({
        status: 'error',
        msg: `Unable to update “${values.name}”.`,
      })
      return false
    }
  }

  const onSubmit = async (values: FormInputProps): Promise<boolean> =>
    isNew ? await create(values) : await update(values)

  return (
    <>
      <Helmet>
        <title>{metaTitle([title])}</title>
      </Helmet>
      <Suspense fallback={null}>
        <Flex align="center" justify="space-between" p={4}>
          <HStack>
            <BackButton onClick={onGoBack} />
            <Title title={title} />
          </HStack>
        </Flex>
        <Box>
          <Box as="form" noValidate onSubmit={handleSubmit(onSubmit)}>
            <Container maxW="container.xl" m={0} pt={4}>
              <Grid
                gap={4}
                templateColumns={{ base: '1fr', lg: 'repeat(2, 1fr)' }}
              >
                <SelectField
                  name="template"
                  label="Template"
                  control={control}
                  options={templateOptions}
                  isRequired
                />
                <InputField
                  name="data_type"
                  label="Data Type"
                  control={control}
                  isRequired
                />
                <InputField
                  name="name"
                  label="Name"
                  control={control}
                  isRequired
                  sx={{
                    gridColumn: '1 / -1',
                  }}
                />
                <InputField
                  name="payload"
                  label="Payload/Abbreviation"
                  control={control}
                  isRequired
                />
                <SelectField
                  name="icon"
                  label="Icon"
                  control={control}
                  options={iconOptions}
                  formatOptionLabel={SelectIconOptionLabel}
                  components={{ Option: SelectRemoveIconOption }}
                  isSearchable
                  isRequired
                />
                <Portal>
                  <Flex
                    sx={{
                      p: 4,
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      position: 'absolute',
                      bottom: 0,
                      left: open ? '250px' : '70px',
                      w: `calc(100% - ${open ? 250 : 70}px)`,
                      bgColor: 'white',
                      _dark: {
                        bgColor: 'gray.900',
                      },
                    }}
                  >
                    <HStack>
                      {!isNew && (
                        <Button
                          type="button"
                          aria-label="Remove"
                          variant="ghost"
                          colorScheme="red"
                          onClick={onRemove}
                        >
                          Remove
                        </Button>
                      )}
                    </HStack>
                    <HStack>
                      <Button
                        type="button"
                        aria-label="Cancel"
                        variant="ghost"
                        colorScheme="gray"
                        onClick={onGoBack}
                      >
                        Cancel
                      </Button>
                      <Button
                        type="button"
                        aria-label="Save"
                        colorScheme="green"
                        minW={120}
                        isLoading={isSubmitting}
                        isDisabled={!isValid || isSubmitting}
                        onClick={handleSubmit(onSubmit)}
                        loadingText="Saving"
                      >
                        Save
                      </Button>
                    </HStack>
                  </Flex>
                </Portal>
              </Grid>
            </Container>
          </Box>
        </Box>
        {dataTypeId && (
          <Box mb={20}>
            {hasTemplate(['value', 'tracking']) && (
              <UnitPropertyList dataTypeId={dataTypeId} records={units} />
            )}
            {hasTemplate(['status', 'toggle']) && (
              <StatusPropertyList dataTypeId={dataTypeId} records={statuses} />
            )}
            {hasTemplate(['button', 'toggle', 'list']) && (
              <CommandPropertyList
                dataTypeId={dataTypeId}
                records={commands}
                template={template}
              />
            )}
          </Box>
        )}
      </Suspense>
    </>
  )
})

export async function loader() {
  const scopes = ['edit:applications']

  const { checkLoaderPermissions } = await import('@/utils/loader-auth')
  await checkLoaderPermissions(scopes)

  return {
    title: 'Data Type',
    scopes,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <DataType />
}
