import * as Sentry from '@sentry/react'
import {
  lazy,
  Suspense,
  useCallback,
  useMemo,
  useState,
  useEffect,
  type BaseSyntheticEvent,
} from 'react'
import { omit } from 'ramda'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { Box, Button, Flex, Switch } from '@chakra-ui/react'
import { Title } from '@/components/title'
import { AddButton } from '@/components/add-button'
import { DataTable } from '@/components/data-table/data-table'
import { useModal } from '@/hooks/use-modal'
import { useToast } from '@/hooks/use-toast'
import { deleteProperty, updateProperty } from '@/api/things/properties'
import type { ChannelCommand } from '@/types/models/channel'
import type {
  PropertyModel,
  CommandDataProperty,
} from '@/types/models/property'
import type { PropertyInput } from '@/types/api'
import type { ColumnProps, ActionProps } from '@/types/table'

const ConfirmDialog = lazy(() =>
  import('@/components/confirm-dialog').then(({ ConfirmDialog }) => ({
    default: ConfirmDialog,
  }))
)

const Edit = lazy(() =>
  import('@/pages/device-templates/data-type/command-list/edit').then(
    ({ Edit }) => ({
      default: Edit,
    })
  )
)

const asCommandProperty = (
  template: string,
  { label, ...rest }: ChannelCommand
): PropertyInput<CommandDataProperty> => ({
  label,
  type: template,
  data: {
    ...omit(['id', 'data_types_id'], rest),
  },
})

interface CommandPropertyListProps {
  template: Maybe<string>
  dataTypeId: Maybe<number>
  records: ChannelCommand[]
}

export const CommandPropertyList = ({
  template,
  dataTypeId,
  records,
}: CommandPropertyListProps) => {
  const tableName = 'command-property-list'
  const modal = useModal()
  const toast = useToast()
  const queryClient = useQueryClient()
  const [commands, setCommands] = useState<ChannelCommand[]>([])

  useEffect(() => {
    if (records) {
      setCommands(records)
    }
  }, [records])

  const { mutateAsync: updatePropertyMutation } = useMutation<
    PropertyModel,
    Error,
    {
      dataTypeId: number
      propertyId: number
      input: PropertyInput<CommandDataProperty>
    }
  >({
    mutationFn: ({ dataTypeId, propertyId, input }) =>
      updateProperty(dataTypeId, propertyId, input),
    onSuccess: () => {
      const cache = ['GetProperties', 'GetProperty']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const { mutateAsync: deletePropertyMutation } = useMutation<
    boolean,
    Error,
    {
      dataTypeId: number
      propertyId: number
    }
  >({
    mutationFn: ({ dataTypeId, propertyId }) =>
      deleteProperty(dataTypeId, propertyId),
    onSuccess: () => {
      const cache = ['GetProperties', 'GetProperty']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const onAdd = useCallback((): void => {
    modal({
      size: '4xl',
      component: <Edit />,
      config: {
        title: 'Add Command',
        data: {
          dataTypeId,
          template,
          row: null,
        },
        onCallback: () => ({}),
      },
    })
  }, [modal, template, dataTypeId])

  const onEdit = useCallback(
    (row: ChannelCommand): void => {
      modal({
        size: '4xl',
        component: <Edit />,
        config: {
          title: 'Edit Command',
          data: {
            dataTypeId,
            template,
            row,
          },
          onCallback: () => ({}),
        },
      })
    },
    [modal, template, dataTypeId]
  )

  const onToggleEnabled = useCallback(
    async (checked: boolean, row: ChannelCommand): Promise<void> => {
      if (!template) {
        return
      }

      const index = commands.findIndex(({ id }) => row.id === id)
      const mutated = commands.reduce(
        (acc: ChannelCommand[], command, key: number) => {
          if (key === index) {
            acc.push({
              ...command,
              enabled: checked,
            })
          } else {
            acc.push(command)
          }
          return acc
        },
        []
      )

      const input = asCommandProperty(template, {
        ...row,
        enabled: checked,
      }) as PropertyInput<CommandDataProperty>

      try {
        await updatePropertyMutation({
          dataTypeId: row.data_types_id,
          propertyId: row.id,
          input: input,
        })

        setCommands(mutated)

        toast({
          status: 'success',
          msg: `Command has been ${checked ? 'enabled' : 'disabled'}.`,
        })
      } catch (error: unknown) {
        Sentry.captureException(error)
        toast({
          status: 'error',
          msg: 'Unable to update command.',
        })
      }
    },
    [toast, template, commands, updatePropertyMutation]
  )

  const onRemove = useCallback(
    ({ id, label, data_types_id }: ChannelCommand): void => {
      modal({
        size: 'md',
        component: <ConfirmDialog />,
        config: {
          title: 'Remove Command',
          description: `Are you sure you want to remove “${label}”?`,
          confirmLabel: 'Remove',
          onCallback: async () => {
            try {
              const mutated = commands.filter(
                (command: ChannelCommand) => command.id !== id
              )

              await deletePropertyMutation({
                dataTypeId: data_types_id,
                propertyId: id,
              })

              setCommands(mutated)

              toast({
                status: 'success',
                msg: `“${label}” has been removed.`,
              })
            } catch (error: unknown) {
              Sentry.captureException(error)
              toast({
                status: 'error',
                msg: `Unable to remove “${label}”.`,
              })
            }
          },
        },
      })
    },
    [modal, toast, commands, deletePropertyMutation]
  )

  const columns = useMemo<ColumnProps<ChannelCommand>[]>(
    () => [
      {
        name: 'Display',
        id: 'label',
        cell: ({ label }) => <Box noOfLines={1}>{label}</Box>,
      },
      {
        name: 'Port',
        id: 'port',
        w: '10%',
        textAlign: 'center',
        cell: ({ port }) => (
          <Box noOfLines={1} textAlign="center">
            {port}
          </Box>
        ),
      },
      {
        name: 'Command',
        id: 'command',
        cell: ({ payload }) => <Box noOfLines={1}>{payload}</Box>,
      },
      {
        name: 'Message Box',
        id: 'message',
        cell: ({ message }) => <Box noOfLines={1}>{message}</Box>,
      },
      {
        name: 'Show in UI',
        id: 'enabled',
        w: '10%',
        textAlign: 'center',
        cell: (row) => (
          <Box textAlign="center">
            <Switch
              id={`toggle-${row.id}`}
              defaultChecked={row.enabled}
              colorScheme="secondary"
              onChange={(event: BaseSyntheticEvent) =>
                onToggleEnabled(event.target.checked, row)
              }
            />
          </Box>
        ),
      },
    ],
    [onToggleEnabled]
  )

  const actions = useMemo<ActionProps<ChannelCommand>[]>(
    () => [
      {
        label: 'Edit Command',
        onClick: onEdit,
        canView: (): boolean => true,
      },
      {
        label: 'Remove Command',
        onClick: onRemove,
        canView: (): boolean => true,
      },
    ],
    [onEdit, onRemove]
  )

  const emptyRows = useCallback(
    () => (
      <Box>
        <Box as="span" pr={1}>
          No records exist,
        </Box>
        <Button
          type="button"
          aria-label="Add Command"
          variant="link"
          colorScheme="secondary"
          fontWeight="normal"
          onClick={onAdd}
        >
          add command
        </Button>
        .
      </Box>
    ),
    [onAdd]
  )

  return (
    <>
      <Flex align="center" justify="space-between" p={4}>
        <Title title="Commands" />
        <AddButton label="Add Command" onClick={onAdd} />
      </Flex>
      <Box px={4} mb={10}>
        <Suspense fallback={null}>
          <DataTable
            tableName={tableName}
            data={{
              rows: commands,
              count: commands.length,
            }}
            columns={columns}
            actions={actions}
            emptyRows={emptyRows}
            sx={{
              borderRadius: 'base',
              overflow: 'hidden',
              boxShadow: 'sm',
              bgColor: 'whiteAlpha.400',
              _dark: {
                bgColor: 'blackAlpha.50',
              },
              th: {
                bgColor: 'whiteAlpha.900',
                _dark: {
                  bgColor: 'blackAlpha.100',
                },
              },
            }}
          />
        </Suspense>
      </Box>
    </>
  )
}
