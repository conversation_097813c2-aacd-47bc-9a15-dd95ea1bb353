import { lazy, Suspense, useEffect, useState, useMemo, memo } from 'react'
import { Helmet } from 'react-helmet-async'
import { useNavigate } from 'react-router'
import { useAtom, useAtomValue } from 'jotai'
import {
  Box,
  Button,
  ButtonGroup,
  Flex,
  HStack,
  IconButton,
  Menu,
  MenuButton,
  MenuItem,
  MenuList,
  Portal,
} from '@chakra-ui/react'
import { LayoutGridIcon, LayoutListIcon, ListFilterIcon } from 'lucide-react'
import { Title } from '@/components/title'
import { AddButton } from '@/components/add-button'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { GlobalSearch } from '@/features/device-templates/global-search'
import { useRouter } from '@/hooks/use-router'
import { useAbility } from '@/hooks/use-ability'
import { metaTitle } from '@/utils/meta-title'
import { transparentize } from '@/utils/transparentize'
import { gridAtom, recordCount<PERSON>tom } from '@/utils/stores/table'
import { useAuth } from '@/contexts/use-auth'
import { templateCatalogAtom } from '@/features/device-templates/utils/catalog'

const GridView = lazy(() =>
  import('@/features/device-templates/grid-view').then(({ GridView }) => ({
    default: GridView,
  }))
)

const TableView = lazy(() =>
  import('@/features/device-templates/table-view').then(({ TableView }) => ({
    default: TableView,
  }))
)

const DeviceTemplates = memo(() => {
  const tableName = 'device-templates'
  const navigate = useNavigate()
  const { title } = useRouter()
  const { can } = useAbility()
  const [isGrid, setGrid] = useAtom(gridAtom)
  const [templateCatalog, setTemplateCatalog] = useAtom(templateCatalogAtom)
  const [templateCatalogLabel, setTemplateCatalogLabel] = useState<
    string | undefined
  >('')
  const count = useAtomValue(recordCountAtom)
  const { isInternalAdmin } = useAuth()

  const templateCatalogOption = useMemo<BaseOption<string>[]>(
    () => [
      { label: 'Application', value: 'application' },
      { label: 'Organization', value: 'organization' },
      { label: 'Public', value: 'public' },
      { label: 'Examples', value: 'example' },
      ...(isInternalAdmin ? [{ label: 'All', value: 'all' }] : []),
    ],
    [isInternalAdmin]
  )

  const canAdd = can(['edit', 'organizations'])

  const onAdd = (): void => {
    navigate('/manage/device-templates/add', {
      viewTransition: true,
    })
  }

  useEffect(() => {
    const catalog = templateCatalogOption.find(
      (option) => option.value === templateCatalog
    )
    setTemplateCatalogLabel(catalog?.label)
  }, [templateCatalog, templateCatalogOption])

  const styles = {
    borderRadius: 'base',
    color: 'primary.500',
    bg: 'gray.100',
    _dark: {
      color: 'whiteAlpha.400',
      bg: 'whiteAlpha.50',
    },
    _active: {
      color: 'gray.100',
      bg: 'primary.500',
      _dark: {
        bg: transparentize('primary.500', 0.1),
      },
    },
  }

  return (
    <>
      <Helmet>
        <title>{metaTitle([title])}</title>
      </Helmet>
      <Flex align="center" justify="space-between" p={4}>
        <Title title={title} count={count ?? 0} suffix="Record" />
        <HStack>
          <Menu>
            <MenuButton
              as={Button}
              rightIcon={<ListFilterIcon size={16} />}
              colorScheme="secondary"
              variant="ghost"
              sx={{
                fontWeight: 'normal',
                minW: 'initial',
              }}
            >
              {templateCatalogLabel} Templates
            </MenuButton>
            <Portal>
              <MenuList sx={{ p: 2 }}>
                {templateCatalogOption.map(({ label, value }, key: number) => (
                  <MenuItem
                    key={key}
                    onClick={() => {
                      setTemplateCatalogLabel(label)
                      setTemplateCatalog(value)
                    }}
                  >
                    {label}
                  </MenuItem>
                ))}
              </MenuList>
            </Portal>
          </Menu>
          <GlobalSearch tableName={tableName} />
          <ButtonGroup isAttached colorScheme="primary">
            <IconButton
              aria-label="List View"
              icon={<LayoutListIcon size={16} />}
              onClick={() => setGrid(false)}
              isActive={!isGrid}
              sx={styles}
            />
            <IconButton
              aria-label="Grid View"
              icon={<LayoutGridIcon size={16} />}
              onClick={() => setGrid(true)}
              isActive={isGrid}
              sx={styles}
            />
          </ButtonGroup>
          {canAdd && (
            <AddButton label="Add Device Template" minW={200} onClick={onAdd} />
          )}
        </HStack>
      </Flex>
      <Box px={4}>
        <Suspense fallback={null}>
          {isGrid ? <GridView /> : <TableView />}
        </Suspense>
      </Box>
    </>
  )
})

export async function loader() {
  const scopes = ['edit:applications']

  const { checkLoaderPermissions } = await import('@/utils/loader-auth')
  await checkLoaderPermissions(scopes)

  return {
    title: 'Device Templates',
    scopes,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <DeviceTemplates />
}
