import * as Sentry from '@sentry/react'
import {
  memo,
  useCallback,
  useEffect,
  useMemo,
  useState,
  Fragment,
} from 'react'
import { useForm, useFieldArray, useWatch } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { v4 as uuid } from 'uuid'
import pMap from 'p-map'
import { omit, is, sortBy, prop } from 'ramda'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { useAtomValue } from 'jotai'
import {
  Box,
  Button,
  Grid,
  HStack,
  Tab,
  TabList,
  TabPanel,
  TabPanels,
  Tabs,
  IconButton,
  ModalBody,
  ModalCloseButton,
  ModalFooter,
  ModalHeader,
  useColorModeValue,
  useModalContext,
} from '@chakra-ui/react'
import { CodeXmlIcon } from 'lucide-react'
import { CheckboxField } from '@/components/checkbox-field'
import { InputField } from '@/components/input-field'
import { SelectField } from '@/components/select-field'
import { JsonViewer } from '@/components/json-viewer/json-viewer'
import { SelectChannelOption } from '@/features/device-uses/select-channel-option'
import { useOrganization } from '@/hooks/use-organization'
import { useToast } from '@/hooks/use-toast'
import { useAuth } from '@/contexts/use-auth'
import { getUtcTime } from '@/utils/date/get-utc-time'
import { modalAtom } from '@/utils/stores/modal'
import { TEMPLATES } from '@/data/templates'
import { getChannels } from '@/api/things/channels'
import { getDataType } from '@/api/things/datatypes'
import { updateUse } from '@/api/things/uses'
import { getReportTemplates } from '@/api/reports'
import type {
  ChannelUnit,
  ChannelModel,
  ChannelStatus,
} from '@/types/models/channel'
import type { DataTypeModel } from '@/types/models/data-type'
import type {
  UseRuleProps,
  UseRuleModel,
  DeviceUseProps,
  UseModel,
} from '@/types/models/use'
import type { PaginatedQueryResponse, UseInput } from '@/types/api'
import type { ReportTemplateModel } from '@/types/models/report'
import type { ModalAtomProps } from '@/types/store'
import type { TemplateData } from '@/types/models/template'

const sortByName = sortBy(prop('name'))

const TABS = [
  { name: 'Conditions', isDisabled: false },
  { name: 'Rules', isDisabled: true },
  { name: 'Report Template', isDisabled: false },
]

const isEq = (type: Maybe<string>): boolean =>
  ['status', 'toggle'].includes(type ?? '')

const asUnitOption = (unit: ChannelUnit): UnitOption => ({
  id: unit.id,
  name: unit.name,
  default: unit.default,
  display: unit.display,
  payload: unit.payload,
  decimals: unit.decimals,
  value: unit.payload,
  label: `${unit.name} (${unit.display})`,
})

const schema = z.object({
  name: z.string().min(2, 'Name is required.'),
  channel_id: z.number(),
  set_channel: z.boolean(),
  report_id: z.number().nullable(),
  type: z.string().min(1, 'Type is required.'),
  rule_type: z.string().min(1, 'Rule Type is required.'),
  enabled: z.boolean(),
  delay: z.object({
    time: z.number(),
    count: z.number().nullable(),
  }),
  overrides: z.array(z.any()),
  actions: z.array(z.any()),
  notifications: z.array(z.any()),
  triggers: z.array(
    z.object({
      unit: z.string().nullable(),
      channel: z.string().min(1, 'Channel is required.'),
      conditions: z.array(
        z.object({
          operator: z.string().min(1, 'Operator is required.'),
          value: z.string().min(1, 'Value is required.'),
        })
      ),
    })
  ),
})

const defaultValues = {
  name: '',
  channel_id: 0,
  set_channel: false,
  report_id: null as Maybe<number>,
  type: '',
  rule_type: '',
  enabled: true,
  delay: {
    time: 60_000,
    count: null as Maybe<number>,
  },
  overrides: [] as Maybe<any>[],
  actions: [] as Maybe<any>[],
  notifications: [] as Maybe<any>[],
  triggers: [] as Maybe<any>[],
}

type FormInputProps = z.infer<typeof schema>

interface DataProps {
  typeId: string
  use: DeviceUseProps
  rule?: UseRuleProps
}

interface UnitOption
  extends Omit<
    ChannelUnit,
    'constant' | 'data_types_id' | 'std' | 'enabled' | 'eq'
  > {
  value: string
  label: string
}

interface ChannelOption
  extends Omit<ChannelModel, 'keywords' | 'ip_rating' | 'certifications'> {
  label: string
  value: string
  sensor: Maybe<DataTypeModel>
}

export const Settings = memo(() => {
  const toast = useToast()
  const queryClient = useQueryClient()
  const { organizationId, applicationId } = useOrganization()
  const { user } = useAuth()
  const { onClose } = useModalContext()
  const activeColor = useColorModeValue('primary.500', 'primary.200')

  const tabStyles = {
    _hover: {
      color: activeColor,
    },
    _active: {
      color: activeColor,
    },
    _selected: {
      color: activeColor,
      borderBottomColor: activeColor,
    },
  }

  const {
    config: { data },
  } = useAtomValue<ModalAtomProps<DataProps>>(modalAtom)

  const typeId = useMemo<string | undefined>(() => data?.typeId, [data])

  const isNew = useMemo<boolean>(() => !data?.rule, [data?.rule])
  const title = useMemo<string>(
    () => (data?.rule ? 'Edit Setting' : 'Add Setting'),
    [data?.rule]
  )

  const [debug, setDebug] = useState<boolean>(false)
  const [template, setTemplate] = useState<any>(null)
  const [channelOptions, setChannelOptions] = useState<ChannelOption[]>([])
  const [unitOptions, setUnitOptions] = useState<UnitOption[]>([])
  const [statusOptions, setStatusOptions] = useState<BaseOption<string>[]>([])
  const [defaultUnit, setDefaultUnit] = useState<UnitOption | null>(null)
  const [defaultChannel, setDefaultChannel] = useState<ChannelOption | null>(
    null
  )
  const [defaultStatus, setDefaultStatus] = useState<BaseOption<string> | null>(
    null
  )
  const [defaultReport, setDefaultReport] = useState<BaseOption<number> | null>(
    null
  )

  const { mutateAsync: updateUseMutation } = useMutation<
    UseModel,
    Error,
    {
      typeId: string
      useId: number
      input: UseInput
    }
  >({
    mutationFn: ({ typeId, useId, input }) =>
      updateUse({ organizationId, applicationId, typeId, useId, input }),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ['GetUses', variables.typeId] })
      queryClient.setQueryData(['GetUses', { id: variables.typeId }], data)
    },
  })

  const reportType = useMemo<string>(
    () => (user?.realm === 'master' ? 'superadmin' : 'console'),
    [user?.realm]
  )

  const { data: reportOptions, isFetching: isFetchingReports } = useQuery<
    ReportTemplateModel[],
    Error,
    BaseOption<number>[]
  >({
    queryKey: ['GetReportTemplates', reportType],
    queryFn: ({ signal }) =>
      getReportTemplates({
        type: reportType,
        signal,
      }),
    select: useCallback(
      (data: ReportTemplateModel[]) =>
        data.reduce((acc: BaseOption<number>[], { id, display_name }) => {
          acc.push({
            label: display_name,
            value: id,
          })
          return acc
        }, []),
      []
    ),
  })

  const { data: rawChannels } = useQuery<
    PaginatedQueryResponse<ChannelModel> | null,
    Error,
    ChannelModel[]
  >({
    queryKey: ['GetChannels', organizationId, applicationId, typeId],
    queryFn: ({ signal }) =>
      typeId
        ? getChannels({
            organizationId,
            applicationId,
            typeId: typeId,
            limit: 50,
            page: 0,
            signal,
          })
        : null,
    enabled: !!organizationId && !!applicationId && !!typeId,
    select: useCallback(
      (data: PaginatedQueryResponse<ChannelModel> | null) =>
        (data?.rows ?? []).filter(({ data }) => {
          if (!data?.template) {
            return false
          }
          return ['value', 'status', 'toggle'].includes(data.template)
        }),
      []
    ),
  })

  const channels = useCallback(
    (): Promise<ChannelOption[]> =>
      pMap<ChannelModel, any>(
        sortByName(rawChannels ?? []),
        async (channel) => {
          const sensor = await queryClient.fetchQuery({
            queryKey: ['GetDataType', channel?.data_types_id],
            queryFn: ({ signal }) =>
              channel.data_types_id
                ? getDataType({
                    id: channel.data_types_id,
                    signal,
                  })
                : null,
          })
          return {
            ...channel,
            value: channel.id,
            label: channel.name,
            sensor,
          }
        },
        {
          concurrency: 1,
        }
      ),
    [rawChannels, queryClient]
  )

  useEffect(() => {
    if (!channels || channelOptions.length > 0) {
      return
    }
    channels().then((result) => setChannelOptions(result))
  }, [channels, channelOptions.length])

  const {
    reset,
    control,
    setValue,
    handleSubmit,
    formState: { isValid, isSubmitting },
  } = useForm({
    mode: 'onTouched',
    resolver: zodResolver(schema),
    defaultValues,
  })

  const form = useWatch({
    control,
  })

  const channelId = useWatch({
    name: 'channel_id',
    control,
  })

  const type = useWatch({
    name: 'type',
    control,
  })

  const name = useWatch({
    name: 'name',
    control,
  })

  const { fields: triggers } = useFieldArray({
    name: 'triggers',
    control,
  })

  const findTemplate = useCallback(
    (value?: Maybe<string>): TemplateData | undefined => {
      if (!value) {
        return
      }
      const tmpl = TEMPLATES.find((t) => t.value === value)
      setTemplate(tmpl)
      return tmpl
    },
    []
  )

  useEffect(() => {
    if (!data?.rule?.channel_id) {
      return
    }

    const channel = channelOptions.find(
      (option) => option.id === data?.rule?.channel_id
    )

    if (!channel) {
      return
    }

    setDefaultChannel(channel)

    if (isEq(channel.data?.template)) {
      const statuses = (channel.data?.statuses ?? []).reduce(
        (acc: ChannelStatus[], status) => {
          acc.push(status)
          return acc
        },
        []
      )

      setStatusOptions(statuses)
      setDefaultStatus(
        (statuses ?? []).find(
          (status) =>
            status.value === data.rule?.triggers[0]?.conditions[0]?.value
        ) ?? null
      )
    } else {
      const units = (channel.data?.units ?? []).reduce(
        (acc: UnitOption[], unit) => {
          acc.push(asUnitOption(unit))
          return acc
        },
        []
      )

      setUnitOptions(units)
      setDefaultUnit(
        units.find((unit) => unit.value === data.rule?.triggers[0]?.unit) ??
          null
      )
    }
  }, [data, channelOptions])

  useEffect(() => {
    if (!data?.rule) {
      return
    }

    const tmpl = findTemplate(data.rule?.type)

    reset({
      ...defaultValues,
      name: data.rule.name,
      channel_id: data.rule.channel_id,
      report_id: data.rule?.report_id,
      type: data.rule.type,
      rule_type: data.rule.rule_type,
      enabled: data.rule.enabled,
      delay: {
        time: data.rule.delay?.time ?? 60000,
        count: data.rule.delay?.count ?? null,
      },
      triggers: (data.rule?.triggers ?? tmpl?.triggers ?? []).reduce(
        (acc: any[], trigger) => {
          acc.push(trigger)
          return acc
        },
        []
      ),
      actions: data.rule?.actions ?? [],
      overrides: data.rule?.overrides ?? [],
      notifications: data.rule?.notifications ?? [],
    })
  }, [reset, data, findTemplate])

  useEffect(() => {
    if (!(data?.rule?.report_id && reportOptions)) {
      return
    }

    setDefaultReport(
      (reportOptions ?? []).find(
        ({ value }) => value === data.rule?.report_id
      ) ?? null
    )
  }, [data?.rule, reportOptions])

  const onChangeChannel = (option: ChannelOption): void => {
    if (!option?.id) {
      return
    }

    const tmpl = findTemplate(option.data.template)

    if (isEq(option.data.template)) {
      setStatusOptions(
        (option.data.statuses ?? []).reduce((acc: ChannelStatus[], status) => {
          acc.push(status)
          return acc
        }, [])
      )
    }

    const units = option.data.units.reduce((acc: UnitOption[], unit) => {
      acc.push(asUnitOption(unit))
      return acc
    }, [])

    setUnitOptions(units)

    const unit = units.find((unit) => unit.default)
    if (unit) {
      setDefaultUnit(unit)
    }

    reset({
      ...defaultValues,
      name: option.name,
      type: option.data.template ?? '',
      rule_type: option.sensor?.payload ?? '',
      channel_id: option.id,
      set_channel: false,
      triggers: (tmpl?.triggers ?? []).reduce((acc: any[], trigger) => {
        acc.push({
          ...trigger,
          channel: option.channel,
          unit: unit?.payload ?? null,
        })
        return acc
      }, []),
      overrides: [], // reset
    })
  }

  const onChangeUnit = (option: UnitOption): void => {
    for (const index of triggers.keys()) {
      setValue(`triggers.${index}.unit`, option?.payload ?? null)
    }
  }

  const conditionLabel = useCallback(
    (index: number): string => {
      const scope = triggers[index]
      const value = scope?.conditions?.[0]?.operator ?? ''
      const label = template?.conditionLabel ?? ''
      return is(Object, label) ? label[value] : label
    },
    [triggers, template?.conditionLabel]
  )

  const statusLabel = useCallback(
    (index: number): string => {
      return `${conditionLabel(index)} “${name ?? '?'}” is:`
    },
    [conditionLabel, name]
  )

  // convert start and end dates to hh:mm:ss
  const overridesToUtc = (overrides: any[]): any[] =>
    overrides.reduce((acc: any[], override: any) => {
      if (override.start) {
        override.start = getUtcTime(override.start)
        override.end = getUtcTime(override.end)
      }
      acc.push(override)
      return acc
    }, [])

  const create = async (values: FormInputProps): Promise<boolean> => {
    if (!data?.use) {
      return false
    }

    const rules = (data.use?.settings?.rules ?? []).reduce(
      (acc: UseRuleModel[], rule) => {
        acc.push(rule)
        return acc
      },
      []
    )

    const input = {
      ...omit(['channelIds', 'hasChildren'], data.use),
      settings: {
        rules: rules.concat({
          ...values,
          id: uuid(),
          name: values.name ?? '',
          type: values.type ?? '',
          rule_type: values.rule_type ?? '',
          overrides: overridesToUtc(values.overrides),
        }),
      },
    } satisfies UseInput

    try {
      updateUseMutation({
        typeId: data?.use.device_type_id,
        useId: data?.use.id,
        input,
      })
      toast({
        status: 'success',
        msg: 'Settings has been created.',
      })
      return true
    } catch (error: unknown) {
      Sentry.captureException(error)
      toast({
        status: 'error',
        msg: 'Unable to create settings.',
      })
      return false
    } finally {
      onClose()
    }
  }

  const update = async (values: FormInputProps): Promise<boolean> => {
    if (!(data?.use && data?.rule)) {
      return false
    }

    const rules = (data.use?.settings?.rules ?? []).reduce(
      (acc: UseRuleProps[], rule) => {
        if (rule.id === data?.rule?.id) {
          acc.push({
            ...values,
            id: rule.id,
            type: values.type ?? '',
            rule_type: values.rule_type ?? '',
            overrides: overridesToUtc(values.overrides),
          })
        } else {
          acc.push(rule)
        }
        return acc
      },
      []
    )

    const input = {
      ...omit(['channelIds', 'hasChildren'], data.use),
      settings: {
        rules,
      },
    } satisfies UseInput

    try {
      updateUseMutation({
        typeId: data?.use.device_type_id,
        useId: data?.use.id,
        input,
      })
      toast({
        status: 'success',
        msg: 'Settings has been updated.',
      })
      return true
    } catch (error: unknown) {
      Sentry.captureException(error)
      toast({
        status: 'error',
        msg: 'Unable to update settings.',
      })
      return false
    } finally {
      onClose()
    }
  }

  const onSubmit = async (values: FormInputProps): Promise<boolean> =>
    isNew ? create(values) : update(values)

  return (
    <Box as="form" noValidate onSubmit={handleSubmit(onSubmit)}>
      <ModalHeader>{title}</ModalHeader>
      <ModalCloseButton />
      <ModalBody
        sx={{
          maxH: '70vh',
          overflow: 'auto',
        }}
      >
        <Tabs>
          <TabList>
            {TABS.map(({ name, isDisabled }, key) => (
              <Tab key={key} isDisabled={isDisabled} sx={tabStyles}>
                {name}
              </Tab>
            ))}
          </TabList>
          <TabPanels>
            <TabPanel px={0}>
              <Grid gap={4} templateColumns="repeat(2, 1fr)">
                <SelectField
                  name="channel_id"
                  label="Channel ID"
                  control={control}
                  options={channelOptions}
                  defaultValue={defaultChannel}
                  components={{ Option: SelectChannelOption }}
                  isLoading={channelOptions.length === 0}
                  isRequired
                  onChange={(option: ChannelOption) => onChangeChannel(option)}
                  menuPosition="fixed"
                />
                <InputField
                  name="name"
                  label="Name"
                  control={control}
                  isRequired
                />
                <CheckboxField
                  name="set_channel"
                  label="Set the channel for gateway."
                  control={control}
                  sx={{
                    gridColumn: '1 / -1',
                  }}
                />
              </Grid>
              {channelId ? (
                <>
                  <Box as="h3" py={4} fontSize="lg" fontWeight="bold">
                    Conditions
                  </Box>
                  <Grid
                    gap={4}
                    sx={{
                      p: 4,
                      gridTemplateColumns: isEq(type)
                        ? 'repeat(2, 1fr)'
                        : 'repeat(3, 1fr)',
                      borderRadius: 'base',
                      borderWidth: '1px',
                      borderColor: 'blackAlpha.200',
                      bg: 'gray.50',
                      _dark: {
                        bg: 'blackAlpha.100',
                      },
                    }}
                  >
                    {!isEq(type) ? (
                      <>
                        {triggers.map((_, index: number) => (
                          <Fragment key={index}>
                            <Box>{conditionLabel(index)}:</Box>
                            <InputField
                              label="Eq"
                              name={`triggers.${index}.conditions.0.value`}
                              control={control}
                              isInline={true}
                            />
                            <SelectField
                              name={`triggers.${index}.unit`}
                              defaultValue={defaultUnit}
                              options={unitOptions}
                              onChange={(option: UnitOption) =>
                                onChangeUnit(option)
                              }
                              control={control}
                              isLoading={unitOptions.length === 0}
                              isInline={true}
                              isRequired
                              menuPosition="fixed"
                            />
                          </Fragment>
                        ))}
                        <Box fontWeight="medium" whiteSpace="pre-line">
                          {'Reading(s) in a row \n before alert is sent:'}
                        </Box>
                        <InputField
                          type="number"
                          label="Count"
                          name="delay.count"
                          control={control}
                          isInline={true}
                          isRequired
                        />
                      </>
                    ) : (
                      <>
                        {triggers.map((_, index: number) => (
                          <Fragment key={index}>
                            <Box pt={2}>{statusLabel(index)}</Box>
                            <SelectField
                              name={`triggers.${index}.conditions.0.value`}
                              label="Eq"
                              hasLabel={false}
                              defaultValue={defaultStatus}
                              options={statusOptions}
                              isLoading={statusOptions.length === 0}
                              control={control}
                              isRequired
                              menuPosition="fixed"
                            />
                          </Fragment>
                        ))}
                      </>
                    )}
                  </Grid>
                </>
              ) : null}
            </TabPanel>
            <TabPanel px={0}>
              <Grid gap={4} templateColumns="1fr">
                Not Implemented
              </Grid>
            </TabPanel>
            <TabPanel px={0}>
              <Grid gap={4} templateColumns="1fr">
                <SelectField
                  name="report_id"
                  label="Report Template"
                  control={control}
                  defaultValue={defaultReport}
                  options={reportOptions}
                  isLoading={isFetchingReports}
                  menuPosition="fixed"
                />
              </Grid>
            </TabPanel>
          </TabPanels>
        </Tabs>
        {debug && <JsonViewer code={JSON.stringify(form, null, 2)} />}
      </ModalBody>
      <ModalFooter justifyContent="space-between">
        <HStack>
          <Button
            type="button"
            aria-label="Cancel"
            variant="ghost"
            colorScheme="gray"
            onClick={onClose}
          >
            Cancel
          </Button>
          <IconButton
            type="button"
            aria-label="Debug Payload"
            variant="ghost"
            colorScheme="secondary"
            icon={<CodeXmlIcon size={16} />}
            onClick={() => setDebug(!debug)}
          />
        </HStack>
        <Button
          type="submit"
          aria-label="Save"
          colorScheme="green"
          minW={120}
          isLoading={isSubmitting}
          isDisabled={!isValid || isSubmitting}
          loadingText="Saving"
        >
          Save
        </Button>
      </ModalFooter>
    </Box>
  )
})
