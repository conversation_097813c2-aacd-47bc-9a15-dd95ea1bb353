import * as Sentry from '@sentry/react'
import {
  lazy,
  Fragment,
  Suspense,
  useCallback,
  useEffect,
  useMemo,
  useState,
  memo,
} from 'react'
import { useCustomCompareEffect } from '@react-hookz/web'
import { Helmet } from 'react-helmet-async'
import { useNavigate, useParams, type LoaderFunctionArgs } from 'react-router'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { useAtom } from 'jotai'
import { omit, equals } from 'ramda'
import pMap, { pMapSkip } from 'p-map'
import {
  Box,
  Flex,
  Radio,
  Table,
  Thead,
  Tbody,
  Td,
  Th,
  Tr,
  HStack,
} from '@chakra-ui/react'
import { CloudAlertIcon } from 'lucide-react'
import { Title } from '@/components/title'
import { Tabs } from '@/components/tabs'
import { Alert } from '@/components/alert'
import { AddButton } from '@/components/add-button'
import { BackButton } from '@/components/back-button'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { Actions } from '@/components/data-table/actions'
import { ToggleRow } from '@/components/data-table/toggle-row'
import { useRouter } from '@/hooks/use-router'
import { useModal } from '@/hooks/use-modal'
import { useOrganization } from '@/hooks/use-organization'
import { useToast } from '@/hooks/use-toast'
import { metaTitle } from '@/utils/meta-title'
import { tableAtom } from '@/utils/stores/table'
import { getType } from '@/api/things/types'
import { getChannel } from '@/api/things/channels'
import { createUse, deleteUse, getUses, updateUse } from '@/api/things/uses'
import type { DeviceUseProps, UseRuleProps, UseModel } from '@/types/models/use'
import type { PaginatedQueryResponse, UseInput } from '@/types/api'
import type { TypeModel } from '@/types/models/type'
import type { ChannelUnit, ChannelStatus } from '@/types/models/channel'
import type { ActionProps } from '@/types/table'

const ConfirmDialog = lazy(() =>
  import('@/components/confirm-dialog').then(({ ConfirmDialog }) => ({
    default: ConfirmDialog,
  }))
)

const RuleList = lazy(() =>
  import('@/features/device-uses/rules-list').then(({ RuleList }) => ({
    default: RuleList,
  }))
)

const Edit = lazy(() =>
  import('@/pages/device-templates/device-uses/edit').then(({ Edit }) => ({
    default: Edit,
  }))
)

const Settings = lazy(() =>
  import('@/pages/device-templates/device-uses/settings').then(
    ({ Settings }) => ({
      default: Settings,
    })
  )
)

const DeviceUses = memo(() => {
  const heading = 'Edit Device Template'
  const tableName = 'device-uses'
  const modal = useModal()
  const toast = useToast()
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  const { organizationId, applicationId } = useOrganization()
  const { title, currentPath } = useRouter()
  const { id } = useParams<Dict<string>>()
  const typeId = useMemo<string | undefined>(() => id, [id])

  const [isDisabled, setIsDisabled] = useState<boolean>(true)
  const [isSupported, setIsSupported] = useState<boolean>(true)
  const [channelIds, setChannelIds] = useState<any[]>([])
  const [rows, setRows] = useState<DeviceUseProps[]>([])
  const [{ visibleIds }, setTable] = useAtom(tableAtom(tableName))

  const { mutateAsync: createUseMutation } = useMutation<
    UseModel,
    Error,
    {
      typeId: string
      input: UseInput
    }
  >({
    mutationFn: ({ typeId, input }) =>
      createUse({ organizationId, applicationId, typeId, input }),
    onSuccess: () => queryClient.invalidateQueries({ queryKey: ['GetUses'] }),
  })

  const { mutateAsync: updateUseMutation } = useMutation<
    UseModel,
    Error,
    {
      typeId: string
      useId: number
      input: Required<UseInput>
    }
  >({
    mutationFn: ({ typeId, useId, input }) =>
      updateUse({ organizationId, applicationId, typeId, useId, input }),
    onSuccess: () => {
      const cache = ['GetUses', 'GetUse']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const { mutateAsync: deleteUseMutation } = useMutation<
    boolean,
    Error,
    {
      typeId: string
      useId: number
    }
  >({
    mutationFn: ({ typeId, useId }) =>
      deleteUse({ organizationId, applicationId, typeId, useId }),
    onSuccess: () => {
      const cache = ['GetUses', 'GetUse']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const { data: type } = useQuery<TypeModel | null, Error, TypeModel>({
    queryKey: ['GetType', organizationId, applicationId, typeId],
    queryFn: ({ signal }) =>
      typeId
        ? getType({
            organizationId,
            applicationId,
            typeId,
            signal,
          })
        : null,
    enabled: !!organizationId && !!applicationId,
  })

  const { data: uses } = useQuery<
    PaginatedQueryResponse<UseModel> | null,
    Error,
    DeviceUseProps[]
  >({
    queryKey: ['GetUses', organizationId, applicationId, typeId],
    queryFn: ({ signal }) =>
      typeId
        ? getUses({
            organizationId,
            applicationId,
            typeId,
            limit: 100,
            page: 0,
            signal,
          })
        : null,
    enabled: !!typeId,
    select: useCallback((data: PaginatedQueryResponse<UseModel> | null) => {
      return (data?.rows ?? []).reduce((acc: DeviceUseProps[], row) => {
        acc.push({
          ...row,
          hasChildren: row.settings ? row.settings?.rules?.length > 0 : false,
          channelIds: (row.settings?.rules ?? []).flatMap(
            ({ channel_id }) => channel_id ?? []
          ),
        })
        return acc
      }, [])
    }, []),
  })

  useEffect(() => {
    if (!type) {
      return
    }
    if (type.channels?.length > 0) {
      setIsDisabled(false)
      setChannelIds([
        ...new Set(type.channels.map(({ id }) => id).filter(Boolean)),
      ])
    } else {
      setIsSupported(false)
    }
  }, [type])

  const getChannels = async (ids: number[]) => {
    if (!typeId) {
      return []
    }
    return await pMap(
      ids,
      async (id: number) => {
        const channel = await queryClient.fetchQuery({
          queryKey: ['GetChannel', typeId, id, organizationId, applicationId],
          queryFn: ({ signal }) =>
            getChannel({
              organizationId,
              applicationId,
              typeId,
              channelId: id,
              signal,
            }),
        })
        return channel || pMapSkip
      },
      { concurrency: 1 }
    )
  }

  const setUses = async (): Promise<DeviceUseProps[] | undefined> => {
    if (!uses || channelIds.length === 0) {
      return
    }

    try {
      // Extend rows with units, statuses from channel result.
      const channels = await getChannels(channelIds)
      const result: DeviceUseProps[] = []
      const channelMap = new Map<number, any>(
        channels
          .filter((channel) => channel?.id !== undefined)
          .map((channel) => [channel?.id as number, channel])
      )

      for (const use of uses) {
        const rules: UseRuleProps[] = []

        for (const rule of use.settings?.rules ?? []) {
          const channel = channelMap.get(rule.channel_id)
          const units = Array.from(
            new Set(channel?.data?.units ?? [])
          ) as ChannelUnit[]
          const statuses = Array.from(
            new Set(channel?.data?.statuses ?? [])
          ) as ChannelStatus[]
          rules.push({
            ...rule,
            units,
            statuses,
          })
        }

        result.push({
          ...use,
          settings: {
            ...use.settings,
            rules,
          },
        })
      }
      return result
    } catch (error: unknown) {
      return Promise.reject(error)
    }
  }

  useCustomCompareEffect(
    () => {
      if (!uses || channelIds.length === 0) {
        return
      }
      setUses()
        .then((data) => {
          if (data) {
            setRows(data)
          }
        })
        .catch((error) => {
          Sentry.captureException(error)
          toast({
            status: 'error',
            msg: 'Device template may have broken JSON structure.',
          })
        })
    },
    [uses, channelIds.length],
    (a, b) => equals(a, b)
  )

  useEffect(() => {
    if (rows.length === 0) {
      return
    }

    setTable((prev) => ({
      ...prev,
      visibleIds: (rows.filter(({ hasChildren }) => hasChildren) ?? []).map(
        (row) => row?.id
      ),
    }))
  }, [rows, setTable])

  const onGoBack = (): void => {
    navigate('/manage/device-templates', {
      viewTransition: true,
    })
  }

  const onHideRow = useCallback(
    (id: number): void =>
      setTable((prev) => ({
        ...prev,
        visibleIds:
          prev.visibleIds.indexOf(id) > -1
            ? prev.visibleIds.filter((v) => v !== id)
            : prev.visibleIds.concat(id),
      })),
    [setTable]
  )

  const onAdd = useCallback((): void => {
    modal({
      size: 'md',
      scrollBehavior: 'inside',
      component: <Edit />,
      config: {
        data: {
          typeId,
          row: null,
        },
        onCallback: () => ({}),
      },
    })
  }, [modal, typeId])

  const onEdit = useCallback(
    (row: DeviceUseProps): void => {
      modal({
        size: 'md',
        scrollBehavior: 'inside',
        component: <Edit />,
        config: {
          data: {
            typeId,
            row,
          },
          onCallback: () => ({}),
        },
      })
    },
    [modal, typeId]
  )

  const onAddSetting = useCallback(
    (row: DeviceUseProps): void => {
      modal({
        component: <Settings />,
        config: {
          data: {
            typeId,
            use: omit(['hasChildren', 'channelIds'], row),
          },
          onCallback: () => ({}),
        },
      })
    },
    [modal, typeId]
  )

  const onDuplicate = useCallback(
    async (row: DeviceUseProps): Promise<void> => {
      if (!typeId) {
        return
      }

      const { name, device_type_id, settings } = row
      const input = {
        device_type_id,
        settings,
        name: `${name} (copy)`,
        default: false,
      } satisfies UseInput

      try {
        await createUseMutation({
          typeId,
          input,
        })
        toast({
          status: 'success',
          msg: `"${name}" has been duplicated.`,
        })
      } catch (error: unknown) {
        Sentry.captureException(error)
        toast({
          status: 'error',
          msg: `Unable to duplicate "${name}".`,
        })
      }
    },
    [toast, typeId, createUseMutation]
  )

  const onChangeDefault = useCallback(
    async (row: Maybe<DeviceUseProps>): Promise<void> => {
      if (!row) {
        return
      }

      const index = rows.findIndex(({ id }) => id === row?.id)
      const mutated = rows.reduce((acc: DeviceUseProps[], row, key: number) => {
        acc.push({
          ...row,
          default: key === index,
        })
        return acc
      }, [])

      await pMap(
        mutated,
        async (use) =>
          await updateUseMutation({
            typeId: use.device_type_id,
            useId: use.id,
            input: omit(['hasChildren', 'channelIds'], use),
          }),
        {
          concurrency: 1,
        }
      )
        .then(() => {
          toast({
            status: 'success',
            msg: 'Default use has been updated.',
          })
        })
        .catch(() => {
          toast({
            status: 'error',
            msg: 'Unable to update default use.',
          })
        })
    },
    [toast, rows, updateUseMutation]
  )

  const onRemove = useCallback(
    (row: DeviceUseProps): void => {
      modal({
        size: 'md',
        component: <ConfirmDialog />,
        config: {
          title: 'Remove Device Use',
          description: `Are you sure you want to remove "${row.name}"?`,
          confirmLabel: 'Remove',
          onCallback: async () => {
            try {
              await deleteUseMutation({
                typeId: row.device_type_id,
                useId: row.id,
              })

              toast({
                status: 'success',
                msg: `"${row.name}" has been removed.`,
              })

              // If record was default set default to first remaining record.
              const remaining = rows.filter(({ id }) => id !== row.id)
              if (row.default && remaining.length > 1) {
                onChangeDefault(remaining?.[0] ?? null)
              }
            } catch (error: unknown) {
              Sentry.captureException(error)
              toast({
                status: 'error',
                msg: `Unable to remove "${row.name}".`,
              })
            }
          },
        },
      })
    },
    [modal, toast, rows, onChangeDefault, deleteUseMutation]
  )

  const actions = useMemo<ActionProps<DeviceUseProps>[]>(
    () => [
      {
        label: 'Edit Device Use',
        onClick: onEdit,
        canView: (): boolean => true,
      },
      {
        label: 'Duplicate Device Use',
        onClick: onDuplicate,
        canView: (): boolean => true,
      },
      {
        label: 'Remove Device Use',
        onClick: onRemove,
        canView: (): boolean => true,
      },
      {
        label: 'Add Settings',
        onClick: onAddSetting,
        canView: (): boolean => true,
      },
    ],
    [onEdit, onAddSetting, onDuplicate, onRemove]
  )

  return (
    <>
      <Helmet>
        <title>{metaTitle([title, heading])}</title>
      </Helmet>
      <Suspense fallback={null}>
        <Flex align="center" justify="space-between" p={4}>
          <HStack>
            <BackButton onClick={onGoBack} />
            <Title title={heading} />
          </HStack>
          <HStack>
            <AddButton
              label="Add Device Use"
              onClick={onAdd}
              isDisabled={isDisabled}
            />
          </HStack>
        </Flex>
        <Box px={4}>
          <Tabs current={currentPath}>
            {!isSupported ? (
              <Alert
                title="Device Uses is not supported for this device."
                icon={CloudAlertIcon}
                color="red.600"
                sx={{
                  bg: 'whiteAlpha.50',
                  _dark: {
                    bg: 'blackAlpha.100',
                  },
                }}
              />
            ) : (
              <Table
                sx={{
                  tableLayout: 'fixed',
                  tbody: {
                    tr: {
                      _hover: {
                        bg: 'blackAlpha.50',
                      },
                    },
                  },
                  th: {
                    border: 0,
                    bg: 'whiteAlpha.900',
                    _dark: {
                      bg: 'blackAlpha.200',
                    },
                  },
                  td: {
                    borderColor: 'blackAlpha.50',
                    _dark: {
                      borderColor: 'gray.900',
                    },
                  },
                }}
              >
                <Thead>
                  <Tr>
                    <Th w="420px">Name</Th>
                    <Th>Alert Conditions</Th>
                    <Th w="10%" textAlign="center">
                      Default
                    </Th>
                    <Th w="10%" textAlign="right">
                      Actions
                    </Th>
                  </Tr>
                </Thead>
                <Tbody>
                  {rows.map((row) => (
                    <Fragment key={row.id}>
                      <Tr>
                        <Td>
                          <ToggleRow
                            id={row.id}
                            tableName={tableName}
                            hasChildren={row.hasChildren}
                            onClick={() => onHideRow(row.id)}
                          >
                            <Box fontWeight="medium" userSelect="none">
                              {row.name}
                            </Box>
                          </ToggleRow>
                        </Td>
                        <Td>
                          {!row.hasChildren && (
                            <AddButton
                              size="sm"
                              label="Add Settings"
                              colorScheme="secondary"
                              variant="ghost"
                              onClick={() => onAddSetting(row)}
                            />
                          )}
                        </Td>
                        <Td textAlign="center">
                          <Radio
                            name="default"
                            isChecked={row.default}
                            colorScheme="secondary"
                            onChange={() => onChangeDefault(row)}
                          />
                        </Td>
                        <Td textAlign="right">
                          <Actions row={row} actions={actions} />
                        </Td>
                      </Tr>
                      {typeId &&
                        row.hasChildren &&
                        (row.settings?.rules ?? []).map((rule: any) => (
                          <Tr
                            key={rule.id}
                            display={
                              visibleIds.includes(row.id) ? 'table-row' : 'none'
                            }
                          >
                            <Td py={1} pr={0} pl={8} colSpan={4}>
                              <RuleList typeId={typeId} use={row} rule={rule} />
                            </Td>
                          </Tr>
                        ))}
                    </Fragment>
                  ))}
                </Tbody>
              </Table>
            )}
          </Tabs>
        </Box>
      </Suspense>
    </>
  )
})

export async function loader({ params }: LoaderFunctionArgs) {
  const scopes = ['edit:applications']

  const { checkLoaderPermissions } = await import('@/utils/loader-auth')
  await checkLoaderPermissions(scopes)

  const { getFilteredTabs } = await import('@/utils/tab-loader')
  const tabs = await getFilteredTabs('template-tabs', params)

  return {
    title: 'Device Uses',
    scopes,
    tabs,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <DeviceUses />
}
