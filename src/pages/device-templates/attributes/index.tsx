import * as Sentry from '@sentry/react'
import { lazy, Suspense, useCallback, useMemo, memo } from 'react'
import { useCustomCompareEffect } from '@react-hookz/web'
import { Helmet } from 'react-helmet-async'
import { useNavigate, useParams, type LoaderFunctionArgs } from 'react-router'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { useAtom } from 'jotai'
import { omit } from 'ramda'
import { isEqual } from '@react-hookz/deep-equal'
import { Box, Flex, HStack } from '@chakra-ui/react'
import { Title } from '@/components/title'
import { Tabs } from '@/components/tabs'
import { AddButton } from '@/components/add-button'
import { BackButton } from '@/components/back-button'
import { JsonViewer } from '@/components/json-viewer/json-viewer'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { DataTable } from '@/components/data-table/data-table'
import { useRouter } from '@/hooks/use-router'
import { useModal } from '@/hooks/use-modal'
import { useOrganization } from '@/hooks/use-organization'
import { useToast } from '@/hooks/use-toast'
import { metaTitle } from '@/utils/meta-title'
import { isJson } from '@/utils/is-json'
import { tableAtom } from '@/utils/stores/table'
import { createMeta, deleteMeta, getMetas } from '@/api/things/metas'
import type { MetaModel } from '@/types/models/meta'
import type { MetaInput, PaginatedQueryResponse } from '@/types/api'
import type { ColumnProps, ActionProps } from '@/types/table'

const ConfirmDialog = lazy(() =>
  import('@/components/confirm-dialog').then(({ ConfirmDialog }) => ({
    default: ConfirmDialog,
  }))
)

const Edit = lazy(() =>
  import('@/pages/device-templates/attributes/edit').then(({ Edit }) => ({
    default: Edit,
  }))
)

const Attributes = memo(() => {
  const heading = 'Edit Device Template'
  const tableName = 'attributes'
  const modal = useModal()
  const toast = useToast()
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  const { organizationId, applicationId } = useOrganization()
  const { title, currentPath } = useRouter()
  const [{ limit, currPage }, setTable] = useAtom(tableAtom(tableName))
  const { id } = useParams()
  const typeId = useMemo<string | undefined>(() => id, [id])

  const { mutateAsync: createMetaMutation } = useMutation<
    MetaModel,
    Error,
    {
      typeId: string
      input: MetaInput
    }
  >({
    mutationFn: ({ typeId, input }) =>
      createMeta({ organizationId, applicationId, typeId, input }),
    onSuccess: () => queryClient.invalidateQueries({ queryKey: ['GetMetas'] }),
  })

  const { mutateAsync: deleteMetaMutation } = useMutation<
    boolean,
    Error,
    {
      typeId: string
      metaId: number
    }
  >({
    mutationFn: ({ typeId, metaId }) =>
      deleteMeta({ organizationId, applicationId, typeId, metaId }),
    onSuccess: () => {
      const cache = ['GetMetas', 'GetMeta']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const { data } = useQuery<PaginatedQueryResponse<MetaModel> | null, Error>({
    queryKey: [
      'GetMetas',
      organizationId,
      applicationId,
      typeId,
      limit,
      currPage,
    ],
    queryFn: () =>
      typeId
        ? getMetas({
            organizationId,
            applicationId,
            typeId,
            limit,
            page: currPage,
          })
        : null,
    enabled: !!organizationId && !!applicationId && !!typeId,
  })

  const records = useMemo<PaginatedQueryResponse<MetaModel>>(
    () =>
      data ?? {
        count: 0,
        limit: 0,
        page: 0,
        rows: [],
      },
    [data]
  )

  useCustomCompareEffect(
    () => {
      if (!data?.count) {
        return
      }
      setTable((prev) => ({ ...prev, totalCount: data.count }))
    },
    [data],
    (a, b) => isEqual(a, b)
  )

  const onAdd = useCallback((): void => {
    modal({
      size: 'full',
      component: <Edit />,
      config: {
        title: 'Add Attribute',
        data: {
          device_type_id: typeId,
        },
        onCallback: () => ({}),
      },
    })
  }, [modal, typeId])

  const onGoBack = (): void => {
    navigate('/manage/device-templates', {
      viewTransition: true,
    })
  }

  const onEdit = useCallback(
    (row: MetaModel): void => {
      modal({
        size: 'full',
        component: <Edit />,
        config: {
          title: 'Edit Attribute',
          data: row,
          onCallback: () => ({}),
        },
      })
    },
    [modal]
  )

  const onDuplicate = useCallback(
    async (row: MetaModel) => {
      if (!typeId) {
        return
      }

      const { key } = row
      const input = {
        ...omit(['id'], row),
        key: `${key}`,
      } satisfies MetaInput

      try {
        await createMetaMutation({ typeId, input })
        toast({
          status: 'success',
          msg: `“${key}” has been duplicated.`,
        })
      } catch (error: unknown) {
        Sentry.captureException(error)
        toast({
          status: 'error',
          msg: `Unable to duplicate “${key}”.`,
        })
      }
    },
    [toast, typeId, createMetaMutation]
  )

  const onRemove = useCallback(
    ({ id, key }: MetaModel) => {
      if (!typeId) {
        return
      }

      modal({
        size: 'md',
        component: <ConfirmDialog />,
        config: {
          title: 'Remove Attribute',
          description: `Are you sure you want to remove “${key}”?`,
          confirmLabel: 'Remove',
          onCallback: async () => {
            try {
              await deleteMetaMutation({ typeId, metaId: id })
              toast({
                status: 'success',
                msg: `“${key}” has been removed.`,
              })
            } catch (error: unknown) {
              Sentry.captureException(error)
              toast({
                status: 'error',
                msg: `Unable to remove “${key}”.`,
              })
            }
          },
        },
      })
    },
    [modal, toast, typeId, deleteMetaMutation]
  )

  const columns = useMemo<ColumnProps<MetaModel>[]>(
    () => [
      {
        name: 'Name',
        id: 'key',
        w: '15%',
        cell: ({ key }) => <Box noOfLines={1}>{key}</Box>,
      },
      {
        name: 'Value',
        id: 'value',
        cell: ({ value }) =>
          isJson(value) ? (
            <JsonViewer code={value} />
          ) : (
            <Box noOfLines={1}>{value}</Box>
          ),
      },
    ],
    []
  )

  const actions = useMemo<ActionProps<MetaModel>[]>(
    () => [
      {
        label: 'Edit Attribute',
        onClick: onEdit,
        canView: () => true,
      },
      {
        label: 'Duplicate Attribute',
        onClick: onDuplicate,
        canView: () => true,
      },
      {
        label: 'Remove Attribute',
        onClick: onRemove,
        canView: () => true,
      },
    ],
    [onEdit, onDuplicate, onRemove]
  )

  return (
    <>
      <Helmet>
        <title>{metaTitle([title, heading])}</title>
      </Helmet>
      <Suspense fallback={null}>
        <Flex align="center" justify="space-between" p={4}>
          <HStack>
            <BackButton onClick={onGoBack} />
            <Title title={heading} />
          </HStack>
          <HStack>
            <AddButton label="Add Attribute" onClick={onAdd} />
          </HStack>
        </Flex>
        <Box px={4}>
          <Tabs current={currentPath}>
            <DataTable
              tableName={tableName}
              data={records}
              columns={columns}
              actions={actions}
            />
          </Tabs>
        </Box>
      </Suspense>
    </>
  )
})

export async function loader({ params }: LoaderFunctionArgs) {
  const scopes = ['edit:applications']

  const { checkLoaderPermissions } = await import('@/utils/loader-auth')
  await checkLoaderPermissions(scopes)

  const { getFilteredTabs } = await import('@/utils/tab-loader')
  const tabs = await getFilteredTabs('template-tabs', params)

  return {
    title: 'Attributes',
    scopes,
    tabs,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <Attributes />
}
