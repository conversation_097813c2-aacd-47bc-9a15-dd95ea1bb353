import * as Sentry from '@sentry/react'
import { memo, lazy, useEffect, useMemo, Suspense } from 'react'
import { useForm, FormProvider, useWatch } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { sortWith, ascend, prop } from 'ramda'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useAtomValue } from 'jotai'
import {
  Box,
  Button,
  Grid,
  ModalBody,
  ModalCloseButton,
  ModalFooter,
  ModalHeader,
  useModalContext,
} from '@chakra-ui/react'
import { SelectField } from '@/components/select-field'
import { TextareaField } from '@/components/textarea-field'
import { InputField } from '@/components/input-field'
import { useOrganization } from '@/hooks/use-organization'
import { useToast } from '@/hooks/use-toast'
import { modalAtom } from '@/utils/stores/modal'
import { createMeta, updateMeta } from '@/api/things/metas'
import { ATTRIBUTES, type AttributesModel } from '@/data/attributes'
import type { MetaModel } from '@/types/models/meta'
import type { MetaInput } from '@/types/api'
import type { ModalAtomProps } from '@/types/store'

const JsonEditorField = lazy(() =>
  import('@/components/json-editor-field').then(({ JsonEditorField }) => ({
    default: JsonEditorField,
  }))
)

const sortByLabel = sortWith<AttributesModel>([ascend(prop('label'))])

const attributeOptions = sortByLabel(ATTRIBUTES)

const numRegex = /^\d+$/

const booleanOptions = [
  { label: 'True', value: 'true' },
  { label: 'False', value: 'false' },
]

const schema = z
  .object({
    key: z.string().min(1, 'Name is required.'),
    type: z.enum(['text', 'textarea', 'url', 'number', 'boolean', 'json']),
    value: z.string(),
  })
  .superRefine((arg, ctx) => {
    if (!arg.value) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Value is required.',
        path: ['value'],
        fatal: true,
      })
    }

    if (arg.type === 'url' && arg.value) {
      if (arg.value === '#') {
        return z.NEVER
      }
      try {
        new URL(arg.value)
      } catch {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Invalid URL.',
          path: ['value'],
        })
      }
    }

    if (arg.type === 'number' && arg.value && !numRegex.test(arg.value)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Only numbers are allowed.',
        path: ['value'],
      })
    }

    if (
      arg.type === 'boolean' &&
      arg.value &&
      !['true', 'false'].includes(arg.value)
    ) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Invalid Boolean',
        path: ['value'],
      })
    }

    if (arg.type === 'json' && arg.value) {
      try {
        JSON.parse(arg.value)
      } catch {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Invalid JSON',
          path: ['value'],
        })
      }
    }

    return z.NEVER
  })

type FormInputProps = z.infer<typeof schema>
type TypeEnum = 'text' | 'textarea' | 'url' | 'number' | 'boolean' | 'json'

export const Edit = memo(() => {
  const toast = useToast()
  const queryClient = useQueryClient()
  const { organizationId, applicationId } = useOrganization()
  const { onClose } = useModalContext()

  const {
    config: { title, data },
  } = useAtomValue<ModalAtomProps<MetaModel>>(modalAtom)

  const isNew = useMemo<boolean>(() => !data?.id, [data])

  const methods = useForm({
    mode: 'onTouched',
    resolver: zodResolver(schema),
    defaultValues: {
      type: 'textarea' as TypeEnum,
      key: data?.key ?? '',
      value: data?.value ?? '',
    },
  })

  const {
    control,
    setValue,
    handleSubmit,
    formState: { isValid, isSubmitting },
  } = methods

  const type = useWatch({
    name: 'type',
    control,
  })

  useEffect(() => {
    if (!(data && attributeOptions.length > 0)) {
      return
    }
    setValue(
      'type',
      attributeOptions.find(({ value }) => value === data.key)?.type ??
        'textarea',
      { shouldValidate: true }
    )
  }, [data, setValue])

  const { mutateAsync: createMetaMutation } = useMutation<
    MetaModel,
    Error,
    any
  >({
    mutationFn: ({ typeId, input }) =>
      createMeta({
        organizationId,
        applicationId,
        typeId,
        input,
      }),
    onSuccess: () => queryClient.invalidateQueries({ queryKey: ['GetMetas'] }),
  })

  const { mutateAsync: updateMetaMutation } = useMutation<
    MetaModel,
    Error,
    any
  >({
    mutationFn: ({ typeId, metaId, input }) =>
      updateMeta({ organizationId, applicationId, typeId, metaId, input }),
    onSuccess: () => {
      const cache = ['GetMetas', 'GetMeta']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const create = async (values: FormInputProps): Promise<boolean> => {
    if (!data) {
      return false
    }

    const input = {
      key: values.key,
      value:
        values.type === 'json'
          ? JSON.stringify(JSON.parse(values.value), null, 0)
          : values.value,
    } satisfies MetaInput

    try {
      await createMetaMutation({
        typeId: data.device_type_id,
        input,
      })
      toast({
        status: 'success',
        msg: 'Attribute has been created.',
      })
      return true
    } catch (error: unknown) {
      Sentry.captureException(error)
      toast({
        status: 'error',
        msg: 'Unable to create attribute.',
      })
      return false
    } finally {
      onClose()
    }
  }

  const update = async (values: FormInputProps): Promise<boolean> => {
    if (!data) {
      return false
    }

    const input = {
      key: values.key,
      value:
        values.type === 'json'
          ? JSON.stringify(JSON.parse(values.value), null, 0)
          : values.value,
    } satisfies MetaInput

    try {
      await updateMetaMutation({
        typeId: data.device_type_id,
        metaId: data.id,
        input,
      })
      toast({
        status: 'success',
        msg: 'Attribute has been updated.',
      })
      return true
    } catch (error: unknown) {
      Sentry.captureException(error)
      toast({
        status: 'error',
        msg: 'Unable to update attribute.',
      })
      return false
    } finally {
      onClose()
    }
  }

  const onSubmit = async (values: FormInputProps): Promise<boolean> =>
    isNew ? await create(values) : await update(values)

  return (
    <Suspense fallback={null}>
      <FormProvider {...methods}>
        <ModalCloseButton />
        <Box as="form" noValidate onSubmit={handleSubmit(onSubmit)}>
          <Grid
            sx={{
              gap: 4,
              h: '100vh',
              gridTemplateColumns: '1fr',
              gridTemplateRows: '60px auto 70px',
            }}
          >
            <ModalHeader>{title}</ModalHeader>
            <ModalBody
              sx={{
                maxH: '70vh',
                overflow: 'auto',
              }}
            >
              <Grid
                sx={{
                  gap: 4,
                  gridTemplateColumns: '1fr',
                }}
              >
                <SelectField
                  name="key"
                  label="Name"
                  control={control}
                  options={attributeOptions}
                  isSearchable
                  isRequired
                  isDisabled={!isNew}
                  onChange={({ value, type }: AttributesModel) => {
                    setValue('key', value)
                    setValue('type', type)
                    setValue('value', '')
                  }}
                />
                {['text', 'url', 'number'].includes(type) && (
                  <InputField
                    name="value"
                    label={`Value (${type})`}
                    type={type}
                    control={control}
                    isRequired
                  />
                )}
                {type === 'boolean' && (
                  <SelectField
                    name="value"
                    label="Value (boolean)"
                    control={control}
                    options={booleanOptions}
                    isRequired
                    isDisabled={!isNew}
                  />
                )}
                {type === 'textarea' && (
                  <TextareaField
                    name="value"
                    label="Value (text multiline)"
                    control={control}
                    rows={8}
                    isRequired
                  />
                )}
                {type === 'json' && (
                  <JsonEditorField
                    name="value"
                    label="Value (json)"
                    control={control}
                    isRequired
                  />
                )}
              </Grid>
            </ModalBody>
            <ModalFooter
              sx={{
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
            >
              <Button
                type="button"
                aria-label="Close"
                variant="ghost"
                onClick={onClose}
              >
                Close
              </Button>
              <Button
                type="submit"
                aria-label="Save"
                colorScheme="green"
                minW={120}
                isLoading={isSubmitting}
                isDisabled={!isValid || isSubmitting}
                loadingText="Saving"
              >
                Save
              </Button>
            </ModalFooter>
          </Grid>
        </Box>
      </FormProvider>
    </Suspense>
  )
})
