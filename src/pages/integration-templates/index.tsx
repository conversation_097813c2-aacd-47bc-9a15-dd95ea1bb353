import { Suspense, useCallback, useMemo, useDeferredValue, memo } from 'react'
import { Helmet } from 'react-helmet-async'
import { useNavigate } from 'react-router'
import { Box, Flex, Badge } from '@chakra-ui/react'
import { useQuery } from '@tanstack/react-query'
import { useAtomValue } from 'jotai'
import { Title } from '@/components/title'
import { AddButton } from '@/components/add-button'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { Clipboard } from '@/components/data-table/clipboard'
import { DataTable } from '@/components/data-table/data-table'
import { useRouter } from '@/hooks/use-router'
import { metaTitle } from '@/utils/meta-title'
import { formatDate } from '@/utils/date/format-date'
import { tableAtom } from '@/utils/stores/table'
import { useOrganization } from '@/hooks/use-organization'
import { getIntegrations } from '@/api/integrations'
import type { IntegrationModel } from '@/types/models/integration'
import type { GetResponse } from '@/types/api'
import type { ColumnProps, ActionProps } from '@/types/table'

const typeOptions = [
  { label: 'Inbound', value: 'inbound' },
  { label: 'Outbound', value: 'outbound' },
]

const IntegrationTemplates = memo(() => {
  const tableName = 'integration-templates'
  const navigate = useNavigate()
  const { title } = useRouter()
  const { organizationId, applicationId } = useOrganization()
  const { limit, currPage, filter } = useAtomValue(tableAtom(tableName))
  const deferredFilter = useDeferredValue(filter)

  const { data } = useQuery<GetResponse<IntegrationModel>, Error>({
    queryKey: [
      'GetIntegrations',
      limit,
      currPage,
      deferredFilter,
      organizationId,
      applicationId,
    ],
    queryFn: ({ signal }) =>
      getIntegrations({
        organizationId,
        applicationId,
        limit,
        page: currPage,
        filter: deferredFilter,
        signal,
      }),
  })

  const records = useMemo<GetResponse<IntegrationModel>>(
    () => data ?? { page: 0, count: 0, rows: [] },
    [data]
  )

  const onAdd = (): void => {
    navigate('/manage/integration-templates/add', {
      viewTransition: true,
    })
  }

  const onEdit = useCallback(
    ({ id }: IntegrationModel): void => {
      navigate(`/manage/integration-templates/${id}`, {
        viewTransition: true,
      })
    },
    [navigate]
  )

  const columns = useMemo<ColumnProps<IntegrationModel>[]>(
    () => [
      {
        name: 'Name',
        id: 'name',
        w: '15%',
        filter: {
          type: 'input',
          condition: 'like',
        },
        cell: ({ name }) => <Clipboard>{name}</Clipboard>,
      },
      {
        name: 'Alias',
        id: 'alias',
        w: '15%',
        filter: {
          type: 'input',
          condition: 'like',
        },
        cell: ({ alias }) => <Clipboard>{alias}</Clipboard>,
      },
      {
        name: 'Type',
        id: 'type',
        w: '10%',
        canHide: true,
        filter: {
          type: 'select',
          condition: 'eq',
          options: typeOptions,
        },
        cell: ({ type }) => <Badge>{type}</Badge>,
      },
      {
        name: 'Description',
        id: 'description',
        canHide: true,
        filter: {
          type: 'input',
          condition: 'like',
        },
        cell: ({ description }) => <Box noOfLines={1}>{description}</Box>,
      },
      {
        name: 'Created',
        id: 'created_at',
        w: '10%',
        cell: ({ created_at }) => (
          <Box noOfLines={1}>{formatDate(created_at)}</Box>
        ),
      },
    ],
    []
  )

  const actions = useMemo<ActionProps<IntegrationModel>[]>(
    () => [
      {
        label: 'Edit Integration',
        onClick: onEdit,
        canView: () => true,
      },
    ],
    [onEdit]
  )

  return (
    <>
      <Helmet>
        <title>{metaTitle([title])}</title>
      </Helmet>
      <Flex align="center" justify="space-between" p={4}>
        <Title title={title} count={records.count} suffix="Record" />
        <AddButton label="Add Integration" onClick={onAdd} />
      </Flex>
      <Box px={4}>
        <Suspense fallback={null}>
          <DataTable
            tableName={tableName}
            data={records}
            columns={columns}
            actions={actions}
          />
        </Suspense>
      </Box>
    </>
  )
})

export async function loader() {
  const scopes = ['manage:all']

  const { checkLoaderPermissions } = await import('@/utils/loader-auth')
  await checkLoaderPermissions(scopes)

  return {
    title: 'Integration Templates',
    scopes,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <IntegrationTemplates />
}
