import * as Sentry from '@sentry/react'
import { lazy, Suspense, useCallback, useEffect, useMemo, memo } from 'react'
import { Helmet } from 'react-helmet-async'
import { useNavigate, useParams, type LoaderFunctionArgs } from 'react-router'
import { pick } from 'ramda'
import pMap from 'p-map'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { useAtomValue, useSetAtom } from 'jotai'
import { Badge, Box, Flex, HStack } from '@chakra-ui/react'
import { Title } from '@/components/title'
import { BackButton } from '@/components/back-button'
import { AddButton } from '@/components/add-button'
import { Tabs } from '@/components/tabs'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { Clipboard } from '@/components/data-table/clipboard'
import { DndTable } from '@/components/dnd-table/dnd-table'
import { useAuth } from '@/contexts/use-auth'
import { useRouter } from '@/hooks/use-router'
import { useModal } from '@/hooks/use-modal'
import { useToast } from '@/hooks/use-toast'
import { metaTitle } from '@/utils/meta-title'
import { tableAtom, isMovingAtom } from '@/utils/stores/table'
import { getVariables, updateVariable, deleteVariable } from '@/api/variables'
import type { VariableModel } from '@/types/models/variable'
import type { GetResponse, VariableInput } from '@/types/api'
import type { ColumnProps, ActionProps } from '@/types/table'

const ConfirmDialog = lazy(() =>
  import('@/components/confirm-dialog').then(({ ConfirmDialog }) => ({
    default: ConfirmDialog,
  }))
)

const Edit = lazy(() =>
  import('@/pages/integration-templates/settings/edit').then(({ Edit }) => ({
    default: Edit,
  }))
)

const Settings = memo(() => {
  const heading = 'Edit Integration'
  const tableName = 'integration-settings'
  const navigate = useNavigate()
  const modal = useModal()
  const toast = useToast()
  const queryClient = useQueryClient()
  const { title, currentPath } = useRouter()
  const { isInternalAdmin } = useAuth()
  const { id } = useParams()
  const integrationId = useMemo<string | undefined>(() => id, [id])
  const setIsMoving = useSetAtom(isMovingAtom)
  const { limit, currPage, filter } = useAtomValue(tableAtom(tableName))

  useEffect(() => {
    if (!id || id === 'add') {
      navigate('/manage/integration-templates/add', {
        viewTransition: true,
      })
    }
  }, [id, navigate])

  const { mutateAsync: updateVariableMutation } = useMutation<
    boolean,
    Error,
    {
      integrationId: string
      variableId: number
      input: VariableInput
    }
  >({
    mutationFn: ({ integrationId, variableId, input }) =>
      updateVariable(integrationId, variableId, input),
    onSuccess: () => {
      const cache = ['GetVariables', 'GetVariable']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const { mutateAsync: deleteVariableMutation } = useMutation<
    boolean,
    Error,
    {
      integrationId: string
      variableId: number
    }
  >({
    mutationFn: ({ integrationId, variableId }) =>
      deleteVariable(integrationId, variableId),
    onSuccess: () => {
      const cache = ['GetVariables', 'GetVariable']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const { data } = useQuery<GetResponse<VariableModel> | null, Error>({
    queryKey: ['GetVariables', integrationId, limit, currPage, filter],
    queryFn: ({ signal }) =>
      integrationId
        ? getVariables({
            integrationId,
            limit,
            page: currPage,
            filter,
            signal,
          })
        : null,
    enabled: !!integrationId,
  })

  const records = useMemo<GetResponse<VariableModel>>(
    () => data ?? { page: 0, count: 0, rows: [] },
    [data]
  )

  const onAddSetting = useCallback((): void => {
    modal({
      component: <Edit />,
      scrollBehavior: 'inside',
      config: {
        title: 'Add Setting',
        data: {
          integration_id: id,
          order: records.count + 1,
        },
        onCallback: () => ({}),
      },
    })
  }, [id, modal, records.count])

  const onEditSetting = useCallback(
    (row: VariableModel): void => {
      modal({
        component: <Edit />,
        scrollBehavior: 'inside',
        config: {
          title: 'Edit Setting',
          data: row,
          onCallback: () => ({}),
        },
      })
    },
    [modal]
  )

  const onRemove = useCallback(
    ({ id, name }: VariableModel): void => {
      if (!integrationId) {
        return
      }

      modal({
        size: 'md',
        component: <ConfirmDialog />,
        config: {
          title: 'Remove setting?',
          description: `Are you sure you want to remove “${name}”?`,
          confirmLabel: 'Remove',
          onCallback: async () => {
            try {
              await deleteVariableMutation({
                integrationId,
                variableId: id,
              })
              toast({
                status: 'success',
                msg: `“${name}” has been removed.`,
              })
            } catch (error: unknown) {
              Sentry.captureException(error)
              toast({
                status: 'error',
                msg: `Unable to remove “${name}”.`,
              })
            }
          },
        },
      })
    },
    [modal, toast, integrationId, deleteVariableMutation]
  )

  const updateOrder = useCallback(
    async (row: VariableModel, index: number): Promise<boolean> => {
      if (!integrationId) {
        return false
      }

      const input = {
        ...pick(['name', 'label', 'datatype'], row),
        order: index,
      } satisfies VariableInput

      try {
        await updateVariableMutation({
          integrationId,
          variableId: row.id,
          input,
        })
        return true
      } catch (error: unknown) {
        Sentry.captureException(error)
        return false
      }
    },
    [integrationId, updateVariableMutation]
  )

  const onUpdateOrder = useCallback(
    async (items: VariableModel[]): Promise<void> => {
      if (items.length === 0) {
        return
      }

      try {
        await pMap(
          items,
          async (row, key: number) => await updateOrder(row, key),
          { concurrency: 1 }
        )

        toast({
          status: 'success',
          msg: 'Settings have been reordered.',
        })

        setIsMoving(false)
      } catch (error: unknown) {
        Sentry.captureException(error)
        toast({
          status: 'error',
          msg: 'Unable to reorder settings.',
        })
      }
    },
    [toast, setIsMoving, updateOrder]
  )

  const onGoBack = (): void => {
    navigate('/manage/integration-templates', {
      viewTransition: true,
    })
  }

  const columns = useMemo<ColumnProps<VariableModel>[]>(
    () => [
      {
        id: 'name',
        name: 'Name',
        cell: ({ name }) => <Clipboard>{name}</Clipboard>,
      },
      {
        name: 'Label',
        id: 'label',
        cell: ({ label }) => <Clipboard>{label}</Clipboard>,
      },
      {
        id: 'datatype',
        name: 'Data Type',
        cell: ({ datatype }) => <Badge colorScheme="green">{datatype}</Badge>,
      },
      {
        name: 'Order',
        id: 'order_number',
        cell: ({ order }) => <Badge colorScheme="gray">{order}</Badge>,
      },
      {
        name: 'Required',
        id: 'is_required',
        cell: ({ is_required }) => (
          <Badge colorScheme={is_required ? 'green' : 'orange'}>
            {is_required === 1 ? 'Yes' : 'No'}
          </Badge>
        ),
      },
    ],
    [records.rows, onUpdateOrder]
  )

  const canEdit = useMemo<boolean>(() => isInternalAdmin, [isInternalAdmin])
  const canRemove = useMemo<boolean>(() => isInternalAdmin, [isInternalAdmin])

  const actions = useMemo<ActionProps<VariableModel>[]>(
    () => [
      {
        label: 'Edit Setting',
        onClick: onEditSetting,
        canView: () => canEdit,
      },
      {
        label: 'Remove Setting',
        onClick: onRemove,
        canView: () => canRemove,
      },
    ],
    [onEditSetting, onRemove, canEdit, canRemove]
  )

  return (
    <>
      <Helmet>
        <title>{metaTitle([title, heading])}</title>
      </Helmet>
      <Suspense fallback={null}>
        <Flex align="center" justify="space-between" p={4}>
          <HStack>
            <BackButton onClick={onGoBack} />
            <Title title={heading} />
          </HStack>
          <AddButton label="Add Setting" onClick={onAddSetting} />
        </Flex>
        <Box px={4}>
          <Tabs current={currentPath}>
            <DndTable
              tableName={tableName}
              data={records}
              columns={columns}
              actions={actions}
              onSort={onUpdateOrder}
            />
          </Tabs>
        </Box>
      </Suspense>
    </>
  )
})

export async function loader({ params }: LoaderFunctionArgs) {
  const scopes = ['manage:all']

  const { checkLoaderPermissions } = await import('@/utils/loader-auth')
  await checkLoaderPermissions(scopes)

  const { getFilteredTabs } = await import('@/utils/tab-loader')
  const tabs = await getFilteredTabs('integration-template-tabs', params)

  return {
    title: 'Integration Templates',
    scopes,
    tabs,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <Settings />
}
