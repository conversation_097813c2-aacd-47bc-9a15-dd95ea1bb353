import * as Sentry from '@sentry/react'
import { Suspense, useEffect, useCallback, useMemo, memo } from 'react'
import { Helmet } from 'react-helmet-async'
import { useNavigate, useParams, type LoaderFunctionArgs } from 'react-router'
import { useAtom } from 'jotai'
import { useForm, useWatch } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  Image,
  Box,
  Button,
  Container,
  Divider,
  Flex,
  Grid,
  HStack,
} from '@chakra-ui/react'
import { Title } from '@/components/title'
import { Tabs } from '@/components/tabs'
import { useTree } from '@/components/tree/hooks/use-tree'
import { TreeField } from '@/components/tree-field'
import { BackButton } from '@/components/back-button'
import { InputField } from '@/components/input-field'
import { CheckboxField } from '@/components/checkbox-field'
import { SelectField } from '@/components/select-field'
import { VisualEditorField } from '@/components/visual-editor-field/visual-editor-field'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { blank64Gif } from '@/utils/constants'
import { sanitizedFields } from '@/components/tree/utils/sanitized-fields'
import { useRouter } from '@/hooks/use-router'
import { useToast } from '@/hooks/use-toast'
import { metaTitle } from '@/utils/meta-title'
import { selectionKeysAtom } from '@/utils/stores/tree'
import { useOrganization } from '@/hooks/use-organization'
import { getFuseFields } from '@/api/fuses'
import {
  getIntegration,
  createIntegration,
  updateIntegration,
} from '@/api/integrations'
import type { IntegrationModel } from '@/types/models/integration'
import type { FuseFieldModel } from '@/types/models/fuse-field'
import type { IntegrationInput } from '@/types/api'
import type { TreeNode } from '@/types/tree'

const eventRegex = /[\s,]+/

const eventOptions = [
  { label: 'Alert', value: 'alert' },
  { label: 'Uplink', value: 'uplink' },
  { label: 'Ping', value: 'ping' },
] satisfies BaseOption<EventEnum>[]

const authenticationOptions = [
  { label: 'Custom Authentication', value: 'custom' },
  { label: 'API Keys', value: 'apikey' },
  { label: 'oAuth2', value: 'oauth2' },
] satisfies BaseOption<AuthenticationEnum>[]

const typeOptions = [
  { label: 'Outbound', value: 'outbound' },
  { label: 'Inbound', value: 'inbound' },
] satisfies BaseOption<TypeEnum>[]

const schema = z.object({
  active: z.boolean(),
  name: z.string().min(1, 'Name is required.'),
  alias: z.string().min(1, 'Alias is required.'),
  description: z.string().min(1, 'Description is required.'),
  events: z
    .array(z.enum(['alert', 'uplink', 'ping']))
    .refine((list) => list.length > 0, {
      message: 'Please select at lest one event.',
    }),
  detail: z.string().min(3, 'Detailed description is required.'),
  documentation: z.string().url({ message: 'Invalid URL.' }),
  icon: z.string().url({ message: 'Invalid URL.' }),
  authentication: z.enum(['custom', 'apikey', 'oauth2']),
  type: z.enum(['outbound', 'inbound']),
})

type FormInputProps = z.infer<typeof schema>
type EventEnum = 'alert' | 'uplink' | 'ping'
type AuthenticationEnum = 'custom' | 'apikey' | 'oauth2'
type TypeEnum = 'outbound' | 'inbound'

interface FieldGroup {
  key: string
  label: string
  descendants: FieldGroup[]
}

const General = memo(() => {
  const toast = useToast()
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  const { currentPath } = useRouter()
  const { id } = useParams()
  const { organizationId, applicationId } = useOrganization()
  const integrationId = useMemo<string | undefined>(() => id, [id])
  const [selectionKeys, setSelectionKeys] = useAtom(selectionKeysAtom)
  const { nodesToSelectionKeys, extendedNodes } = useTree()

  const isNew = useMemo<boolean>(
    () => !integrationId || integrationId === 'add',
    [integrationId]
  )

  const title = useMemo<string>(
    () => (isNew ? 'Add Integration' : 'Edit Integration'),
    [isNew]
  )

  const {
    reset,
    control,
    handleSubmit,
    formState: { isValid, isSubmitting },
  } = useForm({
    mode: 'onTouched',
    resolver: zodResolver(schema),
    defaultValues: {
      active: false,
      name: '',
      alias: '',
      description: '',
      events: ['alert'] as EventEnum[],
      detail: '',
      documentation: '',
      icon: '',
      authentication: 'custom' as AuthenticationEnum,
      type: 'outbound' as TypeEnum,
    },
  })

  const icon = useWatch({
    name: 'icon',
    control,
  })

  const { mutateAsync: createIntegrationMutation } = useMutation<
    IntegrationModel,
    Error,
    IntegrationInput
  >({
    mutationFn: (input) => createIntegration(input),
    onSuccess: () =>
      queryClient.invalidateQueries({ queryKey: ['GetIntegrations'] }),
  })

  const { mutateAsync: updateIntegrationMutation } = useMutation<
    IntegrationModel,
    Error,
    {
      integrationId: string
      input: Partial<IntegrationInput>
    }
  >({
    mutationFn: ({ integrationId, input }) =>
      updateIntegration(integrationId, input),
    onSuccess: () => {
      const cache = ['GetIntegrations', 'GetIntegration']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const { data: tree } = useQuery<FuseFieldModel[], Error, TreeNode[]>({
    queryKey: ['GetFuseFields'],
    queryFn: ({ signal }) =>
      getFuseFields({
        signal,
      }),
    select: useCallback((data: FuseFieldModel[]) => {
      const fieldGroups = data
        .map((field: FuseFieldModel) => ({
          root: field.name.split('.')[0],
          name: field.name,
          label: field.label,
          checked: false,
        }))
        .reduce((acc: any[], { root, label, name, checked }: any) => {
          return Object.assign({}, acc, {
            [root]: (acc[root] || []).concat([{ key: name, label, checked }]),
          })
        }, [])

      return [
        {
          key: 'all',
          label: 'All Data Points',
          descendants: Object.keys(fieldGroups).reduce(
            (acc: FieldGroup[], key: string) =>
              acc.concat([
                {
                  key,
                  label: key,
                  descendants: fieldGroups[key as any] ?? [],
                },
              ]),
            []
          ),
        },
      ]
    }, []),
  })

  const { data: integration } = useQuery<IntegrationModel | null, Error>({
    queryKey: ['GetIntegration', integrationId, organizationId, applicationId],
    queryFn: ({ signal }) =>
      integrationId
        ? getIntegration({
            organizationId,
            applicationId,
            integrationId,
            signal,
          })
        : null,
    enabled: !isNew,
  })

  useEffect(() => {
    if (!tree) {
      return
    }

    const defaultSelectionKeys = nodesToSelectionKeys(extendedNodes(tree))
    const allSelected = Object.keys(defaultSelectionKeys).reduce(
      (acc: any, key: string) => {
        acc[key] = {
          ...defaultSelectionKeys[key],
          checked: true,
          intermediate: false,
        }
        return acc
      },
      {}
    )

    setSelectionKeys(allSelected)

    if (integration?.id) {
      reset({
        name: integration.name,
        alias: integration.alias,
        description: integration.description,
        detail: integration.detail,
        documentation: integration.documentation ?? '',
        icon: integration.icon,
        authentication: integration.authentication as AuthenticationEnum,
        type: integration.type as TypeEnum,
        active: integration.active === 1,
        events: integration.events?.split(eventRegex) as EventEnum[],
      })
    }
  }, [
    tree,
    reset,
    integration,
    extendedNodes,
    nodesToSelectionKeys,
    setSelectionKeys,
  ])

  const onGoBack = (): void => {
    navigate('/manage/integration-templates', {
      viewTransition: true,
    })
  }

  const create = async (values: FormInputProps): Promise<boolean> => {
    const input = {
      ...values,
      active: String(values.active ? 1 : 0),
      events: values.events.join(','),
      required_fields: sanitizedFields(selectionKeys),
    } satisfies IntegrationInput

    try {
      const result = await createIntegrationMutation(input)

      toast({
        status: 'success',
        msg: 'Integration has been created.',
      })

      navigate(`/manage/integration-templates/${result.id}/general`, {
        viewTransition: true,
      })

      return true
    } catch (error: unknown) {
      Sentry.captureException(error)
      toast({
        status: 'error',
        msg: 'Unable to create integration.',
      })
      return false
    }
  }

  const update = async (values: FormInputProps): Promise<boolean> => {
    if (!integrationId) {
      return false
    }

    const input = {
      ...values,
      active: String(values.active ? 1 : 0),
      events: values.events.join(','),
      required_fields: sanitizedFields(selectionKeys),
    } satisfies Partial<IntegrationInput>

    try {
      await updateIntegrationMutation({
        integrationId,
        input,
      })

      toast({
        status: 'success',
        msg: 'Integration has been updated.',
      })

      return true
    } catch (error: unknown) {
      Sentry.captureException(error)
      toast({
        status: 'error',
        msg: 'Unable to update integration.',
      })
      return false
    }
  }

  const onSubmit = async (values: FormInputProps): Promise<boolean> =>
    isNew ? await create(values) : await update(values)

  return (
    <>
      <Helmet>
        <title>{metaTitle([title])}</title>
      </Helmet>
      <Flex align="center" justify="space-between" p={4}>
        <HStack>
          <BackButton onClick={onGoBack} />
          <Title title={title} />
        </HStack>
      </Flex>
      <Box px={4} pb={10}>
        <Tabs current={currentPath} isDisabled={isNew}>
          <Suspense fallback={null}>
            <Box as="form" noValidate onSubmit={handleSubmit(onSubmit)}>
              <Container maxW="container.xl" m={0} pt={4}>
                <Grid
                  gap={4}
                  templateColumns={{ base: '1fr', lg: 'repeat(2, 1fr)' }}
                >
                  <CheckboxField
                    name="active"
                    label="Active"
                    control={control}
                    sx={{
                      gridColumn: '1 / -1',
                    }}
                  />
                  <InputField
                    name="name"
                    label="Name"
                    control={control}
                    isRequired
                    autoFocus
                  />
                  <InputField
                    name="alias"
                    label="Alias"
                    control={control}
                    isRequired
                  />
                  <InputField
                    name="description"
                    label="Description"
                    control={control}
                    isRequired
                  />
                  <SelectField
                    name="events"
                    label="Events"
                    control={control}
                    options={eventOptions}
                    isRequired
                    isMulti
                  />
                  <VisualEditorField
                    name="detail"
                    label="Detailed Description"
                    control={control}
                    isRequired
                    sx={{
                      gridColumn: '1 / -1',
                    }}
                  />
                  <InputField
                    name="documentation"
                    type="url"
                    label="Documentation URL"
                    placeholder="https://example.com/documentation"
                    control={control}
                    isRequired
                  />
                  <HStack>
                    <Image
                      src={icon}
                      fallbackSrc={blank64Gif}
                      sx={{
                        p: 2,
                        boxSize: '4rem',
                        borderRadius: '50%',
                        bg: 'blackAlpha.50',
                        _dark: {
                          bg: 'whiteAlpha.50',
                        },
                      }}
                    />
                    <Box w="100%">
                      <InputField
                        name="icon"
                        type="url"
                        label="Logo URL"
                        placeholder="https://example.com/logo.svg"
                        control={control}
                        isRequired
                      />
                    </Box>
                  </HStack>
                  <SelectField
                    name="authentication"
                    label="Authentication"
                    control={control}
                    options={authenticationOptions}
                    isRequired
                  />
                  <SelectField
                    name="type"
                    label="Type"
                    control={control}
                    options={typeOptions}
                    isRequired
                  />
                  <TreeField
                    tree={tree ?? []}
                    label="Required Fields"
                    sx={{
                      gridColumn: '1 / -1',
                    }}
                  />
                  <Divider gridColumn="1 / -1" />
                  <Box gridColumn="1 / -1">
                    <Button
                      type="submit"
                      aria-label="Save"
                      colorScheme="green"
                      minW={120}
                      isLoading={isSubmitting}
                      isDisabled={!isValid || isSubmitting}
                      loadingText="Saving"
                    >
                      Save
                    </Button>
                  </Box>
                </Grid>
              </Container>
            </Box>
          </Suspense>
        </Tabs>
      </Box>
    </>
  )
})

export async function loader({ params }: LoaderFunctionArgs) {
  const scopes = ['manage:all']

  const { checkLoaderPermissions } = await import('@/utils/loader-auth')
  await checkLoaderPermissions(scopes)

  const { getFilteredTabs } = await import('@/utils/tab-loader')
  const tabs = await getFilteredTabs('integration-template-tabs', params)

  return {
    title: 'Integration Templates',
    scopes,
    tabs,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <General />
}
