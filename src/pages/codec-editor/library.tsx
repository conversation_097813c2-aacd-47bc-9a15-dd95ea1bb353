import * as Sentry from '@sentry/react'
import {
  memo,
  Suspense,
  useEffect,
  useMemo,
  useState,
  useCallback,
  useDeferredValue,
  type BaseSyntheticEvent,
} from 'react'
import Fuse from 'fuse.js'
import { useAtomValue } from 'jotai'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { saveAs } from 'file-saver-es'
import { isNil, omit, isEmpty } from 'ramda'
import type { Theme } from 'react-select'
import {
  Badge,
  Box,
  Button,
  HStack,
  IconButton,
  Input,
  ModalBody,
  ModalCloseButton,
  ModalFooter,
  ModalHeader,
  Table,
  Tbody,
  Td,
  Th,
  Thead,
  Tooltip,
  Tr,
  useColorMode,
  useModalContext,
  useTheme,
  useColorModeValue,
} from '@chakra-ui/react'
import { CloudDownloadIcon, PencilIcon } from 'lucide-react'
import { ModalHeaderBadge } from '@/components/modal-header-badge'
import { Select } from '@/components/select'
import { useOrganization } from '@/hooks/use-organization'
import { useToast } from '@/hooks/use-toast'
import { modalAtom } from '@/utils/stores/modal'
import { selectStyle, selectTheme } from '@/utils/theme/select/filter'
import { getOptimusCodec, getOptimusCodecs } from '@/api/optimus'
import { defaultCodec } from '@/features/codec-editor/utils/schema'
import type { OptimusCodecModel } from '@/types/models/codec'
import type { CodecFormProps } from '@/types/codec-editor'
import type { ModalAtomProps } from '@/types/store'

const filterOptions = [
  { label: 'All', value: null },
  { label: 'Official', value: 'official' },
  { label: 'Public', value: 'public' },
  { label: 'Sample', value: 'opensource' },
  { label: 'Application', value: 'application' },
]

const getProperty = <K extends keyof OptimusCodecModel>(
  object: OptimusCodecModel,
  key: K
): OptimusCodecModel[K] => {
  return object[key]
}

export const Library = memo(() => {
  const toast = useToast()
  const { organizationId, applicationId } = useOrganization()
  const { onClose } = useModalContext()
  const queryClient = useQueryClient()
  const [selectFilter, setSelectFilter] = useState<any>(null)
  const deferredSelectFilter = useDeferredValue(selectFilter)
  const [records, setRecords] = useState<OptimusCodecModel[]>([])

  const { colors } = useTheme()
  const { colorMode } = useColorMode()
  const isDarkMode = useMemo<boolean>(() => colorMode === 'dark', [colorMode])
  const buttonVariant = useColorModeValue('solid', 'ghost')

  const {
    config: { onCallback },
  } = useAtomValue<ModalAtomProps>(modalAtom)

  const { data, refetch, isRefetching } = useQuery<
    OptimusCodecModel[] | null,
    Error
  >({
    queryKey: ['GetOptimusCodecs', organizationId, applicationId],
    queryFn: ({ signal }) =>
      organizationId && applicationId
        ? getOptimusCodecs({
            organizationId,
            applicationId,
            opensourceCodec: true,
            signal,
          })
        : null,
  })

  useEffect(() => {
    if (!data) {
      return
    }
    setRecords(data)
  }, [data])

  const onEdit = useCallback(
    async ({ id }: OptimusCodecModel): Promise<void> => {
      const result = await queryClient.fetchQuery({
        queryKey: ['GetOptimusCodec', organizationId, applicationId, id],
        queryFn: ({ signal }) =>
          organizationId && applicationId && id
            ? getOptimusCodec({
                organizationId,
                applicationId,
                codecId: id,
                signal,
              })
            : null,
      })

      const codec: CodecFormProps = {
        ...defaultCodec, // polyfill
        ...result,
        selectedFileIndex: 1,
      }

      onCallback(codec)
      onClose()
    },
    [onCallback, onClose, queryClient, organizationId, applicationId]
  )

  const onExport = useCallback(
    async ({ id }: OptimusCodecModel): Promise<void> => {
      const result = await queryClient.fetchQuery({
        queryKey: ['GetOptimusCodec', organizationId, applicationId, id],
        queryFn: ({ signal }) =>
          organizationId && applicationId && id
            ? getOptimusCodec({
                organizationId,
                applicationId,
                codecId: id,
                signal,
              })
            : null,
      })

      if (!result?.id) {
        return
      }

      try {
        saveAs(
          new Blob(
            [JSON.stringify(omit(['owner', 'native'], result), null, 2)],
            {
              type: 'application/json',
            }
          ),
          `${id}.json`
        )
        toast({
          status: 'success',
          msg: `“${id}” has been downloaded.`,
        })
      } catch (error: unknown) {
        Sentry.captureException(error)
        toast({
          status: 'error',
          msg: 'Unable to download file.',
        })
      }
    },
    [organizationId, applicationId, queryClient, toast]
  )

  const onTextFilter = useCallback(
    (column: string, value: string): void => {
      if (!data) {
        return
      }

      if (isEmpty(value)) {
        setRecords(data)
        return
      }

      const fuse = new Fuse(data, {
        includeScore: false,
        useExtendedSearch: true,
        keys: [column],
      })

      const result: OptimusCodecModel[] = fuse
        .search<OptimusCodecModel>(`'${value}`)
        .map(({ item }) => item)

      setRecords(result)
    },
    [data]
  )

  const onSelectFilter = useCallback(
    (option: any): void => {
      if (!data) {
        return
      }

      setSelectFilter(option)
      setRecords(
        isNil(option.value)
          ? data
          : option.value === 'application'
            ? data.filter((row) => row.application === applicationId)
            : data.filter((row) => getProperty(row, option.value) === true)
      )
    },
    [applicationId, data]
  )

  return (
    <>
      <ModalHeader
        sx={{
          borderBottomWidth: '1px',
          borderBottomStyle: 'solid',
          borderBottomColor: 'blackAlpha.50',
          _dark: {
            borderBottomColor: 'gray.900',
          },
        }}
      >
        Codec Library
        <ModalHeaderBadge text={`${data?.length ?? 0} codecs`} />
      </ModalHeader>
      <ModalCloseButton />
      <ModalBody
        sx={{
          p: 0,
          maxH: '70vh',
          overflow: 'auto',
        }}
      >
        <HStack gap={1} px={6} py={4}>
          <Badge colorScheme="blue">a = application</Badge>
          <Badge colorScheme="purple">o = official</Badge>
          <Badge colorScheme="green">p = public</Badge>
          <Badge colorScheme="orange">s = sample</Badge>
        </HStack>
        <Suspense fallback={null}>
          <Table
            sx={{
              tableLayout: 'fixed',
              borderCollapse: 'collapse',
              borderSpacing: 0,
              position: 'relative',
              zIndex: 1,
              backgroundColor: 'gray.50',
              _dark: {
                backgroundColor: 'gray.900',
              },
              tbody: {
                tr: {
                  _hover: {
                    bg: 'blackAlpha.50',
                  },
                },
              },
              th: {
                position: 'sticky',
                whiteSpace: 'nowrap',
                border: 0,
                zIndex: 2,
                backgroundColor: 'whiteAlpha.900',
                _dark: {
                  backgroundColor: 'gray.900',
                },
              },
              'th, td': {
                borderColor: 'blackAlpha.50',
                _dark: {
                  borderColor: 'gray.900',
                },
              },
            }}
          >
            <Thead>
              <Tr>
                <Th top={0}>Codec ID</Th>
                <Th top={0}>Codec Name</Th>
                <Th top={0} w="10%" />
                <Th top={0} w="15%" />
              </Tr>
              <Tr>
                <Th top="40px">
                  <Input
                    id="id"
                    size="sm"
                    variant="filled"
                    onChange={(event: BaseSyntheticEvent) =>
                      onTextFilter('id', event.currentTarget.value)
                    }
                  />
                </Th>
                <Th top="40px">
                  <Input
                    id="name"
                    size="sm"
                    variant="filled"
                    onChange={(event: BaseSyntheticEvent) =>
                      onTextFilter('name', event.currentTarget.value)
                    }
                  />
                </Th>
                <Th top="40px" colSpan={2}>
                  <Select
                    aria-label="Codec Filter"
                    placeholder="Filter by"
                    styles={selectStyle(colors, isDarkMode)}
                    theme={(theme: Theme) =>
                      selectTheme(colors, theme, isDarkMode)
                    }
                    options={filterOptions}
                    onChange={onSelectFilter}
                    value={deferredSelectFilter}
                    isSearchable
                  />
                </Th>
              </Tr>
            </Thead>
            <Tbody>
              {records.map((record) => (
                <Tr key={record.id}>
                  <Td>
                    <Box noOfLines={1}>{record.id}</Box>
                  </Td>
                  <Td>
                    <Tooltip
                      hasArrow
                      label={record.name}
                      placement="bottom-start"
                    >
                      <Box noOfLines={1}>{record.name}</Box>
                    </Tooltip>
                  </Td>
                  <Td>
                    <HStack gap={1}>
                      {record.application === applicationId ? (
                        <Badge colorScheme="blue">a</Badge>
                      ) : null}
                      {record.official ? (
                        <Badge colorScheme="purple">o</Badge>
                      ) : null}
                      {record.public ? (
                        <Badge colorScheme="green">p</Badge>
                      ) : null}
                      {record.opensource ? (
                        <Badge colorScheme="orange">s</Badge>
                      ) : null}
                    </HStack>
                  </Td>
                  <Td textAlign="right">
                    <HStack justify="flex-end">
                      <Tooltip hasArrow label={record.owner ? 'Edit' : 'Copy'}>
                        <IconButton
                          isRound
                          aria-label={record.owner ? 'Edit' : 'Copy'}
                          colorScheme="secondary"
                          variant="ghost"
                          icon={<PencilIcon size={16} />}
                          onClick={() => onEdit(record)}
                        />
                      </Tooltip>
                      <Tooltip hasArrow label="Export">
                        <IconButton
                          isRound
                          aria-label="Export"
                          colorScheme="secondary"
                          variant="ghost"
                          icon={<CloudDownloadIcon size={16} />}
                          onClick={() => onExport(record)}
                        />
                      </Tooltip>
                    </HStack>
                  </Td>
                </Tr>
              ))}
            </Tbody>
          </Table>
        </Suspense>
      </ModalBody>
      <ModalFooter justifyContent="space-between">
        <Button
          type="button"
          aria-label="Cancel"
          variant="ghost"
          colorScheme="gray"
          onClick={onClose}
        >
          Cancel
        </Button>
        <Button
          type="button"
          aria-label="Refresh"
          colorScheme="primary"
          variant={buttonVariant}
          isLoading={isRefetching}
          loadingText="Refreshing"
          onClick={() => refetch()}
        >
          Refetch
        </Button>
      </ModalFooter>
    </>
  )
})
