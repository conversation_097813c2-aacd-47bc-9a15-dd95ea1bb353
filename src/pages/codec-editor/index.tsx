import * as Sentry from '@sentry/react'
import { memo, lazy, Suspense, useCallback, useRef } from 'react'
import { Helm<PERSON> } from 'react-helmet-async'
import { useNavigate } from 'react-router'
import { useUnmountEffect } from '@react-hookz/web'
import { useForm, useWatch, FormProvider, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { isEmpty, omit, is } from 'ramda'
import { useAtomValue, useSetAtom } from 'jotai'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import {
  Accordion,
  Box,
  Button,
  ButtonGroup,
  Flex,
  Grid,
  GridItem,
  Input,
} from '@chakra-ui/react'
import {
  Panel,
  PanelGroup,
  type ImperativePanelHandle,
} from 'react-resizable-panels'
import { useMeasure } from 'react-use'
import { PanelResize } from '@/features/codec-editor/panel-resize'
import { UrlLogoDashboard } from '@/components/url-logo-dashboard'
import { BackButton } from '@/components/back-button'
import { Clipboard } from '@/components/data-table/clipboard'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { useRouter } from '@/hooks/use-router'
import { useModal } from '@/hooks/use-modal'
import { useAbility } from '@/hooks/use-ability'
import { useOrganization } from '@/hooks/use-organization'
import { metaTitle } from '@/utils/meta-title'
import {
  createOptimusCodec,
  deleteOptimusCodec,
  updateOptimusCodec,
} from '@/api/optimus'
import { editorAtom, panelSizeAtom } from '@/features/codec-editor/utils/store'
import { useLogStream } from '@/features/codec-editor/hooks/use-log-stream'
import { schema, defaultCodec } from '@/features/codec-editor/utils/schema'
import { defaultValues } from '@/features/codec-editor/utils/default-values'
import { CodecOptionsPanel } from '@/features/codec-editor/codec-options-panel'
import { DecoderDebugPanel } from '@/features/codec-editor/decoder-debug-panel'
import { DecoderBuilderPanel } from '@/features/codec-editor/decoder-builder-panel'
import { DeviceOptionsPanel } from '@/features/codec-editor/device-options-panel'
import { DeviceSessionPanel } from '@/features/codec-editor/device-session-panel'
import { EncoderDebugPanel } from '@/features/codec-editor/encoder-debug-panel'
import { ImportPanel } from '@/features/codec-editor/import-panel'
import { LibraryPanel } from '@/features/codec-editor/library-panel'
import { NewCodecPanel } from '@/features/codec-editor/new-codec-panel'
import { FileTabs } from '@/features/codec-editor/file-tabs'
import type { OptimusCodecModel } from '@/types/models/codec'
import type { OptimusCodecInput } from '@/types/api'
import type { CodecFormProps } from '@/types/codec-editor'

const ConfirmDialog = lazy(() =>
  import('@/components/confirm-dialog').then(({ ConfirmDialog }) => ({
    default: ConfirmDialog,
  }))
)

const MonacoEditor = lazy(() =>
  import('@/features/codec-editor/m-editor').then(({ MEditor }) => ({
    default: MEditor,
  }))
)

const LogPanel = lazy(() =>
  import('@/features/codec-editor/log-panel').then(({ LogPanel }) => ({
    default: LogPanel,
  }))
)

const CodecEditor = memo(() => {
  const [measureRef, { height }] = useMeasure()
  const panelRef = useRef<ImperativePanelHandle>(null)
  const saveRef = useRef<HTMLButtonElement>(null!)
  const modal = useModal()
  const { setLog, resetLog } = useLogStream()
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  const { can } = useAbility()
  const { organizationId, applicationId } = useOrganization()
  const { title } = useRouter()
  const editor = useAtomValue(editorAtom)
  const setPanelSize = useSetAtom(panelSizeAtom)

  const onPanelResize = useCallback(() => {
    const size = panelRef.current?.getSize()
    if (is(Number, size)) {
      setPanelSize(height * size * 0.01)
    }
  }, [setPanelSize, height])

  const canEdit = can(['edit', 'organizations'])

  const methods = useForm({
    mode: 'onTouched',
    resolver: zodResolver(schema),
    defaultValues,
  })

  const {
    reset,
    trigger,
    control,
    setValue,
    formState: { isSubmitting },
  } = methods

  const codec = useWatch({
    name: 'codec',
    control,
  })

  const { mutateAsync: createCodecMutation } = useMutation<
    OptimusCodecModel,
    Error,
    {
      organizationId: string
      applicationId: string
      input: OptimusCodecInput
    }
  >({
    mutationFn: ({ organizationId, applicationId, input }) =>
      createOptimusCodec({
        organizationId,
        applicationId,
        input,
      }),
    onSuccess: () =>
      queryClient.invalidateQueries({ queryKey: ['GetOptimusCodec'] }),
  })

  const { mutateAsync: updateCodecMutation } = useMutation<
    OptimusCodecModel,
    Error,
    {
      organizationId: string
      applicationId: string
      codecId: string
      input: OptimusCodecInput
    }
  >({
    mutationFn: ({ organizationId, applicationId, codecId, input }) =>
      updateOptimusCodec({
        organizationId,
        applicationId,
        codecId,
        input,
      }),
    onSuccess: () => {
      const cache = ['GetOptimusCodecs', 'GetOptimusCodec']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const { mutateAsync: deleteCodecMutation } = useMutation<
    any,
    Error,
    {
      organizationId: string
      applicationId: string
      codecId: string
    }
  >({
    mutationFn: ({ organizationId, applicationId, codecId }) =>
      deleteOptimusCodec({
        organizationId,
        applicationId,
        codecId,
      }),
    onSuccess: () => {
      const cache = ['GetOptimusCodecs', 'GetOptimusCodec']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const onRemove = useCallback((): void => {
    modal({
      size: 'md',
      component: <ConfirmDialog />,
      config: {
        title: 'Remove Codec',
        description: 'Are you sure you want to remove this codec?',
        confirmLabel: 'Remove',
        onCallback: async () => {
          if (!(organizationId && applicationId && codec?.id)) {
            setLog({
              type: 'error',
              text: 'Aborted, no ID set!',
            })
            return
          }
          try {
            await deleteCodecMutation({
              organizationId,
              applicationId,
              codecId: codec.id,
            })
            reset()
            setLog({
              type: 'info',
              text: 'Codec has been removed.',
            })
          } catch (error: unknown) {
            Sentry.captureException(error)
            setLog({
              type: 'error',
              text: 'Unable to remove codec.',
            })
          }
        },
      },
    })
  }, [
    modal,
    reset,
    setLog,
    codec,
    organizationId,
    applicationId,
    deleteCodecMutation,
  ])

  const create = useCallback(
    async (codec: CodecFormProps): Promise<boolean> => {
      if (!(organizationId && applicationId)) {
        return false
      }

      if (!codec.name) {
        setLog({
          type: 'error',
          text: 'Codec has no name.',
        })
        return false
      }

      let input = {
        name: codec.name,
        class: codec.class,
        official: codec.official,
        public: codec.public,
        opensource: codec.opensource,
        files: codec.files,
      } satisfies OptimusCodecInput

      if (!codec.owner) {
        input = {
          ...input,
          name: `Copy of ${codec.name}`,
          public: false,
          opensource: false,
        }
      }

      try {
        const result = await createCodecMutation({
          organizationId,
          applicationId,
          input,
        })

        const payload = {
          ...defaultCodec, // polyfill
          ...codec, // prev
          ...omit(['native'], result),
        } satisfies CodecFormProps

        setValue('codec', payload)

        setLog({
          type: 'info',
          text: 'Codec has been created.',
        })
        return true
      } catch (error: unknown) {
        Sentry.captureException(error)
        setLog({
          type: 'error',
          text: 'Unable to create codec.',
        })
        return false
      }
    },
    [setLog, organizationId, applicationId, setValue, createCodecMutation]
  )

  const update = useCallback(
    async (codec: CodecFormProps): Promise<boolean> => {
      if (!(organizationId && applicationId)) {
        return false
      }

      if (!codec.id) {
        setLog({
          type: 'error',
          text: 'Codec has no ID.',
        })
        return false
      }

      if (!codec.name) {
        setLog({
          type: 'error',
          text: 'Codec has no name.',
        })
        return false
      }

      const input = {
        name: codec.name,
        class: codec.class,
        official: codec.official,
        public: codec.public,
        opensource: codec.opensource,
        files: codec.files,
      } satisfies OptimusCodecInput

      try {
        const result = await updateCodecMutation({
          organizationId,
          applicationId,
          codecId: codec.id,
          input,
        })

        const payload = {
          ...defaultCodec, // polyfill
          ...codec, // prev
          ...omit(['native'], result),
        } satisfies CodecFormProps

        setValue('codec', payload)
        setLog({
          type: 'info',
          text: 'Codec has been updated.',
        })

        return true
      } catch (error: unknown) {
        Sentry.captureException(error)
        setLog({
          type: 'error',
          text: 'Unable to update codec.',
        })
        return false
      }
    },
    [setLog, organizationId, applicationId, setValue, updateCodecMutation]
  )

  const onSave = useCallback(async (): Promise<boolean> => {
    await trigger('codec')
    return codec?.id && codec?.owner ? await update(codec) : await create(codec)
  }, [trigger, codec, create, update])

  useUnmountEffect(() => {
    resetLog()
  })

  return (
    <>
      <Helmet>
        <title>{metaTitle([title])}</title>
      </Helmet>
      <Suspense fallback={null}>
        <FormProvider {...methods}>
          <Box as="form" noValidate>
            <Grid
              sx={{
                gap: 0,
                h: '100vh',
                gridTemplateColumns: '300px 1fr',
                gridTemplateRows: 'auto 1fr',
                justifyItems: 'stretch',
              }}
            >
              <GridItem
                colSpan={1}
                rowSpan={2}
                sx={{
                  overflow: 'auto',
                  borderRightWidth: '2px',
                  borderRightStyle: 'solid',
                  borderRightColor: 'blackAlpha.300',
                  bg: 'white',
                  _dark: {
                    bg: 'gray.900',
                  },
                }}
              >
                <Flex
                  sx={{
                    pos: 'relative',
                    alignItems: 'center',
                    justifyContent: 'center',
                    h: '65px',
                  }}
                >
                  <UrlLogoDashboard />
                  <Box
                    sx={{
                      pos: 'absolute',
                      top: '16px',
                      left: '10px',
                    }}
                  >
                    <BackButton
                      label="Go Back"
                      colorScheme="gray"
                      onClick={() => {
                        navigate('/manage/customers', {
                          viewTransition: true,
                        })
                      }}
                      sx={{
                        _hover: {
                          color: 'primary.500',
                          _dark: {
                            color: 'primary.200',
                          },
                        },
                      }}
                    />
                  </Box>
                </Flex>
                <Controller
                  name="codec.name"
                  control={control}
                  render={({ field: { name, onChange, onBlur, ref } }) => (
                    <Input
                      name={name}
                      value={codec.name}
                      placeholder="Name"
                      variant="filled"
                      isRequired={true}
                      ref={ref}
                      onBlur={onBlur}
                      onChange={onChange}
                      sx={{
                        borderRadius: 0,
                        border: 0,
                        _focus: {
                          border: 0,
                        },
                        _focusVisible: {
                          border: 0,
                        },
                      }}
                    />
                  )}
                />
                <Flex
                  sx={{
                    pos: 'relative',
                    h: '40px',
                    alignItems: 'center',
                    justifyContent: 'center',
                    bg: codec?.id ? 'secondary.100' : 'orange.100',
                    color: 'black',
                    w: 'full',
                  }}
                >
                  {codec?.id ? (
                    <Flex width="290px" pl={3}>
                      <Box noOfLines={1}>
                        <Clipboard>{codec.id}</Clipboard>
                      </Box>
                    </Flex>
                  ) : (
                    <Flex align="center" justify="center">
                      <Box
                        sx={{
                          pl: 6,
                          color: 'black',
                          fontSize: 'md',
                          noOfLines: 1,
                        }}
                      >
                        save to generate id
                      </Box>
                    </Flex>
                  )}
                </Flex>
                <ButtonGroup isAttached w="full">
                  <Button
                    type="button"
                    aria-label="Save"
                    colorScheme="green"
                    loadingText="Saving"
                    isLoading={isSubmitting}
                    isDisabled={
                      isEmpty(codec.name) ||
                      !isEmpty(editor?.error) ||
                      isSubmitting ||
                      !canEdit
                    }
                    onClick={onSave}
                    ref={saveRef}
                    sx={{
                      w: 'full',
                      borderRadius: 0,
                    }}
                  >
                    Save
                  </Button>
                  {codec?.id && (
                    <Button
                      type="button"
                      aria-label="Remove"
                      colorScheme="red"
                      onClick={onRemove}
                      sx={{
                        w: 'full',
                        borderRadius: 0,
                      }}
                    >
                      Remove
                    </Button>
                  )}
                </ButtonGroup>
                <Accordion allowMultiple w="full">
                  <NewCodecPanel />
                  <ImportPanel />
                  <LibraryPanel />
                  <CodecOptionsPanel control={control} />
                  <DecoderBuilderPanel />
                  <DecoderDebugPanel />
                  <EncoderDebugPanel />
                  <DeviceSessionPanel control={control} />
                  <DeviceOptionsPanel control={control} />
                </Accordion>
              </GridItem>
              <GridItem colSpan={1}>
                <Flex
                  sx={{
                    h: 'full',
                    w: 'full',
                    justifyContent: 'space-between',
                  }}
                >
                  <FileTabs />
                </Flex>
              </GridItem>
              <GridItem
                ref={measureRef as never}
                colSpan={1}
                sx={{
                  position: 'relative',
                }}
              >
                <PanelGroup direction="vertical">
                  <Panel
                    ref={panelRef}
                    order={1}
                    style={{
                      height: '100%',
                    }}
                  >
                    <Box sx={{ h: '100%', overflow: 'auto' }}>
                      <MonacoEditor saveRef={saveRef} control={control} />
                    </Box>
                  </Panel>
                  <PanelResize onDragging={onPanelResize} />
                  <Panel
                    defaultSize={20}
                    maxSize={50}
                    order={2}
                    style={{
                      background: 'transparent',
                      position: 'relative',
                    }}
                  >
                    <Suspense fallback={null}>
                      <LogPanel />
                    </Suspense>
                  </Panel>
                </PanelGroup>
              </GridItem>
            </Grid>
          </Box>
        </FormProvider>
      </Suspense>
    </>
  )
})

export async function loader() {
  const scopes = ['view:applications']

  const { checkLoaderPermissions } = await import('@/utils/loader-auth')
  await checkLoaderPermissions(scopes)

  return {
    title: 'Codec Editor',
    scopes,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <CodecEditor />
}
