import * as Sentry from '@sentry/react'
import { useNavigate, useBlocker } from 'react-router'
import {
  lazy,
  memo,
  useMemo,
  Suspense,
  useState,
  useEffect,
  useCallback,
} from 'react'
import { Helmet } from 'react-helmet-async'
import { useForm, FormProvider, useWatch } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { useSetAtom } from 'jotai'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import {
  Box,
  Flex,
  Button,
  HStack,
  Link,
  Grid,
  GridItem,
  Alert,
  AlertIcon,
  AlertDescription,
} from '@chakra-ui/react'
import { Title } from '@/components/title'
import { InputField } from '@/components/input-field'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { useRouter } from '@/hooks/use-router'
import { useAbility } from '@/hooks/use-ability'
import { useToast } from '@/hooks/use-toast'
import { useModal } from '@/hooks/use-modal'
import { useAuth } from '@/contexts/use-auth'
import { metaTitle } from '@/utils/meta-title'
import { logAtom, resetAtom } from '@/features/builder/utils/atoms'
import { useStream } from '@/hooks/use-stream'
import { getCurrentUser } from '@/api/users'
import { getSvgIcon, getSetting } from '@/api/builder'
import { checkSetting } from '@/api/settings'
import { organizationIdAtom, applicationIdAtom } from '@/utils/stores/auth'
import { jsonThemeSchema } from '@/features/builder/utils/schema/json-theme'
import type { EventSourceMessage } from '@microsoft/fetch-event-source'
import type { validPostParamSchema } from '@/features/builder/utils/schema/valid-post-params'
import type { Builder } from '@/types/builder'

const themeId = 'iotinabox'
const isProd = import.meta.env.VITE_DB_ENV === 'production'

const JobIndicator = lazy(() =>
  import('@/features/builder/components/job-indicator').then(
    ({ JobIndicator }) => ({ default: JobIndicator })
  )
)

const ConfirmDialog = lazy(() =>
  import('@/components/confirm-dialog').then(({ ConfirmDialog }) => ({
    default: ConfirmDialog,
  }))
)

const schema = z.object({
  orgId: z
    .string()
    .min(2, 'Organization ID is required.')
    .regex(/^([a-z0-9$]*)(-[a-z0-9]+)*$/g, 'Invalid format.'),
  brandName: z.string().min(1, 'Brand Name is required.'),
  firstName: z.string().min(1, 'First Name is required.'),
  lastName: z.string().min(1, 'Last Name is required.'),
  email: z.string().email('Invalid email address.'),
})

type FormInputProps = z.infer<typeof schema>
type PostParams = z.infer<typeof validPostParamSchema>

const defaultValues: FormInputProps = {
  orgId: '',
  brandName: '',
  firstName: '',
  lastName: '',
  email: '',
}

const CreateOrganization = memo(() => {
  const heading = 'Create Organization'
  const toast = useToast()
  const modal = useModal()
  const { title } = useRouter()
  const { can } = useAbility()
  const { user } = useAuth()
  const setOrganizationId = useSetAtom(organizationIdAtom)
  const setApplicationId = useSetAtom(applicationIdAtom)
  const setLog = useSetAtom(logAtom)
  const resetLog = useSetAtom(resetAtom)
  const [isDone, setIsDone] = useState<boolean>(false)
  const [isFetching, setFetching] = useState<boolean>(false)
  const [url, setUrl] = useState<string>('')
  const [jobCompleted, setJobCompleted] = useState<boolean>(false)
  const [jobFailed, setJobFailed] = useState<boolean>(false)
  const [errorMessage, setErrorMessage] = useState<string>('')
  const queryClient = useQueryClient()
  const navigate = useNavigate()

  const methods = useForm({
    mode: 'onTouched',
    resolver: zodResolver(schema),
    defaultValues,
  })

  const {
    reset,
    control,
    setValue,
    handleSubmit,
    formState: { isValid, isValidating, isSubmitting, isSubmitted, errors },
  } = methods

  const orgId = useWatch({
    control,
    name: 'orgId',
  })

  const shouldBlock = useCallback(() => isSubmitting, [isSubmitting])
  const blocker = useBlocker(shouldBlock)

  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (isSubmitting && !jobCompleted) {
        e.preventDefault()
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
    }
  }, [isSubmitting, jobCompleted])

  const { readyState, connect, disconnect } = useStream({
    onMessage: ({ id, data }: EventSourceMessage) => {
      if (id.length > 0) {
        const log = JSON.parse(data) as Builder
        setLog((prev) => [...prev, log])

        if (log.type === 'job' && log.event === 'completed') {
          setJobCompleted(true)
          setJobFailed(false)
        }

        if (
          log.type === 'error' ||
          (log.type === 'job' && log.event === 'failed')
        ) {
          setJobFailed(true)
          setErrorMessage(
            log.text || 'An error occurred while creating the organization.'
          )
        }
      }
    },
  })

  const { data: currentUser } = useQuery({
    queryKey: ['GetCurrentUser', user?.token],
    queryFn: ({ signal }) =>
      getCurrentUser({
        signal,
      }),
  })

  useEffect(() => {
    if (currentUser) {
      setValue('firstName', currentUser.firstName || '')
      setValue('lastName', currentUser.lastName || '')
      if (isProd) {
        setValue('email', currentUser.email || '')
      } else {
        setValue('email', '<EMAIL>')
      }
    }
  }, [currentUser, setValue, isProd])

  const fetchSvgAsset = async (
    appId: string,
    fileName: string
  ): Promise<string> => {
    try {
      return await queryClient.fetchQuery({
        queryKey: ['GetSvgIcon', appId, fileName],
        queryFn: ({ signal }) => getSvgIcon({ appId, fileName, signal }),
      })
    } catch {
      return ''
    }
  }

  const fetchTheme = async (themeId: string): Promise<string> => {
    try {
      const settings = await queryClient.fetchQuery({
        queryKey: ['GetSettings', themeId],
        queryFn: ({ signal }) => getSetting({ applicationId: themeId, signal }),
      })

      // Return the theme value from settings if it exists
      return settings?.theme || ''
    } catch {
      return ''
    }
  }

  const canEdit = useMemo(() => {
    return isFetching || isSubmitting || !can(['manage', 'all'])
  }, [isFetching, isSubmitting, can])

  const onChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = event.target
    setValue('orgId', value.toLowerCase())
    const envPart = isProd ? 'app' : 'app.staging'
    setUrl(`${value.toLowerCase()}.${envPart}.mydevices.com`)
  }

  const onBlur = async (event: React.FocusEvent<HTMLInputElement>) => {
    const { value } = event.target
    if (!value) return

    const envPart = isProd ? '' : 'staging.'
    const appHost = `${value.toLowerCase()}.app.${envPart}mydevices.com`

    try {
      const taken = await queryClient.fetchQuery({
        queryKey: ['CheckSetting', appHost],
        queryFn: ({ signal }) =>
          checkSetting({
            appId: appHost,
            signal,
          }),
      })

      if (taken) {
        toast({
          status: 'warning',
          msg: 'This organization ID already exists. Please choose a different one.',
        })
        setValue('orgId', '', { shouldValidate: true })
        setUrl('')
      } else {
        methods.clearErrors('orgId')
      }
    } catch (error) {
      Sentry.captureException(error)
    }
  }

  const onCancel = () => {
    navigate('/manage')
  }

  const onSubmit = async (values: FormInputProps): Promise<boolean> => {
    setFetching(true)

    try {
      resetLog()

      const [logo, emailLogo, dashboardLogo, icon, rawTheme] =
        await Promise.all([
          fetchSvgAsset(themeId, 'logo.svg'),
          fetchSvgAsset(themeId, 'logo-email.svg'),
          fetchSvgAsset(themeId, 'logo-dashboard.svg'),
          fetchSvgAsset(themeId, 'app-icon.svg'),
          fetchTheme(themeId),
        ])

      const theme = JSON.parse(rawTheme)

      if (jsonThemeSchema.safeParse(theme).error) {
        toast({
          status: 'error',
          msg: 'Unable to fetch theme.',
        })
        return false
      }

      if (!(logo && emailLogo && dashboardLogo && icon)) {
        toast({
          status: 'error',
          msg: 'Unable to fetch required assets.',
        })
        return false
      }

      const payload = {
        ...values,
        themeId,
        appId: values.orgId,
        company: values.brandName,
        locale: currentUser?.locale || 'en-us',
        primaryColor: theme.colors.primary,
        secondaryColor: theme.colors.secondary,
        backgroundColor: '#ffffff',
        theme: JSON.stringify(theme),
        logo,
        emailLogo,
        dashboardLogo,
        icon,
        iconPadding: 20,
        iconBackground: '#ffffff',
        backgroundImage: '',
      } satisfies PostParams

      await connect('api/builder/new-organization', payload)

      toast({
        status: 'success',
        msg: `Your organization: ${values.brandName} and domain have been setup ${url}`,
      })

      setOrganizationId(values.orgId)
      setApplicationId(values.orgId)

      reset(defaultValues)
      resetLog()

      setIsDone(true)

      return true
    } catch (error: unknown) {
      disconnect()
      Sentry.captureException(error)
      toast({
        status: 'error',
        msg: 'Unable to create organization.',
      })
      return false
    } finally {
      setFetching(false)
    }
  }

  useEffect(() => {
    if (!isProd && orgId) {
      setValue('email', `${orgId}@yopmail.com`)
    }
  }, [orgId, isProd, setValue])

  useEffect(() => {
    if (blocker.state === 'blocked' && !isDone) {
      modal({
        component: <ConfirmDialog />,
        config: {
          title: 'Organization creation is in progress.',
          description: 'Are you sure you want to leave this page?',
          confirmLabel: 'Leave Anyway',
          onCallback: (confirmed?: boolean) => {
            if (confirmed) {
              blocker.proceed()
            } else {
              blocker.reset()
            }
          },
        },
      })
    }
  }, [modal, blocker, isDone])

  useEffect(() => {
    if (isDone) {
      navigate('/manage/settings/product', { replace: true })
    }
  }, [navigate, isDone])

  return (
    <>
      <Helmet>
        <title>{metaTitle([title, heading])}</title>
      </Helmet>
      <Flex align="center" justify="space-between" py={4} px={6}>
        <HStack spacing={2}>
          <Title title={heading} />
          <Suspense fallback={null}>
            <JobIndicator />
          </Suspense>
        </HStack>
      </Flex>
      <Box px={6}>
        <Alert status={jobFailed ? 'error' : jobCompleted ? 'success' : 'info'}>
          <AlertIcon />
          <AlertDescription>
            {jobCompleted ? (
              <>
                Organization has been created successfully. You can access it
                at:{' '}
                <Link href={`https://${url}`} isExternal color="blue.500">
                  {url}
                </Link>
              </>
            ) : jobFailed ? (
              <>Failed to create organization: {errorMessage}</>
            ) : (
              'This will create a new organization and application. The new organization will inherit the theme and styling from myDevices.'
            )}
          </AlertDescription>
        </Alert>
        <Suspense fallback={null}>
          <FormProvider {...methods}>
            <Box as="form" noValidate onSubmit={handleSubmit(onSubmit)}>
              <Grid
                sx={{
                  gap: 0,
                  h: '100%',
                  gridTemplateRows: 'auto',
                }}
              >
                <GridItem>
                  <Box
                    sx={{
                      w: '100%',
                      h: '100%',
                      overflow: 'auto',
                    }}
                  >
                    <Grid
                      sx={{
                        py: 4,
                        gap: 4,
                        gridTemplateColumns: {
                          base: '1fr',
                          md: 'repeat(2, 1fr)',
                        },
                      }}
                    >
                      <InputField
                        name="orgId"
                        label="Organization ID"
                        control={control}
                        isDisabled={canEdit}
                        isRequired
                        isInvalid={!!errors.orgId}
                        longInfo="The unique identifier for your organization and application. This will be used in URLs and as a reference."
                        placeholder="my-organization"
                        onChange={onChange}
                        onBlur={onBlur}
                      />

                      <InputField
                        name="url"
                        label="Application URL"
                        control={control}
                        isDisabled
                        value={url}
                        placeholder="my-organization.app.mydevices.com"
                        longInfo="The URL for your organization and application."
                      />

                      <InputField
                        name="email"
                        label="Email"
                        control={control}
                        isDisabled={canEdit || !isProd}
                        isRequired={isProd}
                        longInfo={
                          isProd
                            ? 'Your email address.'
                            : 'In staging environment, the email will be automatically generated for a Yopmail account since we do not have a mail trapper.'
                        }
                      />

                      <InputField
                        name="brandName"
                        label="Brand Name"
                        control={control}
                        isDisabled={canEdit}
                        isRequired
                        longInfo="The display name for your brand that will be shown to users."
                        placeholder="My Brand Name"
                      />

                      <InputField
                        name="firstName"
                        label="First Name"
                        control={control}
                        isDisabled={canEdit}
                        isRequired
                        longInfo="Primary organization admin first name."
                      />

                      <InputField
                        name="lastName"
                        label="Last Name"
                        control={control}
                        isDisabled={canEdit}
                        isRequired
                        longInfo="Primary organization admin last name."
                      />

                      <Box>
                        <HStack spacing={2}>
                          <Button
                            type="submit"
                            aria-label="Create"
                            colorScheme="green"
                            minW="120px"
                            isDisabled={
                              !isValid ||
                              isSubmitting ||
                              isSubmitted ||
                              isValidating ||
                              !can(['manage', 'all']) ||
                              readyState === 1
                            }
                            loadingText="Creating"
                            isLoading={isSubmitting || readyState === 1}
                          >
                            {isSubmitted ? 'Created' : 'Create'}
                          </Button>
                          <Button
                            type="button"
                            aria-label="Cancel"
                            variant="ghost"
                            onClick={onCancel}
                            isDisabled={isSubmitting || readyState === 1}
                          >
                            Cancel
                          </Button>
                        </HStack>
                      </Box>
                    </Grid>
                  </Box>
                </GridItem>
              </Grid>
            </Box>
          </FormProvider>
        </Suspense>
      </Box>
    </>
  )
})

export async function loader() {
  const scopes = ['manage:all']

  const { checkLoaderPermissions } = await import('@/utils/loader-auth')
  await checkLoaderPermissions(scopes)

  return {
    title: 'Create Organization',
    scopes,
  }
}

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <CreateOrganization />
}
