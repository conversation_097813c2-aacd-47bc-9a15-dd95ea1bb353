import { z } from 'zod'
import chroma from 'chroma-js'
import { isEmpty } from 'ramda'
import { hex, hexRegex } from '@/utils/zod-plus'

export const schema = z
  .object({
    logo: z
      .string()
      .min(1, 'Logo is required.')
      .startsWith('data:image/svg+xml', 'Invalid format.'),
    emailLogo: z
      .string()
      .startsWith('data:image/svg+xml', 'Invalid format.')
      .or(z.string().optional()),
    dashboardLogo: z
      .string()
      .startsWith('data:image/svg+xml', 'Invalid format.')
      .or(z.string().optional()),
    icon: z
      .string()
      .min(1, 'App Icon is required.')
      .startsWith('data:image/svg+xml', 'Invalid format.'),
    iconPadding: z
      .number()
      .or(z.string().min(1, 'Must be a number.'))
      .pipe(z.coerce.number().nonnegative())
      .default(20),
    iconBackground: z.string().superRefine((arg, ctx) => {
      if (arg && !(hexRegex.test(arg) || chroma.valid(arg))) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Invalid hex color.',
        })
      }
      return z.NEVER
    }),
    headingFont: z.string(),
    bodyFont: z.string(),
    primaryColor: hex,
    secondaryColor: hex,
    primaryColorMap: z.string(),
    secondaryColorMap: z.string(),
    backgroundColor: hex,
    textColor: hex,
    customButtonColor: z.string().superRefine((arg, ctx) => {
      if (arg && !(hexRegex.test(arg) || chroma.valid(arg))) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Invalid hex color.',
        })
      }
      return z.NEVER
    }),
    customLinkColor: z.string().superRefine((arg, ctx) => {
      if (arg && !(hexRegex.test(arg) || chroma.valid(arg))) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Invalid hex color.',
        })
      }
      return z.NEVER
    }),
    customTextColor: z.string().superRefine((arg, ctx) => {
      if (arg && !(hexRegex.test(arg) || chroma.valid(arg))) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Invalid hex color.',
        })
      }
      return z.NEVER
    }),
    appIconSize: z
      .number()
      .or(z.string().min(1, 'Must be a number.'))
      .pipe(z.coerce.number().nonnegative()),
    dashboardLogoHeight: z
      .number()
      .or(z.string().min(1, 'Must be a number.'))
      .pipe(z.coerce.number().nonnegative()),
    startLogoHeight: z
      .number()
      .or(z.string().min(1, 'Must be a number.'))
      .pipe(z.coerce.number().nonnegative()),
    buttonRoundness: z
      .number()
      .or(z.string().min(1, 'Must be a number.'))
      .pipe(z.coerce.number().nonnegative()),
    linkTextDecoration: z.string(),
    useBackground: z.boolean(),
    background: z.object({
      backgroundImage: z.string(),
      backgroundImageFile: z.string(),
      backgroundPosition: z.string(),
      backgroundAttachment: z.string(),
      backgroundRepeat: z.string(),
      backgroundSize: z.string(),
    }),
    colors: z.array(
      z.object({
        name: z.string(),
        value: z.string(),
      })
    ),
  })
  .superRefine((arg, ctx) => {
    if (arg.useBackground && isEmpty(arg.background.backgroundImage.trim())) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Background image style is required.',
        path: ['background.backgroundImage'],
      })
    }

    return z.NEVER
  })

export type FormInputProps = z.infer<typeof schema> & {
  [key: string]: any
}
