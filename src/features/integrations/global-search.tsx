import { useCallback, useEffect, useState } from 'react'
import { useAtom } from 'jotai'
import {
  Icon,
  IconButton,
  Input,
  InputGroup,
  InputLeftElement,
  InputRightElement,
  useColorModeValue,
} from '@chakra-ui/react'
import { SearchIcon, XIcon } from 'lucide-react'
import { searchCriteria } from '@/utils/search-criteria'
import { tableAtom } from '@/utils/stores/table'

interface GlobalSearchProps {
  tableName: string
}

export const GlobalSearch = ({ tableName }: GlobalSearchProps) => {
  const [value, setValue] = useState<string>('')
  const [focus, setFocus] = useState<boolean>(false)
  const [{ filters }, setTable] = useAtom(tableAtom(tableName))

  const inputVariant = useColorModeValue('outline', 'filled')
  const iconColor = useColorModeValue('blackAlpha.500', 'whiteAlpha.400')
  const iconColorFocus = useColorModeValue('secondary.500', 'secondary.200')

  useEffect(() => {
    const currValue = filters.find(({ id }: any) => id === 'name')?.value
    if (currValue) {
      setValue(currValue)
    }
  }, [filters])

  const onSearch = useCallback(
    (value: string): void => {
      const stack = searchCriteria({
        id: 'name',
        condition: 'like',
        value,
        prev: [],
      })
      setValue(value)
      setTable((prev) => ({
        ...prev,
        currPage: 0,
        filter: stack?.[0]?.query,
        filters: [...stack],
      }))
    },
    [setTable]
  )

  const onClear = useCallback((): void => {
    setValue('')
    setTable((prev) => ({
      ...prev,
      filter: undefined,
      filters: [],
    }))
  }, [setTable])

  return (
    <InputGroup>
      <InputLeftElement pointerEvents="none">
        <Icon as={SearchIcon} color={focus ? iconColorFocus : iconColor} />
      </InputLeftElement>
      <Input
        id="search"
        name="search"
        aria-label="search"
        type="text"
        minW="230px"
        placeholder="Search by name"
        value={value}
        variant={inputVariant}
        onChange={(event) => onSearch(event.target.value)}
        onMouseDown={() => setFocus(true)}
        onBlur={() => setFocus(false)}
      />
      {value && (
        <InputRightElement>
          <IconButton
            isRound
            aria-label="Clear Search"
            size="sm"
            variant="link"
            colorScheme="gray"
            onClick={onClear}
            icon={<XIcon size={16} />}
          />
        </InputRightElement>
      )}
    </InputGroup>
  )
}
