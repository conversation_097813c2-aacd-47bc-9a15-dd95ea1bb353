import { memo, useEffect, useMemo, useCallback, useDeferredValue } from 'react'
import { useNavigate } from 'react-router'
import { useQuery, keepPreviousData } from '@tanstack/react-query'
import { useAtomValue, useSetAtom } from 'jotai'
import { Box, Badge } from '@chakra-ui/react'
import { DataTable } from '@/components/data-table/data-table'
import { useOrganization } from '@/hooks/use-organization'
import { useColorMap } from '@/hooks/use-color-map'
import { useAbility } from '@/hooks/use-ability'
import { formatDate } from '@/utils/date/format-date'
import { tableAtom, recordCountAtom } from '@/utils/stores/table'
import { getIntegrations } from '@/api/integrations'
import type { IntegrationModel } from '@/types/models/integration'
import type { GetCursorResponse } from '@/types/api'
import type { ColumnProps, ActionProps } from '@/types/table'

export const TableView = memo(() => {
  const tableName = 'integrations'
  const navigate = useNavigate()
  const { mapColor } = useColorMap()
  const { can } = useAbility()
  const setCount = useSetAtom(recordCountAtom)
  const { applicationId, organizationId } = useOrganization()
  const { limit, currPage, filter } = useAtomValue(tableAtom(tableName))
  const deferredFilter = useDeferredValue(filter)

  const canEdit = can(['edit', 'applications'])

  const { data } = useQuery<GetCursorResponse<IntegrationModel>, Error>({
    queryKey: [
      'GetIntegrations',
      organizationId,
      applicationId,
      limit,
      currPage,
      deferredFilter,
    ],
    queryFn: ({ signal }) =>
      getIntegrations({
        organizationId,
        applicationId,
        limit,
        page: currPage,
        filter: deferredFilter,
        signal,
      }),
    placeholderData: keepPreviousData,
  })

  const onConnect = useCallback(
    ({ id }: IntegrationModel) => {
      navigate(`/manage/integrations/connect/${id}`, {
        viewTransition: true,
      })
    },
    [navigate]
  )

  useEffect(() => {
    if (!data) {
      return
    }
    setCount(data?.count)
  }, [data, setCount])

  const columns = useMemo<ColumnProps<IntegrationModel>[]>(
    () => [
      {
        name: 'Name',
        id: 'name',
        cell: ({ name }) => <Box noOfLines={1}>{name}</Box>,
      },
      {
        name: 'Type',
        id: 'type',
        canHide: true,
        cell: ({ type }) => type && <Box noOfLines={1}>{type}</Box>,
      },
      {
        name: 'Created',
        id: 'created_at',
        canHide: true,
        cell: ({ created_at }) =>
          created_at && (
            <Box noOfLines={1}>{formatDate(created_at, 'P pp')}</Box>
          ),
      },
      {
        name: 'Events',
        id: 'events',
        cell: ({ events }) =>
          (events ?? '').split(',').map((v: string, i: number) => (
            <Badge mr={1} key={i} colorScheme={mapColor(v)}>
              {v}
            </Badge>
          )),
      },
      {
        name: 'Last Data',
        id: 'updated_at',
        canHide: true,
        cell: ({ updated_at }) =>
          updated_at && (
            <Box noOfLines={1}>{formatDate(updated_at, 'P pp')}</Box>
          ),
      },
    ],
    [mapColor]
  )

  const actions = useMemo<ActionProps<IntegrationModel>[]>(
    () => [
      {
        label: 'Connect',
        onClick: onConnect,
        canView: () => canEdit,
      },
    ],
    [canEdit, onConnect]
  )

  return (
    <DataTable
      tableName={tableName}
      data={data ?? { count: 0, rows: [] }}
      columns={columns}
      actions={actions}
    />
  )
})
