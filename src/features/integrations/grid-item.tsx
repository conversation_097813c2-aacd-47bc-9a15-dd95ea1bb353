import { useNavigate } from 'react-router'
import { useQuery } from '@tanstack/react-query'
import { capitalize } from 'lodash'
import {
  Box,
  Image,
  Button,
  Card,
  CardBody,
  CardFooter,
  Heading,
  Stack,
} from '@chakra-ui/react'
import { useDarkerBg } from '@/hooks/use-darker-bg'
import { useAbility } from '@/hooks/use-ability'
import { getIntegration } from '@/api/integrations'
import { useOrganization } from '@/hooks/use-organization'
import type { IntegrationModel } from '@/types/models/integration'

interface GridItemProps {
  row: IntegrationModel
}

export const GridItem = ({ row }: GridItemProps) => {
  const navigate = useNavigate()
  const darkerBg = useDarkerBg()
  const { can } = useAbility()
  const { organizationId, applicationId } = useOrganization()

  const canEdit = can(['edit', 'applications'])

  const { data } = useQuery<IntegrationModel, Error>({
    queryKey: ['GetIntegration', organizationId, applicationId, row?.id],
    queryFn: ({ signal }) =>
      getIntegration({
        organizationId,
        applicationId,
        integrationId: row?.id,
        signal,
      }),
    enabled: !!row?.id,
  })

  const onConnect = () => {
    navigate(`/manage/integrations/connect/${row?.id}`, {
      viewTransition: true,
    })
  }

  return (
    <Card
      direction={{ base: 'column', sm: 'row' }}
      overflow="hidden"
      variant="elevation"
      sx={{
        _dark: {
          bg: darkerBg,
        },
      }}
    >
      <Image
        src={data?.icon}
        alt={row?.name}
        sx={{
          p: 10,
          objectFit: 'contain',
          maxW: { base: '100%', sm: '200px' },
        }}
      />
      <Stack>
        <CardBody>
          <Heading size="md">{row?.name}</Heading>
          <Box sx={{ pt: 2, fontSize: 'sm' }}>
            <Box>Events: {capitalize(row?.events) ?? '-'}</Box>
            <Box>Type: {capitalize(row.type)}</Box>
          </Box>
        </CardBody>
        <CardFooter>
          <Button
            aria-label="Connect"
            variant="solid"
            colorScheme="primary"
            onClick={onConnect}
            isDisabled={!canEdit}
          >
            Connect
          </Button>
        </CardFooter>
      </Stack>
    </Card>
  )
}
