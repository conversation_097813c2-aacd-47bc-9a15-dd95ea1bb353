import {
  memo,
  lazy,
  useEffect,
  useDeferredValue,
  Fragment,
  Suspense,
} from 'react'
import { useInfiniteQuery } from '@tanstack/react-query'
import { useInView } from 'react-intersection-observer'
import { useAtomValue, useSetAtom } from 'jotai'
import { Box, Grid, Center, Alert, Button } from '@chakra-ui/react'
import { ArrowDownIcon } from 'lucide-react'
import { Spinner } from '@/components/spinner'
import { tableAtom, recordCountAtom } from '@/utils/stores/table'
import { getIntegrations } from '@/api/integrations'
import { useOrganization } from '@/hooks/use-organization'

const GridItem = lazy(() =>
  import('@/features/integrations/grid-item').then(({ GridItem }) => ({
    default: GridItem,
  }))
)

export const GridView = memo(() => {
  const tableName = 'integrations'
  const { ref, inView } = useInView()
  const setCount = useSet<PERSON>tom(recordCountAtom)
  const { applicationId, organizationId } = useOrganization()
  const { currPage, filter } = useAtomValue(tableAtom(tableName))
  const deferredFilter = useDeferredValue(filter)

  const { data, status, isFetchingNextPage, fetchNextPage, hasNextPage } =
    useInfiniteQuery({
      queryKey: [
        'GetInfinityIntegrations',
        10,
        currPage,
        deferredFilter,
        applicationId,
        organizationId,
      ],
      queryFn: ({ pageParam = 0, signal }) =>
        getIntegrations({
          applicationId,
          organizationId,
          limit: 10,
          page: Number(pageParam),
          filter: deferredFilter,
          signal,
        }),
      initialPageParam: 0,
      getNextPageParam: (lastPage) => lastPage?.nextCursor,
    })

  useEffect(() => {
    if (!data) {
      return
    }
    setCount(data.pages?.[0]?.count ?? 0)
  }, [data, setCount])

  useEffect(() => {
    if (inView) {
      fetchNextPage()
    }
  }, [inView, fetchNextPage])

  return (
    <Box pt={4}>
      {status === 'pending' ? (
        <Spinner />
      ) : status === 'error' ? (
        <Alert status="error">Error loading data</Alert>
      ) : (
        <>
          <Grid
            sx={{
              gap: 4,
              pb: 4,
              gridTemplateColumns: 'repeat(auto-fill, minmax(400px, 1fr))',
            }}
          >
            {data.pages.map((page) => (
              <Fragment key={`${page?.nextCursor}`}>
                {page?.rows.map((row) => (
                  <Suspense key={row.id} fallback={null}>
                    <GridItem row={row} />
                  </Suspense>
                ))}
              </Fragment>
            ))}
          </Grid>
          {hasNextPage && (
            <Center>
              <Button
                ref={ref}
                variant="ghost"
                colorScheme="secondary"
                onClick={() => fetchNextPage()}
                leftIcon={<ArrowDownIcon size={16} />}
                isLoading={isFetchingNextPage}
                isDisabled={isFetchingNextPage}
              >
                {isFetchingNextPage
                  ? 'Loading more ...'
                  : hasNextPage
                    ? 'Load More'
                    : 'Nothing more to load'}
              </Button>
            </Center>
          )}
        </>
      )}
    </Box>
  )
})
