import { useMemo } from 'react'
import { is } from 'ramda'
import { useQuery } from '@tanstack/react-query'
import { Card } from '@/features/admin-api/card'
import { useOrganization } from '@/hooks/use-organization'
import { getLocationsCount } from '@/api/locations'

export const LocationsCard = () => {
  const { organizationId, applicationId } = useOrganization()

  const { data } = useQuery<number | null, Error>({
    queryKey: ['GetLocationsCount', organizationId, applicationId],
    queryFn: ({ signal }) =>
      organizationId && applicationId
        ? getLocationsCount({
            organizationId,
            applicationId,
            signal,
          })
        : null,
    enabled: !!organizationId && !!applicationId,
  })

  const count = useMemo<number>(() => (is(Number, data) ? data : 0), [data])

  return <Card title="Locations" count={count} />
}
