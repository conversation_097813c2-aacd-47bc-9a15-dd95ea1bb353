import { Suspense } from 'react'
import { Flex, Heading, Text } from '@chakra-ui/react'
import { useDarkerBg } from '@/hooks/use-darker-bg'

export const Card = ({
  title,
  count = 0,
}: {
  title: string
  count?: number
}) => {
  const darkerBg = useDarkerBg()

  return (
    <Suspense fallback={null}>
      <Flex
        sx={{
          p: 2,
          height: '85px',
          borderWidth: '1px',
          borderRadius: 'md',
          alignItems: 'center',
          justifyContent: 'center',
          flexDirection: 'column',
          borderColor: 'gray.200',
          bg: 'white',
          _dark: {
            borderColor: 'transparent',
            bg: darkerBg,
          },
        }}
      >
        <Text
          sx={{
            fontSize: 'xs',
            lineHeight: 'normal',
            textTransform: 'uppercase',
          }}
        >
          {title}
        </Text>
        <Heading
          as="h3"
          sx={{
            p: 0,
            m: 0,
            fontSize: '3xl',
            fontWeight: 'medium',
            lineHeight: 'normal',
          }}
        >
          {count}
        </Heading>
      </Flex>
    </Suspense>
  )
}
