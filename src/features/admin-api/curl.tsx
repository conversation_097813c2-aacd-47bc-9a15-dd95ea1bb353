import * as Sentry from '@sentry/react'
import { useWatch, type Control } from 'react-hook-form'
import { useAtomValue } from 'jotai'
import {
  Box,
  Code,
  Link,
  IconButton,
  type SystemStyleObject,
} from '@chakra-ui/react'
import { CopyIcon } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { toggleAtom } from '@/utils/stores/toggle'

interface CurlProps {
  passwordName: string
  control: Control<any, any>
  sx?: SystemStyleObject
}

const IDP_URL = import.meta.env.VITE_IDP_API_URL

export const Curl = ({ passwordName, control, sx }: CurlProps) => {
  const toast = useToast()
  const showPassword = useAtomValue(toggleAtom(passwordName))
  const helpUrl = 'https://mydevices.com/link/iotb-console-adminapi'

  const applicationId = useWatch({
    name: 'applicationId',
    control,
  })

  const clientSecret = useWatch({
    name: 'clientSecret',
    control,
  })

  const getSecret = (value: string, visible: boolean): string =>
    showPassword || visible ? value : 'x'.repeat(value.length)

  const getCurl = (visible = false): string => {
    return [
      'curl --request POST',
      `--url ${IDP_URL}/auth/realms/${applicationId}/protocol/openid-connect/token`,
      `--header 'content-type: application/x-www-form-urlencoded'`,
      `--data 'grant_type=client_credentials&client_id=${applicationId}&client_secret=${getSecret(
        clientSecret,
        visible
      )}'`,
    ].join(' \\ \n')
  }

  const onCopy = async (): Promise<void> => {
    try {
      await navigator.clipboard.writeText(getCurl(true))
      toast({
        status: 'copy',
        msg: 'cURL request has been copied to clipboard.',
      })
    } catch (error: unknown) {
      Sentry.captureException(error, {
        fingerprint: ['clipboard'],
        level: 'warning',
      })
    }
  }

  return (
    <Box sx={sx}>
      <Box
        sx={{
          pos: 'relative',
          borderRadius: 'md',
          p: 4,
          mb: 2,
          bg: 'blackAlpha.50',
          _dark: {
            bg: 'blackAlpha.100',
          },
        }}
      >
        <IconButton
          aria-label="Copy"
          variant="link"
          colorScheme="secondary"
          icon={<CopyIcon size={16} />}
          onClick={onCopy}
          sx={{
            pos: 'absolute',
            top: 3,
            right: 0,
            borderTopLeftRadius: 0,
            borderBottomRightRadius: 0,
          }}
        />
        <Code
          sx={{
            whiteSpace: 'break-spaces',
            bg: 'transparent',
          }}
        >
          {getCurl()}
        </Code>
      </Box>
      <Link href={helpUrl} target="_blank" rel="noopener noreferrer">
        API Help
      </Link>
    </Box>
  )
}
