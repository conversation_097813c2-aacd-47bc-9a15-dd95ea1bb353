import { useMemo } from 'react'
import { is } from 'ramda'
import { useQuery } from '@tanstack/react-query'
import { Card } from '@/features/admin-api/card'
import { useOrganization } from '@/hooks/use-organization'
import { getThingsCount } from '@/api/things/things'

export const ThingsCard = () => {
  const { organizationId, applicationId } = useOrganization()

  const { data } = useQuery<number | null, Error>({
    queryKey: ['GetThingsCount', organizationId, applicationId],
    queryFn: ({ signal }) =>
      organizationId && applicationId
        ? getThingsCount({
            organizationId,
            applicationId,
            signal,
          })
        : null,
    enabled: !!organizationId && !!applicationId,
  })

  const count = useMemo<number>(() => (is(Number, data) ? data : 0), [data])

  return <Card title="Devices Connected" count={count} />
}
