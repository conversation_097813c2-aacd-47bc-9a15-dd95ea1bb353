import type { EventHandler } from '@/types/powerbi'
import { describe, it, expect } from 'vitest'
import { stringifyMap } from './stringify-map'

describe('tests PowerBIEmbed stringifyMap method', () => {
  it('stringifies the event handler map', () => {
    const eventHandlerMap = new Map<string, EventHandler>([
      [
        'loaded',
        () => {
          console.info('Report loaded')
          return undefined
        },
      ],
      [
        'rendered',
        () => {
          console.info('Rendered')
          return undefined
        },
      ],
    ])

    const expected = `[["loaded","()=>{ console.info('Report loaded'); return undefined; }"],["rendered","()=>{ console.info('Rendered'); return undefined; }"]]`

    expect(stringifyMap(eventHandlerMap)).toEqual(expected)
  })

  it('stringifies empty event handler map', () => {
    const eventHandlerMap = new Map<string, EventHandler>([])

    expect(stringifyMap(eventHandlerMap)).toEqual('[]')
  })

  it('stringifies null in event handler map', () => {
    const eventHandlerMap = new Map<string, any>([
      ['loaded', null],
      [
        'rendered',
        () => {
          console.info('Rendered')
        },
      ],
    ])

    const expected = `[["loaded",""],["rendered","()=>{ console.info('Rendered'); }"]]`

    expect(stringifyMap(eventHandlerMap)).toEqual(expected)
  })
})
