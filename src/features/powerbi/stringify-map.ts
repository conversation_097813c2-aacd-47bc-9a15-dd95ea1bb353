import type { EmbedProps } from '@/types/powerbi'

/**
 * Get JSON string representation of the given map.
 */
export const stringifyMap = (map: EmbedProps['eventHandlers']): string => {
  // Return empty string for empty/null map
  if (!map) {
    return ''
  }

  // Return JSON string
  return JSON.stringify(
    [...map.entries()].map(([key, value]) => {
      // Convert event handler method to a string containing its source code for comparison
      return [key, value ? value.toString() : '']
    })
  )
    .replace(/\\n/g, '')
    .replace(/\s+/g, ' ')
}
