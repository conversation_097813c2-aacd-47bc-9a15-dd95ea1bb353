import { Flex } from '@chakra-ui/react'
import { useFullName } from '@/features/subscription/hooks/use-full-name'

interface Props {
  contact: any
  addOns: string[]
}

export const SubscriptionUserListItem = ({ contact, addOns }: Props) => {
  const fullName = useFullName(contact)

  return (
    <Flex
      sx={{
        pos: 'relative',
        alignItems: 'center',
        flexWrap: 'wrap',
        py: '0.5rem',
        mx: { base: '0.5rem', md: '1rem' },
        borderBottom: '1px solid',
        borderBottomColor: 'gray.100',
        _last: {
          borderBottom: '0',
        },
      }}
    >
      <Flex sx={{ flex: { base: '100%', md: '1' } }}>{fullName}</Flex>
      <Flex
        sx={{
          flex: { base: '100%', md: '1' },
          w: '100%',
          overflow: 'hidden',
          whiteSpace: 'nowrap',
          textOverflow: 'ellipsis',
          display: 'block',
          minW: 0,
          pr: '10px',
        }}
      >
        {contact.email}
      </Flex>
      <Flex
        sx={{
          flex: { base: '100%', md: '1' },
        }}
      >
        {addOns.join(', ')}
      </Flex>
    </Flex>
  )
}
