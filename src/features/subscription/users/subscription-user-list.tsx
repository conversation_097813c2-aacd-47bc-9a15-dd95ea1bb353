import { useState, memo } from 'react'
import { Box, Flex, Heading } from '@chakra-ui/react'
import { PageSpinner } from '@/components/page-spinner'
import { useContactsWithSubscriptions } from '@/features/subscription/hooks/use-contacts-with-subscriptions'
import { SubscriptionUserListItem } from '@/features/subscription/users/subscription-user-list-item'
import { SubscriptionUserListEdit } from '@/features/subscription/users/subscription-user-list-edit'
import { UserListHeaderSearch } from '@/features/subscription/users/user-list-header-search'
import { UserListHeader } from '@/features/subscription/users/user-list-header'
import { NoUsers } from '@/features/subscription/users/no-users'

export const SubscriptionUserList = memo(() => {
  const [isEditing, _setIsEditing] = useState(false)
  const {
    contacts,
    contactsWithAddOnsOnly,
    isFetchingContacts,
    isFetchingCompanies,
  } = useContactsWithSubscriptions()

  if (isFetchingCompanies || isFetchingContacts) {
    return <PageSpinner />
  }

  return (
    <>
      <Flex
        sx={{
          w: '100%',
          p: { base: '1rem', md: '2rem' },
          justifyContent: 'center',
          flexWrap: 'wrap',
        }}
      >
        <Box
          sx={{
            w: '100%',
            maxW: '800px',
            borderRadius: '10px',
            backgroundColor: 'white',
            border: '1px solid',
            borderColor: 'gray.100',
            boxShadow: '0 4px 4px -2px #E2E8F0',
          }}
        >
          <Heading as="h2" sx={{ fontSize: 'md', p: '1rem 1rem 0 1rem' }}>
            Users
          </Heading>

          <Box
            sx={{
              w: '100%',
              bg: 'white',
              flexWrap: 'wrap',
              mb: '1rem',
            }}
          >
            <UserListHeaderSearch />
            <UserListHeader />
            {contacts.length === 0 ? <NoUsers /> : null}
            {isEditing
              ? contacts.map(([contact]) => (
                  <SubscriptionUserListEdit
                    key={contact.id}
                    contact={contact}
                  />
                ))
              : contactsWithAddOnsOnly.map(([contact, addOns]) => (
                  <SubscriptionUserListItem
                    key={contact.id}
                    contact={contact}
                    addOns={addOns}
                  />
                ))}
          </Box>
        </Box>
      </Flex>
    </>
  )
})
