import { memo } from 'react'
import { Flex, Text } from '@chakra-ui/react'

export const UserListHeader = memo(() => {
  return (
    <Flex
      sx={{
        alignItems: 'center',
        flexWrap: 'wrap',
        py: '0.5rem',
        mx: { base: '0.5rem', md: '1rem' },
        borderBottom: '2px solid',
        borderBottomColor: 'gray.100',
        _last: {
          borderBottom: '0',
        },
      }}
    >
      <Flex
        sx={{
          flex: { base: '100%', md: '1' },
          display: { base: 'none', md: 'initial' },
        }}
      >
        <Text as="span" sx={{ fontWeight: '600', fontSize: 'lg' }}>
          Contact
        </Text>
      </Flex>
      <Flex
        sx={{
          flex: { base: '50%', md: '1' },
          w: '100%',
          pr: '10px',
        }}
      >
        <Text as="span" sx={{ fontWeight: '600', fontSize: 'lg' }}>
          Email Address
        </Text>
      </Flex>
      <Flex
        sx={{
          flex: { base: '100%', md: '1' },
          display: { base: 'none', md: 'initial' },
        }}
      >
        <Text as="span" sx={{ fontWeight: '600', fontSize: 'lg' }}>
          Licences
        </Text>
      </Flex>
    </Flex>
  )
})
