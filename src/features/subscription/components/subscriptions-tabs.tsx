import { useMemo, memo } from 'react'
import { matchPath, useLocation, Link as RouterLink } from 'react-router'
import { Flex, Link } from '@chakra-ui/react'

const ActiveLink = ({
  href,
  children,
  sx,
}: {
  href: string
  children: React.ReactNode
  sx?: any
}) => {
  return (
    <Link as={RouterLink} to={href} sx={sx}>
      {children}
    </Link>
  )
}

export const SubscriptionsTabs = memo(() => {
  const enabledFeatures = {
    billing_show_subscription_feature_enabled: true,
  } as const

  const links = useMemo(
    () =>
      [
        {
          url: '/account/subscription',
          name: 'Subscriptions',
          enabled: enabledFeatures.billing_show_subscription_feature_enabled,
        },
        {
          url: '/account/subscription/users',
          name: 'Users',
          enabled: enabledFeatures.billing_show_subscription_feature_enabled,
        },
      ].reduce((acc: { url: string; name: string }[], link) => {
        if (!link.enabled) return acc
        const { url, name } = link
        acc.push({ url, name })
        return acc
      }, []),
    [enabledFeatures.billing_show_subscription_feature_enabled]
  )

  return (
    <Flex
      sx={{
        justifyContent: 'start',
        flexDirection: 'row',
        borderBottomStyle: 'solid',
        borderBottomColor: 'gray.100',
        borderBottomWidth: '1px',
        marginBottom: '2rem',
        _dark: {
          borderBottomColor: 'whiteAlpha.100',
        },
      }}
    >
      {links.map((link) => (
        <SubscriptionTab key={link.url} href={link.url} text={link.name} />
      ))}
    </Flex>
  )
})

interface Props {
  href: string
  text: string
}

export const SubscriptionTab = ({ href, text }: Props) => {
  const { pathname } = useLocation()
  const pathInfo = matchPath(href, pathname)
  const isCurrentPath = pathInfo !== null

  return (
    <>
      <ActiveLink
        href={href}
        sx={{
          display: 'flex',
          alignItems: 'center',
          paddingTop: 'var(--chakra-space-2)',
          paddingBottom: 'var(--chakra-space-2)',
          paddingInlineStart: 'var(--chakra-space-4)',
          paddingInlineEnd: 'var(--chakra-space-4)',
          color: isCurrentPath ? 'primary.500' : 'gray.700',
          borderBottomStyle: 'solid',
          borderBottomWidth: '1px',
          borderBottomColor: isCurrentPath ? 'primary.500' : undefined,
          marginBottom: '-1px',
          _dark: {
            color: isCurrentPath ? 'primary.200' : 'white',
            borderBottomColor: isCurrentPath ? 'primary.200' : undefined,
          },
        }}
      >
        {text}
      </ActiveLink>
    </>
  )
}
