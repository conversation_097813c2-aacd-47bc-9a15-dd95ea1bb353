import { memo, useMemo } from 'react'
import { matchPath, useLocation } from 'react-router'
import { Flex } from '@chakra-ui/react'
import { ActiveLink } from '@/features/subscription/components/active-link'
import { useEnabledEnvFeatures } from '@/hooks/use-enabled-env-features'

interface Props {
  href: string
  text: string
}

const SubscriptionTab = ({ href, text }: Props) => {
  const { pathname } = useLocation()
  const pathInfo = matchPath(href, pathname)
  const isCurrentPath = pathInfo !== null
  const color = isCurrentPath
    ? 'primary.500'
    : 'var(--chakra-colors-chakra-body-text)'

  return (
    <>
      <ActiveLink
        href={href}
        sx={{
          display: 'flex',
          alignItems: 'center',
          paddingTop: 'var(--chakra-space-2)',
          paddingBottom: 'var(--chakra-space-2)',
          paddingInlineStart: 'var(--chakra-space-4)',
          paddingInlineEnd: 'var(--chakra-space-4)',
          color,
          borderBottomStyle: 'solid',
          borderBottomWidth: '1px',
          borderBottomColor: isCurrentPath ? 'primary.500' : undefined,
          marginBottom: '-1px',
        }}
      >
        {text}
      </ActiveLink>
    </>
  )
}

export const SubscriptionsTabs = memo(() => {
  const enabledFeatures = useEnabledEnvFeatures()

  const links = useMemo(
    () =>
      [
        {
          url: '/account/subscription',
          name: 'Subscriptions',
          enabled: enabledFeatures.billing_show_subscription_feature_enabled,
        },
        {
          url: '/account/subscription/users',
          name: 'Users',
          enabled: enabledFeatures.billing_show_subscription_feature_enabled,
        },
      ].reduce((acc: { url: string; name: string }[], link) => {
        if (!link.enabled) return acc
        const { url, name } = link
        acc.push({ url, name })
        return acc
      }, []),
    [enabledFeatures.billing_show_subscription_feature_enabled]
  )

  return (
    <Flex
      sx={{
        justifyContent: 'start',
        flexDirection: 'row',
        borderBottomStyle: 'solid',
        borderBottomColor: 'inherit',
        borderBottomWidth: '1px',
        marginBottom: '2rem',
      }}
    >
      {links.map((link) => (
        <SubscriptionTab key={link.url} href={link.url} text={link.name} />
      ))}
    </Flex>
  )
})
