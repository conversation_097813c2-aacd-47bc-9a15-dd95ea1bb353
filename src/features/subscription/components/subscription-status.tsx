import { useNavigation, Form } from 'react-router'
import {
  Box,
  Grid,
  Button,
  Input,
  List,
  ListItem,
  FormLabel,
} from '@chakra-ui/react'
import { formatDate } from '@/utils/date/format-date'
import { useCurrencyFormatter } from '@/features/subscription/hooks/use-currency-formatter'
import { useSubscriptionInterval } from '@/features/subscription/hooks/use-subscription-interval'
import { useIsTrialSubscription } from '@/features/subscription/hooks/use-is-trial-subscription'
import { useSubscriptionHasEnded } from '@/features/subscription/hooks/use-subscription-has-ended'
import { useBillingStatus } from '@/features/subscription/hooks/use-billing-api'
import { Card } from '@/features/subscription/components/card'
import { Loader } from '@/features/subscription/components/loader'
import type { SubscriptionWithProduct } from '@/types/models/billing'
import { useSubscriptionIntervalText } from '@/features/subscription/hooks/use-subscription-interval-text'

interface Props {
  subscription: SubscriptionWithProduct
  hasSubscription: boolean
}

export const SubscriptionStatus = ({
  subscription,
  hasSubscription,
}: Props) => {
  const navigation = useNavigation()
  const currencyFormatter = useCurrencyFormatter(subscription.id)
  const subscriptionInterval = useSubscriptionInterval(subscription.id)
  const isTrial = useIsTrialSubscription(subscription.id)
  const hasEnded = useSubscriptionHasEnded(subscription.id)
  const { data: status, isFetching } = useBillingStatus(
    subscription.id,
    hasSubscription
  )

  const subscriptionIntervalText =
    useSubscriptionIntervalText(subscriptionInterval)

  if (isFetching) return <Loader height="100px" />

  return (
    <Grid
      sx={{
        width: '100%',
        p: { base: '0.5rem', md: '1rem' },
        gridTemplateColumns: '1fr',
        gap: { base: '0.5rem', md: '1rem' },
        alignItems: 'center',
        m: 0,
        borderBottom: '1px solid',
        borderBottomColor: 'gray.100',
        _last: {
          border: 0,
        },
      }}
    >
      <FormLabel
        sx={{
          flex: '1',
          m: '0',
          fontWeight: '600',
        }}
      >
        Current Plan
      </FormLabel>
      <Grid
        sx={{
          gridTemplateColumns: '1fr auto',
          gap: 4,
          alignItems: 'top',
          pb: '1rem',
        }}
      >
        <Box>
          <Box
            sx={{
              fontWeight: 'semibold',
            }}
          >
            {status?.product?.name}
          </Box>
          <Box
            sx={{
              display: 'flex',
              alignItems: 'baseline',
              fontWeight: 'bold',
              fontSize: '1.25rem',
              gap: 1,
            }}
          >
            <Box>
              {currencyFormatter.format(
                (status?.subscription?.plan?.amount ?? 0) / 100
              )}
            </Box>
            <Box sx={{ fontSize: 'sm' }}>{subscriptionIntervalText}</Box>
          </Box>
          <Box
            sx={{
              fontSize: '0.8rem',
              lineHeight: '1.5rem',
              pb: '1rem',
            }}
          >
            {isTrial && hasEnded
              ? `Your trial expired on ${formatDate(subscription.end_date, 'P')}`
              : isTrial
                ? `Your trial expires on ${formatDate(subscription.end_date, 'P')}`
                : `Your plan renews on ${formatDate(subscription.end_date, 'P')}`}
          </Box>
          {status?.subscription.plan?.id.startsWith('card_') && (
            <List>
              <ListItem
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 2,
                }}
              >
                <Card
                  id={status?.subscription.plan.id}
                  subscriptionId={subscription.id}
                />
              </ListItem>
            </List>
          )}
        </Box>
        <Box>
          <Form method="post" action="/account/subscription">
            <Input
              type="hidden"
              name="return_url"
              value={`${window.location.origin}/account/subscription`}
            />
            <Button
              type="submit"
              aria-label="Update Plan"
              colorScheme="primary"
              isLoading={navigation.formAction === '/account/subscription'}
            >
              Update Plan
            </Button>
          </Form>
        </Box>
      </Grid>
    </Grid>
  )
}
