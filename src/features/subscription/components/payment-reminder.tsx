import { memo } from 'react'
import { useNavigation, Form } from 'react-router'
import { isNil } from 'ramda'
import {
  useDisclosure,
  HStack,
  Input,
  Button,
  Text,
  Alert,
  AlertIcon,
  AlertDescription,
  CloseButton,
} from '@chakra-ui/react'
import {
  useBillingStatus,
  useBillingCustomer,
} from '@/features/subscription/hooks/use-billing-api'
import { formatDate } from '@/utils/date/format-date'
import { useSubscriptionEnabled } from '@/features/subscription/hooks/use-subscription-enabled'
import { useAppSubscription } from '@/features/subscription/hooks/use-app-subscription'
import { useShowPaymentReminder } from '@/features/subscription/hooks/use-show-payment-reminder'
import { useSubscriptionDaysLeft } from '@/features/subscription/hooks/use-subscription-days-left'

export const PaymentReminder = memo(() => {
  const navigation = useNavigation()
  const { isOpen, onClose } = useDisclosure({ defaultIsOpen: true })
  const isEnabled = useSubscriptionEnabled()
  const showReminder = useShowPaymentReminder()
  const { days, isSameDay, hasEnded } = useSubscriptionDaysLeft()
  const { subscription } = useAppSubscription()
  const { data: status, error } = useBillingStatus(
    subscription?.id ?? '',
    isEnabled && !isNil(subscription)
  )
  const { data: customer } = useBillingCustomer(
    subscription?.id ?? '',
    isEnabled && !isNil(subscription)
  )

  if (
    !(
      isEnabled &&
      showReminder &&
      subscription &&
      isOpen &&
      status &&
      customer
    ) ||
    error?.status === 404 ||
    !isNil(customer.invoice_settings.default_payment_method) ||
    isNil(days) ||
    subscription?.status === 'trialing'
  ) {
    return null
  }

  const date = formatDate(subscription.end_date, 'P')
  const name = status.product?.name

  return (
    <Alert
      status="info"
      sx={{
        justifyContent: 'center',
        p: '1rem',
        fontSize: 'md',
      }}
    >
      <AlertDescription>
        <HStack>
          <AlertIcon />
          {hasEnded ? (
            <Text>Your subscription has ended.</Text>
          ) : (
            <Text>
              {isSameDay
                ? 'Your subscription ends today.'
                : `Your subscription ends in ${days} days.`}{' '}
              Your subscription for <strong>{name}</strong> is set to expire on{' '}
              <strong>{date}</strong>.
            </Text>
          )}
          <Form method="post" action="/account/subscription">
            <Input
              type="hidden"
              name="return_url"
              value={`${window.location.origin}/account/subscription`}
            />
            <Input
              type="hidden"
              name="subscription_id"
              value={subscription.id}
            />
            <Button
              type="submit"
              aria-label="Renew now"
              colorScheme="primary"
              size="sm"
              isLoading={navigation.formAction === '/account/subscription'}
            >
              Renew now
            </Button>
          </Form>
        </HStack>
      </AlertDescription>
      <CloseButton ml="0.5rem" onClick={onClose} />
    </Alert>
  )
})
