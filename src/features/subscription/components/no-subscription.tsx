import { memo } from 'react'
import { Box, Flex } from '@chakra-ui/react'

export const NoSubscription = memo(() => (
  <Flex
    sx={{
      w: '100%',
      p: { base: '1rem', md: '2rem' },
      justifyContent: 'center',
      flexWrap: 'wrap',
    }}
  >
    <Box
      sx={{
        w: '100%',
        maxW: '800px',
        borderRadius: '10px',
        backgroundColor: 'white',
        border: '1px solid',
        borderColor: 'gray.100',
        boxShadow: '0 4px 4px -2px #E2E8F0',
        p: '1rem',
      }}
    >
      No subscriptions found.
    </Box>
  </Flex>
))
