import { Box, IconButton, HStack } from '@chakra-ui/react'
import { CheckIcon, XIcon, PencilIcon } from 'lucide-react'

export const FloatingActionButton = ({
  editMode,
  isSaving,
  onSave,
  onCancelEdit,
  setEditMode,
  setEditedUsers,
}: {
  editMode: boolean
  isSaving: boolean
  onSave: () => void
  onCancelEdit: () => void
  setEditMode: (mode: boolean) => void
  setEditedUsers: (users: any[]) => void
}) => (
  <Box
    position="fixed"
    bottom={{ base: 6, md: 10 }}
    right={{ base: 6, md: 10 }}
    zIndex="popover"
  >
    <HStack spacing={2}>
      <Box
        display="flex"
        transition="all 0.3s ease-in-out"
        transform={editMode ? 'translateX(0)' : 'translateX(100%)'}
        opacity={editMode ? 1 : 0}
        position="absolute"
        right="100%"
        mr={2}
      >
        <HStack spacing={2}>
          <IconButton
            aria-label="Save Changes"
            icon={<CheckIcon size={20} />}
            colorScheme="green"
            size="lg"
            height="48px"
            minWidth="48px"
            width="48px"
            borderRadius="full"
            boxShadow="lg"
            onClick={onSave}
            isRound
            transition="all 0.2s ease-in-out"
            _hover={{ transform: 'scale(1.05)' }}
            isLoading={isSaving}
            isDisabled={isSaving}
          />
          <IconButton
            aria-label="Cancel Edit"
            icon={<XIcon size={20} />}
            colorScheme="gray"
            size="lg"
            height="48px"
            minWidth="48px"
            width="48px"
            borderRadius="full"
            boxShadow="lg"
            onClick={onCancelEdit}
            isRound
            transition="all 0.2s ease-in-out"
            _hover={{ transform: 'scale(1.05)' }}
          />
        </HStack>
      </Box>
      <IconButton
        aria-label={editMode ? 'Cancel Edit' : 'Edit Licenses'}
        icon={<PencilIcon size={24} />}
        colorScheme={editMode ? 'red' : 'primary'}
        size="lg"
        height="48px"
        minWidth="48px"
        width="48px"
        borderRadius="full"
        boxShadow="lg"
        onClick={() => {
          if (editMode) {
            setEditMode(false)
            setEditedUsers([])
          } else {
            setEditMode(true)
          }
        }}
        isRound
        transition="all 0.2s ease-in-out"
        _hover={{ transform: 'scale(1.05)' }}
      />
    </HStack>
  </Box>
)
