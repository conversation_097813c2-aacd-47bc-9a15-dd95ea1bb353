import { useCallback } from 'react'
import { useNavigate, Navigate } from 'react-router'
import {
  Box,
  Badge,
  Text,
  Icon,
  Image,
  Button,
  Flex,
  Grid,
  Divider,
  <PERSON><PERSON><PERSON><PERSON>,
  Link,
} from '@chakra-ui/react'
import { StoreIcon, CheckCircleIcon } from 'lucide-react'
import { PageSpinner } from '@/components/page-spinner'
import { useBillingStatus } from '@/features/subscription/hooks/use-billing-api'
import { useHasSubscription } from '@/features/subscription/hooks/use-has-subscription'
import { useCurrencyFormatter } from '@/features/subscription/hooks/use-currency-formatter'
import { useSubscriptionInterval } from '@/features/subscription/hooks/use-subscription-interval'
import { useTieredPrice } from '@/features/subscription/hooks/use-tiered-price'
import type { SubscriptionWithProduct } from '@/types/models/billing'

const assetPrefix = import.meta.env.VITE_ASSETS_PREFIX.endsWith('/')
  ? import.meta.env.VITE_ASSETS_PREFIX.slice(0, -1)
  : import.meta.env.VITE_ASSETS_PREFIX

interface Props {
  subscription: SubscriptionWithProduct
}

export const BillingStatus = ({ subscription }: Props) => {
  const navigate = useNavigate()
  const { hasSubscription, isPending: isPendingSubscriptions } =
    useHasSubscription()
  const { data: status, isPending: isPendingStatus } = useBillingStatus(
    subscription.id,
    hasSubscription
  )
  const isPending = isPendingSubscriptions || isPendingStatus
  const currencyFormatter = useCurrencyFormatter(subscription.id)
  const subscriptionInterval = useSubscriptionInterval(subscription.id)
  const price = useTieredPrice(subscription.stripe_price, subscription.quantity)

  const onFinished = useCallback(() => {
    navigate('/manage/list')
  }, [navigate])

  if (!(hasSubscription && status?.subscription?.plan?.active)) {
    return <Navigate to="/manage/list" replace />
  }

  if (isPending) return <PageSpinner />

  return (
    <>
      <Grid
        sx={{
          gridTemplateColumns: { base: '1fr', md: '1fr 2px 1fr' },
          w: '100%',
          h: 'calc(100% - 60px)',
        }}
      >
        <Flex
          sx={{
            m: { base: '1rem', md: '4rem' },
            flexDirection: 'column',
            h: 'auto',
          }}
        >
          <Flex
            sx={{
              fontWeight: 'bold',
              alignItems: 'center',
              pb: '2rem',
            }}
          >
            <Flex
              sx={{
                bg: 'white',
                borderRadius: '50%',
                boxShadow: 'md',
                p: '0.5rem',
                mr: '0.7rem',
                color: 'gray.500',
              }}
            >
              <Icon as={StoreIcon} w={3} h={3} />
            </Flex>
            <Text>myDevices, Inc.</Text>
            {!status?.subscription?.plan?.livemode && (
              <Badge
                colorScheme="orange"
                sx={{
                  ml: '0.5rem',
                  fontWeight: 'bold',
                  textTransform: 'uppercase',
                }}
              >
                Test Mode
              </Badge>
            )}
          </Flex>
          <Box
            sx={{
              fontSize: '1.1rem',
              lineHeight: 'normal',
              fontWeight: 'semibold',
            }}
          >
            Subscribe to {status?.product?.name}
          </Box>
          <Flex
            sx={{
              alignItems: 'baseline',
              py: '0.2rem',
            }}
          >
            <Text
              sx={{
                display: 'inline',
                fontWeight: 'bold',
                fontSize: '2.5rem',
                pr: '0.5rem',
              }}
            >
              {currencyFormatter.format(price ?? 0)}
            </Text>
            <Text
              sx={{
                fontWeight: 'bold',
                fontSize: '1.1rem',
                color: 'gray.600',
              }}
            >
              {subscriptionInterval
                ? subscriptionInterval.transKey.replace(
                    '{interval}',
                    subscriptionInterval.count.toString()
                  )
                : null}
            </Text>
          </Flex>
          <Box sx={{ lineHeight: '1.25rem' }}>
            {status.product?.description}
          </Box>
          <Box mt="2rem">
            <Image
              src={`${assetPrefix}/simplysense/logo.svg`}
              sx={{
                position: 'relative',
                h: '70px',
              }}
            />
          </Box>
        </Flex>
        <Flex
          sx={{
            bg: 'gray.50',
            display: { base: 'none', md: 'block' },
            mb: '1rem',
          }}
        />
        <Flex
          sx={{
            m: { base: '1rem', md: '4rem' },
            alignItems: 'center',
            flexDirection: 'column',
          }}
        >
          <Flex
            sx={{
              flexDirection: 'column',
              alignItems: 'center',
              textAlign: 'center',
              w: '100%',
            }}
          >
            <Icon as={CheckCircleIcon} color="green.500" boxSize={14} />
            <Text
              sx={{
                fontSize: '1.3rem',
                fontWeight: 'bold',
                my: '0.5rem',
                w: '100%',
              }}
            >
              Thanks for subscribing
            </Text>
            <Text
              sx={{
                fontSize: '1rem',
                color: 'gray.500',
                lineHeight: '1.2rem',
                w: '100%',
                pt: '0.5rem',
              }}
            >
              A payment to MYDEVICES, INC. will appear on your statement.
            </Text>
            <HStack
              sx={{
                w: '100%',
                my: '1rem',
                alignItems: 'baseline',
                border: '1px solid',
                borderColor: 'gray.100',
                bg: 'gray.50',
                borderRadius: 'md',
                p: '1rem',
                whiteSpace: 'nowrap',
                fontFamily: 'mono',
                fontSize: '0.9rem',
                fontWeight: 'semibold',
                color: 'gray.400',
                boxShadow: 'md',
              }}
            >
              <Flex>MYDEVICES, INC.</Flex>
              <Flex
                sx={{
                  bg: 'blackAlpha.50',
                  height: '3px',
                  w: '100%',
                  mx: '0.3rem',
                }}
              />
              <Flex>{currencyFormatter.format(price ?? 0)}</Flex>
            </HStack>
            <HStack
              sx={{
                mt: '1rem',
                gap: '1rem',
                fontSize: '0.9rem',
                color: 'gray.500',
              }}
            >
              <Text>Powered by Stripe</Text>
              <Divider orientation="vertical" />
              <Text>
                <Link
                  href="https://stripe.com/privacy"
                  isExternal
                  color="gray.500"
                >
                  Privacy & terms
                </Link>
              </Text>
            </HStack>
          </Flex>
        </Flex>
      </Grid>
      <Flex justify="center" align="center">
        <Button
          isLoading={isPending}
          loadingText="Waiting..."
          colorScheme="green"
          variant="solid"
          onClick={onFinished}
          minW="150px"
        >
          Done
        </Button>
      </Flex>
    </>
  )
}
