import { memo } from 'react'
import { Flex, Box, Skeleton } from '@chakra-ui/react'

export const boxStyles = {
  flex: { base: '100%', md: '1' },
  pl: { base: '0', md: '10px' },
  order: { base: '2', md: '1' },
  maxW: '100%',
}

export const ItemSkeleton = memo(() => (
  <Flex
    sx={{
      bg: 'gray.100',
      borderRadius: '8px',
      alignItems: 'center',
      py: '0.5rem',
      px: { base: '0.5rem', md: '0' },
      fontSize: 'sm',
      mb: '0.5rem',
      opacity: '1',
      flexWrap: 'wrap',
    }}
  >
    <Box sx={boxStyles}>
      <Skeleton height="20px" width="100%" />
    </Box>
    <Box sx={boxStyles}>
      <Skeleton height="20px" width="100%" />
    </Box>
    <Box sx={boxStyles}>
      <Skeleton height="20px" width="100%" />
    </Box>
  </Flex>
))
