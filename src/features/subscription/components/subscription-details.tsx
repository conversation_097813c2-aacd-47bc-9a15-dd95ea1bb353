import { memo } from 'react'
import { Link as RouterLink, useParams } from 'react-router'
import { Box, Flex, Text, Heading, HStack, Icon, Link } from '@chakra-ui/react'
import { ArrowLeftIcon } from 'lucide-react'
import { AccountLoader } from '@/features/subscription/components/account-loader'
import { useSubscription } from '@/features/subscription/hooks/use-subscription'
import { NoSubscription } from '@/features/subscription/components/no-subscription'
import { SubscriptionStatus } from '@/features/subscription/components/subscription-status'
import { PaymentInfo } from '@/features/subscription/components/payment-info'
import { BillingInformation } from '@/features/subscription/components/billing-information'
import { Invoices } from '@/features/subscription/components/invoices'

export const SubscriptionDetails = memo(() => {
  const { subscriptionId = '' } = useParams()
  const { subscription, isFetching } = useSubscription(subscriptionId)

  if (isFetching) {
    return <AccountLoader />
  }

  if (!subscription) {
    return <NoSubscription />
  }

  return (
    <Flex
      sx={{
        w: '100%',
        p: { base: '1rem', md: '2rem' },
        justifyContent: 'center',
        flexWrap: 'wrap',
      }}
    >
      <Box
        key={subscription.id}
        sx={{
          w: '100%',
          maxW: '800px',
          borderRadius: '10px',
          backgroundColor: 'white',
          border: '1px solid',
          borderColor: 'gray.100',
          boxShadow: '0 4px 4px -2px #E2E8F0',
          p: '1rem',
        }}
      >
        <Flex
          sx={{
            m: '0 0 1rem 1rem',
            p: '0.25rem',
            bg: 'gray.50',
            borderRadius: 6,
          }}
        >
          <Link as={RouterLink} to="/account/subscription">
            <HStack>
              <Icon as={ArrowLeftIcon} />
              <Text>Back to Subscriptions</Text>
            </HStack>
          </Link>
        </Flex>
        <Heading as="h2" sx={{ fontSize: 'md', p: '1rem 1rem 0 1rem' }}>
          Subscription Details
        </Heading>
        <Box>
          <SubscriptionStatus subscription={subscription} hasSubscription />
          <PaymentInfo subscription={subscription} />
          <BillingInformation subscription={subscription} />
          <Invoices subscription={subscription} />
        </Box>
      </Box>
    </Flex>
  )
})
