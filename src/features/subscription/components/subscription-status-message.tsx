import { Box } from '@chakra-ui/react'
import type { Subscription } from '@/types/models/billing'

interface SubscriptionStatusMessageProps {
  name: string
  subscription?: Subscription | null
  isTrial: boolean
  hasEnded: boolean
  formatDate: (date: string, format: string) => string
}

export const SubscriptionStatusMessage = ({
  name,
  subscription,
  isTrial,
  hasEnded,
  formatDate,
}: SubscriptionStatusMessageProps) => {
  if (!subscription) {
    return <Box>Add {name} to your deployments</Box>
  }
  const formattedDate = formatDate(subscription.end_date, 'P')
  const dateText = formattedDate ? formattedDate : 'an unknown date'
  if (isTrial && hasEnded) {
    return <Box>{`Your plan expired on ${dateText}`}</Box>
  }
  if (isTrial) {
    return <Box>{`Your plan expires on ${dateText}`}</Box>
  }
  return <Box>{`Your plan renews on ${dateText}`}</Box>
}
