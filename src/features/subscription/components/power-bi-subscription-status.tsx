import { useMemo, useState } from 'react'
import { useNavigation, Form, Link as RouterLink } from 'react-router'
import { isNil } from 'ramda'
import { Box, Grid, Button, Link, Input, Icon, VStack } from '@chakra-ui/react'
import { useDebouncedEffect } from '@react-hookz/web'
import { MinusIcon, PlusIcon } from 'lucide-react'
import { formatDate } from '@/utils/date/format-date'
import { useUpdatePowerBiSubscription } from '@/features/subscription/hooks/use-billing-api'
import { useContactSubscriptions } from '@/features/subscription/hooks/use-contact-subscriptions'
import { useAddOnSubscriptions } from '@/features/subscription/hooks/use-add-on-subscriptions'
import { useSubscriptionHasEnded } from '@/features/subscription/hooks/use-subscription-has-ended'
import { useIsTrialSubscription } from '@/features/subscription/hooks/use-is-trial-subscription'
import { useSubscriptionInterval } from '@/features/subscription/hooks/use-subscription-interval'
import { useCurrency<PERSON>ormatter } from '@/features/subscription/hooks/use-currency-formatter'
import { useTieredPrice } from '@/features/subscription/hooks/use-tiered-price'
import { SubscriptionStatusMessage } from '@/features/subscription/components/subscription-status-message'
import { useOrganization } from '@/hooks/use-organization'
import { useSubscriptionIntervalText } from '@/features/subscription/hooks/use-subscription-interval-text'

type QuantityStatus = 'inactive' | 'increasing' | 'decreasing' | 'input'

interface Props {
  isDisabled: boolean
  showUpdatePlan?: boolean
}

export const PowerBiSubscriptionStatus = ({
  isDisabled,
  showUpdatePlan = true,
}: Props) => {
  const navigation = useNavigation()
  const { organizationId } = useOrganization()
  const updatePowerBiSubscription = useUpdatePowerBiSubscription()
  const { subscriptions } = useAddOnSubscriptions()
  const subscription = useMemo(
    () =>
      subscriptions.find(
        (subscription) => subscription.product.category === 'app-powerbi'
      ),
    [subscriptions]
  )
  const [loadingStatus, setLoadingStatus] = useState<QuantityStatus>('inactive')
  const quantity = subscription?.quantity
  const [input, setInput] = useState<number | undefined>(quantity)
  const currencyFormatter = useCurrencyFormatter(subscription?.id ?? '')
  const subscriptionInterval = useSubscriptionInterval(subscription?.id ?? '')
  const { subscriptions: contactSubscriptions } = useContactSubscriptions()
  const hasEnded = useSubscriptionHasEnded(subscription?.id ?? '')
  const isTrial = useIsTrialSubscription(subscription?.id ?? '')

  const { count, total } = useMemo(() => {
    const found = contactSubscriptions.find(
      (s) => s.subscription_id === subscription?.id
    )
    return {
      count: found?.count ?? 0,
      total: found?.quantity ?? 0,
    }
  }, [contactSubscriptions, subscription?.id])

  const price = useTieredPrice(subscription?.stripe_price, total)

  const subscriptionIntervalText =
    useSubscriptionIntervalText(subscriptionInterval)

  useDebouncedEffect(
    () => {
      if (
        isNil(subscription) ||
        isNil(input) ||
        loadingStatus !== 'inactive' ||
        input < count ||
        input === quantity
      ) {
        return
      }
      try {
        setLoadingStatus('input')
        updatePowerBiSubscription
          .mutateAsync({
            subscriptionId: subscription.id,
            quantity: input,
          })
          .then(() => setLoadingStatus('inactive'))
      } catch {}
    },
    [input],
    500
  )

  return (
    <Grid
      sx={{
        width: '100%',
        p: { base: '0.5rem', md: '1rem' },
        gridTemplateColumns: '1fr',
        gap: { base: '0.5rem', md: '1rem' },
        alignItems: 'center',
        m: 0,
        borderTop: '1px solid',
        borderTopColor: 'gray.100',
      }}
    >
      <Grid
        sx={{
          gridTemplateColumns: '1fr auto',
          gap: 4,
          alignItems: 'top',
        }}
      >
        <Box>
          <Box
            sx={{
              fontWeight: 'semibold',
            }}
          >
            <Link
              as={RouterLink}
              to={`/account/subscription/${subscription?.id}`}
            >
              {subscription?.product.name ?? 'PowerBi Reports (Add-On)'}
            </Link>
          </Box>
          <Box
            sx={{
              display: 'flex',
              alignItems: 'baseline',
              fontWeight: 'bold',
              fontSize: '1.25rem',
              gap: 1,
            }}
          >
            <Box>{currencyFormatter.format(price ?? 0)}</Box>
            <Box sx={{ fontSize: 'sm' }}>{subscriptionIntervalText}</Box>
          </Box>
          <Box
            sx={{
              fontSize: '0.8rem',
              lineHeight: '1.5rem',
            }}
          >
            <SubscriptionStatusMessage
              name="PowerBi Reports"
              subscription={subscription}
              isTrial={isTrial}
              hasEnded={hasEnded}
              formatDate={(date, format) => formatDate(date, format) ?? ''}
            />
          </Box>
        </Box>
        <Box>
          <Box>
            {subscription ? (
              <VStack minW="120px">
                {showUpdatePlan ? (
                  <Box>
                    <Form method="post" action="/account/subscription">
                      <Input
                        type="hidden"
                        name="return_url"
                        value={`${window.location.origin}/account/subscription`}
                      />
                      <Input
                        type="hidden"
                        name="subscription_id"
                        value={subscription.id}
                      />
                      <Button
                        type="submit"
                        aria-label="Update Plan"
                        colorScheme="primary"
                        isLoading={
                          navigation.formAction === '/account/subscription'
                        }
                      >
                        Update Plan
                      </Button>
                    </Form>
                  </Box>
                ) : null}
                <Grid templateColumns="auto 1fr auto" gap={0}>
                  <Button
                    size="sm"
                    aria-label="Decrease licenses"
                    onClick={async () => {
                      if (isNil(quantity)) return
                      if (loadingStatus !== 'inactive') return
                      try {
                        setLoadingStatus('decreasing')
                        await updatePowerBiSubscription.mutateAsync({
                          subscriptionId: subscription.id,
                          quantity: quantity - 1,
                        })
                        setInput(quantity - 1)
                      } finally {
                        setLoadingStatus('inactive')
                      }
                    }}
                    isDisabled={
                      isDisabled ||
                      (quantity ?? 0) <= 0 ||
                      quantity === count ||
                      loadingStatus === 'input' ||
                      loadingStatus === 'increasing'
                    }
                    isLoading={loadingStatus === 'decreasing'}
                    borderRightRadius={0}
                    variant="solid"
                    colorScheme="gray"
                  >
                    <Icon as={MinusIcon} />
                  </Button>
                  <Input
                    size="sm"
                    type="number"
                    value={input ?? ''}
                    onChange={(e) => {
                      const val = e.target.valueAsNumber
                      if (val < count) return
                      setInput(Number.isNaN(val) ? undefined : val)
                    }}
                    borderRadius={0}
                    textAlign="center"
                    min={count}
                    max={total}
                    isDisabled={isDisabled || loadingStatus !== 'inactive'}
                  />
                  <Button
                    size="sm"
                    aria-label="Increase licenses"
                    onClick={async () => {
                      if (isNil(quantity)) return
                      if (loadingStatus !== 'inactive') return
                      try {
                        setLoadingStatus('increasing')
                        await updatePowerBiSubscription.mutateAsync({
                          subscriptionId: subscription.id,
                          quantity: quantity + 1,
                        })
                        setInput(quantity + 1)
                      } finally {
                        setLoadingStatus('inactive')
                      }
                    }}
                    isDisabled={
                      isDisabled ||
                      loadingStatus === 'input' ||
                      loadingStatus === 'decreasing'
                    }
                    isLoading={loadingStatus === 'increasing'}
                    borderLeftRadius={0}
                    variant="solid"
                    colorScheme="gray"
                  >
                    <Icon as={PlusIcon} />
                  </Button>
                </Grid>
              </VStack>
            ) : (
              <Box minW="120px">
                <Form method="post" action="/account/create-subscription">
                  <Input
                    type="hidden"
                    name="return_url"
                    value={`${window.location.origin}/account/subscription`}
                  />
                  <Input
                    type="hidden"
                    name="organization_id"
                    value={organizationId ?? ''}
                  />
                  <Input type="hidden" name="quantity" value="1" />
                  <Input type="hidden" name="category" value="app-powerbi" />
                  <Button
                    type="submit"
                    aria-label="Add"
                    colorScheme="orange"
                    width="full"
                    isDisabled={isDisabled}
                    isLoading={
                      navigation.formAction === '/account/create-subscription'
                    }
                  >
                    Add
                  </Button>
                </Form>
              </Box>
            )}
          </Box>
          <Box>
            {total > 0 ? (
              <Box>
                <strong>
                  {count}/{total}
                </strong>{' '}
                licenses used
              </Box>
            ) : (
              <Box>
                <strong>0</strong> licenses used.
              </Box>
            )}
          </Box>
        </Box>
      </Grid>
    </Grid>
  )
}
