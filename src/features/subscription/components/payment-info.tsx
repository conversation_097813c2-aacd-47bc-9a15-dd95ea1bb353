import {
  Box,
  Flex,
  Grid,
  Skeleton,
  List,
  ListItem,
  FormLabel,
} from '@chakra-ui/react'
import { useBillingCards } from '@/features/subscription/hooks/use-billing-api'
import { Loader } from '@/features/subscription/components/loader'
import type { SubscriptionWithProduct } from '@/types/models/billing'

interface Props {
  subscription: SubscriptionWithProduct
}

export const PaymentInfo = ({ subscription }: Props) => {
  const { data: cards, isFetching } = useBillingCards(subscription.id, true)

  if (isFetching) return <Loader height="60px" />

  return (
    <>
      {(cards?.data?.length ?? 0) > 0 ? (
        <Grid
          sx={{
            width: '100%',
            p: { base: '0.5rem', md: '1rem' },
            gridTemplateColumns: '1fr',
            gap: { base: '0.5rem', md: '1rem' },
            alignItems: 'center',
            m: 0,
            borderBottom: '1px solid',
            borderBottomColor: 'gray.100',
            _last: {
              border: 0,
            },
          }}
        >
          <FormLabel
            sx={{
              flex: '1',
              m: '0',
              fontWeight: '600',
            }}
          >
            Payment Method
          </FormLabel>
          <Box>
            <List>
              {cards?.data?.map((card, index) => (
                <ListItem
                  key={index}
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    gap: 2,
                    pb: '0.5rem',
                  }}
                >
                  <Flex
                    sx={{
                      alignItems: 'center',
                      gap: 2,
                    }}
                  >
                    <Flex>
                      <Skeleton
                        startColor="gray.100"
                        endColor="gray.200"
                        w={6}
                        height={4}
                      />
                    </Flex>
                    <Flex>
                      <Box>{card.brand}</Box>
                      <Box
                        as="span"
                        sx={{
                          letterSpacing: '0.1rem',
                          px: '0.3rem',
                        }}
                      >
                        ••••
                      </Box>
                      <Box>{card.last4}</Box>
                    </Flex>
                  </Flex>
                  <Flex>
                    Expires {card.exp_month}/{card.exp_year}
                  </Flex>
                </ListItem>
              ))}
            </List>
          </Box>
        </Grid>
      ) : null}
    </>
  )
}

// export const PaymentInformation = ({
//   subscription,
//   onBack,
// }: {
//   subscription: any
//   onBack: () => void
// }) => (
//   <VStack spacing={6} align="stretch">
//     <Link
//       onClick={onBack}
//       sx={{
//         display: 'flex',
//         alignItems: 'center',
//         gap: 2,
//         color: 'blue.500',
//         cursor: 'pointer',
//         fontSize: 'lg',
//         _dark: {
//           color: 'secondary.200',
//         },
//       }}
//     >
//       <ArrowLeftIcon size={16} />
//       Back to Subscriptions
//     </Link>

//     <Heading
//       as="h1"
//       sx={{
//         fontSize: '2xl',
//         fontWeight: 'semibold',
//         _dark: {
//           color: 'white',
//         },
//       }}
//     >
//       Subscription Details
//     </Heading>

//     <Grid
//       templateColumns="1fr auto"
//       gap={6}
//       sx={{
//         p: 6,
//         border: '1px solid',
//         borderColor: 'gray.100',
//         borderRadius: 'md',
//         alignItems: 'center',
//         bg: 'white',
//         _dark: {
//           bg: 'gray.900',
//           borderColor: 'whiteAlpha.100',
//         },
//       }}
//     >
//       <GridItem>
//         <Text
//           sx={{
//             fontSize: 'lg',
//             fontWeight: 'semibold',
//             mb: 2,
//             _dark: {
//               color: 'white',
//             },
//           }}
//         >
//           {subscription.product.name}
//         </Text>
//         <Text
//           sx={{
//             display: 'flex',
//             fontWeight: 'bold',
//             gap: 1,
//             alignItems: 'baseline',
//             fontSize: '2xl',
//             mb: 2,
//             _dark: {
//               color: 'white',
//             },
//           }}
//         >
//           <Text as="span">
//             {formatUSD(subscription.stripe_price.unit_amount / 100)}
//           </Text>
//           <Text
//             as="span"
//             sx={{
//               fontSize: 'sm',
//               fontWeight: 'normal',
//               _dark: {
//                 color: 'whiteAlpha.800',
//               },
//             }}
//           >
//             every 1 month
//           </Text>
//         </Text>
//         <Text
//           sx={{
//             fontSize: 'sm',
//             color: 'gray.600',
//             _dark: {
//               color: 'whiteAlpha.800',
//             },
//           }}
//         >
//           Your plan renews on{' '}
//           {format(new Date(subscription.end_date), 'MM/dd/yyyy')}
//         </Text>
//       </GridItem>
//       <GridItem>
//         <VStack spacing={3} align="center" justify="center">
//           <Button colorScheme="blue">Update Plan</Button>
//           <Text
//             sx={{
//               fontSize: 'md',
//               fontWeight: 'semibold',
//               _dark: {
//                 color: 'white',
//               },
//             }}
//           />
//         </VStack>
//       </GridItem>
//     </Grid>

//     <VStack spacing={4} align="stretch">
//       <Heading
//         as="h2"
//         sx={{
//           fontSize: 'xl',
//           fontWeight: 'semibold',
//           _dark: {
//             color: 'white',
//           },
//         }}
//       >
//         Billing Information
//       </Heading>
//       <Grid
//         templateColumns="1fr 1fr"
//         gap={4}
//         sx={{
//           p: 4,
//           border: '1px solid',
//           borderColor: 'gray.100',
//           borderRadius: 'md',
//           bg: 'white',
//           _dark: {
//             bg: 'gray.900',
//             borderColor: 'whiteAlpha.100',
//           },
//         }}
//       >
//         <GridItem>
//           <Text
//             sx={{
//               fontWeight: 'semibold',
//               mb: 1,
//               _dark: {
//                 color: 'white',
//               },
//             }}
//           >
//             Name
//           </Text>
//           <Text
//             sx={{
//               _dark: {
//                 color: 'whiteAlpha.800',
//               },
//             }}
//           >
//             Ryan Smith
//           </Text>
//         </GridItem>
//         <GridItem>
//           <Text
//             sx={{
//               fontWeight: 'semibold',
//               mb: 1,
//               _dark: {
//                 color: 'white',
//               },
//             }}
//           >
//             Billing address
//           </Text>
//           <Text
//             sx={{
//               _dark: {
//                 color: 'whiteAlpha.800',
//               },
//             }}
//           >
//             123 Main St, Anytown, USA
//           </Text>
//         </GridItem>
//       </Grid>
//     </VStack>

//     <VStack spacing={4} align="stretch">
//       <Heading
//         as="h2"
//         sx={{
//           fontSize: 'xl',
//           fontWeight: 'semibold',
//           _dark: {
//             color: 'white',
//           },
//         }}
//       >
//         Invoice History
//       </Heading>
//       <VStack
//         spacing={0}
//         align="stretch"
//         sx={{
//           border: '1px solid',
//           borderColor: 'gray.100',
//           borderRadius: 'md',
//           bg: 'white',
//           _dark: {
//             bg: 'gray.900',
//             borderColor: 'whiteAlpha.100',
//           },
//         }}
//       >
//         {invoices.map((invoice, index) => (
//           <Grid
//             key={index}
//             templateColumns="1fr 1fr 1fr 1fr"
//             gap={4}
//             sx={{
//               p: 4,
//               borderBottom: index < 2 ? '1px solid' : 'none',
//               borderColor: 'gray.100',
//               _dark: {
//                 borderColor: 'whiteAlpha.100',
//               },
//             }}
//           >
//             <GridItem>
//               <Link
//                 sx={{
//                   color: 'blue.500',
//                   _dark: {
//                     color: 'secondary.200',
//                   },
//                 }}
//                 href="#"
//                 target="_blank"
//                 rel="noopener noreferrer"
//               >
//                 {invoice.date}
//               </Link>
//             </GridItem>
//             <GridItem>
//               <Text
//                 sx={{
//                   fontWeight: 'semibold',
//                   _dark: {
//                     color: 'white',
//                   },
//                 }}
//               >
//                 {invoice.amount}
//               </Text>
//             </GridItem>
//             <GridItem>
//               <Badge
//                 colorScheme="green"
//                 sx={{
//                   textTransform: 'uppercase',
//                 }}
//               >
//                 {invoice.status}
//               </Badge>
//             </GridItem>
//             <GridItem>
//               <Text
//                 sx={{
//                   _dark: {
//                     color: 'whiteAlpha.800',
//                   },
//                 }}
//               >
//                 {invoice.product}
//               </Text>
//             </GridItem>
//           </Grid>
//         ))}
//       </VStack>
//     </VStack>
//   </VStack>
// )
