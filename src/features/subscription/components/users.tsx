import { memo, useMemo, useState, useCallback, useEffect } from 'react'
import { Navigate } from 'react-router'
import {
  Box,
  IconButton,
  Input,
  InputGroup,
  InputLeftElement,
  InputRightElement,
} from '@chakra-ui/react'
import { XIcon, SearchIcon } from 'lucide-react'
import { Select } from '@/components/select'
import { useToast as useCustomToast } from '@/hooks/use-toast'
import { DataTable } from '@/components/data-table/data-table'
import { useUsers } from '@/hooks/use-subscription'
import { useSubscriptions } from '@/features/subscription/hooks/use-subscriptions'
import {
  useUserSubscriptions,
  useUpdateSubscriptionLicenses,
  useDeleteSubscriptionLicenses,
} from '@/features/subscription/hooks/use-billing-api'
import { useEnabledEnvFeatures } from '@/hooks/use-enabled-env-features'
import type { ColumnProps } from '@/types/table'
import type {
  SubscriptionWithProduct,
  SubscriptionUserListResponse,
} from '@/types/models/billing'
import { useAbility } from '@/hooks/use-ability'
import { FloatingActionButton } from '@/features/subscription/components/floating-action-button'

export const Users = memo(() => {
  const toast = useCustomToast()
  const { can } = useAbility()
  const [searchTerm, setSearchTerm] = useState('')

  const enabledFeatures = useEnabledEnvFeatures()

  // Replace old subscription fetching with new billing API
  const { subscriptions, isFetching: isFetchingSubscriptions } =
    useSubscriptions()

  const { data: userSubscriptions, isFetching: isFetchingUserSubscriptions } =
    useUserSubscriptions()

  // Get PowerBI subscription from the list
  const powerBiSubscription = useMemo(() => {
    return subscriptions?.find(
      (sub: SubscriptionWithProduct) => sub.product.category === 'app-powerbi'
    )
  }, [subscriptions])

  // Calculate available licenses
  const availableLicenses = useMemo(() => {
    if (!powerBiSubscription?.quantity) return 0
    const assignedCount =
      userSubscriptions?.reduce(
        (count: number, userSub: SubscriptionUserListResponse) => {
          return (
            count +
            (userSub.subscriptions.includes(powerBiSubscription.id) ? 1 : 0)
          )
        },
        0
      ) || 0
    return powerBiSubscription.quantity - assignedCount
  }, [powerBiSubscription, userSubscriptions])

  // Replace old mutation hooks
  const { mutateAsync: updateLicenses } = useUpdateSubscriptionLicenses()
  const { mutateAsync: deleteLicenses } = useDeleteSubscriptionLicenses()

  const [editMode, setEditMode] = useState(false)
  const [editedUsers, setEditedUsers] = useState<any[]>([])
  const [localAvailableLicenses, setLocalAvailableLicenses] =
    useState(availableLicenses)
  const [isSaving, setIsSaving] = useState(false)

  // Update local available licenses when the prop changes
  useEffect(() => {
    setLocalAvailableLicenses(availableLicenses)
  }, [availableLicenses])

  const allUsers = useUsers() ?? []

  // Merge users with their license data using the new billing API structure
  const mergedUsers = useMemo(() => {
    // Create a map of user_id to their subscription IDs
    const userLicensesMap: Record<string, string[]> = {}
    for (const userSub of userSubscriptions ?? []) {
      userLicensesMap[userSub.user_id] = userSub.subscriptions
    }

    return allUsers.map((user: any) => ({
      ...user,
      licenses: userLicensesMap[user.id] || [], // array of subscription IDs
    }))
  }, [allUsers, userSubscriptions])

  // For editing, keep a local copy
  const records = editMode
    ? editedUsers.length > 0
      ? editedUsers
      : mergedUsers
    : mergedUsers

  // Create license options from all available subscriptions
  const licenseOptions = useMemo(() => {
    return (
      subscriptions?.map((subscription: SubscriptionWithProduct) => ({
        label: subscription.product.name || subscription.product.category,
        value: subscription.id,
      })) || []
    )
  }, [subscriptions])

  // Handle license change in edit mode
  const onLicenseChange = useCallback(
    (userId: string, newLicenses: string[]) => {
      // Count how many new licenses are being added for PowerBI specifically
      const currentUser = mergedUsers.find(({ id }) => id === userId)
      const currentLicenses = currentUser?.licenses || []

      // Filter to only PowerBI licenses for counting
      const currentPowerBiLicenses = currentLicenses.filter(
        (id: string) => id === powerBiSubscription?.id
      )
      const newPowerBiLicenses = newLicenses.filter(
        (id: string) => id === powerBiSubscription?.id
      )

      const addedPowerBiCount =
        newPowerBiLicenses.length - currentPowerBiLicenses.length
      const newAvailableCount = localAvailableLicenses - addedPowerBiCount

      // Check if we're trying to add more PowerBI licenses than available
      if (newAvailableCount < 0) {
        toast({
          status: 'error',
          msg: `Cannot assign more PowerBI licenses. Only ${localAvailableLicenses} available.`,
        })
        return
      }

      setLocalAvailableLicenses(newAvailableCount)
      setEditedUsers((prev) => {
        const updated = prev.length > 0 ? [...prev] : [...mergedUsers]
        const idx = updated.findIndex(({ id }) => id === userId)
        if (idx !== -1) {
          updated[idx] = { ...updated[idx], licenses: newLicenses }
        }
        return updated
      })
    },
    [mergedUsers, localAvailableLicenses, powerBiSubscription?.id, toast]
  )

  // Reset local available licenses when edit mode is disabled
  const onCancelEdit = useCallback(() => {
    setEditMode(false)
    setEditedUsers([])
    setLocalAvailableLicenses(availableLicenses)
  }, [availableLicenses])

  // Save changes using new billing API
  const onSave = async () => {
    try {
      setIsSaving(true)

      // Find users whose licenses have changed
      const changedUsers = editedUsers.filter((editedUser) => {
        const originalUser = mergedUsers.find(({ id }) => id === editedUser.id)
        if (!originalUser) return false

        const originalLicenses = originalUser.licenses || []
        const newLicenses = editedUser.licenses || []

        // Check if licenses have changed
        return (
          originalLicenses.length !== newLicenses.length ||
          !originalLicenses.every((l: string) => newLicenses.includes(l))
        )
      })

      // Group users by their new license state
      const usersToUpdate = changedUsers.filter(
        ({ licenses }) => licenses.length > 0
      )
      const usersToRemove = changedUsers.filter(
        ({ licenses }) => licenses.length === 0
      )

      // Remove licenses first
      if (usersToRemove.length > 0) {
        for (const user of usersToRemove) {
          // For each subscription the user currently has, remove it
          const originalUser = mergedUsers.find(({ id }) => id === user.id)
          const currentLicenses = originalUser?.licenses || []
          for (const subscriptionId of currentLicenses) {
            await deleteLicenses({
              subscription_id: subscriptionId,
              user_ids: [user.id],
            })
          }
        }
      }

      // Then update licenses
      if (usersToUpdate.length > 0) {
        for (const user of usersToUpdate) {
          for (const subscriptionId of user.licenses) {
            await updateLicenses({
              subscription_id: subscriptionId,
              user_ids: [user.id],
            })
          }
        }
      }

      toast({
        status: 'success',
        msg: 'Licenses updated successfully.',
      })

      setEditMode(false)
      setEditedUsers([])
      setLocalAvailableLicenses(availableLicenses)
    } catch (error) {
      console.error(error)
      toast({
        status: 'error',
        msg: 'Failed to update licenses.',
      })
    } finally {
      setIsSaving(false)
    }
  }

  const canViewPage = useMemo(() => {
    return (
      can(['edit', 'organizations']) &&
      enabledFeatures?.billing_powerbi_console_subscription_feature_enabled
    )
  }, [can, enabledFeatures])

  // Filter records based on search term
  const filteredRecords = useMemo(() => {
    if (!searchTerm) return records
    const searchLower = searchTerm.toLowerCase()
    return records.filter(
      (user) =>
        user.firstName?.toLowerCase().includes(searchLower) ||
        user.lastName?.toLowerCase().includes(searchLower) ||
        user.email?.toLowerCase().includes(searchLower) ||
        user.attributes?.phoneNumber?.[0]?.toLowerCase().includes(searchLower)
    )
  }, [records, searchTerm])

  const columns: ColumnProps[] = [
    {
      name: 'Name',
      id: 'name',
      cell: (row) => <Box>{`${row.firstName} ${row.lastName}`}</Box>,
    },
    {
      name: 'Email',
      id: 'email',
      cell: (row) => <Box>{row.email}</Box>,
    },
    {
      name: 'Phone',
      id: 'phone',
      cell: (row) => <Box>{row.attributes?.phoneNumber?.[0] || '-'}</Box>,
    },
    {
      name: `Licenses (${localAvailableLicenses} PowerBI available)`,
      id: 'licenses',
      w: '350px',
      cell: (row) =>
        editMode ? (
          <Box>
            <Select
              options={licenseOptions}
              isMulti
              isClearable
              value={Array.isArray(row.licenses) ? row.licenses : []}
              onChange={(val: any) => onLicenseChange(row.id, val)}
              isDisabled={
                localAvailableLicenses <= 0 && row.licenses?.length === 0
              }
            />
          </Box>
        ) : Array.isArray(row.licenses) && row.licenses.length > 0 ? (
          <Box>
            {row.licenses.map((licenseId: string) => {
              const subscription = subscriptions?.find(
                ({ id }: SubscriptionWithProduct) => id === licenseId
              )
              return (
                <Box
                  as="span"
                  key={licenseId}
                  mr={1}
                  px={2}
                  py={1}
                  bg="gray.100"
                  borderRadius="md"
                >
                  {subscription?.product.name ||
                    subscription?.product.category ||
                    licenseId}
                </Box>
              )
            })}
          </Box>
        ) : (
          <Box>-</Box>
        ),
    },
  ]

  const isPending = isFetchingSubscriptions || isFetchingUserSubscriptions

  if (!canViewPage) {
    return <Navigate to="/manage/customers" replace />
  }

  return (
    <Box py={0} position="relative">
      {!(powerBiSubscription || isPending) ? (
        <Box px={4}>No subscription exist.</Box>
      ) : (
        <>
          <Box p={1}>
            <InputGroup>
              <InputLeftElement pointerEvents="none">
                <SearchIcon size={16} />
              </InputLeftElement>
              <Input
                placeholder="Search users by name, email or phone number..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                borderRadius="none"
                border="none"
                variant="solid"
                bg="gray.50"
              />
              {searchTerm && (
                <InputRightElement>
                  <IconButton
                    aria-label="Clear search"
                    icon={<XIcon size={16} />}
                    variant="ghost"
                    size="sm"
                    onClick={() => setSearchTerm('')}
                  />
                </InputRightElement>
              )}
            </InputGroup>
          </Box>
          <DataTable
            tableName="subscription-users"
            data={{ rows: filteredRecords, count: filteredRecords.length }}
            columns={columns}
            skipPagination
          />
          <FloatingActionButton
            editMode={editMode}
            isSaving={isSaving}
            onSave={onSave}
            onCancelEdit={onCancelEdit}
            setEditMode={setEditMode}
            setEditedUsers={setEditedUsers}
          />
        </>
      )}
    </Box>
  )
})
