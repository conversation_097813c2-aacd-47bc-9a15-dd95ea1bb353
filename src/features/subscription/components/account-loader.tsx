import { memo } from 'react'
import { Flex, Box, Grid, Skeleton } from '@chakra-ui/react'

const Item = memo(() => (
  <Grid
    sx={{
      p: { base: '0.5rem', md: '1rem' },
      width: '100%',
      gridTemplateColumns: {
        base: '1fr',
        lg: '200px 1fr',
      },
      gap: { base: '0.5rem', md: '1rem' },
      alignItems: 'center',
      m: 0,
      borderBottom: '1px solid',
      borderBottomColor: 'gray.100',
    }}
  >
    <Skeleton
      sx={{
        flex: '1',
        m: '0',
        fontWeight: '600',
        height: '24px',
      }}
    />
    <Skeleton sx={{ height: '24px' }} />
  </Grid>
))

export const AccountLoader = memo(() => (
  <Flex
    sx={{
      w: '100%',
      p: { base: '1rem', md: '2rem' },
      justifyContent: 'center',
      flexWrap: 'wrap',
    }}
  >
    <Box
      sx={{
        w: '100%',
        maxW: '800px',
        borderRadius: '10px',
        backgroundColor: 'white',
        border: '1px solid',
        borderColor: 'gray.100',
        boxShadow: '0 4px 4px -2px #E2E8F0',
        p: '1rem',
      }}
    >
      <Item />
      <Item />
      <Item />
      <Item />
      <Item />
      <Item />
    </Box>
  </Flex>
))
