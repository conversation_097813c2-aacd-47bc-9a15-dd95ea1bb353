import { memo } from 'react'
import { Box, Flex } from '@chakra-ui/react'
import { isNotNil } from 'ramda'
import { AccountLoader } from '@/features/subscription/components/account-loader'
import { useLocationAdminSubscriptions } from '@/features/subscription/hooks/use-location-admin-subscriptions'
import { useAppSubscription } from '@/features/subscription/hooks/use-app-subscription'
import { useSubscriptionEnabled } from '@/features/subscription/hooks/use-subscription-enabled'
import { NoSubscription } from '@/features/subscription/components/no-subscription'
import { SubscriptionStatus } from '@/features/subscription/components/subscription-status'
import { PaymentInfo } from '@/features/subscription/components/payment-info'
import { BillingInformation } from '@/features/subscription/components/billing-information'
import { Invoices } from '@/features/subscription/components/invoices'
import { NonAdminSubscription } from '@/features/subscription/components/non-admin-subscription'

export const SubscriptionForm = memo(() => {
  const isEnabled = useSubscriptionEnabled()
  const adminSubscriptions = useLocationAdminSubscriptions()
  const { subscription, isFetching } = useAppSubscription()
  const hasSubscription = isNotNil(subscription)

  const hasAdminSubscription =
    isNotNil(adminSubscriptions) && adminSubscriptions.length > 0 && isEnabled

  if (isFetching) return <AccountLoader />

  if (!(hasSubscription || hasAdminSubscription)) {
    return <NoSubscription />
  }

  return (
    <Flex
      sx={{
        w: '100%',
        p: { base: '1rem', md: '2rem' },
        justifyContent: 'center',
        flexWrap: 'wrap',
      }}
    >
      <Box
        sx={{
          w: '100%',
          maxW: '800px',
          borderRadius: '10px',
          backgroundColor: 'white',
          border: '1px solid',
          borderColor: 'gray.100',
          boxShadow: '0 4px 4px -2px #E2E8F0',
          p: '1rem',
        }}
      >
        {hasSubscription ? (
          <Box>
            <SubscriptionStatus
              subscription={subscription}
              hasSubscription={hasSubscription}
            />
            <PaymentInfo subscription={subscription} />
            <BillingInformation subscription={subscription} />
            <Invoices subscription={subscription} />
          </Box>
        ) : null}

        {isEnabled && adminSubscriptions?.length > 0 && !hasSubscription ? (
          <>
            {adminSubscriptions.map((subscription) => (
              <NonAdminSubscription
                key={subscription.location_id}
                subscription={subscription}
              />
            ))}
          </>
        ) : null}
      </Box>
    </Flex>
  )
})
