import type { PropsWithChildren } from 'react'
import { NavLink } from 'react-router'
import { Link, type SystemStyleObject } from '@chakra-ui/react'

type Props = PropsWithChildren<{
  href: string
  sx?: SystemStyleObject
  isExternal?: boolean
  end?: boolean
}>

export const ActiveLink = ({ href, sx, children, isExternal, end }: Props) => {
  return (
    <Link
      as={NavLink}
      to={href}
      sx={sx}
      end={end}
      isExternal={isExternal}
      preventScrollReset
    >
      {children}
    </Link>
  )
}
