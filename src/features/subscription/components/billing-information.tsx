import { useMemo } from 'react'
import { Box, Grid, FormLabel } from '@chakra-ui/react'
import { isNotNil } from 'ramda'
import { useBillingCustomer } from '@/features/subscription/hooks/use-billing-api'
import { Loader } from '@/features/subscription/components/loader'
import type { SubscriptionWithProduct } from '@/types/models/billing'

interface Props {
  subscription: SubscriptionWithProduct
}

export const BillingInformation = ({ subscription }: Props) => {
  const { data: customer, isFetching } = useBillingCustomer(
    subscription.id,
    true
  )
  const address = useMemo(() => {
    if (!customer?.address) return ''
    return Object.values(customer.address).filter(isNotNil).join(' ')
  }, [customer])

  if (isFetching) return <Loader height="50px" />

  return (
    <Grid
      sx={{
        width: '100%',
        p: { base: '0.5rem', md: '1rem' },
        gridTemplateColumns: '1fr',
        gap: { base: '0.5rem', md: '1rem' },
        alignItems: 'center',
        m: 0,
        borderBottom: '1px solid',
        borderBottomColor: 'gray.100',
        _last: {
          border: 0,
        },
      }}
    >
      <FormLabel
        sx={{
          flex: '1',
          m: '0',
          fontWeight: '600',
        }}
      >
        Billing Information
      </FormLabel>
      <Box>
        <Grid
          templateColumns="repeat(2, minmax(0, 120px))"
          sx={{
            gap: 2,
            width: 'reset',
            justifyContent: 'flex-start',
            pb: '0.9rem',
          }}
        >
          <Box>Name</Box>
          <Box>{customer?.name}</Box>
          <Box>Billing address</Box>
          <Box>{address}</Box>
        </Grid>
      </Box>
    </Grid>
  )
}
