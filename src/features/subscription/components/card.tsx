import { Box, Flex } from '@chakra-ui/react'
import { useBillingCard } from '@/features/subscription/hooks/use-billing-api'

interface Props {
  subscriptionId: string
  id?: string
}

export const Card = ({ subscriptionId, id }: Props) => {
  const { data: card } = useBillingCard(subscriptionId, id)

  return (
    <Box>
      <Flex>
        <Box>{card?.brand}</Box>
        <Box
          as="span"
          sx={{
            letterSpacing: '0.1rem',
            px: '0.3rem',
          }}
        >
          ••••
        </Box>
        <Box>{card?.last4}</Box>
      </Flex>
      <Flex>
        Expires {card?.exp_month}/{card?.exp_year}
      </Flex>
    </Box>
  )
}
