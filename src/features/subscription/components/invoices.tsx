import { fromUnixTime, format } from 'date-fns'
import {
  Box,
  Flex,
  Grid,
  Badge,
  Link,
  List,
  ListItem,
  FormLabel,
} from '@chakra-ui/react'
import {
  useBillingStatus,
  useBillingInvoices,
} from '@/features/subscription/hooks/use-billing-api'
import { useCurrencyFormatter } from '@/features/subscription/hooks/use-currency-formatter'
import { Loader } from '@/features/subscription/components/loader'
import type { InvoiceStatus } from '@/types/models/billing'
import type { SubscriptionWithProduct } from '@/types/models/billing'

interface Props {
  subscription: SubscriptionWithProduct
}

const colors: Record<InvoiceStatus, string> = {
  open: 'teal',
  paid: 'green',
  void: 'gray',
  draft: 'blue',
  uncollectible: 'red',
}

export const Invoices = ({ subscription }: Props) => {
  const currencyFormatter = useCurrencyFormatter(subscription.id)
  const { data: invoices, isFetching } = useBillingInvoices(
    subscription.id,
    true
  )
  const { data: status } = useBillingStatus(subscription.id, true)

  if (isFetching) {
    return (
      <>
        <Loader height="20px" />
        <Loader height="20px" />
        <Loader height="20px" />
      </>
    )
  }

  return (
    <>
      {(invoices?.data?.length ?? 0) > 0 ? (
        <Grid
          sx={{
            width: '100%',
            p: { base: '0.5rem', md: '1rem' },
            gridTemplateColumns: '1fr',
            gap: { base: '0.5rem', md: '1rem' },
            alignItems: 'center',
            m: 0,
            borderBottom: '1px solid',
            borderBottomColor: 'gray.100',
            _last: {
              border: 0,
            },
          }}
        >
          <FormLabel
            sx={{
              flex: '1',
              m: '0',
              fontWeight: '600',
            }}
          >
            Invoice History
          </FormLabel>
          <Box>
            <List>
              {invoices?.data?.map((invoice, index) => (
                <ListItem
                  key={index}
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    gap: 2,
                  }}
                >
                  <Flex>
                    <Link href={invoice.invoice_pdf ?? ''} isExternal>
                      {format(fromUnixTime(invoice.created), 'PP')}
                    </Link>
                  </Flex>
                  <Flex>
                    {currencyFormatter.format(invoice.amount_paid / 100)}
                  </Flex>
                  <Badge
                    colorScheme={colors[invoice.status] ?? 'blue'}
                    sx={{
                      textTransform: 'uppercase',
                    }}
                  >
                    {invoice.status}
                  </Badge>
                  <Flex>{status?.product?.name}</Flex>
                </ListItem>
              ))}
            </List>
          </Box>
        </Grid>
      ) : null}
    </>
  )
}
