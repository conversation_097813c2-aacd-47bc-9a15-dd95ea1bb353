import { memo, Suspense } from 'react'
import { Box } from '@chakra-ui/react'
import { AccountLoader } from '@/features/subscription/components/account-loader'
import { SubscriptionList } from '@/features/subscription/components/subscription-list'
import { SubscriptionsTabs } from '@/features/subscription/components/subscriptions-tabs'

export const Subscription = memo(() => (
  <Suspense fallback={<AccountLoader />}>
    <Box
      sx={{
        w: '100%',
        p: { base: '1rem', md: '1rem 2rem 2rem' },
      }}
    >
      <SubscriptionsTabs />
      <SubscriptionList />
    </Box>
  </Suspense>
))
