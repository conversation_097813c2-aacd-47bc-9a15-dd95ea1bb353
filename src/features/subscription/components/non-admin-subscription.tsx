import { Box, Grid } from '@chakra-ui/react'
import type { LocationSubscription } from '@/types/models/billing'

interface Props {
  subscription: LocationSubscription
}

export const NonAdminSubscription = ({ subscription }: Props) => (
  <Grid
    sx={{
      width: '100%',
      p: { base: '0.5rem', md: '1rem' },
      gridTemplateColumns: '1fr',
      gap: { base: '0.5rem', md: '1rem' },
      alignItems: 'center',
      m: 0,
      borderBottom: '1px solid',
      borderBottomColor: 'gray.100',
      _last: {
        border: 0,
      },
    }}
  >
    <Grid
      sx={{
        gridTemplateColumns: '1fr auto',
        gap: 4,
        alignItems: 'top',
        pb: '1rem',
      }}
    >
      <Box>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'baseline',
            fontWeight: 'bold',
            fontSize: '1.25rem',
            gap: 1,
          }}
        >
          <Box>{subscription.subscription_name}</Box>
        </Box>
      </Box>
      <Box
        sx={{
          fontSize: '0.8rem',
          lineHeight: '1.5rem',
          pb: '1rem',
        }}
      >
        Subscription Admin: {subscription.email}
      </Box>
    </Grid>
  </Grid>
)
