import { useRef } from 'react'
import {
  useDisclosure,
  Button,
  Flex,
  HStack,
  AlertDialog,
  AlertDialogBody,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogContent,
  AlertDialogOverlay,
} from '@chakra-ui/react'

interface Props {
  successLabel: string
  cancelAction?: () => void
  submitAction?: () => void
  deleteAction?: () => void
  deleteHeader?: string
  deleteText?: string
  rightMargin?: string
  isDisabled?: boolean
  isLoading?: boolean
  canDelete?: boolean
}

export function ActionFooter({
  successLabel,
  submitAction,
  cancelAction,
  deleteAction,
  deleteHeader,
  deleteText,
  rightMargin,
  isDisabled,
  isLoading,
  canDelete,
}: Props) {
  const cancelRef = useRef<any>()
  const { isOpen, onOpen, onClose } = useDisclosure()

  return (
    <Flex
      sx={{
        alignItems: { base: 'flex-start', md: 'center' },
        px: '5px',
        py: { base: '5px', md: 0 },
        position: 'fixed',
        bottom: { base: '0', md: '16px' },
        right: {
          base: '0',
          md: rightMargin ? rightMargin : '16px',
        },
        w: { base: '100%', md: 'calc(100% - 32px)' },
        height: { base: '100px', md: '50px' },
        zIndex: '1',
        borderRadius: { base: '8px 8px 0 0', md: '8px' },
        bg: 'white',
        boxShadow: {
          base: '0 1px 2px rgba(0,0,0,.07), 0 2px 4px rgba(0,0,0,.07), 0 0 32px rgba(0,0,0,.2)',
          md: '0 1px 2px rgba(0,0,0,.07), 0 2px 4px rgba(0,0,0,.07), 0 0 32px rgba(0,0,0,.07)',
        },
      }}
    >
      <HStack>
        <Button onClick={cancelAction} variant="white" isDisabled={isLoading}>
          Cancel
        </Button>

        {deleteAction ? (
          <>
            <Button
              type="button"
              colorScheme="red"
              variant="ghost"
              aria-label="Delete"
              onClick={onOpen}
              isDisabled={canDelete ? false : isLoading || isDisabled}
            >
              Delete
            </Button>
            <AlertDialog
              isOpen={isOpen}
              leastDestructiveRef={cancelRef}
              onClose={onClose}
            >
              <AlertDialogOverlay>
                <AlertDialogContent>
                  <AlertDialogHeader fontSize="lg" fontWeight="bold">
                    {deleteHeader ? deleteHeader : 'Delete'}
                  </AlertDialogHeader>
                  <AlertDialogBody>
                    {deleteText
                      ? deleteText
                      : 'Are you sure you wish to delete? This cannot be undone.'}
                  </AlertDialogBody>
                  <AlertDialogFooter
                    display="flex"
                    justifyContent="space-between"
                  >
                    <Button
                      size="sm"
                      variant="ghost"
                      ref={cancelRef}
                      onClick={onClose}
                      isDisabled={isLoading}
                    >
                      Cancel
                    </Button>
                    <Button
                      size="sm"
                      colorScheme="red"
                      onClick={deleteAction}
                      isLoading={isLoading}
                    >
                      Confirm Delete
                    </Button>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialogOverlay>
            </AlertDialog>
          </>
        ) : null}
      </HStack>

      <Button
        isLoading={isLoading}
        loadingText="Submitting"
        isDisabled={isDisabled}
        variant="success"
        sx={{ ml: 'auto' }}
        type={submitAction ? 'button' : 'submit'}
        onClick={submitAction}
      >
        {successLabel}
      </Button>
    </Flex>
  )
}
