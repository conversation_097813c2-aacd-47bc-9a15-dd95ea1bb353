import { memo, useState } from 'react'
import {
  Flex,
  Input,
  InputGroup,
  InputLeftElement,
  Icon,
  Spinner,
} from '@chakra-ui/react'
import { SearchIcon } from 'lucide-react'
import { useDebouncedEffect } from '@react-hookz/web'
import { useSearchQuery } from '@/hooks/use-search-query'

export const UserListHeaderSearch = memo(() => {
  const [, setQuery] = useSearchQuery()
  const [input, setInput] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  useDebouncedEffect(
    () => {
      setQuery(input)
      setIsLoading(false)
    },
    [input],
    300
  )

  return (
    <Flex sx={{ position: 'relative', mt: '1rem' }}>
      <InputGroup>
        <InputLeftElement sx={{ height: '2rem' }} pointerEvents="none">
          {isLoading ? (
            <Spinner color="primary.500" size="sm" />
          ) : (
            <Icon as={SearchIcon} color="gray.500" />
          )}
        </InputLeftElement>
        <Input
          size="sm"
          placeholder="Search Name, Email or Phone Number"
          sx={{
            border: '0',
            bg: 'gray.100',
            borderRadius: 0,
            _focus: { bg: 'gray.200' },
          }}
          value={input}
          onChange={(e) => {
            setIsLoading(true)
            setInput(e.target.value)
          }}
        />
      </InputGroup>
    </Flex>
  )
})
