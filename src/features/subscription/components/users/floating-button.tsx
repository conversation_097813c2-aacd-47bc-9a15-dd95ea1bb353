import {
  Button,
  Box,
  Icon,
  type SystemStyleObject,
  type ComponentWithAs,
  type IconProps,
} from '@chakra-ui/react'

interface Props {
  size?: string
  action?: () => void
  icon?: ComponentWithAs<'svg', IconProps> | any
  sx?: SystemStyleObject
  colorScheme?: string
  iconColor?: string
  rightPosition?: string
}

export const FloatingButton = ({
  size = '48px',
  action,
  icon,
  sx,
  colorScheme = 'primary',
  iconColor = 'white',
  rightPosition,
}: Props) => (
  <Box
    sx={{
      position: 'fixed',
      bottom: { base: '30px', md: '40px' },
      right: { base: '20px', md: rightPosition ?? '40px' },
      zIndex: '1002',
      borderRadius: 'full',
      boxShadow:
        '0 20px 25px -5px rgba(0,0,0,.2), 0 10px 10px -5px rgba(0,0,0,.02)',
      ...sx,
    }}
  >
    <Button
      colorScheme={colorScheme}
      sx={{
        w: '60px',
        h: '60px',
        p: '0',
        minW: '0',
        borderRadius: 'full',
        position: 'relative',
      }}
      _focus={{}}
      _hover={{}}
      _active={{}}
      onClick={action}
    >
      <Icon as={icon} boxSize={size} sx={{ color: iconColor }} />
    </Button>
  </Box>
)
