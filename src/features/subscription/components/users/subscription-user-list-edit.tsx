import { useMemo, useState } from 'react'
import { Box, Flex } from '@chakra-ui/react'
import { Select } from '@/components/select'
import { useContactSubscriptions } from '@/features/subscription/hooks/use-contact-subscriptions'
import { useFullName } from '@/features/subscription/hooks/use-full-name'
import {
  useUpdateSubscriptionLicenses,
  useDeleteSubscriptionLicenses,
} from '@/features/subscription/hooks/use-billing-api'

interface Props {
  contact: any
}

export const SubscriptionUserListEdit = ({ contact }: Props) => {
  const fullName = useFullName(contact)
  const [isLoading, setIsLoading] = useState(false)
  const updateLicenses = useUpdateSubscriptionLicenses()
  const deleteLicenses = useDeleteSubscriptionLicenses()
  const { subscriptions, subscriptionUsers } = useContactSubscriptions(
    contact?.id
  )
  const options = useMemo(() => {
    return subscriptions.map((subscription) => ({
      value: subscription.subscription_id,
      label: subscription.name,
      isDisabled:
        subscription.count >= subscription.quantity && !subscription.enabled,
    }))
  }, [subscriptions])
  const value = useMemo(() => {
    const enabledSubscriptions = subscriptions.filter((subscription) => {
      const alreadyHasLicense = subscriptionUsers.some((users) =>
        users.some(
          (user) =>
            user.user_id === contact.id &&
            user.subscription_id === subscription.subscription_id
        )
      )
      return subscription.enabled && alreadyHasLicense
    })
    return enabledSubscriptions.map((subscription) => ({
      value: subscription.subscription_id,
      label: subscription.name,
    }))
  }, [subscriptionUsers, subscriptions, contact.id])

  return (
    <Flex
      sx={{
        pos: 'relative',
        alignItems: 'center',
        flexWrap: 'wrap',
        py: '0.5rem',
        mx: { base: '0.5rem', md: '1rem' },
        borderBottom: '1px solid',
        borderBottomColor: 'gray.100',
        _last: {
          borderBottom: '0',
        },
      }}
    >
      <Flex sx={{ flex: { base: '100%', md: '1' } }}>{fullName}</Flex>
      <Flex
        sx={{
          flex: { base: '100%', md: '1' },
          w: '100%',
          overflow: 'hidden',
          whiteSpace: 'nowrap',
          textOverflow: 'ellipsis',
          display: 'block',
          minW: 0,
          pr: '10px',
        }}
      >
        {contact.email}
      </Flex>
      <Flex
        sx={{
          flex: { base: '100%', md: '1' },
        }}
      >
        <Box w="100%">
          <Select
            size="md"
            placeholder="Select Licenses"
            data={options}
            value={value}
            isLoading={isLoading}
            isClearable={false}
            isMulti
            isOptionDisabled={({ isDisabled }: { isDisabled: boolean }) =>
              isDisabled
            }
            onChange={async (selection: { value: string }[]) => {
              const licensesToAdd = selection.filter((option) => {
                return !value.some((val) => val.value === option.value)
              })
              const licensesToRemove = value.filter((option) => {
                return !selection.some((val) => val.value === option.value)
              })

              try {
                setIsLoading(true)
                await Promise.all(
                  licensesToAdd.map((license) =>
                    updateLicenses.mutateAsync({
                      subscription_id: license.value,
                      user_ids: [contact.id],
                    })
                  )
                )
                await Promise.all(
                  licensesToRemove.map((license) =>
                    deleteLicenses.mutateAsync({
                      subscription_id: license.value,
                      user_ids: [contact.id],
                    })
                  )
                )
              } finally {
                setIsLoading(false)
              }
            }}
          />
        </Box>
      </Flex>
    </Flex>
  )
}
