import { memo } from 'react'
import { Box, Flex, Heading } from '@chakra-ui/react'
import { isNil, isNotNil } from 'ramda'
import { useEnabledEnvFeatures } from '@/hooks/use-enabled-env-features'
import { AccountLoader } from '@/features/subscription/components/account-loader'
import { useAppSubscription } from '@/features/subscription/hooks/use-app-subscription'
import { useAppSubscriptions } from '@/features/subscription/hooks/use-app-subscriptions'
import { useSubscriptionEnabled } from '@/features/subscription/hooks/use-subscription-enabled'
import { useIsTrialSubscription } from '@/features/subscription/hooks/use-is-trial-subscription'
import { useLocationAdminSubscriptions } from '@/features/subscription/hooks/use-location-admin-subscriptions'
import { NoSubscription } from '@/features/subscription/components/no-subscription'
import { SubscriptionStatus } from '@/features/subscription/components/subscription-status'
import { PowerBiSubscriptionStatus } from '@/features/subscription/components/power-bi-subscription-status'
import { TriggersSubscriptionStatus } from '@/features/subscription/components/triggers-subscription-status'
import { NonAdminSubscription } from '@/features/subscription/components/non-admin-subscription'

export const SubscriptionList = memo(() => {
  const isEnabled = useSubscriptionEnabled()
  const enabledFeatures = useEnabledEnvFeatures()
  const { subscription } = useAppSubscription()
  const isTrial = useIsTrialSubscription(subscription?.id ?? '')
  const { subscriptions, isFetching } = useAppSubscriptions()
  const adminSubscriptions = useLocationAdminSubscriptions()

  const hasAdminSubscriptions =
    isNotNil(adminSubscriptions) && adminSubscriptions.length > 0

  if (isFetching || isNil(subscriptions)) {
    return <AccountLoader />
  }
  if (!(subscriptions.length > 0 || hasAdminSubscriptions)) {
    return <NoSubscription />
  }

  return (
    <Flex
      sx={{
        w: '100%',
        p: { base: '1rem', md: '2rem' },
        justifyContent: 'center',
        flexWrap: 'wrap',
      }}
    >
      <Box
        sx={{
          w: '100%',
          maxW: '800px',
          borderRadius: '10px',
          backgroundColor: 'white',
          border: '1px solid',
          borderColor: 'gray.100',
          boxShadow: '0 4px 4px -2px #E2E8F0',
          p: '1rem',
        }}
      >
        <Heading as="h2" sx={{ fontSize: 'md', p: '1rem 1rem 0 1rem' }}>
          Subscriptions
        </Heading>

        {subscriptions.map((subscription) => (
          <SubscriptionStatus
            key={subscription.id}
            subscription={subscription}
            hasSubscription={hasAdminSubscriptions}
          />
        ))}

        {enabledFeatures.billing_powerbi_subscription_feature_enabled ? (
          <PowerBiSubscriptionStatus isDisabled={isTrial} />
        ) : null}

        {enabledFeatures.billing_triggers_subscription_feature_enabled ? (
          <TriggersSubscriptionStatus isDisabled={isTrial} />
        ) : null}

        {isEnabled &&
        adminSubscriptions?.length > 0 &&
        subscriptions.length === 0 ? (
          <>
            {adminSubscriptions.map((subscription) => (
              <NonAdminSubscription
                key={subscription.location_id}
                subscription={subscription}
              />
            ))}
          </>
        ) : null}
      </Box>
    </Flex>
  )
})
