import { useMemo } from 'react'
import { useNavigation, Form, Link as RouterLink } from 'react-router'
import { Box, Grid, Button, Link, Input } from '@chakra-ui/react'
import { formatDate } from '@/utils/date/format-date'
import { useAddOnSubscriptions } from '@/features/subscription/hooks/use-add-on-subscriptions'
import { useSubscriptionHasEnded } from '@/features/subscription/hooks/use-subscription-has-ended'
import { useIsTrialSubscription } from '@/features/subscription/hooks/use-is-trial-subscription'
import { useSubscriptionInterval } from '@/features/subscription/hooks/use-subscription-interval'
import { useCurrencyFormatter } from '@/features/subscription/hooks/use-currency-formatter'
import { useTieredPrice } from '@/features/subscription/hooks/use-tiered-price'
import { SubscriptionStatusMessage } from '@/features/subscription/components/subscription-status-message'

interface Props {
  isDisabled: boolean
}

export const TriggersSubscriptionStatus = ({ isDisabled }: Props) => {
  const navigation = useNavigation()
  const { subscriptions } = useAddOnSubscriptions()

  const subscription = useMemo(
    () =>
      subscriptions.find(
        (subscription) => subscription.product.category === 'app-triggers'
      ),
    [subscriptions]
  )

  const currencyFormatter = useCurrencyFormatter(subscription?.id ?? '')
  const subscriptionInterval = useSubscriptionInterval(subscription?.id ?? '')
  const hasEnded = useSubscriptionHasEnded(subscription?.id ?? '')
  const isTrial = useIsTrialSubscription(subscription?.id ?? '')
  const price = useTieredPrice(subscription?.stripe_price, 1)

  return (
    <Grid
      sx={{
        width: '100%',
        p: { base: '0.5rem', md: '1rem' },
        gridTemplateColumns: '1fr',
        gap: { base: '0.5rem', md: '1rem' },
        alignItems: 'center',
        m: 0,
        borderTop: '1px solid',
        borderTopColor: 'gray.100',
      }}
    >
      <Grid
        sx={{
          gridTemplateColumns: '1fr auto',
          gap: 4,
          alignItems: 'top',
        }}
      >
        <Box>
          <Box
            sx={{
              fontWeight: 'semibold',
            }}
          >
            <Link
              as={RouterLink}
              to={`/account/subscription/${subscription?.id}`}
            >
              {subscription?.product.name ?? 'Triggers (Add-On)'}
            </Link>
          </Box>
          <Box
            sx={{
              display: 'flex',
              alignItems: 'baseline',
              fontWeight: 'bold',
              fontSize: '1.25rem',
              gap: 1,
            }}
          >
            <Box>{currencyFormatter.format(price ?? 0)}</Box>
            <Box sx={{ fontSize: 'sm' }}>
              {subscriptionInterval ? (
                <>
                  {subscriptionInterval.transKey} {subscriptionInterval.count}
                </>
              ) : null}
            </Box>
          </Box>
          <Box
            sx={{
              fontSize: '0.8rem',
              lineHeight: '1.5rem',
            }}
          >
            <SubscriptionStatusMessage
              name="Triggers"
              subscription={subscription}
              isTrial={isTrial}
              hasEnded={hasEnded}
              formatDate={(date, format) => formatDate(date, format) ?? ''}
            />
          </Box>
        </Box>
        <Box>
          {subscription ? (
            <Box minW="120px">
              <Form method="post" action="/account/subscription">
                <Input
                  type="hidden"
                  name="return_url"
                  value={`${window.location.origin}/account/subscription`}
                />
                <Input
                  type="hidden"
                  name="subscription_id"
                  value={subscription.id}
                />
                <Button
                  type="submit"
                  aria-label="Update Plan"
                  colorScheme="primary"
                  isLoading={navigation.formAction === '/account/subscription'}
                >
                  Update Plan
                </Button>
              </Form>
            </Box>
          ) : (
            <Box minW="120px">
              <Form method="post" action="/account/create-subscription">
                <Input
                  type="hidden"
                  name="return_url"
                  value={`${window.location.origin}/account/subscription`}
                />
                <Input type="hidden" name="category" value="app-triggers" />
                <Button
                  type="submit"
                  aria-label="Add"
                  colorScheme="orange"
                  width="full"
                  isDisabled={isDisabled}
                  isLoading={
                    navigation.formAction === '/account/create-subscription'
                  }
                >
                  Add
                </Button>
              </Form>
            </Box>
          )}
        </Box>
      </Grid>
    </Grid>
  )
}
