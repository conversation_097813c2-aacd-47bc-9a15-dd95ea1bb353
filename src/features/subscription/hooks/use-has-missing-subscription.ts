import { useMemo } from 'react'
import { isNil } from 'ramda'
import { useSubscriptionEnabled } from '@/features/subscription/hooks/use-subscription-enabled'
import { useAppSubscription } from '@/features/subscription/hooks/use-app-subscription'

export const useHasMissingSubscription = () => {
  const enabled = useSubscriptionEnabled()
  const { subscription, isFetching: isFetchingSubscription } =
    useAppSubscription()

  return useMemo(
    () => enabled && isNil(subscription) && !isFetchingSubscription,
    [enabled, subscription, isFetchingSubscription]
  )
}
