import { useMemo } from 'react'
import { useEnabledEnvFeatures } from '@/hooks/use-enabled-env-features'
import { useSubscriptions } from '@/features/subscription/hooks/use-billing-api'
import { useSubscriptionEnabled } from '@/features/subscription/hooks/use-subscription-enabled'

export const useAddOnSubscriptions = () => {
  const isEnabled = useSubscriptionEnabled()
  const enabledFeatures = useEnabledEnvFeatures()
  const { data, isFetching } = useSubscriptions(isEnabled)

  const subscriptions = useMemo(() => {
    return (
      data
        ?.filter((subscription) => subscription.product.category !== 'app')
        .filter((subscription) => {
          if (
            enabledFeatures.billing_powerbi_subscription_feature_enabled &&
            subscription.product.category === 'app-powerbi'
          ) {
            return true
          }
          if (
            enabledFeatures.billing_triggers_subscription_feature_enabled &&
            subscription.product.category === 'app-triggers'
          ) {
            return true
          }
          return false
        }) ?? []
    )
  }, [
    data,
    enabledFeatures.billing_powerbi_subscription_feature_enabled,
    enabledFeatures.billing_triggers_subscription_feature_enabled,
  ])

  return {
    subscriptions,
    isFetching,
  }
}
