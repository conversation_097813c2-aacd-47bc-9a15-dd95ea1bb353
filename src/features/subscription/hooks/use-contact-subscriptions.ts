import { useMemo } from 'react'
import { useSubscriptionEnabled } from '@/features/subscription/hooks/use-subscription-enabled'
import { useMultipleSubscriptionUsersBySubscriptionId } from '@/features/subscription/hooks/use-billing-api'
import { useQuantityAddOnSubscriptions } from '@/features/subscription/hooks/use-quantity-add-on-subscriptions'

export const useContactSubscriptions = (contactId?: string) => {
  const enabled = useSubscriptionEnabled()
  const { subscriptions, isFetching } = useQuantityAddOnSubscriptions()
  const subscriptionIds = useMemo(
    () => subscriptions.map((subscription) => subscription.id),
    [subscriptions]
  )
  const queries = useMultipleSubscriptionUsersBySubscriptionId(
    subscriptionIds,
    enabled
  )
  const isFetchingUsers = useMemo(
    () => queries.some((query) => query.isFetching),
    [queries]
  )
  const subscriptionUsers = useMemo(() => {
    return queries.map((query) => query.data ?? [])
  }, [queries])
  const userSubscriptions = useMemo(() => {
    return subscriptions.map((subscription) => {
      const users = queries.find(
        (query) => query.data?.[0]?.subscription_id === subscription.id
      )
      const user = users?.data?.find((user) => user.user_id === contactId)
      return {
        enabled: !!user,
        subscription_id: subscription.id,
        name: subscription.product.name,
        quantity: subscription.quantity ?? 0,
        count: users?.data?.length ?? 0,
        category: subscription.product.category,
      }
    })
  }, [queries, subscriptions, contactId])

  return {
    subscriptions: userSubscriptions,
    subscriptionUsers,
    isFetching: isFetching || isFetchingUsers,
  }
}
