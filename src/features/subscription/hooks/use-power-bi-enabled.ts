import { useMemo } from 'react'
import { useEnabledEnvFeatures } from '@/hooks/use-enabled-env-features'
import { useHasPowerBiLicense } from '@/features/subscription/hooks/use-billing-api'
import { useSubscriptionEnabled } from '@/features/subscription/hooks/use-subscription-enabled'

export const usePowerBiEnabled = () => {
  const enabledFeatures = useEnabledEnvFeatures()
  const isEnabled = useSubscriptionEnabled()

  const { data: hasLicense, isFetching } = useHasPowerBiLicense(
    isEnabled &&
      !(enabledFeatures.billing_powerbi_reports_feature_enabled as boolean) &&
      (enabledFeatures.billing_powerbi_subscription_feature_enabled as boolean)
  )

  const powerBiEnabled = useMemo(() => {
    if (enabledFeatures.powerbi_reports_feature_enabled as boolean) {
      return true
    }
    if (
      !(enabledFeatures.billing_powerbi_subscription_feature_enabled as boolean)
    ) {
      return false
    }
    return Boolean(hasLicense)
  }, [
    hasLicense,
    enabledFeatures.powerbi_reports_feature_enabled,
    enabledFeatures.billing_powerbi_subscription_feature_enabled,
  ])

  return {
    powerBiEnabled,
    isFetching,
  }
}
