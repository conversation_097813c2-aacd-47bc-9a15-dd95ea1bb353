import { useMemo } from 'react'
import { useQuery } from '@tanstack/react-query'
import { useEnabledEnvFeatures } from '@/hooks/use-enabled-env-features'
import { getCurrentUser } from '@/api/users'
import type { CurrentUserModel } from '@/types/models/user'

export const useClientEmail = () => {
  const enabledFeatures = useEnabledEnvFeatures()
  const { data: user } = useQuery<CurrentUserModel | null>({
    queryKey: ['GetCurrentUser'],
    queryFn: ({ signal }) => getCurrentUser({ signal }),
    enabled: !!enabledFeatures?.billing_show_subscription_feature_enabled,
  })

  return useMemo(() => user?.email, [user?.email])
}
