import { useMemo } from 'react'
import { isNil } from 'ramda'
import { differenceInDays, isSameDay } from 'date-fns'
import { useAppSubscription } from '@/features/subscription/hooks/use-app-subscription'
import { useSubscriptionEnabled } from '@/features/subscription/hooks/use-subscription-enabled'

const defaultResponse = { days: null, isSameDay: false, hasEnded: false }

export const useSubscriptionDaysLeft = () => {
  const isEnabled = useSubscriptionEnabled()
  const { subscription } = useAppSubscription()

  return useMemo(() => {
    if (!isEnabled) return defaultResponse
    if (isNil(subscription)) return defaultResponse
    const days = differenceInDays(subscription.end_date, new Date())
    return {
      days: days + 1,
      isSameDay: isSameDay(subscription.end_date, new Date()),
      hasEnded: days < 0,
    }
  }, [isEnabled, subscription])
}
