import { useMemo } from 'react'
import { isNil } from 'ramda'
import { useEnabledEnvFeatures } from '@/hooks/use-enabled-env-features'
import {
  useBillingStatus,
  useBillingCustomer,
} from '@/features/subscription/hooks/use-billing-api'
import { useActiveSubscription } from '@/features/subscription/hooks/use-active-subscription'
import { useSubscriptionEnabled } from '@/features/subscription/hooks/use-subscription-enabled'

export const useShowPaywall = () => {
  const enabledFeatures = useEnabledEnvFeatures()
  const isEnabled = useSubscriptionEnabled()
  const { subscription } = useActiveSubscription()

  const { data: status, error } = useBillingStatus(
    subscription?.id ?? '',
    isEnabled
  )

  const { data: customer } = useBillingCustomer(
    subscription?.id ?? '',
    isEnabled
  )

  return useMemo(() => {
    if (!isEnabled) return false
    if (isNil(status) || error?.status === 404) return false
    if (enabledFeatures.billing_stripe_pricing_catalog_popup_feature_enabled) {
      return isNil(customer?.invoice_settings.default_payment_method)
    }
    return !subscription
  }, [
    subscription,
    isEnabled,
    status,
    error?.status,
    enabledFeatures.billing_stripe_pricing_catalog_popup_feature_enabled,
    customer?.invoice_settings.default_payment_method,
  ])
}
