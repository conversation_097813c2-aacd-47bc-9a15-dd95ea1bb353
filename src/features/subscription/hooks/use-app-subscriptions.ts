import { useMemo } from 'react'
import { useSubscriptions } from '@/features/subscription/hooks/use-subscriptions'

export const useAppSubscriptions = () => {
  const { subscriptions, isFetching } = useSubscriptions()

  const filteredSubscriptions = useMemo(
    () =>
      subscriptions?.filter(
        (subscription) => subscription.product.category === 'app'
      ) ?? [],
    [subscriptions]
  )

  return {
    subscriptions: filteredSubscriptions,
    isFetching,
  }
}
