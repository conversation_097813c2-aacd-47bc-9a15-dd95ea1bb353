import { useMemo } from 'react'
import { isAfter } from 'date-fns'
import { useActiveSubscription as useAppActiveSubscription } from '@/features/subscription/hooks/use-active-subscription'

export const useActiveSubscription = () => {
  const { subscription, isPending } = useAppActiveSubscription() as any

  const activeSubscription = useMemo(
    () =>
      (subscription?.status === 'trialing' ||
        subscription?.status === 'active') &&
      isAfter(subscription?.end_date, new Date())
        ? subscription
        : undefined,
    [subscription]
  )

  return {
    subscription: activeSubscription,
    isPending,
  }
}
