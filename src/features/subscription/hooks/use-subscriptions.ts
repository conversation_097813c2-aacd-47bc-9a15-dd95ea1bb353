import { useSubscriptions as useSubscriptionsApi } from '@/features/subscription/hooks/use-billing-api'
import { useSubscriptionEnabled } from '@/features/subscription/hooks/use-subscription-enabled'

export const useSubscriptions = () => {
  const isEnabled = useSubscriptionEnabled()
  const { data: subscriptions, isPending } = useSubscriptionsApi(isEnabled)

  return {
    subscriptions,
    isFetching: isPending,
  }
}
