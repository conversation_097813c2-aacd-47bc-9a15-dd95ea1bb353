import { useMemo } from 'react'
import {
  calculateTieredPrice,
  formatPriceForDisplay,
} from '@/features/subscription/utils/calculate-tiered-price'
import type { StripePrice } from '@/types/models/billing'

export const useTieredPrice = (
  priceObject: StripePrice | undefined,
  quantity: number | undefined
) => {
  return useMemo(() => {
    if (!(quantity && priceObject)) return null
    const price = calculateTieredPrice(priceObject, quantity)
    const formatted = formatPriceForDisplay(price)
    return formatted
  }, [priceObject, quantity])
}
