import { useMemo } from 'react'
import { interpolate } from '@/utils/interpolate'

// Translation map for subscription interval keys
const SUBSCRIPTION_INTERVAL_TRANSLATIONS: Record<string, string> = {
  subscriptionInterval_year_pluralization_one: 'every ${interval} year',
  subscriptionInterval_year_pluralization_other: 'every ${interval} years',
  subscriptionInterval_month_pluralization_one: 'every ${interval} month',
  subscriptionInterval_month_pluralization_other: 'every ${interval} months',
  subscriptionInterval_week_pluralization_one: 'every ${interval} week',
  subscriptionInterval_week_pluralization_other: 'every ${interval} weeks',
  subscriptionInterval_day_pluralization_one: 'every ${interval} day',
  subscriptionInterval_day_pluralization_other: 'every ${interval} days',
}

export function useSubscriptionIntervalText(
  interval: { transKey: string; count: number } | null
): string | null {
  return useMemo(() => {
    if (!interval) return null
    const type = interval.transKey.split('_')[1]
    const pluralization = interval.count === 1 ? 'one' : 'other'
    const key = `subscriptionInterval_${type}_pluralization_${pluralization}`
    const template = SUBSCRIPTION_INTERVAL_TRANSLATIONS[key]
    if (!template) return null
    return interpolate(template, { interval: interval.count })
  }, [interval])
}
