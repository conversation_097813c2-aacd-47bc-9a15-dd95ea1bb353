import { useMemo } from 'react'
import { useContactSubscriptions } from '@/features/subscription/hooks/use-contact-subscriptions'
import { useQuantityAddOnSubscriptions } from '@/features/subscription/hooks/use-quantity-add-on-subscriptions'

// dummy hook for now
const useSearchedContacts = () => {
  return {
    contacts: [],
    currentContact: null,
    isFetchingContacts: false,
    isFetchingCompanies: false,
  }
}

export const useContactsWithSubscriptions = () => {
  const { subscriptionUsers } = useContactSubscriptions()
  const { subscriptions } = useQuantityAddOnSubscriptions()
  const { contacts, currentContact, isFetchingContacts, isFetchingCompanies } =
    useSearchedContacts()

  const contactsWithAddOns = useMemo(() => {
    return contacts.map((contact: any) => {
      const addOns = subscriptionUsers.reduce((acc: string[], users) => {
        const user = users.find((user) => user.user_id === contact.id)
        const subscription = subscriptions.find(
          (subscription) => subscription.id === user?.subscription_id
        )
        if (subscription) {
          acc.push(subscription.product.name)
        }
        return acc
      }, [])
      return [contact, addOns] as const
    })
  }, [subscriptionUsers, contacts, subscriptions])

  const contactsWithAddOnsOnly = useMemo(
    () => contactsWithAddOns.filter(([, addOns]) => addOns.length > 0),
    [contactsWithAddOns]
  )

  return {
    contacts: contactsWithAddOns,
    contactsWithAddOnsOnly,
    currentContact,
    isFetchingContacts,
    isFetchingCompanies,
  }
}
