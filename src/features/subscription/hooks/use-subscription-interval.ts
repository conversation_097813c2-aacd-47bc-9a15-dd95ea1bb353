import { useMemo } from 'react'
import { isNotNil } from 'ramda'
import { useBillingStatus } from '@/features/subscription/hooks/use-billing-api'
import { useSubscription } from '@/features/subscription/hooks/use-subscription'

export const useSubscriptionInterval = (subscriptionId: string) => {
  const { subscription } = useSubscription(subscriptionId)
  const hasSubscription = isNotNil(subscription)

  const { data: status } = useBillingStatus(
    subscriptionId,
    hasSubscription && Boolean(subscriptionId)
  )

  return useMemo(() => {
    if (
      !(
        status?.subscription.plan?.interval ||
        status?.subscription.plan?.interval_count
      )
    ) {
      return null
    }
    const interval = status.subscription.plan.interval
    const interval_count = status.subscription.plan.interval_count
    return {
      transKey: `subscriptionInterval_${interval}_pluralization`,
      count: interval_count,
    }
  }, [
    status?.subscription?.plan?.interval,
    status?.subscription?.plan?.interval_count,
  ])
}
