import { useMemo } from 'react'
import { useSubscriptions } from '@/features/subscription/hooks/use-billing-api'

export const useQuantityAddOnSubscriptions = () => {
  const isEnabled = true
  const enabledFeatures = {
    billing_powerbi_subscription_feature_enabled: true,
  } as const
  const { data, isFetching } = useSubscriptions(isEnabled)

  const subscriptions = useMemo(() => {
    return (
      data
        ?.filter((subscription) => subscription.product.category !== 'app')
        .filter((subscription) => {
          if (
            enabledFeatures.billing_powerbi_subscription_feature_enabled &&
            subscription.product.category === 'app-powerbi'
          ) {
            return true
          }
          return false
        }) ?? []
    )
  }, [data, enabledFeatures.billing_powerbi_subscription_feature_enabled])

  return {
    subscriptions,
    isFetching,
  }
}
