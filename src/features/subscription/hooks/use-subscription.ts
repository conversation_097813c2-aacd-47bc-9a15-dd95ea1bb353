import { useMemo } from 'react'
import { useSubscriptions } from '@/features/subscription/hooks/use-billing-api'

export const useSubscription = (subscriptionId: string) => {
  const { data: subscriptions, isFetching } = useSubscriptions()

  const subscription = useMemo(
    () => subscriptions?.find(({ id }) => id === subscriptionId),
    [subscriptions, subscriptionId]
  )

  return {
    subscription,
    isFetching,
  }
}
