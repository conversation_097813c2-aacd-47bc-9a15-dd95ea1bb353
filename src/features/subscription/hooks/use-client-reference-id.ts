import { useMemo } from 'react'
import { useEnabledEnvFeatures } from '@/hooks/use-enabled-env-features'
import { useCurrentUser } from '@/features/subscription/hooks/use-current-user'

export const useClientReferenceId = () => {
  const enabledFeatures = useEnabledEnvFeatures()
  const { data: user } = useCurrentUser({
    enabled: !!enabledFeatures?.billing_show_subscription_feature_enabled,
  })

  return useMemo(() => (user ? `app_${user.tina_user_id}` : undefined), [user])
}
