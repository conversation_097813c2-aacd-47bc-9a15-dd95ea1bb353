import { useMemo } from 'react'
import { isNil } from 'ramda'
import { useBillingCustomer } from '@/features/subscription/hooks/use-billing-api'
import { useSubscription } from '@/features/subscription/hooks/use-subscription'

const usDollar = new Intl.NumberFormat('en-US', {
  style: 'currency',
  currency: 'USD',
})

export const useCurrencyFormatter = (subscriptionId: string) => {
  const { subscription } = useSubscription(subscriptionId)
  const hasSubscription = !isNil(subscription)
  const { data: customer } = useBillingCustomer(subscriptionId, hasSubscription)

  return useMemo(() => {
    if (
      !(
        customer?.currency &&
        customer?.preferred_locales &&
        customer.preferred_locales.length > 0
      )
    ) {
      return usDollar
    }
    const locale = customer.preferred_locales[0]
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: customer.currency.toUpperCase(),
    })
  }, [customer])
}
