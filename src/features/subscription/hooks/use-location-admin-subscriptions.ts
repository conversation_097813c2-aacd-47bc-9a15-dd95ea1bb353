import { useMemo } from 'react'
import { useSubscriptionEnabled } from '@/features/subscription/hooks/use-subscription-enabled'
import { useLocationSubscriptions } from '@/features/subscription/hooks/use-billing-api'
import type { LocationSubscription } from '@/types/models/billing'

export const useLocationAdminSubscriptions = () => {
  const isEnabled = useSubscriptionEnabled()
  const { data: subscriptions } = useLocationSubscriptions(isEnabled)

  return useMemo(
    () =>
      subscriptions?.reduce((acc: LocationSubscription[], item) => {
        if (item.is_valid) {
          return acc
        }
        if (acc.some(({ email }) => email === item.email)) {
          return acc
        }
        acc.push(item)
        return acc
      }, []) ?? [],
    [subscriptions]
  )
}
