import { useMemo } from 'react'
import { isNil } from 'ramda'
import { useLocationSubscriptions } from '@/features/subscription/hooks/use-billing-api'
import { useSubscriptionEnabled } from '@/features/subscription/hooks/use-subscription-enabled'

export const useHasInvalidAdminSubscription = (enabled: boolean) => {
  const isEnabled = useSubscriptionEnabled()
  const { data } = useLocationSubscriptions(isEnabled && enabled)

  return useMemo(() => {
    if (!isEnabled) return false
    if (isNil(data)) return false
    return data.some(({ is_valid }) => !is_valid)
  }, [data, isEnabled])
}
