import { useMemo } from 'react'
import { useUserSubscriptions } from '@/features/subscription/hooks/use-billing-api'
import { useSubscriptions } from '@/features/subscription/hooks/use-subscriptions'

export const useUserLicenses = (userId: string) => {
  const { subscriptions, isFetching: isFetchingSubscriptions } =
    useSubscriptions()

  const { data, isFetching: isFetchingUserSubscriptions } =
    useUserSubscriptions()

  const names = useMemo(() => {
    const licenseIds = data?.find((d) => d.user_id === userId)?.subscriptions
    return licenseIds
      ?.map((id) => {
        const subscription = subscriptions?.find((s) => s.id === id)
        return subscription?.product.name
      })
      .filter(Boolean)
      .join(', ')
  }, [subscriptions, data, userId])

  return {
    names,
    isFetchingLicenses: isFetchingSubscriptions || isFetchingUserSubscriptions,
  }
}
