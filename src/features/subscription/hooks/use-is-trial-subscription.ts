import { useMemo } from 'react'
import { isNil } from 'ramda'
import { useBillingCustomer } from '@/features/subscription/hooks/use-billing-api'
import { useSubscriptionEnabled } from '@/features/subscription/hooks/use-subscription-enabled'
import { useSubscription } from '@/features/subscription/hooks/use-subscription'

export const useIsTrialSubscription = (subscriptionId: string) => {
  const isEnabled = useSubscriptionEnabled()
  const { data: customer } = useBillingCustomer(subscriptionId, isEnabled)
  const { subscription } = useSubscription(subscriptionId)

  return useMemo(() => {
    return (
      subscription?.status === 'trialing' &&
      isNil(customer?.invoice_settings.default_payment_method)
    )
  }, [subscription?.status, customer?.invoice_settings.default_payment_method])
}
