import { useMemo } from 'react'
import { isNil } from 'ramda'
import { isAfter } from 'date-fns'
import { useSubscription } from '@/features/subscription/hooks/use-subscription'

export const useSubscriptionHasEnded = (subscriptionId: string) => {
  const { subscription } = useSubscription(subscriptionId)

  return useMemo(() => {
    if (isNil(subscription?.end_date)) return false
    return isAfter(new Date(), subscription.end_date)
  }, [subscription?.end_date])
}
