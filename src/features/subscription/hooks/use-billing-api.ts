import {
  useQuery,
  useQueries,
  useMutation,
  useQueryClient,
} from '@tanstack/react-query'
import type { AxiosError } from 'axios'
import * as api from '@/api/billing'
import type {
  BillingStatus,
  SubscriptionWithProduct,
  SubscriptionLicensePayload,
  SubscriptionUserListResponse,
} from '@/types/models/billing'

export const useSubscriptions = (enabled = false) => {
  return useQuery<SubscriptionWithProduct[], AxiosError>({
    queryKey: ['Billing', 'Subscription'],
    queryFn: ({ signal }) => api.getSubscriptions(signal),
    enabled,
  })
}

export const useUserSubscriptions = (enabled = true) => {
  return useQuery<SubscriptionUserListResponse[], AxiosError>({
    queryKey: ['Billing', 'Subscription', 'Users'],
    queryFn: ({ signal }) => api.getSubscriptionUsers(signal),
    enabled,
  })
}

export const useSubscriptionUsersBySubscriptionId = (
  subscriptionId: string,
  enabled = false
) => {
  return useQuery({
    queryKey: ['Billing', 'Subscription', 'Users', subscriptionId],
    queryFn: ({ signal }) =>
      api.getSubscriptionUsersBySubscriptionId(subscriptionId, signal),
    enabled,
  })
}

export const useMultipleSubscriptionUsersBySubscriptionId = (
  subscriptionIds: string[],
  enabled = false
) => {
  return useQueries({
    queries: subscriptionIds.map((subscriptionId) => ({
      queryKey: ['Billing', 'Subscription', 'Users', subscriptionId],
      queryFn: ({ signal }: { signal?: AbortSignal }) => {
        return api.getSubscriptionUsersBySubscriptionId(subscriptionId, signal)
      },
      enabled,
    })),
  })
}

export const useLocationSubscriptions = (enabled = false) => {
  return useQuery({
    queryKey: ['Billing', 'Subscription', 'Location'],
    queryFn: ({ signal }) => api.getLocationSubscriptions(signal),
    enabled,
  })
}

export const useLocationTriggersSubscriptions = (enabled = false) => {
  return useQuery({
    queryKey: ['Billing', 'Subscription', 'Location', 'Triggers'],
    queryFn: ({ signal }) => api.getLocationTriggersSubscriptions(signal),
    enabled,
  })
}

export const useHasPowerBiLicense = (enabled = false) => {
  return useQuery({
    queryKey: ['Billing', 'Subscription', 'PowerBi'],
    queryFn: ({ signal }) => api.hasPowerBiSubscription(signal),
    enabled,
  })
}

export const useCreateSubscription = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationFn: () => api.createSubscription(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['Billing'] })
    },
  })
}

export const useCreatePowerBiSubscription = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationFn: ({ quantity }: { quantity: number }) => {
      return api.createPowerBiSubscription({ quantity })
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['Billing'] })
    },
  })
}

export const useCreateTriggersSubscription = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationFn: () => api.createTriggersSubscription(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['Billing'] })
    },
  })
}

export const useUpdatePowerBiSubscription = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationFn: ({
      subscriptionId,
      quantity,
    }: {
      subscriptionId: string
      quantity: number
    }) => {
      return api.updatePowerBiSubscription(subscriptionId, { quantity })
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['Billing'] })
    },
  })
}

export const useUpdateSubscriptionLicenses = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationFn: (payload: SubscriptionLicensePayload) =>
      api.updateSubscriptionLicenses(payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['Billing'] })
    },
  })
}

export const useDeleteSubscriptionLicenses = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationFn: (payload: SubscriptionLicensePayload) =>
      api.deleteSubscriptionLicenses(payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['Billing'] })
    },
  })
}

export const useBillingStatus = (subscriptionId: string, enabled = false) => {
  return useQuery<BillingStatus, AxiosError>({
    queryKey: ['Billing', 'Status', subscriptionId],
    queryFn: ({ signal }) => api.getBillingStatus(subscriptionId, signal),
    enabled: !!subscriptionId && enabled,
  })
}

export const useBillingCards = (subscriptionId: string, enabled = false) => {
  return useQuery({
    queryKey: ['Billing', 'Cards', subscriptionId],
    queryFn: ({ signal }) => api.getBillingCards(subscriptionId, signal),
    enabled: !!subscriptionId && enabled,
  })
}

export const useBillingCard = (subscriptionId: string, id?: string) => {
  return useQuery({
    queryKey: ['Billing', 'Card', subscriptionId, id],
    queryFn: ({ signal }) => api.getBillingCard(subscriptionId, id, signal),
    enabled: !!id,
  })
}

export const useBillingInvoices = (subscriptionId: string, enabled = false) => {
  return useQuery({
    queryKey: ['Billing', 'Invoices', subscriptionId],
    queryFn: ({ signal }) => api.getBillingInvoices(subscriptionId, signal),
    enabled: !!subscriptionId && enabled,
  })
}

export const useBillingCustomer = (subscriptionId: string, enabled = false) => {
  return useQuery({
    queryKey: ['Billing', 'Customer', subscriptionId],
    queryFn: ({ signal }) => api.getBillingCustomer(subscriptionId, signal),
    enabled: !!subscriptionId && enabled,
  })
}

export const useCustomerSession = (subscriptionId: string, enabled = false) => {
  return useQuery({
    queryKey: ['Billing', 'CustomerSession', subscriptionId],
    queryFn: ({ signal }) => api.getCustomerSession(subscriptionId, signal),
    enabled: !!subscriptionId && enabled,
    staleTime: 1_800_000, // 30 minutes
  })
}

export const usePortalSession = (
  subscriptionId: string,
  params: { return_url: string },
  enabled = false
) => {
  return useQuery({
    queryKey: ['Billing', 'PortalSession', subscriptionId, params],
    queryFn: ({ signal }) =>
      api.getPortalSession(subscriptionId, params, signal),
    enabled: !!subscriptionId && enabled,
  })
}
