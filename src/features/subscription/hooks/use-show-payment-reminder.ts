import { useMemo } from 'react'
import { isNil } from 'ramda'
import { differenceInDays } from 'date-fns'
import { useAppSubscription } from '@/features/subscription/hooks/use-app-subscription'
import { useSubscriptionEnabled } from '@/features/subscription/hooks/use-subscription-enabled'
import { useSubscriptionDaysReminder } from '@/features/subscription/hooks/use-subscription-days-reminder'

export const useShowPaymentReminder = () => {
  const isEnabled = useSubscriptionEnabled()
  const days = useSubscriptionDaysReminder()
  const { subscription } = useAppSubscription()

  return useMemo(() => {
    if (!isEnabled) return false
    if (isNil(subscription)) return false
    return (
      differenceInDays(subscription?.end_date, new Date()) <= days &&
      subscription?.status === 'trialing'
    )
  }, [days, isEnabled, subscription])
}
