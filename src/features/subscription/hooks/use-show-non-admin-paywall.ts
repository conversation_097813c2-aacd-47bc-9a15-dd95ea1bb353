import { useMemo } from 'react'
import { isNil } from 'ramda'
import { useActiveSubscription } from '@/features/subscription/hooks/use-active-subscription'
import { useHasInvalidAdminSubscription } from '@/features/subscription/hooks/use-has-invalid-admin-subscription'
import { useSubscriptionEnabled } from '@/features/subscription/hooks/use-subscription-enabled'

export const useShowNonAdminPaywall = () => {
  const isEnabled = useSubscriptionEnabled()
  const { subscription, isPending } = useActiveSubscription()

  const hasInvalid = useHasInvalidAdminSubscription(
    !isPending && isNil(subscription)
  )

  return useMemo(() => {
    if (!isEnabled) return false
    if (!hasInvalid) return false
    return !subscription
  }, [subscription, isEnabled, hasInvalid])
}
