import { useEnabledEnvFeatures } from '@/hooks/use-enabled-env-features'

export const useSubscriptionEnabled = () => {
  const enabledFeatures = useEnabledEnvFeatures()
  const isEnabled =
    enabledFeatures?.billing_show_subscription_feature_enabled &&
    (enabledFeatures?.billing_stripe_pricing_catalog_popup_feature_enabled ||
      enabledFeatures?.billing_automatic_subscription_creation_feature_enabled)

  return !!isEnabled
}
