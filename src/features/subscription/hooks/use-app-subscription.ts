import { useMemo } from 'react'
import { useSubscriptions } from '@/features/subscription/hooks/use-subscriptions'

export const useAppSubscription = () => {
  const { subscriptions, isFetching } = useSubscriptions()

  const subscription = useMemo(
    () =>
      subscriptions?.find(
        (subscription) => subscription.product.category === 'app'
      ),
    [subscriptions]
  )

  return {
    subscription,
    isFetching,
  }
}
