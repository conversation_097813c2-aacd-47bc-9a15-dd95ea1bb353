import {
  Box,
  Flex,
  Grid,
  Skeleton,
  List,
  ListItem,
  FormLabel,
} from '@chakra-ui/react'
import { useBillingCards } from '@/features/subscription/hooks/use-billing-api'
import { Loader } from '@/features/subscription/components/loader'
import type { SubscriptionWithProduct } from '@/types/models/billing'

interface Props {
  subscription: SubscriptionWithProduct
}

export const PaymentInfo = ({ subscription }: Props) => {
  const { data: cards, isFetching } = useBillingCards(subscription.id, true)

  if (isFetching) {
    return <Loader height="60px" />
  }

  return (
    <>
      {(cards?.data?.length ?? 0) > 0 ? (
        <Grid
          sx={{
            width: '100%',
            p: { base: '0.5rem', md: '1rem' },
            gridTemplateColumns: '1fr',
            gap: { base: '0.5rem', md: '1rem' },
            alignItems: 'center',
            m: 0,
            borderBottom: '1px solid',
            borderBottomColor: 'gray.100',
            _last: {
              border: 0,
            },
          }}
        >
          <FormLabel
            sx={{
              flex: '1',
              m: '0',
              fontWeight: '600',
            }}
          >
            Payment Method
          </FormLabel>
          <Box>
            <List>
              {cards?.data?.map((card, index) => (
                <ListItem
                  key={index}
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    gap: 2,
                    pb: '0.5rem',
                  }}
                >
                  <Flex
                    sx={{
                      alignItems: 'center',
                      gap: 2,
                    }}
                  >
                    <Flex>
                      <Skeleton
                        startColor="gray.100"
                        endColor="gray.200"
                        w={6}
                        height={4}
                      />
                    </Flex>
                    <Flex>
                      <Box>{card.brand}</Box>
                      <Box
                        as="span"
                        sx={{
                          letterSpacing: '0.1rem',
                          px: '0.3rem',
                        }}
                      >
                        ••••
                      </Box>
                      <Box>{card.last4}</Box>
                    </Flex>
                  </Flex>
                  <Flex>
                    Expires {card.exp_month}/{card.exp_year}
                  </Flex>
                </ListItem>
              ))}
            </List>
          </Box>
        </Grid>
      ) : null}
    </>
  )
}
