import { useMemo } from 'react'
import { useNavigation, Form, Link as RouterLink } from 'react-router'
import {
  Box,
  Grid,
  Button,
  Input,
  List,
  ListItem,
  Link,
  VStack,
} from '@chakra-ui/react'
import { formatDate } from '@/utils/date/format-date'
import { useContactSubscriptions } from '@/features/subscription/hooks/use-contact-subscriptions'
import { useCurrencyFormatter } from '@/features/subscription/hooks/use-currency-formatter'
import { useSubscriptionInterval } from '@/features/subscription/hooks/use-subscription-interval'
import { useIsTrialSubscription } from '@/features/subscription/hooks/use-is-trial-subscription'
import { useSubscriptionHasEnded } from '@/features/subscription/hooks/use-subscription-has-ended'
import { useTieredPrice } from '@/features/subscription/hooks/use-tiered-price'
import { useBillingStatus } from '@/features/subscription/hooks/use-billing-api'
import { Card } from '@/features/subscription/components/card'
import { Loader } from '@/features/subscription/components/loader'
import type { SubscriptionWithProduct } from '@/types/models/billing'
import { SubscriptionStatusMessage } from '@/features/subscription/components/subscription-status-message'

interface Props {
  subscription: SubscriptionWithProduct
  isDetails?: boolean
}

export const SubscriptionStatus = ({ subscription, isDetails }: Props) => {
  const navigation = useNavigation()
  const currencyFormatter = useCurrencyFormatter(subscription.id)
  const subscriptionInterval = useSubscriptionInterval(subscription.id)
  const isTrial = useIsTrialSubscription(subscription.id)
  const hasEnded = useSubscriptionHasEnded(subscription.id)
  const { data: status, isPending } = useBillingStatus(subscription.id, true)
  const price = useTieredPrice(subscription.stripe_price, subscription.quantity)
  const { subscriptions } = useContactSubscriptions()

  const { count, total } = useMemo(() => {
    const found = subscriptions.find(
      (s) => s.subscription_id === subscription?.id
    )
    return {
      count: found?.count ?? 0,
      total: found?.quantity ?? 0,
    }
  }, [subscriptions, subscription?.id])

  if (isPending) {
    return <Loader height="100px" />
  }

  return (
    <Grid
      sx={{
        width: '100%',
        p: { base: '0.5rem', md: '1rem' },
        gridTemplateColumns: '1fr',
        gap: { base: '0.5rem', md: '1rem' },
        alignItems: 'center',
        m: 0,
        borderBottom: '1px solid',
        borderBottomColor: 'gray.100',
        _last: {
          border: 0,
        },
      }}
    >
      <Grid
        sx={{
          gridTemplateColumns: '1fr auto',
          gap: 4,
          alignItems: 'top',
        }}
      >
        <Box>
          <Box
            sx={{
              fontWeight: 'semibold',
            }}
          >
            {isDetails ? (
              <>{status?.product?.name}</>
            ) : (
              <Link
                as={RouterLink}
                to={`/account/subscription/${subscription.id}`}
              >
                {status?.product?.name}
              </Link>
            )}
          </Box>
          <Box
            sx={{
              display: 'flex',
              alignItems: 'baseline',
              fontWeight: 'bold',
              fontSize: '1.25rem',
              gap: 1,
            }}
          >
            <Box>{currencyFormatter.format(price ?? 0)}</Box>
            <Box sx={{ fontSize: 'sm' }}>
              {subscriptionInterval ? (
                <>
                  {subscriptionInterval.transKey} {subscriptionInterval.count}
                </>
              ) : null}
            </Box>
          </Box>
          <Box
            sx={{
              fontSize: '0.8rem',
              lineHeight: '1.5rem',
            }}
          >
            <SubscriptionStatusMessage
              name={status?.product?.name ?? ''}
              subscription={subscription}
              isTrial={isTrial}
              hasEnded={hasEnded}
              formatDate={(date, format) => formatDate(date, format) ?? ''}
            />
          </Box>
          {status?.subscription.plan?.id.startsWith('card_') && (
            <List>
              <ListItem
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 2,
                }}
              >
                <Card
                  subscriptionId={subscription.id}
                  id={status?.subscription.plan.id}
                />
              </ListItem>
            </List>
          )}
        </Box>
        <Box>
          <VStack>
            <Box>
              <Form method="post" action="/account/subscription">
                <Input
                  type="hidden"
                  name="return_url"
                  value={`${window.location.origin}/account/subscription`}
                />
                <Input
                  type="hidden"
                  name="subscription_id"
                  value={subscription.id}
                />
                <Button
                  type="submit"
                  aria-label="Update Plan"
                  colorScheme="primary"
                  isLoading={navigation.formAction === '/account/subscription'}
                >
                  Update Plan
                </Button>
              </Form>
            </Box>
            {subscription.product.category !== 'app' && (
              <Box>
                {total > 0 ? (
                  <>
                    {count}/{total} licenses used
                  </>
                ) : (
                  <>
                    <strong>0</strong> licenses used.
                  </>
                )}
              </Box>
            )}
          </VStack>
        </Box>
      </Grid>
    </Grid>
  )
}
