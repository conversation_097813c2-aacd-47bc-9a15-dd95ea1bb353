import { describe, it, expect } from 'vitest'
import {
  findTierForQuantity,
  calculateTieredPrice,
  formatPriceForDisplay,
} from './calculate-tiered-price'
import type { StripeTier, StripePrice } from '@/types/models/billing'

// Match the real StripeTier interface
const makeTier = (tier: Partial<StripeTier>): StripeTier => ({
  flat_amount: null,
  flat_amount_decimal: null,
  unit_amount: null,
  unit_amount_decimal: null,
  up_to: null,
  ...tier,
})

// Match the real StripePrice interface
const makePrice = (price: Partial<StripePrice>): StripePrice => ({
  id: 'price_1',
  object: 'price',
  active: true,
  billing_scheme: 'tiered',
  created: 0,
  currency: 'usd',
  custom_unit_amount: null,
  livemode: false,
  lookup_key: null,
  metadata: {},
  nickname: null,
  product: 'prod_1',
  recurring: {
    aggregate_usage: null,
    interval: 'month',
    interval_count: 1,
    meter: null,
    trial_period_days: null,
    usage_type: 'licensed',
  },
  tax_behavior: 'exclusive',
  tiers: undefined,
  tiers_mode: undefined,
  transform_quantity: null,
  type: 'recurring',
  unit_amount: null,
  unit_amount_decimal: null,
  ...price,
})

describe('findTierForQuantity', () => {
  const tiers = [
    makeTier({ up_to: 10, unit_amount: 100 }),
    makeTier({ up_to: 20, unit_amount: 90 }),
    makeTier({ up_to: null, unit_amount: 80 }),
  ]

  it('returns undefined for undefined or empty tiers', () => {
    expect(findTierForQuantity(undefined, 5)).toBeUndefined()
    expect(findTierForQuantity([], 5)).toBeUndefined()
  })

  it('finds the correct tier for a given quantity', () => {
    expect(findTierForQuantity(tiers, 5)).toEqual(tiers[0])
    expect(findTierForQuantity(tiers, 15)).toEqual(tiers[1])
    expect(findTierForQuantity(tiers, 25)).toEqual(tiers[2])
  })
})

describe('calculateTieredPrice', () => {
  const tiers = [
    makeTier({ up_to: 10, unit_amount: 100 }),
    makeTier({ up_to: 20, unit_amount: 90 }),
    makeTier({ up_to: null, unit_amount: 80 }),
  ]

  it('returns unit_amount if no tiers or tiers empty', () => {
    expect(calculateTieredPrice(undefined, 5)).toBeNull()
    expect(calculateTieredPrice(makePrice({ unit_amount: 123 }), 5)).toBe(123)
    expect(
      calculateTieredPrice(makePrice({ unit_amount: 123, tiers: [] }), 5)
    ).toBe(123)
  })

  it('returns unit_amount if tiers_mode is not volume or graduated', () => {
    expect(
      calculateTieredPrice(
        makePrice({ unit_amount: 123, tiers, tiers_mode: undefined }),
        5
      )
    ).toBe(123)
    expect(
      calculateTieredPrice(
        makePrice({ unit_amount: 123, tiers, tiers_mode: 'other' as any }),
        5
      )
    ).toBe(123)
  })

  it('calculates price for volume pricing', () => {
    expect(
      calculateTieredPrice(makePrice({ tiers, tiers_mode: 'volume' }), 5)
    ).toBe(100 * 5)
    expect(
      calculateTieredPrice(makePrice({ tiers, tiers_mode: 'volume' }), 15)
    ).toBe(90 * 15)
    expect(
      calculateTieredPrice(makePrice({ tiers, tiers_mode: 'volume' }), 25)
    ).toBe(80 * 25)
  })

  it('returns flat_amount if unit_amount is not present in tier', () => {
    const flatTiers = [
      makeTier({ up_to: 10, flat_amount: 500, unit_amount: null }),
      makeTier({ up_to: null, flat_amount: 1000, unit_amount: null }),
    ]
    expect(
      calculateTieredPrice(
        makePrice({ tiers: flatTiers, tiers_mode: 'volume' }),
        5
      )
    ).toBe(500)
    expect(
      calculateTieredPrice(
        makePrice({ tiers: flatTiers, tiers_mode: 'volume' }),
        15
      )
    ).toBe(1000)
  })

  it('returns null if no matching tier is found', () => {
    expect(
      calculateTieredPrice(makePrice({ tiers: [], tiers_mode: 'volume' }), 5)
    ).toBeNull()
  })

  it('returns null for graduated pricing (not implemented)', () => {
    expect(
      calculateTieredPrice(makePrice({ tiers, tiers_mode: 'graduated' }), 5)
    ).toBeNull()
  })
})

describe('formatPriceForDisplay', () => {
  it('returns null if price is null', () => {
    expect(formatPriceForDisplay(null)).toBeNull()
  })

  it('formats price from cents to dollars', () => {
    expect(formatPriceForDisplay(12345)).toBe(123.45)
    expect(formatPriceForDisplay(100)).toBe(1)
  })
})
