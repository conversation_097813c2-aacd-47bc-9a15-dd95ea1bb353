import type { StripePrice, StripeTier } from '@/types/models/billing'

/**
 * Find the appropriate tier for a given quantity based on volume pricing
 * @param tiers Array of price tiers
 * @param quantity The quantity of devices
 * @returns The appropriate tier object or undefined
 */
export const findTierForQuantity = (
  tiers: StripeTier[] | undefined,
  quantity: number
): StripeTier | undefined => {
  if (!tiers || tiers.length === 0) return undefined

  // For volume pricing, find the tier where quantity falls within its range
  return tiers.find((tier, index) => {
    const isLastTier = index === tiers.length - 1

    // For the last tier (with null 'up_to'), it covers all quantities above the previous tier's limit
    if (isLastTier) return true

    const upperLimit = tier.up_to
    if (upperLimit === null) return true

    return quantity <= upperLimit
  })
}

/**
 * Calculate the price based on tiered pricing and quantity
 * @param priceObject The Stripe price object containing tiers
 * @param quantity The quantity of devices
 * @returns The calculated price in the smallest currency unit (e.g., cents)
 */
export const calculateTieredPrice = (
  priceObject: StripePrice | undefined,
  quantity: number
): number | null => {
  if (!priceObject?.tiers || priceObject.tiers.length === 0) {
    return priceObject?.unit_amount ?? null
  }

  const { tiers, tiers_mode } = priceObject

  // If not using tiered pricing or no valid tier mode, fall back to unit amount
  if (!tiers_mode || (tiers_mode !== 'volume' && tiers_mode !== 'graduated')) {
    return priceObject.unit_amount
  }

  // For volume pricing (entire quantity at tier rate)
  if (tiers_mode === 'volume') {
    const tier = findTierForQuantity(tiers, quantity)
    if (!tier) return null
    return tier.unit_amount ? tier.unit_amount * quantity : tier.flat_amount
  }

  // For graduated pricing (each portion at its tier rate) - not implemented yet
  // as the current requirement mentions volume pricing

  return null
}

/**
 * Format the price for display
 * @param price The price in the smallest currency unit (e.g., cents)
 * @returns The formatted price in major currency unit (e.g., dollars)
 */
export const formatPriceForDisplay = (price: number | null): number | null => {
  if (price === null) return null
  return price / 100
}
