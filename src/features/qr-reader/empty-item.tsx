import { Box } from '@chakra-ui/react'

interface EmptyItemProps {
  paused?: boolean
}

export const EmptyItem = ({ paused }: EmptyItemProps) => (
  <Box
    sx={{
      py: 2,
      px: 3,
      borderRadius: 'base',
      borderWidth: '1px',
      bg: 'white',
      _dark: {
        borderWidth: 0,
        bg: 'blackAlpha.400',
      },
    }}
  >
    {paused ? 'Camera is paused.' : 'No codes detected.'}
  </Box>
)
