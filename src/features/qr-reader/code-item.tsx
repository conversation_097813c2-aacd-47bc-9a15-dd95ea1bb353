import * as Sentry from '@sentry/react'
import { Box, Grid, Icon<PERSON>utton } from '@chakra-ui/react'
import { ClipboardIcon, Trash2Icon } from 'lucide-react'
import { useSetAtom } from 'jotai'
import { codeAtom, qrAtom } from '@/utils/stores/qr'
import { useToast } from '@/hooks/use-toast'

interface CodeItemProps {
  code: string
}

export const CodeItem = ({ code }: CodeItemProps) => {
  const toast = useToast()
  const setQr = useSetAtom(qrAtom)
  const setCodes = useSetAtom(codeAtom)

  const onRemove = (): void => {
    setCodes((prev) => [
      ...prev.reduce((acc: string[], c) => {
        if (c !== code) {
          acc.push(c)
        }
        return acc
      }, []),
    ])
    setQr(null)
    toast({
      status: 'info',
      msg: 'Code has been removed.',
    })
  }

  const onClipboard = async (): Promise<void> => {
    try {
      await navigator.clipboard.writeText(code.trim())
      toast({
        status: 'copy',
        msg: 'Code has been copied to clipboard.',
      })
    } catch (error: unknown) {
      Sentry.captureException(error, {
        fingerprint: ['clipboard'],
        level: 'warning',
      })
    }
  }

  return (
    <Grid
      sx={{
        gridTemplateColumns: 'auto 40px 40px',
        borderRadius: 'base',
        alignItems: 'center',
        borderWidth: '1px',
        bg: 'white',
        _dark: {
          borderWidth: 0,
          bg: 'blackAlpha.400',
        },
      }}
    >
      <Box py={2} px={3}>
        {code}
      </Box>
      <IconButton
        aria-label="Copy code to clipboard"
        size="md"
        variant="link"
        colorScheme="yellow"
        icon={<ClipboardIcon size={16} />}
        onClick={onClipboard}
      />
      <IconButton
        aria-label="Remove"
        size="md"
        variant="link"
        colorScheme="red"
        icon={<Trash2Icon size={16} />}
        onClick={onRemove}
      />
    </Grid>
  )
}
