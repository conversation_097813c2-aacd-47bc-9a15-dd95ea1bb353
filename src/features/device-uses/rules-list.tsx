import * as Sentry from '@sentry/react'
import { lazy, useCallback, useMemo } from 'react'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { Table, Tbody, Td, Tr } from '@chakra-ui/react'
import { omit } from 'ramda'
import { Actions } from '@/components/data-table/actions'
import { useModal } from '@/hooks/use-modal'
import { useOrganization } from '@/hooks/use-organization'
import { useToast } from '@/hooks/use-toast'
import { updateUse } from '@/api/things/uses'
import type { DeviceUseProps, UseRuleProps, UseModel } from '@/types/models/use'
import type { UseInput } from '@/types/api'
import type { ActionProps } from '@/types/table'

const ConfirmDialog = lazy(() =>
  import('@/components/confirm-dialog').then(({ ConfirmDialog }) => ({
    default: ConfirmDialog,
  }))
)

const Settings = lazy(() =>
  import('@/pages/device-templates/device-uses/settings').then(
    ({ Settings }) => ({
      default: Settings,
    })
  )
)

export const RuleList = ({
  typeId,
  use,
  rule,
}: {
  typeId: string
  use: DeviceUseProps
  rule: UseRuleProps
}) => {
  const modal = useModal()
  const toast = useToast()
  const queryClient = useQueryClient()
  const { organizationId, applicationId } = useOrganization()

  const { mutateAsync: updateUseMutation } = useMutation<
    UseModel,
    Error,
    {
      typeId: string
      useId: number
      input: Required<UseInput>
    }
  >({
    mutationFn: ({ typeId, useId, input }) =>
      updateUse({ organizationId, applicationId, typeId, useId, input }),
    onSuccess: () => {
      const cache = ['GetUses', 'GetUse']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const getStatusLabel = useCallback(
    (value: Maybe<string>): string => {
      if (!value) {
        return ''
      }
      const match = (rule?.statuses ?? []).find(
        (status) => status.value === value
      )
      return match?.label ?? ''
    },
    [rule.statuses]
  )

  const getUnitDisplay = useCallback(
    (unit: string): string => {
      const match = (rule.units ?? []).find(({ payload }) => payload === unit)
      return match?.display ?? ''
    },
    [rule.units]
  )

  const getValueAtIndex = useCallback(
    (index: number): Maybe<string> => {
      return rule.triggers?.[index]?.conditions?.[0]?.value ?? null
    },
    [rule.triggers]
  )

  const getUnitAtIndex = useCallback(
    (index: number): string => {
      return rule.triggers?.[index]?.unit ?? ''
    },
    [rule.triggers]
  )

  const getStatusCondition = (): string => {
    const value = getValueAtIndex(0)
    const label = getStatusLabel(value)
    return `Notify when “${rule.name}” is ${label || value || 'Unknown'}.`
  }

  const getValueCondition = (): string => {
    const conditions = [
      {
        value: getValueAtIndex(1),
        label: 'min',
        unit: getUnitDisplay(getUnitAtIndex(1)),
      },
      {
        value: getValueAtIndex(0),
        label: 'max',
        unit: getUnitDisplay(getUnitAtIndex(0)),
      },
    ]

    const description = conditions
      .filter(({ value }) => value)
      .map(({ label, value, unit }) => `${label} ${value}${unit}`)
      .join(' or ')

    return `Notify when “${rule.name}” is ${description}.`
  }

  const verboseCondition = (): string => {
    switch (rule.type) {
      case 'status':
      case 'toggle':
        return getStatusCondition()
      case 'value':
        return getValueCondition()
      default:
        return '...'
    }
  }

  const onEdit = useCallback(
    (row: UseRuleProps): void => {
      modal({
        size: '4xl',
        component: <Settings />,
        config: {
          data: {
            typeId,
            use: omit(['hasChildren'], use),
            rule: row,
          },
          onCallback: () => ({}),
        },
      })
    },
    [modal, use, typeId]
  )

  const onRemove = useCallback(
    (row: UseRuleProps): void => {
      modal({
        size: 'md',
        component: <ConfirmDialog />,
        config: {
          title: 'Remove Settings',
          description: `Are you sure you want to remove “${row.name}”?`,
          confirmLabel: 'Remove',
          onCallback: async () => {
            try {
              const input = {
                ...omit(['hasChildren', 'channelIds'], use),
                settings: {
                  ...use.settings,
                  rules: (use.settings?.rules ?? []).filter(
                    (rule) => rule.id !== row.id
                  ),
                },
              } satisfies Required<UseInput>

              await updateUseMutation({
                typeId,
                useId: use.id,
                input,
              })

              toast({
                status: 'success',
                msg: `“${row.name}” has been removed.}`,
              })
            } catch (error: unknown) {
              Sentry.captureException(error)
              toast({
                status: 'error',
                msg: `Unable to remove “${row.name}”.`,
              })
            }
          },
        },
      })
    },
    [use, modal, toast, typeId, updateUseMutation]
  )

  const actions = useMemo<ActionProps[]>(
    () => [
      {
        label: 'Edit Settings',
        onClick: onEdit,
        canView: () => true,
      },
      {
        label: 'Remove Settings',
        onClick: onRemove,
        canView: () => true,
      },
    ],
    [onEdit, onRemove]
  )

  return (
    <Table
      sx={{
        tableLayout: 'fixed',
        bgColor: 'white',
        _dark: {
          bgColor: 'gray.900',
        },
      }}
    >
      <Tbody>
        <Tr>
          <Td w="calc(420px - 2rem)">{rule.name}</Td>
          <Td>{verboseCondition()}</Td>
          <Td w="10%" />
          <Td w="10%" textAlign="right">
            <Actions row={rule} actions={actions} />
          </Td>
        </Tr>
      </Tbody>
    </Table>
  )
}
