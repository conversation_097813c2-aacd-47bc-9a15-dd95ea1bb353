import { Box, Flex } from '@chakra-ui/react'
import { components, type OptionProps } from 'react-select'

export const SelectChannelOption = (props: OptionProps<any>) => {
  const { label, channel } = props.data

  return (
    <components.Option {...props}>
      <Flex
        sx={{
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <Box>{label}</Box>
        <Box
          sx={{
            fontSize: 'xs',
            fontFamily: 'mono',
            color: props.isSelected ? 'whiteAlpha.600' : 'blackAlpha.900',
            _dark: {
              color: 'whiteAlpha.900',
            },
          }}
        >
          ch: {channel.padStart(3, '0')}
        </Box>
      </Flex>
    </components.Option>
  )
}
