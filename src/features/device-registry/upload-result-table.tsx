import { useMemo, useCallback } from 'react'
import {
  Box,
  Grid,
  Flex,
  Table,
  Tbody,
  Td,
  Th,
  Thead,
  Tr,
  useColorModeValue,
} from '@chakra-ui/react'
import { registrationStatus as STATUS } from '@/features/device-registry/registration-status'
import type {
  RegistrationResult,
  RegistrationStatProps,
  RegistrationProps,
} from '@/types/models/registry'

export const UploadResultTable = ({
  registrations,
}: {
  registrations: RegistrationResult[]
}) => {
  const errorColor = useColorModeValue('red.600', 'red.200')
  const successColor = useColorModeValue('green.500', 'green.200')
  const totalColor = useColorModeValue('secondary.500', 'secondary.200')

  const stats = useMemo<RegistrationStatProps[]>(
    () => [
      {
        label: 'LNS OK',
        color: successColor,
        num: registrations.reduce(
          (n: number, { activation }) =>
            n + Number(activation.status === STATUS.OK),
          0
        ),
      },
      {
        label: 'LNS Error',
        color: errorColor,
        num: registrations.reduce(
          (n: number, { activation }) =>
            n + Number(activation.status === STATUS.ERROR),
          0
        ),
      },
      {
        label: 'Reg OK',
        color: successColor,
        num: registrations.reduce(
          (n: number, { registration }) =>
            n + Number(registration.status === STATUS.OK),
          0
        ),
      },
      {
        label: 'Reg Error',
        color: errorColor,
        num: registrations.reduce(
          (n: number, { registration }) =>
            n + Number(registration.status === STATUS.ERROR),
          0
        ),
      },
      {
        label: 'Total',
        color: totalColor,
        num: registrations.length,
      },
    ],
    [registrations, successColor, errorColor, totalColor]
  )

  const records = useMemo<{
    count: number
    rows: RegistrationResult[]
  }>(
    () => ({ count: registrations.length, rows: registrations ?? [] }),
    [registrations]
  )

  const resultColors = useCallback(
    ({ status }: RegistrationProps): string => {
      if (status === STATUS.ERROR) {
        return errorColor
      }
      if (status === STATUS.OK) {
        return successColor
      }
      return 'inherit'
    },
    [errorColor, successColor]
  )

  if (registrations.length === 0) {
    return null
  }

  return (
    <Box
      sx={{
        pt: 6,
        gridColumn: '1 / -1',
      }}
    >
      <Grid
        sx={{
          gridTemplateColumns: 'repeat(5, 1fr)',
          justifyItems: 'stretch',
          textAlign: 'center',
          gap: 4,
        }}
      >
        {stats.map(
          ({ label, color, num }: RegistrationStatProps, key: number) => (
            <Flex
              key={key}
              sx={{
                p: 2,
                borderRadius: 'base',
                flexDirection: 'column',
                color: color,
                bg: 'gray.100',
                _dark: {
                  bg: 'whiteAlpha.50',
                },
              }}
            >
              <Box
                sx={{
                  fontSize: 'xs',
                  textTransform: 'uppercase',
                  fontWeight: 'medium',
                }}
              >
                {label}
              </Box>
              <Box
                sx={{
                  fontSize: '2xl',
                  fontWeight: 'bold',
                }}
              >
                {num}
              </Box>
            </Flex>
          )
        )}
      </Grid>
      <Table variant="striped" size="sm" mt={4}>
        <Thead>
          <Tr>
            <Th w="5%" textAlign="center">
              #
            </Th>
            <Th w="30%">Dev EUI</Th>
            <Th w="30%">LNS</Th>
            <Th>Registry</Th>
          </Tr>
        </Thead>
        <Tbody>
          {records.rows.map(
            ({
              number,
              deveui,
              activation,
              registration,
            }: RegistrationResult) => (
              <Tr key={`${number}-${deveui}`}>
                <Td textAlign="center">{number}</Td>
                <Td>{deveui}</Td>
                <Td>{activation.message}</Td>
                <Td color={resultColors(registration)}>
                  {registration.message}
                </Td>
              </Tr>
            )
          )}
        </Tbody>
      </Table>
    </Box>
  )
}
