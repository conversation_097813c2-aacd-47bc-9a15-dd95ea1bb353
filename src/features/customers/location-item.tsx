import * as Sentry from '@sentry/react'
import {
  lazy,
  useCallback,
  type PropsWithChildren,
  type BaseSyntheticEvent,
  Suspense,
} from 'react'
import { Box, HStack, IconButton, useDisclosure } from '@chakra-ui/react'
import { ChevronRightIcon } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { useAbility } from '@/hooks/use-ability'
import { fullAddress } from '@/utils/full-address'
import type { LocationModel } from '@/types/models/location'

const Hierarchy = lazy(() =>
  import('@/features/customers/hierarchy').then(({ Hierarchy }) => ({
    default: Hierarchy,
  }))
)

const DeviceList = lazy(() =>
  import('@/features/customers/device-list').then(({ DeviceList }) => ({
    default: DeviceList,
  }))
)

interface LocationItemProps extends PropsWithChildren {
  location: LocationModel
}

export function LocationItem({ location }: LocationItemProps) {
  const toast = useToast()
  const { can } = useAbility()

  const { isOpen, getButtonProps } = useDisclosure()

  const getAddress = (location: LocationModel): string =>
    fullAddress({
      address: location.address,
      city: location.city,
      zip: location.zip,
      state: location.state ?? '',
      country: location.country,
    })

  const getClipboardText = (location: LocationModel): string =>
    [location.name, getAddress(location)].join(' - ')

  const onClipboard = async (
    event: BaseSyntheticEvent,
    text: string
  ): Promise<void> => {
    event.preventDefault()

    try {
      await navigator.clipboard.writeText(text.trim())
      toast({
        status: 'copy',
        msg: `“${text}” has been copied to clipboard.`,
      })
    } catch (error: unknown) {
      Sentry.captureException(error, {
        fingerprint: ['clipboard'],
        level: 'warning',
      })
    }
  }

  const canView = can(['view', 'applications'])

  const canViewDevices = useCallback(
    ({ totalThings }: LocationModel): boolean => totalThings > 0 && canView,
    [canView]
  )

  return (
    <>
      <HStack
        sx={{
          width: '100%',
          p: '1rem 0.5rem',
          my: 2,
          bg: 'blue.100',
          border: '1px solid',
          borderColor: 'blue.200',
          borderRadius: 'sm',
          _first: {
            mt: 0,
          },
        }}
      >
        <IconButton
          isRound
          size="xs"
          variant="ghost"
          colorScheme="secondary"
          aria-label="Toggle"
          transform={isOpen ? 'rotate(90deg)' : 'rotate(0deg)'}
          icon={<ChevronRightIcon size={16} />}
          {...getButtonProps()}
        />
        <Box>
          <HStack
            spacing={3}
            cursor="pointer"
            onClick={(event) => onClipboard(event, getClipboardText(location))}
          >
            <Box fontWeight={500}>{location.name}</Box>
            <Box
              sx={{
                color: 'blackAlpha.700',
                _dark: {
                  color: 'whiteAlpha.700',
                },
              }}
            >
              -
            </Box>
            <Box fontStyle="italic">{getAddress(location)}</Box>
          </HStack>
        </Box>
      </HStack>
      {isOpen && location.locations && (
        <>
          {canViewDevices(location) ? (
            <Suspense fallback={null}>
              <DeviceList location={location} />
            </Suspense>
          ) : (
            <Box
              sx={{
                p: '1rem',
                fontStyle: 'italic',
                border: '1px solid',
                borderRadius: 'sm',
                borderColor: 'gray.100',
              }}
            >
              {canView
                ? 'You have not added any devices.'
                : 'You have no permission to view devices.'}
            </Box>
          )}
          <Suspense fallback={null}>
            <Hierarchy
              isRoot={location.rootLocation}
              locations={location?.locations}
            />
          </Suspense>
        </>
      )}
    </>
  )
}
