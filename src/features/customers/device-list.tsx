import * as Sentry from '@sentry/react'
import { lazy, Suspense, useCallback, useMemo } from 'react'
import { useAtomValue } from 'jotai'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { Clipboard } from '@/components/data-table/clipboard'
import { DataTable } from '@/components/data-table/data-table'
import { useModal } from '@/hooks/use-modal'
import { useAbility } from '@/hooks/use-ability'
import { useOrganization } from '@/hooks/use-organization'
import { useToast } from '@/hooks/use-toast'
import { tableAtom } from '@/utils/stores/table'
import { getThings, deactivateThing } from '@/api/things/things'
import type { LocationModel } from '@/types/models/location'
import type { ThingModel } from '@/types/models/thing'
import type { PaginatedQueryResponse, DeactivateThingInput } from '@/types/api'
import type { ColumnProps, ActionProps } from '@/types/table'

const ConfirmDialog = lazy(() =>
  import('@/components/confirm-dialog').then(({ ConfirmDialog }) => ({
    default: ConfirmDialog,
  }))
)

const DeviceHistory = lazy(() =>
  import('@/pages/customers/device-history').then(({ DeviceHistory }) => ({
    default: DeviceHistory,
  }))
)

interface DeviceListProps {
  location: LocationModel
}

export function DeviceList({ location }: DeviceListProps) {
  const tableName = `devices-${location.id}`
  const modal = useModal()
  const queryClient = useQueryClient()
  const toast = useToast()
  const { can } = useAbility()
  const { organizationId, applicationId } = useOrganization()
  const { limit, currPage } = useAtomValue(tableAtom(tableName))

  const { mutateAsync: deactivateThingMutation } = useMutation<
    boolean,
    Error,
    DeactivateThingInput
  >({
    mutationFn: (input) => deactivateThing(input),
    onSuccess: () => {
      const cache = ['GetDevices', 'GetRegistries', 'GetThings']
      for (const cacheName of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheName] })
      }
    },
  })

  const { data } = useQuery<PaginatedQueryResponse<ThingModel> | null, Error>({
    queryKey: [
      'GetThings',
      organizationId,
      applicationId,
      location.id,
      limit,
      currPage,
    ],
    queryFn: ({ signal }) =>
      organizationId && applicationId
        ? getThings({
            organizationId,
            applicationId,
            locationId: location.id,
            limit,
            page: currPage,
            signal,
          })
        : null,
    enabled: !!organizationId && !!applicationId,
  })

  const records = useMemo<PaginatedQueryResponse<ThingModel>>(
    () =>
      data ?? {
        count: 0,
        limit: 0,
        page: 0,
        rows: [],
      },
    [data]
  )

  const canViewDevices = useMemo<boolean>(
    () => can(['view', 'applications']),
    [can]
  )

  const canDeactivate = useCallback(
    (row?: ThingModel): boolean =>
      !!row && !row.status && can(['edit', 'applications']),
    [can]
  )

  const onHistory = useCallback(
    (row: ThingModel): void => {
      modal({
        size: '6xl',
        component: <DeviceHistory />,
        scrollBehavior: 'inside',
        config: {
          data: row,
          onCallback: () => ({}),
        },
      })
    },
    [modal]
  )

  const onDeactivate = useCallback(
    ({ hardware_id }: ThingModel) => {
      if (!(organizationId && applicationId)) {
        return
      }

      modal({
        size: 'md',
        component: <ConfirmDialog />,
        config: {
          title: 'Device deactivation',
          description: `Are you sure you want to deactivate “${hardware_id}”?`,
          confirmLabel: 'Deactivate',
          onCallback: async () => {
            try {
              await deactivateThingMutation({
                organizationId,
                applicationId,
                hardwareId: hardware_id,
              })
              toast({
                status: 'success',
                msg: `“${hardware_id}” has been deactivated.`,
              })
            } catch (error: unknown) {
              Sentry.captureException(error)
              toast({
                status: 'error',
                msg: `Unable to deactivate “${hardware_id}”.`,
              })
            }
          },
        },
      })
    },
    [modal, toast, deactivateThingMutation, organizationId, applicationId]
  )

  const columns = useMemo<ColumnProps<ThingModel>[]>(
    () => [
      {
        name: 'Name',
        id: 'thing_name',
        cell: ({ thing_name }) => <Clipboard>{thing_name}</Clipboard>,
      },
      {
        name: 'Hardware ID',
        id: 'hardware_id',
        cell: ({ hardware_id }) => <Clipboard>{hardware_id}</Clipboard>,
      },
    ],
    []
  )

  const actions = useMemo<ActionProps<ThingModel>[]>(
    () => [
      {
        label: 'Device History',
        onClick: onHistory,
        canView: () => canViewDevices,
      },
      {
        label: 'Deactivate Device',
        onClick: onDeactivate,
        canView: (row) => canDeactivate(row),
      },
    ],
    [onHistory, canViewDevices, onDeactivate, canDeactivate]
  )

  return (
    <Suspense fallback={null}>
      <DataTable
        tableName={tableName}
        data={records}
        columns={columns}
        actions={actions}
        disabledRow={({ status }) => status === 1}
        sx={{
          tableLayout: 'fixed',
          borderCollapse: 'collapse',
          borderSpacing: 0,
          zIndex: 'auto',
          tbody: {
            tr: {
              _hover: {
                bg: 'none',
              },
            },
          },
          th: {
            position: 'relative',
            borderBottomWidth: '1px',
            borderBottomColor: 'blackAlpha.50',
            zIndex: 'auto',
          },
          'th, td': {
            paddingLeft: 8,
            backgroundColor: 'gray.50',
            _dark: {
              backgroundColor: 'blackAlpha.50',
            },
          },
          caption: {
            marginTop: '4px',
            backgroundColor: 'transparent',
          },
        }}
      />
    </Suspense>
  )
}
