import * as Sentry from '@sentry/react'
import { lazy, type PropsWithChildren, type BaseSyntheticEvent } from 'react'
import { Box, HStack, IconButton, useDisclosure } from '@chakra-ui/react'
import { ChevronRightIcon } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { fullAddress } from '@/utils/full-address'
import type { LocationModel } from '@/types/models/location'

const DeviceList = lazy(() =>
  import('@/features/customers/device-list').then(({ DeviceList }) => ({
    default: DeviceList,
  }))
)

interface HierarchyProps extends PropsWithChildren {
  isRoot: boolean
  locations?: LocationModel[]
}

export function Hierarchy({ isRoot, locations }: HierarchyProps) {
  const toast = useToast()

  const { isOpen, getButtonProps } = useDisclosure({
    defaultIsOpen: isRoot,
  })

  const getAddress = (location: LocationModel): string =>
    fullAddress({
      address: location.address,
      city: location.city,
      zip: location.zip,
      state: location.state ?? '',
      country: location.country,
    })

  const getClipboardText = (location: LocationModel): string =>
    [location.name, getAddress(location)].join(' - ')

  const onClipboard = async (
    event: BaseSyntheticEvent,
    text: string
  ): Promise<void> => {
    event.preventDefault()

    try {
      await navigator.clipboard.writeText(text.trim())
      toast({
        status: 'copy',
        msg: `“${text}” has been copied to clipboard.`,
      })
    } catch (error: unknown) {
      Sentry.captureException(error, {
        fingerprint: ['clipboard'],
        level: 'warning',
      })
    }
  }

  if (!locations) {
    return null
  }

  return (
    <>
      {locations?.map((loc) => (
        <Box
          key={loc.id}
          sx={{
            width: '100%',
            p: `0 0 0 ${isRoot ? 2 : 3}rem`,
          }}
        >
          <HStack
            sx={{
              width: '100%',
              p: '1rem 0.5rem',
              my: 2,
              bg: 'blue.100',
              border: '1px solid',
              borderColor: 'blue.200',
              borderRadius: 'sm',
            }}
          >
            <IconButton
              isRound
              size="xs"
              variant="ghost"
              colorScheme="secondary"
              aria-label="Toggle"
              transform={isOpen ? 'rotate(90deg)' : 'rotate(0deg)'}
              icon={<ChevronRightIcon size={16} />}
              {...getButtonProps()}
            />
            <Box>
              <HStack
                spacing={3}
                cursor="pointer"
                onClick={(event) => onClipboard(event, getClipboardText(loc))}
              >
                <Box fontWeight={500}>{loc.name}</Box>
                <Box
                  sx={{
                    color: 'blackAlpha.700',
                    _dark: {
                      color: 'whiteAlpha.700',
                    },
                  }}
                >
                  -
                </Box>
                <Box fontStyle="italic">{getAddress(loc)}</Box>
              </HStack>
            </Box>
          </HStack>
          {isOpen && loc.locations && (
            <Box>
              {loc.totalThings > 0 ? (
                <DeviceList location={loc} />
              ) : (
                <Box
                  sx={{
                    p: '1rem',
                    fontStyle: 'italic',
                    border: '1px solid',
                    borderRadius: 'sm',
                    borderColor: 'gray.100',
                  }}
                >
                  You have not added any devices.
                </Box>
              )}
              <Hierarchy isRoot={loc.rootLocation} locations={loc.locations} />
            </Box>
          )}
        </Box>
      ))}
    </>
  )
}
