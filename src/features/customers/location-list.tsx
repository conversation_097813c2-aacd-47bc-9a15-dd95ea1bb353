import {
  lazy,
  Suspense,
  Fragment,
  useMemo,
  type PropsWithChildren,
} from 'react'
import { useAtom } from 'jotai'
import { useQuery } from '@tanstack/react-query'
import { Pagination } from '@/components/data-table/pagination'
import { useOrganization } from '@/hooks/use-organization'
import { tableAtom } from '@/utils/stores/table'
import { getLocations } from '@/api/locations'
import type { CustomerModel } from '@/types/models/customer'
import type { LocationModel } from '@/types/models/location'
import type { PaginatedQueryResponse } from '@/types/api'

const LocationItem = lazy(() =>
  import('@/features/customers/location-item').then(({ LocationItem }) => ({
    default: LocationItem,
  }))
)

interface LocationListProps extends PropsWithChildren {
  row: CustomerModel
}

export function LocationList({ row }: LocationListProps) {
  const tableName = `locations-${row.id}`
  const { organizationId, applicationId } = useOrganization()
  const [{ limit, currPage }] = useAtom(tableAtom(tableName))

  const userId = useMemo<string>(() => row?.tina_user_id, [row])

  const { data } = useQuery<
    PaginatedQueryResponse<LocationModel> | null,
    Error
  >({
    queryKey: [
      'GetLocations',
      organizationId,
      applicationId,
      userId,
      limit,
      currPage,
    ],
    queryFn: ({ signal }) =>
      organizationId && applicationId && userId
        ? getLocations({
            organizationId,
            applicationId,
            userId,
            limit,
            page: currPage,
            signal,
          })
        : null,
    enabled: !!organizationId && !!applicationId && !!userId,
  })

  const count = useMemo<number>(() => data?.count ?? 0, [data])

  return (
    <>
      {(data?.rows ?? []).map((location) => (
        <Fragment key={location.id}>
          <Suspense fallback={null}>
            <LocationItem location={location} />
          </Suspense>
        </Fragment>
      ))}
      {count > limit ? (
        <Pagination tableName={tableName} count={count} />
      ) : null}
    </>
  )
}
