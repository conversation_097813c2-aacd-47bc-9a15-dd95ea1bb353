import { useMemo } from 'react'
import { useWatch, type Control } from 'react-hook-form'
import { <PERSON><PERSON><PERSON><PERSON>, StackDivider, SimpleGrid, Box } from '@chakra-ui/react'
import type { FormInputProps } from '@/features/builder/utils/schema/types'

interface PreviewSummaryProps {
  control: Control<any, any>
}

export const PreviewSummary = ({ control }: PreviewSummaryProps) => {
  const values = useWatch<FormInputProps>({
    control,
  })

  const summary = useMemo(
    () => [
      { name: 'Build Mode', value: values.buildMode },
      { name: 'Organization ID', value: values.orgId },
      { name: 'Application ID', value: values.appId },
      { name: 'Theme ID', value: values.themeId },
      { name: 'Brand Name', value: values.brandName },
      { name: 'Company Name', value: values.company },
      { name: 'First Name', value: values.firstName },
      { name: 'Last Name', value: values.lastName },
      { name: 'Email', value: values.email },
      { name: 'Default Language', value: values.locale },
      { name: 'App Icon Padding', value: `${values.iconPadding}%` },
      { name: 'Icon Background', value: values.iconBackground },
      { name: 'Heading Font', value: values.headingFont },
      { name: 'Body Font', value: values.bodyFont },
      { name: 'Primary Color', value: values.primaryColor },
      { name: 'Secondary Color', value: values.secondaryColor },
      { name: 'Primary Color Map', value: values.primaryColorMap },
      { name: 'Secondary Color Map', value: values.secondaryColorMap },
      { name: 'Background Color', value: values.backgroundColor },
      { name: 'Text Color', value: values.textColor },
      { name: 'Button Roundness', value: values.buttonRoundness },
      {
        name: 'Link Text Decoration',
        value: values.linkTextDecoration,
      },
      { name: 'Dashboard Logo Height', value: values.dashboardLogoHeight },
      { name: 'Start Page Logo Height', value: values.startLogoHeight },
      { name: 'App Icon Size', value: values.appIconSize },
      {
        name: 'Use Background',
        value: values.useBackground ? 'Yes' : 'No',
      },
      {
        name: 'Background Image Position',
        value: values.useBackground
          ? values.background?.backgroundPosition
          : '',
      },
      {
        name: 'Background Image Attachment',
        value: values.useBackground
          ? values.background?.backgroundAttachment
          : '',
      },
      {
        name: 'Background Image Repeat',
        value: values.useBackground ? values.background?.backgroundRepeat : '',
      },
      {
        name: 'Background Image Size',
        value: values.useBackground ? values.background?.backgroundSize : '',
      },
    ],
    [values]
  )

  return (
    <VStack
      spacing={2}
      align="stretch"
      divider={
        <StackDivider
          sx={{
            borderColor: 'blackAlpha.300',
            borderStyle: 'dashed',
          }}
        />
      }
    >
      {summary.map(({ name, value }) => (
        <SimpleGrid columns={2} spacing={2} key={name}>
          <Box>{name}</Box>
          <Box>{value}</Box>
        </SimpleGrid>
      ))}
    </VStack>
  )
}
