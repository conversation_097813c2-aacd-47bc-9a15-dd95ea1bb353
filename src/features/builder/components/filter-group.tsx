import { memo } from 'react'
import { HStack } from '@chakra-ui/react'
import { FilterButton } from '@/features/builder/components/filter-button'

const filters = [
  { label: 'All logs', value: null },
  { label: 'Job', value: 'job' },
  { label: 'Errors', value: 'error' },
]

export const FilterGroup = memo(() => (
  <HStack gap={2} p={3}>
    {filters.map((props) => (
      <FilterButton {...props} key={props.value} />
    ))}
  </HStack>
))
