import { useAtomValue } from 'jotai'
import { useWatch, type Control } from 'react-hook-form'
import { Box } from '@chakra-ui/react'
import { InputField } from '@/components/input-field'
import { SelectField } from '@/components/select-field'
import { DropZoneField } from '@/components/drop-zone-field'
import { ColorPickerField } from '@/components/color-picker-field/color-picker-field'
import { NumberField } from '@/components/number-field'

import { BuilderPanel } from '@/features/builder/components/builder-panel'
import { isFetchingAtom } from '@/features/builder/utils/atoms'
import type { FormInputProps } from '@/features/builder/utils/schema/types'
import { backgroundOptions } from '@/features/builder/utils/options'

interface Props {
  control: Control<any, any>
}

export const SectionUi = ({ control }: Props) => {
  const isFetching = useAtomValue(isFetchingAtom)

  const useBackground = useWatch<FormInputProps, 'useBackground'>({
    name: 'useBackground',
    defaultValue: false,
    control,
  })

  return (
    <BuilderPanel title="Start Page" index={4}>
      <ColorPickerField
        name="customButtonColor"
        label="Custom Button Color"
        control={control}
        isDisabled={isFetching}
      />
      <ColorPickerField
        name="customLinkColor"
        label="Custom Link Color"
        control={control}
        isDisabled={isFetching}
      />
      <ColorPickerField
        name="customTextColor"
        label="Custom Text Color"
        control={control}
        isDisabled={isFetching}
      />
      <NumberField
        name="startLogoHeight"
        label="Start Logo Height"
        control={control}
        isDisabled={isFetching}
        isRequired
      />
      <NumberField
        name="dashboardLogoHeight"
        label="Dashboard Logo Height"
        type="number"
        control={control}
        isDisabled={isFetching}
        isRequired
      />
      <NumberField
        name="appIconSize"
        label="App Icon Size"
        type="number"
        control={control}
        isDisabled={isFetching}
        isRequired
      />
      <SelectField
        name="useBackground"
        label="Background"
        control={control}
        options={backgroundOptions}
        isDisabled={isFetching}
        menuPosition="fixed"
      />
      <Box
        sx={{
          display: useBackground ? 'grid' : 'none',
          gridTemplateColumns: 'repeat(2, 1fr)',
          gridColumn: '1 / -1',
          gap: 4,
        }}
      >
        <DropZoneField
          name="background.backgroundImageFile"
          label="Background Image File"
          control={control}
          height={100}
          shortInfo="Supported files; PNG, JPG."
          accept={{
            'image/jpg': ['.jpg'],
            'image/jpeg': ['.jpeg'],
            'image/png': ['.png'],
          }}
          sx={{
            gridColumn: '1 / -1',
          }}
        />
        <InputField
          name="background.backgroundImage"
          label="Background Image"
          control={control}
          isRequired={useBackground}
          shortInfo="Gradient and/or image URL."
          longInfo="URL to a background image or gradient. e.g. linear-gradient(to bottom, rgba(0,0,0,0.6), rgba(0,0,0,0.5)), url('https://s3.amazonaws.com/mydevicescdn.com/web/assets/iotinabox/background.jpg')"
          sx={{
            gridColumn: '1 / -1',
          }}
        />
        <InputField
          name="background.backgroundPosition"
          label="Background Position"
          control={control}
          longInfo="Only used with background URLs e.g. center center, top left, 50% 50% etc."
        />
        <InputField
          name="background.backgroundAttachment"
          label="Background Attachment"
          control={control}
          longInfo="Only used with background URLs e.g. fixed, local etc."
        />
        <InputField
          name="background.backgroundRepeat"
          label="Background Repeat"
          control={control}
          longInfo="Only used with background URLs e.g. repeat, no-repeat, repeat-x etc."
        />
        <InputField
          name="background.backgroundSize"
          label="Background Size"
          control={control}
          longInfo="Only used with background URLs e.g. cover, contain, 100% 100% etc."
        />
      </Box>
    </BuilderPanel>
  )
}
