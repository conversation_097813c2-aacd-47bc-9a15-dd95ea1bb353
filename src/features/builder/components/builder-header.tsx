import { memo } from 'react'
import { useNavigate } from 'react-router'
import { Box, Flex, GridItem } from '@chakra-ui/react'
import { BackButton } from '@/components/back-button'
import { UrlLogo } from '@/components/url-logo'

export const BuilderHeader = memo(() => {
  const navigate = useNavigate()

  const onBack = () => {
    navigate('/manage/customers', {
      viewTransition: true,
    })
  }

  return (
    <GridItem
      sx={{
        overflow: 'auto',
        borderBottomWidth: '2px',
        borderBottomStyle: 'solid',
        borderBottomColor: 'blackAlpha.300',
        bg: 'white',
        _dark: {
          bg: 'gray.900',
        },
      }}
    >
      <Flex
        sx={{
          justifyContent: 'flex-start',
          alignItems: 'center',
          h: '100%',
        }}
      >
        <Box pl={2}>
          <BackButton
            label="Go Back"
            colorScheme="gray"
            onClick={onBack}
            sx={{
              _hover: {
                color: 'primary.500',
                _dark: {
                  color: 'primary.200',
                },
              },
            }}
          />
        </Box>
        <Box w="200px">
          <UrlLogo />
        </Box>
      </Flex>
    </GridItem>
  )
})
