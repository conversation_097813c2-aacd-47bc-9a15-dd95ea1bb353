import { useMemo } from 'react'
import { Helmet } from 'react-helmet-async'
import chroma from 'chroma-js'
import { isEmpty } from 'ramda'
import { useAtomValue } from 'jotai'
import { useFormState, useWatch, type Control } from 'react-hook-form'
import {
  <PERSON>,
  Link,
  HStack,
  ChakraProvider,
  extendTheme,
} from '@chakra-ui/react'
import { useAuth } from '@/contexts/use-auth'
import { useOrganization } from '@/hooks/use-organization'
import { simplePalette } from '@/utils/simple-palette'
import { fallbackColors } from '@/utils/theme/fallback-theme'
import { trans64Bg } from '@/utils/constants'

import type { FormInputProps } from '@/features/builder/utils/schema/types'
import { jsonThemeAtom } from '@/features/builder/utils/atoms'
import { themeColorsVars } from '@/features/builder/utils/theme-colors-vars'
import { PreviewStartPage } from '@/features/builder/components/preview-start-page'
import { PreviewDashboard } from '@/features/builder/components/preview-dashboard'
import { PreviewPending } from '@/features/builder/components/preview-pending'
import { PreviewHeading } from '@/features/builder/components/preview-heading'
import { PreviewIcon } from '@/features/builder/components/preview-icon'
import { PreviewEmailLogo } from '@/features/builder/components/preview-email-logo'
import { PreviewDiffViewer } from '@/features/builder/components/preview-diff-viewer'

interface Props {
  control: Control<any, any>
}

export const BuilderPreviewSimple = ({ control }: Props) => {
  const jsonTheme = useAtomValue(jsonThemeAtom)
  const { applicationId } = useOrganization()
  const { isInternalAdmin } = useAuth()
  const { isLoading, isValidating, isValid } = useFormState<FormInputProps>()

  const values = useWatch<FormInputProps>({
    control,
  })

  const domain = useMemo(
    () => (values?.url ? new URL(values.url).hostname : null),
    [values]
  )

  const colors = useMemo(() => {
    const primary =
      !values?.primaryColor || isEmpty(values?.primaryColor)
        ? fallbackColors.primary
        : values?.primaryColor

    const secondary =
      !values?.secondaryColor || isEmpty(values?.secondaryColor)
        ? fallbackColors.secondary
        : values?.secondaryColor

    let base = {
      ...fallbackColors,
      primary,
      secondary,
    }

    if (
      values.primaryColorMap &&
      Object.keys(fallbackColors).includes(values.primaryColorMap)
    ) {
      base = {
        ...base,
        [values.primaryColorMap]: primary,
      }
    }

    if (
      values.secondaryColorMap &&
      Object.keys(fallbackColors).includes(values.secondaryColorMap)
    ) {
      base = {
        ...base,
        [values.secondaryColorMap]: secondary,
      }
    }

    return base
  }, [values])

  const validColors = useMemo<boolean>(
    () =>
      [
        values.textColor,
        values.backgroundColor,
        values.customButtonColor,
        values.customLinkColor,
        values.customTextColor,
        ...Object.values(colors),
      ]
        .filter((color) => !isEmpty(color))
        .every((color) => chroma.valid(color)),
    [colors, values]
  )

  const palette = useMemo(
    () => (validColors ? simplePalette(colors) : null),
    [colors, validColors]
  )

  const cssVars = useMemo(
    () =>
      palette
        ? themeColorsVars({
            colors: palette,
            prefix: 'ck',
          })
        : null,
    [palette]
  )

  const fontUrl = useMemo(() => {
    try {
      if (!jsonTheme) {
        return null
      }
      const theme = JSON.parse(jsonTheme)
      return theme?.font_url ?? null
    } catch {
      return null
    }
  }, [jsonTheme])

  const faviconBgStyle = useMemo(
    () =>
      isEmpty(values?.iconBackground)
        ? {
            bg: `url(${trans64Bg}) 0 0/16px 16px`,
            bgColor: 'gray.50',
            _dark: {
              bgColor: 'gray.900',
            },
          }
        : {
            bgColor: values.iconBackground,
          },
    [values.iconBackground]
  )

  const nestedTheme = useMemo(
    () =>
      extendTheme({
        config: {
          cssVarPrefix: 'ck',
          initialColorMode: 'light',
          useSystemColorMode: false,
        },
        styles: {
          global: () => ({
            ':root': {
              ...cssVars,
            },
          }),
        },
        fonts: {
          body: values.bodyFont,
          heading: values.headingFont,
        },
        colors: {
          ...palette,
        },
      }),
    [cssVars, palette, values]
  )

  if (isLoading || isValidating || !isValid || !validColors || !fontUrl) {
    return <PreviewPending />
  }

  return (
    <>
      <Helmet>{fontUrl && <link href={fontUrl} rel="stylesheet" />}</Helmet>
      <ChakraProvider
        theme={nestedTheme}
        resetCSS={false}
        disableGlobalStyle
        cssVarsRoot="preview-root"
      >
        <Box
          id="preview-root"
          sx={{
            h: 'calc(100vh - 124px)',
            overflow: 'auto',
            px: 4,
          }}
        >
          <Box pb={8}>
            <PreviewHeading>
              {domain ? (
                <>
                  <Box as="span" pr={1}>
                    {'Preview for'}
                  </Box>
                  <Link href={`https://${domain}`} isExternal>
                    {domain}
                  </Link>
                </>
              ) : (
                <Box as="span">{'Preview'}</Box>
              )}
            </PreviewHeading>

            <PreviewStartPage control={control} hasHeader={false} />
            <PreviewDashboard />
            <PreviewEmailLogo control={control} />

            {values.icon && (
              <>
                <PreviewHeading>App Icon</PreviewHeading>
                <HStack spacing={2} align="stretch">
                  <PreviewIcon
                    src={values.icon}
                    padding={values.iconPadding}
                    sx={{
                      borderRadius: 'lg',
                      ...faviconBgStyle,
                    }}
                  />
                  <PreviewIcon
                    src={values.icon}
                    padding={values.iconPadding}
                    sx={{
                      borderRadius: 'full',
                      ...faviconBgStyle,
                    }}
                  />
                  <PreviewIcon
                    src={values.icon}
                    padding={values.iconPadding}
                    sx={{
                      borderRadius: 'full',
                      boxSize: '32px',
                      ...faviconBgStyle,
                    }}
                  />
                </HStack>
              </>
            )}

            {isInternalAdmin && (
              <PreviewDiffViewer applicationId={applicationId} />
            )}
          </Box>
        </Box>
      </ChakraProvider>
    </>
  )
}
