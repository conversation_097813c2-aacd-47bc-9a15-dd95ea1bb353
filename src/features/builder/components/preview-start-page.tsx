import { useMemo } from 'react'
import { useAtomValue } from 'jotai'
import { useWatch, type Control } from 'react-hook-form'
import { Box, Flex, Icon, Button, Heading, Text, Link } from '@chakra-ui/react'
import { MapPinIcon, ChevronLeftIcon, ChevronRightIcon } from 'lucide-react'
import { extractCssUrls } from '@/features/builder/utils/extract-css-urls'
import { replaceCssUrls } from '@/features/builder/utils/replace-css-urls'
import { jsonThemeSchema } from '@/features/builder/utils/schema/json-theme'
import { jsonThemeAtom } from '@/features/builder/utils/atoms'
import { PreviewHeading } from '@/features/builder/components/preview-heading'
import type {
  JsonThemeProps,
  FormInputProps,
} from '@/features/builder/utils/schema/types'

interface CarouselProps {
  theme: JsonThemeProps
}

const Carousel = ({ theme }: CarouselProps) => (
  <>
    <Box
      sx={{
        position: 'relative',
        border: 0,
      }}
    >
      <Icon
        as={MapPinIcon}
        sx={{
          boxSize: '90px',
          verticalAlign: 'bottom',
          color: theme.custom_colors.start_page_carousel_icon_color,
        }}
      />
      <Heading
        as="h4"
        sx={{
          fontFamily: theme.fonts?.heading ?? 'heading',
          fontSize: '4xl',
          lineHeight: 1.2,
          color: theme.custom_colors.start_page_carousel_heading_color,
        }}
      >
        Sensor Maps
      </Heading>
      <Text
        sx={{
          fontFamily: theme.fonts?.body ?? 'body',
          color: theme.custom_colors.start_page_carousel_text_color,
        }}
      >
        Upload your own image and visualize sensor data
      </Text>
      <Flex
        sx={{
          position: 'absolute',
          top: 0,
          bottom: 0,
          width: '100%',
          justifyContent: 'space-between',
          alignItems: 'center',
          color: theme.custom_colors.start_page_carousel_action_color,
          userSelect: 'none',
        }}
      >
        <Icon as={ChevronLeftIcon} boxSize="20px" />
        <Icon as={ChevronRightIcon} boxSize="20px" />
      </Flex>
    </Box>
    <Flex
      sx={{
        pt: 8,
        justifyContent: 'center',
        alignItems: 'center',
        gap: '0.5rem',
      }}
    >
      {Array.from({ length: 5 }).map((_, index) => (
        <Box
          key={index}
          sx={{
            boxSize: '0.5rem',
            borderRadius: '100%',
            bg: theme.custom_colors.start_page_carousel_action_color,
            _hover: {
              bg: theme.custom_colors.start_page_carousel_action_color,
            },
            _active: {
              bg: theme.custom_colors.start_page_carousel_action_color,
            },
          }}
        />
      ))}
    </Flex>
  </>
)

interface PreviewStartPageProps {
  control: Control<any, any>
  hasHeader?: boolean
}

export const PreviewStartPage = ({
  control,
  hasHeader = true,
}: PreviewStartPageProps) => {
  const jsonTheme = useAtomValue(jsonThemeAtom)

  const logo = useWatch<FormInputProps, 'logo'>({
    name: 'logo',
    control,
  })

  const backgroundImageFile = useWatch<
    FormInputProps,
    'background.backgroundImageFile'
  >({
    name: 'background.backgroundImageFile',
    control,
  })

  const theme = useMemo(() => {
    try {
      if (!jsonTheme) return null
      const re = /--chakra/gi
      return jsonThemeSchema.parse(JSON.parse(jsonTheme.replace(re, '--ck')))
    } catch {
      return null
    }
  }, [jsonTheme])

  const backgroundCss = useMemo<string>(() => {
    const css = theme?.start_page.background_image
    if (css && backgroundImageFile) {
      const urls = extractCssUrls(theme.start_page.background_image)
      return replaceCssUrls(theme.start_page.background_image, urls, [
        backgroundImageFile,
      ])
    }
    return ''
  }, [theme, backgroundImageFile])

  if (!theme) return ''

  return (
    <>
      {hasHeader && <PreviewHeading>Start Page Preview</PreviewHeading>}
      <Flex
        sx={{
          pt: 8,
          position: 'relative',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          borderRadius: 'base',
          bgImage: backgroundCss,
          bgPosition: theme.start_page.background_position,
          bgRepeat: theme.start_page.background_repeat,
          bgSize: theme.start_page.background_size,
          bgColor: theme.custom_colors.start_page_background_color,
        }}
      >
        <Flex
          sx={{
            flexDir: 'column',
            w: '100%',
            maxW: '600px',
            alignItems: 'center',
          }}
        >
          <Flex
            sx={{
              position: 'relative',
              h: `${Number.parseInt(`${theme.start_page.logo_height}`) ?? 120}px`,
              w: '100%',
              maxW: '230px',
              justifyContent: 'center',
              alignItems: 'center',
              bg: `url(${logo}) no-repeat 50% 50%`,
              bgSize: 'contain',
            }}
          />
          <Box
            sx={{
              textAlign: 'center',
              w: '100%',
              m: 'auto',
              p: '2rem',
            }}
          >
            <Carousel theme={theme} />
          </Box>
          <Flex
            sx={{
              w: '100%',
              flexWrap: 'wrap',
              p: '2rem',
            }}
          >
            <Button
              variant="solid"
              size="lg"
              sx={{
                w: '100%',
                fontFamily: theme.fonts.body ?? 'body',
                borderRadius:
                  Number.parseInt(`${theme.start_page.button_roundness}`) ??
                  'md',
                color: theme.custom_colors.start_page_button_color,
                bg: theme.custom_colors.start_page_button_background_color,
                _hover: {
                  bg: theme.custom_colors
                    .start_page_button_background_hover_color,
                },
                _active: {
                  bg: theme.custom_colors
                    .start_page_button_background_hover_color,
                },
              }}
            >
              Create Account
            </Button>
            <Link
              variant="solid"
              size="lg"
              sx={{
                pt: '1rem',
                fontWeight: 600,
                fontFamily: theme.fonts.body ?? 'body',
                color: theme.custom_colors.start_page_link_color,
                textDecoration: theme.link_text_decoration,
                userSelect: 'none',
                _hover: {
                  color: theme.custom_colors.start_page_link_hover_color,
                },
                _active: {
                  color: theme.custom_colors.start_page_link_hover_color,
                },
              }}
            >
              Sign In
            </Link>
          </Flex>
        </Flex>
      </Flex>
    </>
  )
}
