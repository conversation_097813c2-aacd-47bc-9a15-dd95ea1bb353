import { memo, useState, useEffect } from 'react'
import { useRerender } from '@react-hookz/web'
import { useFormContext, useWatch } from 'react-hook-form'
import { useQueryClient, useQuery } from '@tanstack/react-query'
import { use<PERSON>tom, useSet<PERSON>tom, useAtomValue } from 'jotai'
import { z } from 'zod'
import { jsonrepair } from 'jsonrepair'
import { mergeDeepRight, isEmpty } from 'ramda'
import { Grid } from '@chakra-ui/react'
import { InputField } from '@/components/input-field'
import { SelectField } from '@/components/select-field'
import { SelectOrganizationField } from '@/components/select-organization-field'
import { useToast } from '@/hooks/use-toast'
import { LANGUAGES } from '@/data/languages'
import { getApplications } from '@/api/applications'
import { getCurrentUser } from '@/api/users'

import { BuilderPanel } from '@/features/builder/components/builder-panel'
import type { FormInputProps } from '@/features/builder/utils/schema/types'
import { jsonThemeSchema } from '@/features/builder/utils/schema/json-theme'
import { jsonThemePolyfill } from '@/features/builder/utils/json-theme-polyfill'
import { substituteFont } from '@/features/builder/utils/substitute-font'
import { extractCssUrls } from '@/features/builder/utils/extract-css-urls'
import { defaultValues } from '@/features/builder/utils/default-values'
import { convertColor } from '@/features/builder/utils/convert-color'
import { getSetting, getAdmin, getSvgIcon } from '@/api/builder'
import { buildModeOptions } from '@/features/builder/utils/options'
import {
  panelSizeAtom,
  jsonThemeAtom,
  isFetchingAtom,
} from '@/features/builder/utils/atoms'

const htmlRegex = /"([^"]*)"/
const isProd = import.meta.env.VITE_DB_ENV === 'production'

export const SectionBrand = memo(() => {
  const toast = useToast()
  const queryClient = useQueryClient()
  const [appOptions, setAppOptions] = useState<BaseOption<string>[]>([])
  const setJsonTheme = useSetAtom(jsonThemeAtom)
  const panelSize = useAtomValue(panelSizeAtom)
  const [isFetching, setFetching] = useAtom(isFetchingAtom)
  const rerender = useRerender()

  const { trigger, control, reset, setValue, getValues } =
    useFormContext<FormInputProps>()

  const buildMode = useWatch<FormInputProps, 'buildMode'>({
    name: 'buildMode',
    control,
  })

  const orgInheritId = useWatch<FormInputProps, 'orgInheritId'>({
    name: 'orgInheritId',
    control,
  })

  const appInheritId = useWatch<FormInputProps, 'appInheritId'>({
    name: 'appInheritId',
    control,
  })

  const orgId = useWatch<FormInputProps, 'orgId'>({ name: 'orgId', control })

  const appId = useWatch<FormInputProps, 'appId'>({ name: 'appId', control })

  const { data: currentUser } = useQuery({
    queryKey: ['GetCurrentUser'],
    queryFn: ({ signal }) => getCurrentUser({ signal }),
  })

  useEffect(() => {
    if (currentUser) {
      setValue('firstName', currentUser.firstName)
      setValue('lastName', currentUser.lastName)
      setValue(
        'email',
        isProd ? currentUser.email : `${appId.replace(/\$/g, '-')}@yopmail.com`
      )
    }
  }, [currentUser, setValue, appId])

  const onChangeBuildMode = (value: string) => {
    rerender() // force rerender to update the UI
    setJsonTheme(null)
    reset({
      ...defaultValues,
      buildMode: value,
      firstName: currentUser?.firstName ?? '',
      lastName: currentUser?.lastName ?? '',
      email: currentUser?.email ?? '',
    })
  }

  const svgIcon = async (appId: string, url: string) => {
    const fileName = url.split('/').pop()
    if (!fileName) return ''
    return await queryClient.fetchQuery({
      queryKey: ['GetSvgIcon', appId, fileName],
      queryFn: ({ signal }) => getSvgIcon({ appId, fileName, signal }),
    })
  }

  const isUpdateMode = buildMode === 'update-application'

  const loadInherited =
    buildMode === 'update-application' || buildMode.startsWith('new-inherited')

  const isUserRequired = buildMode.endsWith('organization')

  const buildDatabaseTheme = async (value: string) => {
    const values = getValues()

    try {
      // fetch current settings
      let setting = await queryClient.fetchQuery({
        queryKey: ['GetSetting', value],
        queryFn: ({ signal }) => getSetting({ applicationId: value, signal }),
      })

      // collect all data
      let collector = {
        ...defaultValues,
        buildMode: values.buildMode,
        orgInheritId: values.orgInheritId,
        appInheritId: value,
        orgId: values.orgId,
        appId: value,
        themeId: setting.theme_id ?? value,
        url: setting.mydevices_url ?? '',
        brandName: setting.brand_name ?? '',
        company: setting.company ?? '',
        locale: setting.default_language ?? '',
      } satisfies FormInputProps

      const user = await queryClient.fetchQuery({
        queryKey: ['GetAdmin', orgInheritId, appId],
        queryFn: ({ signal }) =>
          getAdmin({
            organizationId: values.orgInheritId ?? '',
            applicationId: value,
            signal,
          }),
      })

      collector = {
        ...collector,
        firstName: user?.firstName ?? '',
        lastName: user?.lastName ?? '',
        email: user?.email ?? '',
      } satisfies FormInputProps

      const themeId = setting.theme_id ?? value

      if (themeId !== value) {
        setting = await queryClient.fetchQuery({
          queryKey: ['GetSettings', themeId],
          queryFn: ({ signal }) =>
            getSetting({ applicationId: themeId, signal }),
        })

        toast({
          msg: `Fetching inherited theme settings from “${themeId}”`,
          status: 'info',
        })
      } else {
        toast({
          msg: `Fetching theme settings from “${value}”`,
          status: 'info',
        })
      }

      const logo = await svgIcon(themeId, setting?.logo ?? 'logo.svg')
      const emailLogo = await svgIcon(themeId, 'logo-email.svg')
      const dashboardLogo = await svgIcon(themeId, 'logo-dashboard.svg')
      const icon = await svgIcon(themeId, setting?.icon ?? 'app-icon.svg')

      // repair theme and add polyfill due to legacy inconsistencies
      const parsedJson = jsonrepair(setting.theme ?? '{}')
      const theme = jsonThemeSchema.parse(
        mergeDeepRight(jsonThemePolyfill, JSON.parse(parsedJson))
      )

      const heading = theme.fonts.heading.match(htmlRegex)
      const headingFont = heading?.[1] ? substituteFont(heading[1]) : ''

      const body = theme.fonts.body.match(htmlRegex)
      const bodyFont = body?.[1] ? substituteFont(body[1]) : ''

      const colors = Object.entries(theme.colors).reduce(
        (acc: Array<{ name: string; value: string }>, [name, value]) => {
          if (
            name === 'primary' ||
            name === 'secondary' ||
            isEmpty(name?.trim())
          ) {
            return acc
          }
          acc.push({ name, value })
          return acc
        },
        []
      )

      const backgroundUrls = extractCssUrls(theme.start_page.background_image)

      // collect all data
      collector = {
        ...collector,
        logo: logo,
        emailLogo: emailLogo,
        dashboardLogo: dashboardLogo,
        icon: icon,
        iconPadding: theme.meta?.icon_padding ?? 20,
        iconBackground: theme.meta?.icon_background ?? '',
        headingFont,
        bodyFont,
        primaryColor: setting.primary_color ?? '',
        secondaryColor: setting.secondary_color ?? '',
        backgroundColor: setting.background_color ?? '',
        primaryColorMap: theme.meta?.primary_color_map ?? '',
        secondaryColorMap: theme.meta?.secondary_color_map ?? '',
        customButtonColor: convertColor(
          theme.custom_colors.start_page_button_background_color
        ),
        customLinkColor: convertColor(theme.meta?.custom_link_color ?? ''),
        customTextColor: convertColor(theme.meta?.custom_text_color ?? ''),
        appIconSize: theme.start_page.app_icon_size,
        dashboardLogoHeight: theme.dashboard_header.logo_height,
        startLogoHeight: theme.start_page.logo_height,
        buttonRoundness: Number.parseInt(theme.start_page.button_roundness),
        linkTextDecoration: theme.link_text_decoration,
        useBackground: !isEmpty(theme.start_page.background_image),
        background: {
          backgroundImageFile: backgroundUrls?.[0] ?? '',
          backgroundImage: theme.start_page.background_image,
          backgroundPosition: theme.start_page.background_position,
          backgroundAttachment: theme.start_page.background_attachment,
          backgroundRepeat: theme.start_page.background_repeat,
          backgroundSize: theme.start_page.background_size,
        },
        colors,
      } satisfies FormInputProps

      setJsonTheme(JSON.stringify(theme, null, 2))

      // change values depending on build mode
      if (buildMode === 'new-inherited-organization') {
        reset({
          ...collector,
          orgId: '',
          appId: '',
          themeId: collector.orgInheritId ?? '',
          brandName: '',
          company: '',
          firstName: '',
          lastName: '',
          email: '',
        })
      } else if (buildMode === 'new-inherited-application') {
        reset({
          ...collector,
          appId: '',
          themeId: `${collector.orgInheritId}$`,
          brandName: '',
          company: '',
          firstName: '',
          lastName: '',
          email: '',
        })
      } else {
        reset(collector)
      }
    } catch (error: unknown) {
      setJsonTheme(null)

      reset({
        ...defaultValues,
        buildMode: values.buildMode,
        orgInheritId: values.orgInheritId,
        appInheritId: value,
      })

      if (error instanceof z.ZodError) console.error(error.issues)
      else if (error instanceof Error) console.error(error.message)
    } finally {
      trigger()
      setFetching(false)
    }
  }

  const onChangeInheritedApp = async (value: string) => {
    const values = getValues()

    setJsonTheme(null)
    setFetching(true)

    reset({
      ...defaultValues,
      buildMode: values.buildMode,
      orgInheritId: values.orgInheritId,
      appInheritId: value,
      orgId: values.orgInheritId ?? '',
      appId: value,
    })

    await buildDatabaseTheme(value)
  }

  const onChangeInheritedOrg = async (value: string) => {
    setValue('appInheritId', null)
    setValue('appId', '')

    const applications = await queryClient.fetchQuery({
      queryKey: ['GetApplications', value],
      queryFn: ({ signal }) =>
        getApplications({ organizationId: value, signal }),
    })

    const apps = (applications?.rows ?? []).reduce(
      (acc: BaseOption<string>[], { id, name }) => {
        acc.push({ label: name, value: id })
        return acc
      },
      []
    )

    setAppOptions(apps ?? [])

    const firstId = apps?.[0]?.value
    if (!firstId) return

    setValue('orgInheritId', value)
    setValue('orgId', value)
    onChangeInheritedApp(firstId)
  }

  useEffect(() => {
    if (buildMode === 'new-application') {
      setValue('themeId', `${orgId}$${appId}`)
    }
    if (buildMode === 'new-inherited-application' && appInheritId) {
      setValue('themeId', `${appInheritId}`)
    }
  }, [appId, orgId, appInheritId, buildMode, setValue])

  return (
    <BuilderPanel title="Brand" index={1}>
      <SelectField
        name="buildMode"
        label="Build Mode"
        control={control}
        options={buildModeOptions}
        isDisabled={isFetching}
        isRequired
        menuPosition="fixed"
        onChange={({ value }: BaseOption<string>) => onChangeBuildMode(value)}
        sx={{ gridColumn: '1 / -1' }}
      />

      <Grid
        sx={{
          p: 4,
          gridGap: 4,
          gridTemplateColumns:
            panelSize < 30 ? '1fr' : { base: '1fr', lg: 'repeat(2, 1fr)' },
          gridColumn: '1 / -1',
          borderRadius: 'base',
          border: '1px solid',
          bg: 'blackAlpha.50',
          borderColor: 'blackAlpha.200',
          display: loadInherited ? 'grid' : 'none',
        }}
      >
        <SelectOrganizationField
          name="orgInheritId"
          label="Organization"
          control={control}
          menuPosition="fixed"
          onChange={({ value }: any) => onChangeInheritedOrg(value)}
        />
        <SelectField
          name="appInheritId"
          label="Application"
          control={control}
          options={appOptions}
          menuPosition="fixed"
          onChange={({ value }: any) => onChangeInheritedApp(value)}
        />
      </Grid>

      <InputField
        name="orgId"
        label="Organization ID"
        control={control}
        isDisabled={isFetching || isUpdateMode}
        isRequired
      />
      <InputField
        name="appId"
        label="Application ID"
        control={control}
        isDisabled={isFetching || isUpdateMode}
        isRequired
      />
      <InputField
        name="themeId"
        label="Theme ID"
        control={control}
        isDisabled={isFetching || isUpdateMode}
        isRequired
        longInfo="Theme ID defines what application will inherit assets from, in most cases it will just be app ID."
      />
      <InputField
        name="brandName"
        label="Brand Name"
        control={control}
        isDisabled={isFetching}
        isRequired
      />
      <InputField
        name="company"
        label="Company Name"
        control={control}
        isDisabled={isFetching}
        isRequired
      />
      <InputField
        name="firstName"
        label="First Name"
        control={control}
        isDisabled={isFetching}
        isRequired={isUserRequired}
      />
      <InputField
        name="lastName"
        label="Last Name"
        control={control}
        isDisabled={isFetching}
        isRequired={isUserRequired}
      />
      <InputField
        name="email"
        type="email"
        label="Email"
        control={control}
        isDisabled={isFetching}
        isRequired={isUserRequired}
      />
      <SelectField
        name="locale"
        label="Default Language"
        control={control}
        options={LANGUAGES}
        isDisabled={isFetching}
        menuPosition="fixed"
      />
    </BuilderPanel>
  )
})
