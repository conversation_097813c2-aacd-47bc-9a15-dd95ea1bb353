import { useState, useRef, useEffect } from 'react'
import { useAtomValue } from 'jotai'
import { format } from 'date-fns'
import {
  Box,
  Grid,
  VStack,
  Drawer,
  DrawerBody,
  DrawerContent,
  DrawerOverlay,
  useColorModeValue,
} from '@chakra-ui/react'
import { FilterGroup } from '@/features/builder/components/filter-group'
import { filteredLogAtom } from '@/features/builder/utils/atoms'

interface BuilderLogProps {
  isOpen: boolean
  onClose: () => void
}

export const BuilderLog = ({ isOpen, onClose }: BuilderLogProps) => {
  const ref = useRef<HTMLDivElement>(null)
  const log = useAtomValue(filteredLogAtom)
  const [activeRow, setActiveRow] = useState<number>(0)
  const color = useColorModeValue('gray.600', 'white')
  const hover = useColorModeValue('gray.100', 'blackAlpha.100')

  const scrollToBottom = () => {
    if (ref.current) {
      ref.current.scrollTop = ref.current.scrollHeight
    }
  }

  useEffect(() => {
    scrollToBottom()
  }, [log])

  return (
    <Drawer size="xl" placement="right" isOpen={isOpen} onClose={onClose}>
      <DrawerOverlay />
      <DrawerContent>
        <DrawerBody
          sx={{
            p: 0,
            bg: 'white',
            _dark: {
              bg: 'gray.900',
            },
          }}
        >
          <FilterGroup />
          <VStack
            ref={ref}
            spacing={0}
            align="stretch"
            sx={{
              maxHeight: '100vh',
              overflowY: 'auto',
              overflowX: 'hidden',
              p: 2,
            }}
          >
            {log.map((props, key) => (
              <Grid
                onClick={() => setActiveRow(key)}
                key={key}
                sx={{
                  gridTemplateColumns: '120px 70px 120px 1fr',
                  wordBreak: 'break-word',
                  fontFamily: 'mono',
                  fontSize: 'xs',
                  cursor: 'pointer',
                  p: 1,
                  bg: activeRow === key ? 'blue.700' : 'transparent',
                  color: activeRow === key ? 'white' : color,
                  _hover: {
                    bg: activeRow === key ? 'blue.700' : hover,
                    color: activeRow === key ? 'white' : color,
                  },
                  _active: {
                    bg: activeRow === key ? 'blue.700' : hover,
                    color: activeRow === key ? 'white' : color,
                  },
                }}
              >
                <>
                  <Box>{format(props.time ?? 0, 'kk:mm:ss.SSS')}</Box>
                  <Box>{props.type}</Box>
                  <Box>{props.event}</Box>
                  <Box>{props.text}</Box>
                </>
              </Grid>
            ))}
          </VStack>
        </DrawerBody>
      </DrawerContent>
    </Drawer>
  )
}
