import { Box, Flex, SimpleGrid, type Colors } from '@chakra-ui/react'
import { upperFirst } from '@/utils/upper-first'
import { textContrastColor } from '@/features/builder/utils/text-contrast-color'
import { PreviewHeading } from '@/features/builder/components/preview-heading'

interface Props {
  palette: Colors | null
}

export const PreviewPalette = ({ palette }: Props) => {
  if (!palette) {
    return null
  }

  return (
    <>
      <PreviewHeading>Palette</PreviewHeading>
      {Object.entries(palette).map(([name, shades]) => (
        <SimpleGrid
          columns={2}
          spacing={0}
          key={name}
          sx={{
            p: 2,
            my: 2,
            borderRadius: 'base',
            alignItems: 'center',
            justifyContent: 'space-between',
            bg: 'blackAlpha.50',
          }}
        >
          <Box>{upperFirst(name)}</Box>
          <SimpleGrid columns={10} spacing={1} key={name}>
            {Object.entries(shades).map(([shade, hex]) => (
              <Flex
                key={shade}
                sx={{
                  p: 2,
                  h: 10,
                  fontSize: '7px',
                  fontFamily: 'mono',
                  borderRadius: 'base',
                  boxShadow: 'sm',
                  bg: hex.toString(),
                  justifyContent: 'center',
                  alignItems: 'center',
                  color: textContrastColor(hex as string),
                }}
              >
                {shade}
              </Flex>
            ))}
          </SimpleGrid>
        </SimpleGrid>
      ))}
    </>
  )
}
