import { useMemo } from 'react'
import { useAtomValue } from 'jotai'
import { jsonrepair } from 'jsonrepair'
import { DiffViewer } from '@/components/diff-viewer/diff-viewer'
import { useQuery } from '@tanstack/react-query'
import { Box } from '@chakra-ui/react'
import { PreviewHeading } from '@/features/builder/components/preview-heading'
import { jsonThemeAtom } from '@/features/builder/utils/atoms'
import { getSetting } from '@/api/builder'

interface Props {
  applicationId?: string | null
}

export const PreviewDiffViewer = ({ applicationId }: Props) => {
  const jsonTheme = useAtomValue(jsonThemeAtom)

  const { data: prevTheme } = useQuery({
    queryKey: ['getSettings', applicationId],
    queryFn: ({ signal }) =>
      applicationId
        ? getSetting({
            applicationId,
            signal,
          })
        : null,
    select: (data) => {
      try {
        const theme = data?.theme
        if (!theme) {
          return null
        }
        return JSON.parse(jsonrepair(theme))
      } catch {
        return null
      }
    },
  })

  const theme = useMemo(
    () => (jsonTheme ? JSON.parse(jsonTheme) : null),
    [jsonTheme]
  )

  if (!theme) {
    return null
  }

  return (
    <>
      <PreviewHeading>Theme.json</PreviewHeading>
      <Box
        sx={{
          bg: 'blackAlpha.50',
          borderRadius: 'base',
          overflow: 'hidden',
          py: 0,
        }}
      >
        <DiffViewer
          oldValue={JSON.stringify(prevTheme ?? theme, null, 2)}
          newValue={JSON.stringify(theme, null, 2)}
        />
      </Box>
    </>
  )
}
