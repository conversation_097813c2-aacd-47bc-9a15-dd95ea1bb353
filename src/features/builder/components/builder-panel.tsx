import type { PropsWithChildren } from 'react'
import { useAtomValue } from 'jotai'
import {
  Box,
  Grid,
  Icon,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
} from '@chakra-ui/react'
import { PlusIcon, MinusIcon, SquircleIcon } from 'lucide-react'
import { panelSizeAtom } from '@/features/builder/utils/atoms'
import { SECTIONS } from '@/features/builder/utils/constants'

interface BuilderPanelProps extends PropsWithChildren {
  index: number
  title: string
}

export const BuilderPanel = ({ index, title, children }: BuilderPanelProps) => {
  const panelSize = useAtomValue(panelSizeAtom)

  return (
    <AccordionItem
      sx={{
        '&:first-of-type': {
          borderTop: 0,
        },
        '&:last-of-type': {
          borderBottom: 0,
        },
      }}
    >
      {({ isExpanded }) => (
        <>
          <AccordionButton
            sx={{
              display: 'flex',
              alignItems: 'center',
              _expanded: {
                bg: 'blackAlpha.100',
              },
            }}
          >
            <Box
              sx={{
                position: 'relative',
                display: 'flex',
                _before: {
                  content: `"${index}"`,
                  position: 'absolute',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  top: 0,
                  left: 0,
                  w: 4,
                  h: 4,
                  color: 'white',
                  fontSize: '7px',
                  fontWeight: 'bold',
                  fontFamily: 'mono',
                  borderRadius: 2,
                  zIndex: 1,
                },
                _dark: {
                  _before: {
                    color: 'black',
                  },
                },
              }}
            >
              <Icon
                as={SquircleIcon}
                sx={{
                  fill: 'black',
                  _dark: {
                    fill: 'white',
                  },
                }}
              />
            </Box>
            <Box
              as="span"
              sx={{
                flex: 1,
                pl: 2,
                textAlign: 'left',
                fontWeight: 'semibold',
                fontSize: 'md',
              }}
            >
              {title}
            </Box>
            {isExpanded ? <Icon as={MinusIcon} /> : <Icon as={PlusIcon} />}
          </AccordionButton>
          <AccordionPanel
            sx={{
              h: `calc(100vh - ${128 + 40 * SECTIONS}px)`,
              overflowY: 'auto',
            }}
          >
            <Grid
              gap={4}
              templateColumns={
                panelSize < 30
                  ? '1fr'
                  : {
                      base: '1fr',
                      lg: 'repeat(2, 1fr)',
                    }
              }
            >
              {children}
            </Grid>
          </AccordionPanel>
        </>
      )}
    </AccordionItem>
  )
}
