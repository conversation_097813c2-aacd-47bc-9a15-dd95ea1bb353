import { memo, useMemo } from 'react'
import { useAtomValue } from 'jotai'
import { is } from 'ramda'
import {
  Box,
  Flex,
  Grid,
  Icon,
  IconButton,
  Heading,
  Link,
} from '@chakra-ui/react'
import { XIcon, MenuIcon, Building2Icon } from 'lucide-react'
import { simplePalette } from '@/utils/simple-palette'

import { jsonThemeSchema } from '@/features/builder/utils/schema/json-theme'
import { jsonThemeAtom } from '@/features/builder/utils/atoms'
import { lightContrast } from '@/features/builder/utils/text-contrast-color'
import { themeColor } from '@/features/builder/utils/theme-color'
import type { JsonThemeProps } from '@/features/builder/utils/schema/types'
import { PreviewHeading } from '@/features/builder/components/preview-heading'
import type { PaletteColors } from '@/types/theme'

interface Props {
  theme: JsonThemeProps
}

const Logo = ({ theme }: Props) => (
  <Box
    sx={{
      ml: 4,
      height: Number.isFinite(theme.dashboard_header.logo_height)
        ? `${theme.dashboard_header.logo_height}px`
        : `calc(${theme.dashboard_header.height}px - 10px)`,
      width: '100%',
      maxW: '180px',
      background: `url(${theme.dashboard_header.logo}) no-repeat 0 50%`,
      backgroundSize: 'contain',
    }}
  />
)

const Header = ({ theme }: Props) => (
  <Heading
    as="h1"
    sx={{
      w: '100%',
      textAlign: 'center',
      fontWeight: 'normal',
      fontSize: '24px',
      overflow: 'hidden',
      whiteSpace: 'nowrap',
      textOverflow: 'ellipsis',
      fontFamily: theme.fonts?.heading ?? 'heading',
      color: theme.custom_colors.dashboard_header_heading_color,
    }}
  >
    Dashboard
  </Heading>
)

export const PreviewDashboard = memo(() => {
  const jsonTheme = useAtomValue(jsonThemeAtom)

  const theme = useMemo(() => {
    try {
      const re = /--chakra/gi
      return jsonTheme
        ? jsonThemeSchema.parse(JSON.parse(jsonTheme.replace(re, '--ck')))
        : null
    } catch {
      return null
    }
  }, [jsonTheme])

  const overlayColor = useMemo(() => {
    const cssVar = theme?.custom_colors.main_nav_item_background_color
    if (!(cssVar && theme && is(Object, theme?.colors))) return null
    const colors = simplePalette(theme.colors as PaletteColors)
    const color = themeColor({ cssVar, colors, prefix: 'ck' })
    return color
      ? lightContrast(color) < 4.5
        ? 'blackAlpha.600'
        : 'whiteAlpha.600'
      : null
  }, [theme])

  if (!(theme && overlayColor)) return null

  return (
    <>
      <PreviewHeading>Dashboard Header Preview</PreviewHeading>
      <Grid
        sx={{
          gridTemplateColumns: 'repeat(3, 1fr)',
          alignItems: 'center',
          borderRadius: 'base',
          overflow: 'hidden',
          bg: theme.custom_colors.start_page_background_color,
          h: '60px',
        }}
      >
        <Logo theme={theme} />
        <Header theme={theme} />
        <Flex
          sx={{
            textAlign: 'right',
            w: '100%',
            maxW: '320px',
            h: 'inherit',
            justifyContent: 'flex-end',
            alignItems: 'center',
            pr: 4,
          }}
        >
          <IconButton
            aria-label="Open Menu"
            isRound
            icon={
              <Icon
                as={MenuIcon}
                boxSize="24px"
                sx={{
                  color: theme.custom_colors.main_nav_button_icon_color,
                }}
              />
            }
            sx={{
              border: 0,
              boxSize: '40px',
              bg: theme.custom_colors.main_nav_button_background_color,
              _hover: {
                bg: theme.custom_colors.main_nav_button_background_color,
              },
              _active: {
                opacity: '0.8',
                bg: theme.custom_colors.main_nav_button_background_color,
              },
              _focus: {},
            }}
          />
        </Flex>
      </Grid>
      <Grid
        sx={{
          mt: 2,
          gridTemplateColumns: '180px auto 320px',
          h: '60px',
          position: 'relative',
          alignItems: 'center',
          borderRadius: 'base',
          overflow: 'hidden',
          bg: theme.custom_colors.main_nav_item_background_color,
          _before: {
            content: '""',
            position: 'absolute',
            width: 'calc(100% - 320px)',
            height: '100%',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            bg: overlayColor,
          },
        }}
      >
        <Logo theme={theme} />
        <Header theme={theme} />
        <Flex
          sx={{
            pr: 0,
            w: '100%',
            h: 'inherit',
            maxW: '320px',
            justifyContent: 'flex-end',
            alignItems: 'center',
          }}
        >
          <IconButton
            aria-label="Close Menu"
            isRound
            icon={<Icon as={XIcon} boxSize="24px" color="black" />}
            sx={{
              left: '-5px',
              border: 0,
              boxSize: '40px',
              borderRadius: 'full',
              bg: 'white',
              zIndex: 1,
              color: theme.custom_colors.main_nav_item_background_color,
              _hover: {},
              _focus: {},
            }}
          />
          <Flex
            sx={{
              minW: '320px',
              h: 'inherit',
              justifyContent: 'flex-start',
              alignItems: 'center',
              borderRadius: 'base',
              overflow: 'hidden',
              bg: theme.custom_colors.main_nav_item_background_color,
            }}
          >
            <Link
              sx={{
                userSelect: 'none',
                padding: '0 16px',
                fontSize: '14px',
                fontWeight: 'normal',
                textTransform: 'uppercase',
                lineHeight: '60px',
                display: 'flex',
                alignItems: 'center',
                fontFamily: theme.fonts?.body ?? 'body',
                color: theme.custom_colors.main_nav_item_link_color,
                bg: theme.custom_colors.main_nav_item_background_color,
                _hover: {
                  bg: theme.custom_colors.main_nav_item_background_hover_color,
                  color: theme.custom_colors.main_nav_item_link_hover_color,
                },
                _focus: {},
                _active: {},
              }}
            >
              <Icon as={Building2Icon} boxSize="24px" mr="1rem" />
              Companies & Locations
            </Link>
          </Flex>
        </Flex>
      </Grid>
    </>
  )
})
