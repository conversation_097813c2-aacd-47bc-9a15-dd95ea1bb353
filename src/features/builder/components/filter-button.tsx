import { useMemo } from 'react'
import { useAtom, useAtomValue } from 'jotai'
import { Box, Button } from '@chakra-ui/react'
import { logAtom, filterAtom } from '@/features/builder/utils/atoms'

export const FilterButton = ({ label, value }: BaseOption<string | null>) => {
  const [filter, setFilter] = useAtom(filterAtom)
  const isActive = filter === value
  const log = useAtomValue(logAtom)

  const count = useMemo(
    () =>
      value === null
        ? log.length
        : log.filter((item) => item.type === value).length,
    [log, value]
  )

  const activeStyle = useMemo(
    () => ({
      bg: isActive ? 'blue.700' : 'gray.200',
      color: isActive ? 'white' : 'gray.600',
    }),
    [isActive]
  )

  return (
    <Button
      type="button"
      aria-label={`Filter logs by ${label.toLowerCase()}`}
      onClick={() => setFilter(value)}
      isDisabled={count === 0}
      colorScheme="gray"
      size="xs"
      sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        minW: '80px',
        ...activeStyle,
        _hover: { ...activeStyle },
      }}
    >
      <Box pr={1}>{label}</Box>
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          borderRadius: 'full',
          bg: isActive ? 'white' : 'blue.700',
          color: isActive ? 'blue.700' : 'white',
          fontSize: '7px',
          fontWeight: 'bold',
          w: '1rem',
          h: '1rem',
          minWidth: '1rem',
          minHeight: '1rem',
          p: 1,
        }}
      >
        {count}
      </Box>
    </Button>
  )
}
