import {
  chakra,
  Flex,
  Icon,
  shouldForwardProp,
  useConst,
} from '@chakra-ui/react'
import { motion, isValidMotionProp } from 'framer-motion'
import { CircleDashedIcon } from 'lucide-react'

const MotionBox = chakra(motion.div, {
  shouldForwardProp: (prop) =>
    isValidMotionProp(prop) || shouldForwardProp(prop),
})

interface SpinnerProps {
  text: string
}

export const Spinner = ({ text }: SpinnerProps) => {
  const transition = useConst<any>(() => ({
    repeat: Number.POSITIVE_INFINITY,
    duration: 1,
    ease: 'linear',
    repeatType: 'loop',
    repeatDelay: 0,
  }))

  return (
    <Flex sx={{ alignItems: 'center' }}>
      <MotionBox
        key="spinner"
        animate={{ rotate: 180 }}
        transition={transition}
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'gray.400',
        }}
      >
        <Icon as={CircleDashedIcon} boxSize="16px" />
      </MotionBox>
      <Flex
        sx={{
          fontSize: 'sm',
          fontWeight: 'normal',
          pl: 2,
          color: 'inherit',
        }}
      >
        {text}
      </Flex>
    </Flex>
  )
}
