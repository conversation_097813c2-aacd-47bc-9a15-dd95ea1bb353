import { useAtomValue } from 'jotai'
import { useWatch, type Control } from 'react-hook-form'
import { InputField } from '@/components/input-field'
import { DropZoneField } from '@/components/drop-zone-field'
import { ColorPickerField } from '@/components/color-picker-field/color-picker-field'

import { BuilderPanel } from '@/features/builder/components/builder-panel'
import { ColorSwatches } from '@/features/builder/components/color-swatches'
import { isFetchingAtom } from '@/features/builder/utils/atoms'
import type { FormInputProps } from '@/features/builder/utils/schema/types'

interface Props {
  control: Control<any, any>
}

export const SectionAssets = ({ control }: Props) => {
  const isFetching = useAtomValue(isFetchingAtom)

  const logo = useWatch<FormInputProps, 'logo'>({
    name: 'logo',
    control,
  })

  const icon = useWatch<FormInputProps, 'icon'>({
    name: 'icon',
    control,
  })

  return (
    <BuilderPanel title="Assets" index={2}>
      <DropZoneField
        name="logo"
        label="Logo"
        control={control}
        isDisabled={isFetching}
        isRequired
        height={100}
        shortInfo="Supported files; SVG. Make sure your logo is vector."
        accept={{
          'image/svg+xml': ['.svg'],
        }}
        sx={{
          gridColumn: '1 / -1',
        }}
      />

      <ColorSwatches dataUri={logo} />

      <DropZoneField
        name="dashboardLogo"
        label="Logo (Dashboard)"
        control={control}
        isDisabled={isFetching}
        height={100}
        shortInfo="Alternative logo for dashboard. Supported files; SVG. Make sure your logo is vector."
        accept={{
          'image/svg+xml': ['.svg'],
        }}
        sx={{
          gridColumn: '1 / -1',
        }}
      />

      <DropZoneField
        name="emailLogo"
        label="Logo (Email)"
        control={control}
        isDisabled={isFetching}
        height={100}
        shortInfo="Alternative logo for emails. Supported files; SVG. Make sure your logo is vector."
        longInfo="This logo will be featured in system-generated emails, which consistently use a white background. Please ensure that the logo is designed to stand out against white and has a transparent background for optimal display."
        accept={{
          'image/svg+xml': ['.svg'],
        }}
        sx={{
          gridColumn: '1 / -1',
        }}
      />

      <DropZoneField
        name="icon"
        label="App Icon"
        control={control}
        isDisabled={isFetching}
        isRequired
        height={100}
        shortInfo="Supported files; SVG. Make sure your logo is vector."
        accept={{
          'image/svg+xml': ['.svg'],
        }}
        sx={{
          gridColumn: '1 / -1',
        }}
      />

      <ColorSwatches dataUri={icon} />

      <InputField
        name="iconPadding"
        label="App Icon Padding (in %)"
        type="number"
        control={control}
        isDisabled={isFetching}
        isRequired
        shortInfo="If icon has custom background, set padding to 0."
      />
      <ColorPickerField
        name="iconBackground"
        label="Icon Background Color"
        control={control}
        isDisabled={isFetching}
        shortInfo="Custom background color for the favicon."
      />
    </BuilderPanel>
  )
}
