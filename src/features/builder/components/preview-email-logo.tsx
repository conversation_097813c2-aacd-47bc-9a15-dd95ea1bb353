import { isEmpty } from 'ramda'
import { useWatch, type Control } from 'react-hook-form'
import { Flex, Image } from '@chakra-ui/react'
import { blank64Gif } from '@/utils/constants'
import type { FormInputProps } from '@/features/builder/utils/schema/types'
import { PreviewHeading } from '@/features/builder/components/preview-heading'

interface Props {
  control: Control<any, any>
}

export const PreviewEmailLogo = ({ control }: Props) => {
  const logo = useWatch<FormInputProps, 'logo'>({
    name: 'logo',
    control,
  })

  const emailLogo = useWatch<FormInputProps, 'emailLogo'>({
    name: 'emailLogo',
    control,
  })

  if (!logo) {
    return null
  }

  return (
    <>
      <PreviewHeading>Email Logo</PreviewHeading>
      <Flex
        sx={{
          p: 4,
          position: 'relative',
          height: '100px',
          borderRadius: 'base',
          justifyContent: 'flex-start',
          alignItems: 'center',
          bg: 'white',
        }}
      >
        <Image
          src={isEmpty(emailLogo) ? logo : emailLogo}
          fallbackSrc={blank64Gif}
          sx={{
            height: '100%',
            objectFit: 'contain',
            maxWidth: '100%',
            w: 'auto',
          }}
        />
      </Flex>
    </>
  )
}
