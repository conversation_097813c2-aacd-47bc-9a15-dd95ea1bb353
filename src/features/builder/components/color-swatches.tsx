import { useState, useMemo } from 'react'
import { Box, HStack } from '@chakra-ui/react'
import { useToast } from '@/hooks/use-toast'
import { imageDataUri } from '@server/builder/utils/image-data-uri'

const extractSvgHexColors = (svgString: string) => {
  const regex = /#([a-fA-F0-9]{6}|[a-fA-F0-9]{3})/g
  const matches = svgString.match(regex)
  return matches ? Array.from(new Set(matches)) : []
}

const colorsFromDataUri = (dataUri: string) => {
  const parsed = imageDataUri.decode(dataUri)
  const svg = new TextDecoder().decode(parsed?.dataBuffer)
  return extractSvgHexColors(svg)
}

const getSvgColors = (dataUri: string) => {
  return dataUri.startsWith('data:image/svg+xml')
    ? colorsFromDataUri(dataUri)
    : []
}

interface ColorSwatchesProps {
  dataUri: string
}

export const ColorSwatches = ({ dataUri }: ColorSwatchesProps) => {
  const toast = useToast()
  const [clip, setClip] = useState<string>('')
  const colors = useMemo(() => getSvgColors(dataUri ?? ''), [dataUri])

  const onCopy = async (color: string) => {
    await navigator.clipboard.writeText(color)
    setClip(color)
    toast({ msg: `Color ${color} copied to clipboard.`, status: 'copy' })
  }

  if (colors.length === 0) {
    return null
  }

  return (
    <HStack gridColumn="1 / -1">
      {colors.slice(0, 20).map((color) => (
        <Box
          key={color}
          onClick={async () => onCopy(color)}
          sx={{
            boxSize: '20px',
            borderRadius: 'full',
            cursor: 'pointer',
            border: '2px solid',
            borderColor: clip === color ? 'whiteAlpha.600' : 'transparent',
            boxShadow: clip === color ? 'inset 0 0 0 2px white' : 'none',
            bg: color,
          }}
        />
      ))}
    </HStack>
  )
}
