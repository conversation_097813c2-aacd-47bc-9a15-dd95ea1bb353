import { useAtomValue } from 'jotai'
import type { Control } from 'react-hook-form'
import { useQuery } from '@tanstack/react-query'
import { Alert, AlertIcon } from '@chakra-ui/react'
import { SelectField } from '@/components/select-field'
import { ColorPickerField } from '@/components/color-picker-field/color-picker-field'

import { BuilderPanel } from '@/features/builder/components/builder-panel'
import { colorOptions } from '@/features/builder/utils/options'
import { isFetchingAtom } from '@/features/builder/utils/atoms'
import { getFontOptions } from '@/api/builder'

interface Props {
  control: Control<any, any>
}

export const SectionTheme = ({ control }: Props) => {
  const isFetching = useAtomValue(isFetchingAtom)

  const { data: fontOptions } = useQuery({
    queryKey: ['getFontOptions'],
    queryFn: ({ signal }) => getFontOptions({ signal }),
  })

  return (
    <BuilderPanel title="Theme" index={3}>
      <SelectField
        name="headingFont"
        label="Heading Font"
        longInfo="This font is designated for headings and titles. It should only be used if the brand guidelines specifically call for different fonts for headings and body text."
        control={control}
        options={fontOptions}
        isLoading={fontOptions?.length === 0}
        isDisabled={isFetching}
        isRequired
        isSearchable
        menuPosition="fixed"
      />
      <SelectField
        name="bodyFont"
        label="Body Font"
        control={control}
        options={fontOptions}
        isLoading={fontOptions?.length === 0}
        isDisabled={isFetching}
        isRequired
        isSearchable
        menuPosition="fixed"
      />
      <Alert gridColumn="1 / -1" borderRadius="base">
        <AlertIcon />
        Make sure to set the appropriate color map for legacy themes if they
        being updated, as the previous values were not stored.
      </Alert>
      <ColorPickerField
        name="primaryColor"
        label="Primary Color"
        control={control}
        isRequired
      />
      <ColorPickerField
        name="secondaryColor"
        label="Secondary Color"
        control={control}
        isRequired
      />
      <SelectField
        name="primaryColorMap"
        label="Primary Color Map"
        control={control}
        options={colorOptions}
        menuPosition="fixed"
      />
      <SelectField
        name="secondaryColorMap"
        label="Secondary Color Map"
        control={control}
        options={colorOptions}
        menuPosition="fixed"
      />
      <ColorPickerField
        name="backgroundColor"
        label="Background Color"
        control={control}
        isRequired
        shortInfo="Background color is used on start page and dashboard header."
      />
      <ColorPickerField
        name="textColor"
        label="Text Color"
        control={control}
        isRequired
        shortInfo="Text color is not used on start page."
      />
    </BuilderPanel>
  )
}
