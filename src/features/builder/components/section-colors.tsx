import { memo } from 'react'
import { useFormContext, useFieldArray } from 'react-hook-form'
import {
  Flex,
  Button,
  Alert,
  AlertIcon,
  AlertDescription,
} from '@chakra-ui/react'
import { InputField } from '@/components/input-field'
import { ColorPickerField } from '@/components/color-picker-field/color-picker-field'
import { fallbackColors } from '@/utils/theme/fallback-theme'

import { BuilderPanel } from '@/features/builder/components/builder-panel'
import { ALLOWED_COLOR_NAMES } from '@/features/builder/utils/constants'
import type { FormInputProps } from '@/features/builder/utils/schema/types'

export const SectionColors = memo(() => {
  const { control, setValue } = useFormContext<FormInputProps>()

  const { fields } = useFieldArray({
    name: 'colors',
    control,
  })

  const onResetColors = () => {
    setValue(
      'colors',
      Object.entries(fallbackColors).reduce(
        (acc: Array<{ name: string; value: string }>, [name, value]) => {
          if (name === 'primary' || name === 'secondary') {
            return acc
          }
          acc.push({ name, value })
          return acc
        },
        []
      )
    )
  }

  return (
    <BuilderPanel title="Custom Colors" index={5}>
      <Flex gridColumn="1 / -1">
        <Alert>
          <AlertIcon />
          <AlertDescription>
            {`Only change these in very specific cases, valid color names are; ${ALLOWED_COLOR_NAMES.join(', ')}. Color mapping in previous section will take precedence over these.`}{' '}
            <Button
              variant="link"
              colorScheme="secondary"
              onClick={onResetColors}
            >
              Reset colors to default if you are unsure.
            </Button>
          </AlertDescription>
        </Alert>
      </Flex>
      {fields.map((field, index) => (
        <Flex
          key={field.id}
          gridGap={4}
          sx={{
            p: 2,
            pb: 4,
            borderRadius: 'base',
            bg: 'blackAlpha.50',
          }}
        >
          <Flex w="40%">
            <InputField
              name={`colors.${index}.name`}
              label="Color Name"
              control={control}
            />
          </Flex>
          <ColorPickerField
            name={`colors.${index}.value`}
            label="Hex Value"
            control={control}
          />
        </Flex>
      ))}
    </BuilderPanel>
  )
})
