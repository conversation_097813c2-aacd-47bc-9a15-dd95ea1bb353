import { Flex, Image, type SystemStyleObject } from '@chakra-ui/react'
import { blank64Gif, trans64Bg } from '@/utils/constants'

interface Props {
  src?: string
  padding?: number
  sx?: SystemStyleObject
}

export const PreviewIcon = ({ src, padding, sx }: Props) => (
  <Flex
    sx={{
      p: 0,
      position: 'relative',
      borderRadius: 'lg',
      boxSize: '70px',
      justifyContent: 'center',
      alignItems: 'center',
      overflow: 'hidden',
      bg: `url(${trans64Bg}) 0 0/16px 16px`,
      bgColor: 'gray.50',
      _dark: {
        bgColor: 'gray.900',
      },
      ...sx,
    }}
  >
    <Image
      src={src}
      fallbackSrc={blank64Gif}
      sx={{
        p: `${padding ?? 0}%`,
        height: '100%',
        objectFit: 'contain',
        maxWidth: '100%',
        w: 'auto',
      }}
    />
  </Flex>
)
