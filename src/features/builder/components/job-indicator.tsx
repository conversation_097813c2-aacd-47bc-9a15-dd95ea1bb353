import { memo, useMemo } from 'react'
import { useAtomValue } from 'jotai'
import { startCase } from 'lodash'
import { chakra, Flex, Icon, shouldForwardProp } from '@chakra-ui/react'
import { motion, isValidMotionProp } from 'framer-motion'
import { CircleDashedIcon } from 'lucide-react'
import { lastJobAtom, isFetchingAtom } from '@/features/builder/utils/atoms'

const MotionBox = chakra(motion.div, {
  shouldForwardProp: (prop) =>
    isValidMotionProp(prop) || shouldForwardProp(prop),
})

const colorMap: StaticDict<string> = {
  error: 'red.600',
  in_progress: 'blue.600',
  requires_action: 'purple.600',
  cancelling: 'orange.600',
  cancelled: 'red.600',
  failed: 'red.600',
  expired: 'red.600',
  completed: 'green.600',
  queued: 'gray.400',
  idle: 'gray.400',
}

export const JobIndicator = memo(() => {
  const lastJob = useAtomValue(lastJob<PERSON>tom)
  const isFetching = useAtomValue(isFetchingAtom)

  const event = lastJob?.type === 'error' ? 'error' : (lastJob?.event ?? 'idle')
  const slow = ['error', 'idle', 'failed', 'cancelled', 'completed'].includes(
    event
  )

  const transition = useMemo<any>(
    () => ({
      repeat: Number.POSITIVE_INFINITY,
      duration: slow ? 6 : 0.4,
      ease: 'linear',
      repeatType: 'loop',
      repeatDelay: 0,
    }),
    [slow]
  )

  const text = useMemo(() => {
    if (isFetching) {
      return 'Fetching data ...'
    }
    return [startCase(lastJob?.event ?? 'Idle'), lastJob?.text ?? '']
      .filter(Boolean)
      .join(': ')
  }, [lastJob, isFetching])

  return (
    <Flex sx={{ alignItems: 'center' }}>
      <MotionBox
        key={event}
        animate={{ rotate: 180 }}
        transition={transition}
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: colorMap[event],
        }}
      >
        <Icon as={CircleDashedIcon} boxSize="16px" />
      </MotionBox>
      <Flex sx={{ fontSize: 'sm', color: 'inherit', pl: 2 }}>{text}</Flex>
    </Flex>
  )
})
