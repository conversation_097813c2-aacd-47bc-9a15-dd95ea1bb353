import { describe, it, expect } from 'vitest'
import { theme } from '@chakra-ui/react'
import { themeColorsVars } from './theme-colors-vars'

const colors = {
  black: theme.colors.black,
  blue: theme.colors.blue,
}

describe('themeColorsVars', () => {
  it('should return css variables for colors', () => {
    expect(themeColorsVars({ colors })).toStrictEqual([
      '--chakra-colors-black: #000000;',
      '--chakra-colors-blue-50: #ebf8ff;',
      '--chakra-colors-blue-100: #bee3f8;',
      '--chakra-colors-blue-200: #90cdf4;',
      '--chakra-colors-blue-300: #63b3ed;',
      '--chakra-colors-blue-400: #4299e1;',
      '--chakra-colors-blue-500: #3182ce;',
      '--chakra-colors-blue-600: #2b6cb0;',
      '--chakra-colors-blue-700: #2c5282;',
      '--chakra-colors-blue-800: #2a4365;',
      '--chakra-colors-blue-900: #1A365D;',
    ])
  })

  it('should return css variables for colors with prefix', () => {
    expect(themeColorsVars({ colors, prefix: 'ch' })).toStrictEqual([
      '--ch-colors-black: #000000;',
      '--ch-colors-blue-50: #ebf8ff;',
      '--ch-colors-blue-100: #bee3f8;',
      '--ch-colors-blue-200: #90cdf4;',
      '--ch-colors-blue-300: #63b3ed;',
      '--ch-colors-blue-400: #4299e1;',
      '--ch-colors-blue-500: #3182ce;',
      '--ch-colors-blue-600: #2b6cb0;',
      '--ch-colors-blue-700: #2c5282;',
      '--ch-colors-blue-800: #2a4365;',
      '--ch-colors-blue-900: #1A365D;',
    ])
  })
})
