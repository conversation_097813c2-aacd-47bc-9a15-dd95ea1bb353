import type { Colors } from '@chakra-ui/react'

interface Props {
  cssVar: string
  colors: Colors
  prefix?: string
}

const varsRegex = /\)/

/**
 * Find hex color by theme var color
 * var(--chakra-colors-red-500) => #ff0000
 */
export const themeColor = ({
  cssVar,
  colors,
  prefix = 'chakra',
}: Props): string | null => {
  if (cssVar.startsWith('#')) {
    return cssVar
  }

  const re = new RegExp(`var\\(--${prefix}-colors-`, 'g')

  const colorValue = cssVar.replace(re, '').replace(varsRegex, '')

  if (colorValue === 'black') {
    return '#000000'
  }

  if (colorValue === 'white') {
    return '#ffffff'
  }

  const match = Object.entries(colors).flatMap(([name, value]) => {
    if (typeof value === 'string') {
      return false
    }
    return Object.entries(value).flatMap(([shade, hex]: any) =>
      colorValue === `${name}-${shade}` ? hex : false
    )
  })

  return match ? match[0] : null
}
