import chroma from 'chroma-js'
import { fallbackColors } from '@/utils/theme/fallback-theme'

import { composeBackground } from '@/features/builder/utils/compose-background'
import { validContrast } from '@/features/builder/utils/valid-contrast'
import { getHexColorName } from '@/features/builder/utils/get-hex-color-name'
import { textContrastColor } from '@/features/builder/utils/text-contrast-color'

const isProd = import.meta.env.VITE_DB_ENV === 'production'

export const composeTheme = ({
  themeId,
  font,
  colors,
  primaryColor,
  primaryColorMap,
  secondaryColor,
  secondaryColorMap,
  backgroundColor,
  textColor,
  customButtonColor,
  customLinkColor,
  customTextColor,
  linkTextDecoration,
  dashboardLogoHeight,
  startLogoHeight,
  appIconSize,
  buttonRoundness,
  iconPadding,
  iconBackground,
  hasDashboardLogo,
  useBackground,
  background,
}: {
  themeId: string
  font: {
    font_url: string
    fonts: Dict<string>
  }
  colors: Dict<string>
  primaryColor: string
  primaryColorMap: string
  secondaryColor: string
  secondaryColorMap: string
  backgroundColor: string
  textColor: string
  customButtonColor: string
  customLinkColor: string
  customTextColor: string
  linkTextDecoration: string
  buttonRoundness: number
  dashboardLogoHeight: number
  startLogoHeight: number
  appIconSize: number
  iconPadding: number
  iconBackground: string
  hasDashboardLogo: boolean
  useBackground: boolean
  background: {
    backgroundImage?: string
    backgroundPosition?: string
    backgroundAttachment?: string
    backgroundRepeat?: string
    backgroundSize?: string
  }
}) => {
  const safeUrlId = themeId.replace(/\$/g, '-')
  const stagingPath = isProd ? '' : '/staging'

  const colorMode =
    chroma.contrast(backgroundColor, 'black') > 4.5 ? 'light' : 'dark'

  const bg = composeBackground({
    stagingPath,
    themeId: safeUrlId,
    useBackground,
    background,
  })

  const getColorMapName = (shade: string, hex: string, fallback = 'blue') => {
    return shade === '' ? (getHexColorName(hex, colors) ?? fallback) : shade
  }

  const getLinkColor = () => {
    if (chroma.valid(customLinkColor)) {
      return customLinkColor
    }
    const isValid = validContrast(secondaryColor, backgroundColor)
    const shade = colorMode === 'light' ? '500' : isValid ? '500' : '100'
    const name = getColorMapName(secondaryColorMap, secondaryColor)
    if (name === 'white' || name === 'black') {
      return `var(--chakra-colors-${name})`
    }
    return `var(--chakra-colors-${name}-${shade})`
  }

  const getLinkHoverColor = () => {
    if (chroma.valid(customLinkColor)) {
      return customLinkColor
    }
    const isValid = validContrast(secondaryColor, backgroundColor)
    const shade = colorMode === 'light' ? '600' : isValid ? '600' : '300'
    const name = getColorMapName(secondaryColorMap, secondaryColor)
    if (name === 'white' || name === 'black') {
      return `var(--chakra-colors-${name})`
    }
    return `var(--chakra-colors-${name}-${shade})`
  }

  const getBackgroundColor = () => {
    const name = getHexColorName(backgroundColor, colors)
    if (name === 'white' || name === 'black') {
      return `var(--chakra-colors-${name})`
    }
    if (name) {
      return `var(--chakra-colors-${name}-500)`
    }
    return backgroundColor
  }

  const getTextColor = () => {
    if (chroma.valid(customTextColor)) {
      return customTextColor
    }
    const isValid = validContrast(textColor, backgroundColor)
    const name = getHexColorName(textColor, colors)
    const shade = colorMode === 'light' ? '700' : isValid ? '700' : '200'
    if (name === 'white' || name === 'black') {
      const color = textContrastColor(backgroundColor)
      return `var(--chakra-colors-${color})`
    }
    if (name) {
      return `var(--chakra-colors-${name}-${shade})`
    }
    return textColor
  }

  const getButtonColor = () => {
    if (chroma.valid(customButtonColor)) {
      return customButtonColor
    }
    const name = getColorMapName(primaryColorMap, primaryColor)
    if (name === 'white' || name === 'black') {
      return `var(--chakra-colors-${textContrastColor(name)})`
    }
    return `var(--chakra-colors-${name}-500)`
  }

  const getButtonTextColor = () => {
    if (chroma.valid(customTextColor)) {
      const buttonBg = getButtonColor()
      const bgColor = buttonBg.startsWith('var(--chakra-colors-')
        ? primaryColor
        : buttonBg
      const isValid = validContrast(customTextColor, bgColor)
      if (isValid) {
        return customTextColor
      }
      return textContrastColor(bgColor)
    }
    const buttonBg = getButtonColor()
    const bgColor = buttonBg.startsWith('var(--chakra-colors-')
      ? primaryColor
      : buttonBg
    return textContrastColor(bgColor)
  }

  const getButtonHoverColor = () => {
    if (chroma.valid(customButtonColor)) {
      return chroma(customButtonColor).darken(0.3).hex()
    }
    const name = getColorMapName(primaryColorMap, primaryColor)
    if (name === 'white' || name === 'black') {
      return `var(--chakra-colors-${textContrastColor(name)})`
    }
    return `var(--chakra-colors-${name}-600)`
  }

  const getLabelColor = () => {
    const name = colorMode === 'light' ? 'gray-500' : 'gray-200'
    return `var(--chakra-colors-${name})`
  }

  const getHeaderColor = () => {
    const shade = colorMode === 'light' ? '700' : '100'
    return `var(--chakra-colors-gray-${shade})`
  }

  const getNavLinkColor = () => {
    const name = colorMode === 'light' ? 'gray-700' : 'white'
    return `var(--chakra-colors-${name})`
  }

  const getNavLinkHoverColor = () => {
    const name = getColorMapName(secondaryColorMap, secondaryColor)
    if (name === 'white' || name === 'black') {
      return `var(--chakra-colors-${name})`
    }
    return `var(--chakra-colors-${name}-500)`
  }

  const tokens = {
    linkColor: getLinkColor(),
    linkHoverColor: getLinkHoverColor(),
    labelColor: getLabelColor(),
    headerColor: getHeaderColor(),
    buttonColor: getButtonColor(),
    buttonHoverColor: getButtonHoverColor(),
    buttonTextColor: getButtonTextColor(),
    backgroundColor: getBackgroundColor(),
    textColor: getTextColor(),
    navLinkColor: getNavLinkColor(),
    navLinkHoverColor: getNavLinkHoverColor(),
  }

  const primary = () => {
    return Object.keys(fallbackColors).includes(primaryColorMap)
      ? {
          [primaryColorMap]: primaryColor,
          primary: primaryColor,
        }
      : {
          primary: primaryColor,
        }
  }

  const secondary = () => {
    return Object.keys(fallbackColors).includes(secondaryColorMap)
      ? {
          [secondaryColorMap]: secondaryColor,
          secondary: secondaryColor,
        }
      : {
          secondary: secondaryColor,
        }
  }

  return {
    colors: {
      ...colors,
      ...primary(),
      ...secondary(),
    },
    custom_colors: {
      start_page_background_color: `${tokens.backgroundColor}`,
      start_page_link_color: `${tokens.linkColor}`,
      start_page_link_hover_color: `${tokens.linkHoverColor}`,
      start_page_heading_color: `${tokens.headerColor}`,
      start_page_app_icon_background_color: `${tokens.backgroundColor}`,
      start_page_text_color: `${tokens.textColor}`,
      start_page_button_background_color: `${tokens.buttonColor}`,
      start_page_button_background_hover_color: `${tokens.buttonHoverColor}`,
      start_page_button_color: `${tokens.buttonTextColor}`,
      start_page_button_hover_color: `${tokens.buttonTextColor}`,
      start_page_carousel_icon_color: `${tokens.linkColor}`,
      start_page_carousel_heading_color: `${tokens.textColor}`,
      start_page_carousel_text_color: `${tokens.textColor}`,
      start_page_carousel_action_color: `${tokens.linkColor}`,
      start_page_input_background_color: 'transparent',
      start_page_input_border_color: `transparent transparent ${tokens.linkColor} transparent`,
      start_page_label_color: `${tokens.labelColor}`,
      dashboard_header_background_color: `${tokens.backgroundColor}`,
      dashboard_header_actions_color: `${tokens.headerColor}`,
      dashboard_header_heading_color: `${tokens.headerColor}`,
      main_nav_background_color: `${tokens.backgroundColor}`,
      main_nav_button_background_color: `${tokens.buttonColor}`,
      main_nav_button_icon_color: `${tokens.backgroundColor}`,
      main_nav_item_background_color: `${tokens.backgroundColor}`,
      main_nav_item_background_hover_color: `${tokens.backgroundColor}`,
      main_nav_item_background_active_color: `${tokens.backgroundColor}`,
      main_nav_item_link_color: `${tokens.navLinkColor}`,
      main_nav_item_link_hover_color: `${tokens.navLinkHoverColor}`,
      main_nav_item_link_active_color: `${tokens.navLinkHoverColor}`,
    },
    ...font,
    text_color: textColor,
    link_text_decoration: linkTextDecoration,
    dashboard_header: {
      logo: `https://s3.amazonaws.com/mydevicescdn.com${stagingPath}/web/assets/${safeUrlId}/${hasDashboardLogo ? 'logo-dashboard' : 'logo'}.svg`,
      logo_height: dashboardLogoHeight,
      height: 60,
    },
    start_page: {
      ...bg,
      logo: `https://s3.amazonaws.com/mydevicescdn.com${stagingPath}/web/assets/${safeUrlId}/logo.svg`,
      logo_height: startLogoHeight,
      app_icon: `https://s3.amazonaws.com/mydevicescdn.com${stagingPath}/web/assets/${safeUrlId}/app-icon.svg`,
      app_icon_size: appIconSize,
      button_roundness: `${buttonRoundness}px`,
    },
    meta: {
      icon_padding: Number.parseInt(`${iconPadding}`),
      icon_background: iconBackground,
      primary_color_map: primaryColorMap,
      secondary_color_map: secondaryColorMap,
      custom_link_color: customLinkColor,
      custom_text_color: customTextColor,
    },
  }
}
