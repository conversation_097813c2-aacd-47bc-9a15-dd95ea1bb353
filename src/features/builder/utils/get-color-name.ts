import chroma from 'chroma-js'
import { omit } from 'ramda'
import { fallbackColors } from '@/utils/theme/fallback-theme'
import { ALLOWED_COLOR_NAMES } from '@/features/builder/utils/constants'

export const getColorName = (hex: string) => {
  const match = Object.entries(
    omit(['primary', 'secondary'], fallbackColors)
  ).find(([, color]) => color.toLowerCase() === hex.toLowerCase())

  if (match) {
    const [shade] = match
    return shade
  }

  const name = chroma(hex).name()
  const n = name === 'aqua' ? 'cyan' : name
  return ALLOWED_COLOR_NAMES.includes(n) ? n : null
}
