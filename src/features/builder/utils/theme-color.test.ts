import { describe, it, expect } from 'vitest'
import { themeColor } from './theme-color'

describe('themeColor', () => {
  it('returns the color if it starts with #', () => {
    const cssVar = '#ff0000'
    const colors = {}
    expect(themeColor({ cssVar, colors })).toBe(cssVar)
  })

  it('returns the color from the colors object', () => {
    const cssVar = 'var(--chakra-colors-red-500)'
    const colors = {
      red: {
        500: '#ff0000',
      },
    }
    expect(themeColor({ cssVar, colors })).toBe('#ff0000')
  })

  it('returns the color from the colors object', () => {
    const cssVar = 'var(--chakra-colors-black)'
    const colors = {
      black: '#000000',
    }
    expect(themeColor({ cssVar, colors })).toBe('#000000')
  })

  it('returns the color from the colors object', () => {
    const cssVar = 'black'
    const colors = {
      black: '#000000',
    }
    expect(themeColor({ cssVar, colors })).toBe('#000000')
  })

  it('returns the color from the colors object', () => {
    const cssVar = 'white'
    const colors = {
      white: '#ffffff',
    }
    expect(themeColor({ cssVar, colors })).toBe('#ffffff')
  })

  it('support prefix', () => {
    const cssVar = 'var(--ck-colors-red-500)'
    const colors = {
      red: {
        500: '#ff0000',
      },
    }
    expect(themeColor({ cssVar, colors, prefix: 'ck' })).toBe('#ff0000')
  })
})
