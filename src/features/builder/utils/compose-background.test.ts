import { describe, it, expect, vi, afterEach } from 'vitest'
import { composeBackground } from './compose-background'
import * as extractCssUrlsModule from './extract-css-urls'
import * as replaceCssUrlsModule from './replace-css-urls'

describe('composeBackground', () => {
  const defaultParams = {
    stagingPath: '/staging-path',
    themeId: 'theme-123',
    useBackground: true,
    background: {
      backgroundImage: 'url("https://example.com/image.jpg")',
      backgroundPosition: 'center',
      backgroundAttachment: 'fixed',
      backgroundRepeat: 'no-repeat',
      backgroundSize: 'cover',
    },
  }

  it('should return background properties when useBackground is true', () => {
    // Setup spies
    const extractSpy = vi.spyOn(extractCssUrlsModule, 'extractCssUrls')
    extractSpy.mockReturnValue(['https://example.com/image.jpg'])

    const replaceSpy = vi.spyOn(replaceCssUrlsModule, 'replaceCssUrls')
    replaceSpy.mockReturnValue(
      'url("https://s3.amazonaws.com/mydevicescdn.com/staging-path/web/assets/theme-123/background.jpg")'
    )

    const result = composeBackground(defaultParams)

    expect(result).toEqual({
      background_image:
        'url("https://s3.amazonaws.com/mydevicescdn.com/staging-path/web/assets/theme-123/background.jpg")',
      background_position: 'center',
      background_attachment: 'fixed',
      background_repeat: 'no-repeat',
      background_size: 'cover',
    })

    expect(extractSpy).toHaveBeenCalledWith(
      'url("https://example.com/image.jpg")'
    )
    expect(replaceSpy).toHaveBeenCalledWith(
      'url("https://example.com/image.jpg")',
      ['https://example.com/image.jpg'],
      [
        'https://s3.amazonaws.com/mydevicescdn.com/staging-path/web/assets/theme-123/background.jpg',
      ]
    )
  })

  it('should return empty background properties when useBackground is false', () => {
    const result = composeBackground({
      ...defaultParams,
      useBackground: false,
    })

    expect(result).toEqual({
      background_image: '',
      background_position: '',
      background_attachment: '',
      background_repeat: '',
      background_size: '',
    })
  })

  it('should use png prefix for PNG image data', () => {
    const extractSpy = vi.spyOn(extractCssUrlsModule, 'extractCssUrls')
    extractSpy.mockReturnValue(['https://example.com/image.png'])

    const replaceSpy = vi.spyOn(replaceCssUrlsModule, 'replaceCssUrls')
    replaceSpy.mockReturnValue(
      'url("https://s3.amazonaws.com/mydevicescdn.com/staging-path/web/assets/theme-123/background.png")'
    )

    const result = composeBackground({
      ...defaultParams,
      background: {
        ...defaultParams.background,
        backgroundImageFile: 'data:image/png;base64,abc123',
      },
    })

    expect(result.background_image).toBe(
      'url("https://s3.amazonaws.com/mydevicescdn.com/staging-path/web/assets/theme-123/background.png")'
    )
    expect(replaceSpy).toHaveBeenCalledWith(
      'url("https://example.com/image.jpg")',
      ['https://example.com/image.png'],
      [
        'https://s3.amazonaws.com/mydevicescdn.com/staging-path/web/assets/theme-123/background.png',
      ]
    )
  })

  it('should handle missing backgroundImage', () => {
    const extractSpy = vi.spyOn(extractCssUrlsModule, 'extractCssUrls')
    extractSpy.mockReturnValue([])

    const replaceSpy = vi.spyOn(replaceCssUrlsModule, 'replaceCssUrls')
    replaceSpy.mockReturnValue(
      'url("https://s3.amazonaws.com/mydevicescdn.com/staging-path/web/assets/theme-123/background.jpg")'
    )

    const result = composeBackground({
      ...defaultParams,
      background: {
        backgroundPosition: 'center',
        backgroundAttachment: 'fixed',
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'cover',
      },
    })

    expect(result).toEqual({
      background_image:
        'url("https://s3.amazonaws.com/mydevicescdn.com/staging-path/web/assets/theme-123/background.jpg")',
      background_position: 'center',
      background_attachment: 'fixed',
      background_repeat: 'no-repeat',
      background_size: 'cover',
    })

    expect(extractSpy).toHaveBeenCalledWith('')
  })

  it('should handle partial background properties', () => {
    const extractSpy = vi.spyOn(extractCssUrlsModule, 'extractCssUrls')
    extractSpy.mockReturnValue(['https://example.com/image.jpg'])

    const replaceSpy = vi.spyOn(replaceCssUrlsModule, 'replaceCssUrls')
    replaceSpy.mockReturnValue(
      'url("https://s3.amazonaws.com/mydevicescdn.com/staging-path/web/assets/theme-123/background.jpg")'
    )

    const result = composeBackground({
      ...defaultParams,
      background: {
        backgroundImage: 'url("https://example.com/image.jpg")',
        backgroundPosition: 'center',
        // Missing backgroundAttachment, backgroundRepeat, backgroundSize
      },
    })

    expect(result).toEqual({
      background_image:
        'url("https://s3.amazonaws.com/mydevicescdn.com/staging-path/web/assets/theme-123/background.jpg")',
      background_position: 'center',
      background_attachment: undefined,
      background_repeat: undefined,
      background_size: undefined,
    })
  })

  // Reset all mocks after each test
  afterEach(() => {
    vi.restoreAllMocks()
  })
})
