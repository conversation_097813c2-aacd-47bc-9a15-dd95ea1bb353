import { extractCssUrls } from '@/features/builder/utils/extract-css-urls'
import { replaceCssUrls } from '@/features/builder/utils/replace-css-urls'

export const composeBackground = ({
  stagingPath,
  themeId,
  useBackground,
  background,
}: {
  stagingPath: string
  themeId: string
  useBackground: boolean
  background: {
    backgroundImageFile?: string
    backgroundImage?: string
    backgroundPosition?: string
    backgroundAttachment?: string
    backgroundRepeat?: string
    backgroundSize?: string
  }
}) => {
  const {
    backgroundImageFile,
    backgroundImage,
    backgroundPosition,
    backgroundAttachment,
    backgroundRepeat,
    backgroundSize,
  } = background

  const prefix = backgroundImageFile?.startsWith('data:image/png')
    ? 'png'
    : 'jpg'

  const oldUrls = extractCssUrls(backgroundImage ?? '')
  const newUrls = [
    `https://s3.amazonaws.com/mydevicescdn.com${stagingPath}/web/assets/${themeId}/background.${prefix}`,
  ]

  return {
    background_image: useBackground
      ? replaceCssUrls(backgroundImage ?? '', oldUrls, newUrls)
      : '',
    background_position: useBackground ? backgroundPosition : '',
    background_attachment: useBackground ? backgroundAttachment : '',
    background_repeat: useBackground ? backgroundRepeat : '',
    background_size: useBackground ? backgroundSize : '',
  }
}
