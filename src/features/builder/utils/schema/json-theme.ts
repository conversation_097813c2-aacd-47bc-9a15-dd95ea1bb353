import { z } from 'zod'

export const jsonThemeSchema = z.object({
  colors: z.object({
    primary: z.string().optional(),
    secondary: z.string().optional(),
    gray: z.string(),
    red: z.string(),
    orange: z.string(),
    yellow: z.string(),
    lime: z.string(),
    green: z.string(),
    teal: z.string(),
    cyan: z.string().optional(),
    blue: z.string(),
    purple: z.string(),
    pink: z.string().optional(),
  }),
  custom_colors: z.object({
    start_page_background_color: z.string(),
    start_page_link_color: z.string(),
    start_page_link_hover_color: z.string(),
    start_page_heading_color: z.string(),
    start_page_app_icon_background_color: z.string(),
    start_page_text_color: z.string(),
    start_page_button_background_color: z.string(),
    start_page_button_background_hover_color: z.string(),
    start_page_button_color: z.string(),
    start_page_button_hover_color: z.string(),
    start_page_carousel_icon_color: z.string(),
    start_page_carousel_heading_color: z.string(),
    start_page_carousel_text_color: z.string(),
    start_page_carousel_action_color: z.string(),
    start_page_input_background_color: z.string(),
    start_page_input_border_color: z.string(),
    start_page_label_color: z.string(),
    dashboard_header_background_color: z.string(),
    dashboard_header_actions_color: z.string(),
    dashboard_header_heading_color: z.string(),
    main_nav_background_color: z.string(),
    main_nav_button_background_color: z.string(),
    main_nav_button_icon_color: z.string(),
    main_nav_item_background_color: z.string(),
    main_nav_item_background_hover_color: z.string(),
    main_nav_item_background_active_color: z.string(),
    main_nav_item_link_color: z.string(),
    main_nav_item_link_hover_color: z.string(),
    main_nav_item_link_active_color: z.string(),
  }),
  start_page: z.object({
    background_image: z.string(),
    background_position: z.string(),
    background_attachment: z.string(),
    background_repeat: z.string(),
    background_size: z.string(),
    logo: z.string(),
    logo_height: z.number(),
    app_icon: z.string(),
    app_icon_size: z.number(),
    button_roundness: z.string(),
  }),
  font_url: z.string(),
  fonts: z.object({
    body: z.string(),
    heading: z.string(),
    mono: z.string(),
  }),
  text_color: z.string().optional(),
  link_text_decoration: z.string(),
  dashboard_header: z.object({
    logo: z.string(),
    logo_height: z.number(),
    height: z.number(),
  }),
  meta: z
    .object({
      icon_padding: z.number().optional(),
      icon_background: z.string().optional(),
      primary_color_map: z.string().optional(),
      secondary_color_map: z.string().optional(),
      custom_link_color: z.string().optional(),
      custom_text_color: z.string().optional(),
    })
    .optional(),
})
