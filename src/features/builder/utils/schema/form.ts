import { z } from 'zod'
import { isEmpty } from 'ramda'
import chroma from 'chroma-js'
import { hex, hexRegex, emailRegex } from '@/utils/zod-plus'
import { ALLOWED_COLOR_NAMES } from '@/features/builder/utils/constants'

export const formSchema = z
  .object({
    buildMode: z.string(),
    orgInheritId: z.string().nullish(),
    appInheritId: z.string().nullish(),
    orgId: z
      .string()
      .min(2, 'Organization ID is required.')
      .regex(/^([a-z0-9]*)(-[a-z0-9]+)*$/g, 'Invalid format.'),
    appId: z
      .string()
      .min(2, 'Application ID is required.')
      .regex(/^([a-z0-9$]*)(-[a-z0-9]+)*$/g, 'Invalid format.'),
    themeId: z.string(),
    url: z.string().url().or(z.literal('').optional()),
    brandName: z.string().min(1, 'Brand Name is required.'),
    company: z.string().min(1, 'Company Name is required.'),
    firstName: z.string(),
    lastName: z.string(),
    email: z.string(),
    locale: z.string(),
    logo: z
      .string()
      .min(1, 'Logo is required.')
      .startsWith('data:image/svg+xml', 'Invalid format.'),
    emailLogo: z
      .string()
      .startsWith('data:image/svg+xml', 'Invalid format.')
      .or(z.string().optional()),
    dashboardLogo: z
      .string()
      .startsWith('data:image/svg+xml', 'Invalid format.')
      .or(z.string().optional()),
    icon: z
      .string()
      .min(1, 'App Icon is required.')
      .startsWith('data:image/svg+xml', 'Invalid format.'),
    iconPadding: z
      .number()
      .or(z.string().min(1, 'Must be a number.'))
      .pipe(z.coerce.number().nonnegative())
      .default(20),
    iconBackground: z.string().superRefine((arg, ctx) => {
      if (arg && !(hexRegex.test(arg) || chroma.valid(arg))) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Invalid hex color.',
        })
      }
      return z.NEVER
    }),
    headingFont: z.string(),
    bodyFont: z.string(),
    primaryColor: hex,
    secondaryColor: hex,
    primaryColorMap: z.string(),
    secondaryColorMap: z.string(),
    backgroundColor: hex,
    textColor: hex,
    customButtonColor: z.string().superRefine((arg, ctx) => {
      if (arg && !(hexRegex.test(arg) || chroma.valid(arg))) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Invalid hex color.',
        })
      }
      return z.NEVER
    }),
    customLinkColor: z.string().superRefine((arg, ctx) => {
      if (arg && !(hexRegex.test(arg) || chroma.valid(arg))) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Invalid hex color.',
        })
      }
      return z.NEVER
    }),
    customTextColor: z.string().superRefine((arg, ctx) => {
      if (arg && !(hexRegex.test(arg) || chroma.valid(arg))) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Invalid hex color.',
        })
      }
      return z.NEVER
    }),
    appIconSize: z
      .number()
      .or(z.string().min(1, 'Must be a number.'))
      .pipe(z.coerce.number().nonnegative()),
    dashboardLogoHeight: z
      .number()
      .or(z.string().min(1, 'Must be a number.'))
      .pipe(z.coerce.number().nonnegative()),
    startLogoHeight: z
      .number()
      .or(z.string().min(1, 'Must be a number.'))
      .pipe(z.coerce.number().nonnegative()),
    buttonRoundness: z
      .number()
      .or(z.string().min(1, 'Must be a number.'))
      .pipe(z.coerce.number().nonnegative()),
    linkTextDecoration: z.string(),
    useBackground: z.boolean(),
    background: z.object({
      backgroundImage: z.string(),
      backgroundImageFile: z.string(),
      backgroundPosition: z.string(),
      backgroundAttachment: z.string(),
      backgroundRepeat: z.string(),
      backgroundSize: z.string(),
    }),
    colors: z.array(
      z.object({
        name: z.string().min(1, 'Color name is required.'),
        value: hex,
      })
    ),
  })
  .superRefine((arg, ctx) => {
    if (arg.buildMode.endsWith('organization')) {
      if (arg.firstName.trim() === '') {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'First Name is required.',
          path: ['firstName'],
        })
      }

      if (arg.lastName.trim() === '') {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Last Name is required.',
          path: ['lastName'],
        })
      }

      if (!emailRegex.test(arg.email)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Invalid email address.',
          path: ['email'],
        })
      }
    }

    // Background image checks

    if (arg.useBackground && isEmpty(arg.background.backgroundImage.trim())) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Background image style is required.',
        path: ['background.backgroundImage'],
      })
    }

    // Custom colors checks

    const colorNames = arg.colors.map(({ name }) => name)

    colorNames.forEach((name, i) => {
      if (!ALLOWED_COLOR_NAMES.includes(name)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Invalid color name.',
          path: [`colors.${i}.name`],
        })
      }
    })

    if (isEmpty(colorNames)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Please add at least 1 color.',
        path: ['colors.0.name'],
      })
    }

    if (colorNames.length !== new Set(colorNames).size) {
      const duplicates = colorNames.reduce((acc: number[], name, key) => {
        if (colorNames.indexOf(name) !== key) {
          acc.push(key)
        }
        return acc
      }, [])

      for (const key of duplicates) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Duplicate color name.',
          path: ['colors', key, 'name'],
        })
      }
    }

    // Org, app, and theme ID checks for different build modes

    if (arg.buildMode === 'new-organization') {
      if (arg.appId?.includes('$')) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: '$ is not allowed in Application ID.',
          path: ['appId'],
        })
      }
      if (arg.orgId !== arg.appId) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Application ID and Organization ID must match.',
          path: ['appId'],
        })
      }
      if (arg.appId !== arg.themeId) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Application ID and Theme ID must match.',
          path: ['themeId'],
        })
      }
    }

    if (arg.buildMode === 'new-inherited-organization') {
      if (!arg.appId?.includes('$')) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Missing $ in Application ID.',
          path: ['appId'],
        })
      }
      if (!arg.themeId?.includes('$')) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: '$ is not allowed in Theme ID.',
          path: ['themeId'],
        })
      }
      if (arg.orgInheritId !== arg.orgId) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Organization ID must match inherited.',
          path: ['orgId'],
        })
      }
      if (arg.appInheritId !== arg.themeId) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Theme ID must match inherited.',
          path: ['themeId'],
        })
      }
    }

    if (arg.buildMode === 'new-application') {
      if (!arg.appId?.includes('$')) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Missing $ in application ID.',
          path: ['appId'],
        })
      }
      if (arg.appId !== arg.themeId) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Application ID and Theme ID must match.',
          path: ['themeId'],
        })
      }
    }

    if (arg.buildMode === 'new-inherited-application') {
      if (!arg.appId?.includes('$')) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Missing $ in Application ID.',
          path: ['appId'],
        })
      }
      if (arg.appId !== arg.themeId) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Application ID and Theme ID must match.',
          path: ['themeId'],
        })
      }
    }

    if (arg.buildMode === 'update-application') {
      if (arg.orgInheritId !== arg.orgId) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Organization ID must match inherited.',
          path: ['orgId'],
        })
      }
      if (arg.appInheritId !== arg.appId) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Application ID must match inherited.',
          path: ['appId'],
        })
      }
    }

    return z.NEVER
  })
