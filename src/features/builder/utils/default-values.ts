import { fallbackColors } from '@/utils/theme/fallback-theme'

export const defaultValues = {
  // brand
  buildMode: '',
  orgInheritId: null, // virtual
  appInheritId: null, // virtual
  orgId: '',
  appId: '',
  themeId: '', // virtual
  url: '', // virtual
  brandName: '',
  company: '',
  firstName: '',
  lastName: '',
  email: '',
  locale: 'en-us',
  // assets
  logo: '',
  emailLogo: '',
  dashboardLogo: '',
  icon: '',
  iconPadding: 20,
  iconBackground: '',
  // theme
  headingFont: '',
  bodyFont: '',
  primaryColor: '',
  secondaryColor: '',
  primaryColorMap: '',
  secondaryColorMap: '',
  backgroundColor: '#ffffff',
  textColor: '#000000',
  // start page
  customButtonColor: '',
  customLinkColor: '',
  customTextColor: '',
  buttonRoundness: 6,
  linkTextDecoration: 'underline',
  dashboardLogoHeight: 40,
  startLogoHeight: 120,
  appIconSize: 100,
  useBackground: false,
  background: {
    backgroundImageFile: '',
    backgroundImage: '',
    backgroundPosition: '',
    backgroundAttachment: '',
    backgroundRepeat: '',
    backgroundSize: '',
  },
  // custom colors
  colors: Object.entries(fallbackColors).reduce(
    (acc: Array<{ name: string; value: string }>, [name, value]) => {
      if (name === 'primary' || name === 'secondary') {
        return acc
      }
      acc.push({
        name,
        value,
      })
      return acc
    },
    []
  ),
}
