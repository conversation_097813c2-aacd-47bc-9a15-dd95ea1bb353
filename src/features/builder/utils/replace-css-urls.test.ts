import { describe, it, expect } from 'vitest'
import { replaceCssUrls } from './replace-css-urls'

describe('replaceCssUrls', () => {
  it('should replace a single URL in CSS', () => {
    const cssString = `url("https://example.com/image1.png")`
    const urls = ['https://example.com/image1.png']
    const newUrls = ['https://example.com/image2.png']
    const result = replaceCssUrls(cssString, urls, newUrls)
    expect(result).toEqual(`url("https://example.com/image2.png")`)
  })

  it('should replace multiple URLs in CSS', () => {
    const cssString = `url("https://example.com/image1.jpg"), url("https://example.com/image2.jpg")`
    const urls = [
      'https://example.com/image1.jpg',
      'https://example.com/image2.jpg',
    ]
    const newUrls = [
      'https://example.com/image3.jpg',
      'https://example.com/image4.jpg',
    ]
    const result = replaceCssUrls(cssString, urls, newUrls)
    expect(result).toEqual(
      `url("https://example.com/image3.jpg"), url("https://example.com/image4.jpg")`
    )
  })

  it('should return original if no URLs match', () => {
    const cssString = `url("https://example.com/image1.jpg")`
    const urls = ['https://example.com/image2.jpg']
    const newUrls = ['https://example.com/image3.jpg']
    const result = replaceCssUrls(cssString, urls, newUrls)
    expect(result).toEqual(`url("https://example.com/image1.jpg")`)
  })

  it('should ignore if only css without URLs', () => {
    const cssString = 'repeating-linear-gradient(red, yellow 10%, green 20%)'
    const urls = [] as string[]
    const newUrls = [] as string[]
    const result = replaceCssUrls(cssString, urls, newUrls)
    expect(result).toEqual(cssString)
  })
})
