import { describe, it, expect } from 'vitest'
import { getHexColorName } from './get-hex-color-name'
import { fallbackColors } from '@/utils/theme/fallback-theme'

describe('getHexColorName', () => {
  it('should return the color name if it is a named color', () => {
    const testColors = {
      red: '#FF0000',
      orange: '#FF9800',
      yellow: '#FFC107',
      lime: '#00FF00',
      green: '#38A169',
      teal: '#008080',
      cyan: '#00B5D8',
      blue: '#2196F3',
      purple: '#800080',
      pink: '#D53F8C',
      gray: '#808080',
      white: '#FFFFFF',
      black: '#000000',
    }

    for (const [name, hex] of Object.entries(testColors)) {
      expect(getHexColorName(hex, fallbackColors)).toBe(name)
    }
  })
})
