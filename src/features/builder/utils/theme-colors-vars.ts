interface Props {
  colors: Dict<any>
  prefix?: string
}

export const themeColorsVars = ({ colors, prefix = 'chakra' }: Props) => {
  return Object.entries(colors).reduce((acc: string[], [name, value]) => {
    if (typeof value === 'string') {
      acc.push(`--${prefix}-colors-${name}: ${value};`)
    } else {
      for (const [n, v] of Object.entries(value)) {
        acc.push(`--${prefix}-colors-${name}-${n}: ${v};`)
      }
    }
    return acc
  }, [])
}
