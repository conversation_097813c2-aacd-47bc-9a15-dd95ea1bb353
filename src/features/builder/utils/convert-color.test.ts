import { describe, it, expect } from 'vitest'
import { convertColor } from './convert-color'

describe('convertColor', () => {
  it('returns hex for white', () => {
    expect(convertColor('white')).toEqual('#FFFFFF')
  })

  it('returns hex for black', () => {
    expect(convertColor('black')).toEqual('#000000')
  })

  it('returns the same value if already a hex color', () => {
    expect(convertColor('#FF5733')).toEqual('#FF5733')
  })

  it('returns empty string for non-hex, non-special colors', () => {
    expect(convertColor('blue')).toEqual('')
  })
})
