// Helper to substitute legacy fonts
export const substituteFont = (font: string) => {
  switch (font) {
    case 'Source Sans Pro':
      return 'Source Sans 3'
    case 'Muli':
      return 'Mulish'
    case 'Corbel':
      return 'Quattrocento Sans'
    case 'ClearviewATT':
      return 'Work Sans'
    case 'Myriad Pro SemiCondensed':
      return 'Open Sans'
    case 'Arial':
    case 'Helvetica':
    case 'Helvetica Neue':
    case 'proxima-nova':
    case 'Lucida Grande':
    case 'Cresta':
    case 'Baloo Da 2':
    case 'Segoe UI':
    case 'Swiss 721 W01 Roman':
      return 'Roboto'
    default:
      return font
  }
}
