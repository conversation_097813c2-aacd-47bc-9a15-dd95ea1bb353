import { describe, expect, it } from 'vitest'
import { validContrast } from './valid-contrast'

describe('validContrast', () => {
  it('should return true when both inputs are valid colors with sufficient contrast', () => {
    // Black and white have very high contrast
    expect(validContrast('#000000', '#FFFFFF')).toBe(true)
    // Dark blue and yellow have high contrast
    expect(validContrast('#0000FF', '#FFFF00')).toBe(true)
  })

  it('should return false when both inputs are valid colors with insufficient contrast', () => {
    // Light gray and white have low contrast
    expect(validContrast('#DDDDDD', '#FFFFFF')).toBe(false)
    // Similar shades of blue
    expect(validContrast('#0000FF', '#0000DD')).toBe(false)
  })

  it('should return true when at least one input is not a valid color', () => {
    // Invalid first color
    expect(validContrast('not-a-color', '#FFFFFF')).toBe(true)
    // Invalid second color
    expect(validContrast('#000000', 'invalid-color')).toBe(true)
    // Both colors invalid
    expect(validContrast('invalid1', 'invalid2')).toBe(true)
  })

  it('should handle various color formats correctly', () => {
    // CSS color names
    expect(validContrast('black', 'white')).toBe(true)
    // RGB format
    expect(validContrast('rgb(0,0,0)', 'rgb(255,255,255)')).toBe(true)
    // RGBA format (alpha should be ignored for contrast calculation)
    expect(validContrast('rgba(0,0,0,0.5)', 'rgba(255,255,255,0.5)')).toBe(true)
    // HSL format
    expect(validContrast('hsl(0,0%,0%)', 'hsl(0,0%,100%)')).toBe(true)
  })
})
