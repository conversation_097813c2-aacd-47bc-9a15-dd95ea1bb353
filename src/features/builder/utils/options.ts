export const buildModeOptions = [
  {
    label: 'New Organization',
    value: 'new-organization',
  },
  {
    label: 'New Organization (Inherited Theme)',
    value: 'new-inherited-organization',
  },
  {
    label: 'New Application',
    value: 'new-application',
  },
  {
    label: 'New Application (Inherited Theme)',
    value: 'new-inherited-application',
  },
  {
    label: 'Update Application',
    value: 'update-application',
  },
]

export const colorOptions = [
  { label: 'None', value: '' },
  { label: 'Red', value: 'red' },
  { label: 'Orange', value: 'orange' },
  { label: 'Yellow', value: 'yellow' },
  { label: 'Lime', value: 'lime' },
  { label: 'Green', value: 'green' },
  { label: 'Teal', value: 'teal' },
  { label: 'Cyan', value: 'cyan' },
  { label: 'Blue', value: 'blue' },
  { label: 'Purple', value: 'purple' },
  { label: 'Pink', value: 'pink' },
]

export const backgroundOptions = [
  { label: 'None', value: false },
  { label: 'Background Image', value: true },
]
