import { describe, it, expect } from 'vitest'
import { omit } from 'ramda'
import { fallbackColors } from '@/utils/theme/fallback-theme'
import { getColorName } from '@/features/builder/utils/get-color-name'

describe('getColorName', () => {
  it('should return the color name if it is a named color', () => {
    const omitFallbackColors = omit(
      ['primary', 'secondary', 'gray'],
      fallbackColors
    )

    for (const [name, hex] of Object.entries(omitFallbackColors)) {
      expect(getColorName(hex)).toBe(name)
    }
  })

  it('should return null if the color is not a named color or a shade of a color', () => {
    expect(getColorName('#8cb4ff')).toBe(null)
  })

  it('should return color name by hex name', () => {
    const colors = {
      red: '#ff0000',
      orange: '#ffa500',
      yellow: '#ffff00',
      lime: '#00ff00',
      teal: '#008080',
      cyan: '#00ffff',
      blue: '#0000ff',
      purple: '#800080',
      pink: '#ffc0cb',
      gray: '#808080',
    }

    for (const [name, hex] of Object.entries(colors)) {
      expect(getColorName(hex)).toBe(name)
    }
  })

  it('should return cyan for aqua', () => {
    expect(getColorName('#00ffff')).toBe('cyan')
  })

  it('should return null for invalid color', () => {
    expect(getColorName('#8cb4ff')).toBe(null)
  })

  it('should support back and white color', () => {
    expect(getColorName('#000')).toBe('black')
    expect(getColorName('#fff')).toBe('white')
  })

  it('should support named colors color', () => {
    expect(getColorName('black')).toBe('black')
    expect(getColorName('white')).toBe('white')
    expect(getColorName('gray')).toBe('gray')
  })
})
