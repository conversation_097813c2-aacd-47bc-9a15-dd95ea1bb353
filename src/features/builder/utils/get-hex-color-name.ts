import { omit } from 'ramda'
import { getColorName } from '@/features/builder/utils/get-color-name'

export const getHexColorName = (hex: string, colors: Dict<string>) => {
  const name = getColorName(hex)
  if (name) {
    return name
  }

  const match = Object.entries(omit(['primary', 'secondary'], colors)).find(
    ([, color]) => color === hex
  )

  if (match) {
    const [shade] = match
    return shade
  }

  return null
}
