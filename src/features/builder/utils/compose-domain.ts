interface Props {
  buildMode?: string
  orgId?: string
  appId?: string
  url?: string
}

const isProd = import.meta.env.VITE_DB_ENV === 'production'

export const composeDomain = ({ buildMode, orgId, appId, url }: Props) => {
  if (!appId) {
    return null
  }

  if (url && buildMode === 'update-application') {
    return url?.replace('https://', '')
  }

  const isSub =
    buildMode === 'new-application' || buildMode === 'new-inherited-application'

  const app = isSub ? [orgId, appId].filter(Boolean).join('$') : appId

  return [
    app.replace('$', '-'),
    isProd ? null : '.staging',
    isSub ? '.app' : '',
    '.mydevices.com',
  ].join('')
}
