import { describe, it, expect } from 'vitest'
import { extractCssUrls } from './extract-css-urls'

describe('extractCssUrls', () => {
  it('should extract a single URL from background-image', () => {
    const cssString = `url("https://example.com/image1.png")`
    const result = extractCssUrls(cssString)
    expect(result).toEqual(['https://example.com/image1.png'])
  })

  it('should ignore URLs query strings', () => {
    const cssString = `url("https://example.com/image1.png?size=medium")`
    const result = extractCssUrls(cssString)
    expect(result).toEqual(['https://example.com/image1.png'])
  })

  it('extract multiple URLs from background-image', () => {
    const cssString = `url("https://example.com/image1.jpg"), url("https://example.com/image2.jpg")`
    const result = extractCssUrls(cssString)
    expect(result).toEqual([
      'https://example.com/image1.jpg',
      'https://example.com/image2.jpg',
    ])
  })

  it('ignore other CSS properties and only extract URLs from background-image', () => {
    const cssString = `linear-gradient(to bottom, rgba(0,0,0,0.6), rgba(0,0,0,0.5)), url('https://example.com/image1.jpg')`
    const result = extractCssUrls(cssString)
    expect(result).toEqual(['https://example.com/image1.jpg'])
  })

  it('should ignore if only css without URLs', () => {
    const cssString = 'repeating-linear-gradient(red, yellow 10%, green 20%)'
    const result = extractCssUrls(cssString)
    expect(result).toEqual([])
  })

  it('should extract plain URLs without url() syntax', () => {
    const cssString = 'https://example.com/image1.jpg'
    const result = extractCssUrls(cssString)
    expect(result).toEqual(['https://example.com/image1.jpg'])
  })

  it('should extract both plain URLs and url() syntax URLs', () => {
    const cssString = `url("https://example.com/image1.png"), https://example.com/image2.jpg`
    const result = extractCssUrls(cssString)
    expect(result).toEqual([
      'https://example.com/image1.png',
      'https://example.com/image2.jpg',
    ])
  })
})
