import { atom } from 'jotai'
import { selectAtom } from 'jotai/utils'
import type { Builder } from '@/types/builder'

export const panelSizeAtom = atom<number>(50)
export const isFetchingAtom = atom<boolean>(false)
export const jsonThemeAtom = atom<string | null>(null)

const defaultBuilder = {
  time: Date.now(),
  type: 'pending',
  event: 'idle',
  text: '',
} satisfies Builder

const builderAtom = atom(defaultBuilder)

export const logAtom = atom<Builder[]>([])

const jobAtom = selectAtom(logAtom, (log) =>
  log.filter((atom) => ['job'].includes(atom.type))
)

export const lastJobAtom = selectAtom(jobAtom, (job) => job[job.length - 1])

export const resetAtom = atom(null, (_get, set) => {
  set(builderAtom, defaultBuilder)
  set(logAtom, [])
})

export const filterAtom = atom<string | null>(null)

export const filteredLogAtom = atom<Builder[]>((get) => {
  const filter = get(filterAtom)
  const logs = get(logAtom)
  if (filter === null) {
    return logs
  }
  return logs.filter((log) => log.type === filter)
})
