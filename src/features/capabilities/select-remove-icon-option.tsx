import * as Sentry from '@sentry/react'
import type { BaseSyntheticEvent } from 'react'
import { useQueryClient, useMutation } from '@tanstack/react-query'
import { components } from 'react-select'
import { Box, Flex, HStack, Icon, IconButton, Image } from '@chakra-ui/react'
import { Trash2Icon } from 'lucide-react'
import { deleteProperty } from '@/api/things/properties'
import { useToast } from '@/hooks/use-toast'

export const SelectRemoveIconOption = (props: any) => {
  const toast = useToast()
  const queryClient = useQueryClient()
  const { dataTypeId, propertyId, label, icon, value, type } = props.data

  const { mutateAsync: deletePropertyMutation } = useMutation<
    boolean,
    Error,
    {
      dataTypeId: number
      propertyId: number
    }
  >({
    mutationFn: ({ dataTypeId, propertyId }) =>
      deleteProperty(dataTypeId, propertyId),
    onSuccess: () => {
      const cache = ['GetProperties', 'GetProperty', 'GetIcons']
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const onRemove = async (event: BaseSyntheticEvent): Promise<void> => {
    event.preventDefault()
    event.stopPropagation()

    if (!(dataTypeId && propertyId)) {
      toast({
        status: 'error',
        msg: 'Missing property ID.',
      })
      return
    }

    try {
      await deletePropertyMutation({
        dataTypeId,
        propertyId,
      })
      toast({
        status: 'success',
        msg: 'Icon has been deleted.',
      })
    } catch (error: unknown) {
      Sentry.captureException(error)
      toast({
        status: 'error',
        msg: 'Unable to delete icon.',
      })
    }
  }

  return (
    <components.Option {...props}>
      <Flex justify="space-between" align="center">
        <HStack>
          {type === 'url' ? (
            <Image src={value} boxSize="24px" />
          ) : (
            <Icon as={icon as any} />
          )}
          <Box pl={1}>{label}</Box>
        </HStack>
        {type === 'url' && (
          <IconButton
            isRound
            size="sm"
            variant="ghost"
            colorScheme="red"
            aria-label="Remove"
            icon={<Trash2Icon size={16} />}
            onClick={onRemove}
          />
        )}
      </Flex>
    </components.Option>
  )
}
