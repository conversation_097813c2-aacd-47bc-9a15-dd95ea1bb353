import { Box } from '@chakra-ui/react'
import { components, type OptionProps } from 'react-select'

export const SelectCodecOption = (props: OptionProps<any>) => (
  <components.Option {...props}>
    <Box>
      <Box>{props.children}</Box>
      <Box
        sx={{
          fontSize: 'sm',
          color: props.isSelected ? 'whiteAlpha.600' : 'blackAlpha.600',
          _dark: {
            color: 'whiteAlpha.600',
          },
        }}
      >
        {props.data.value}
      </Box>
    </Box>
  </components.Option>
)
