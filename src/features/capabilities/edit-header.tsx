import { lazy, useMemo } from 'react'
import { useNavigate, useParams } from 'react-router'
import { useAtomValue, useSet<PERSON>tom } from 'jotai'
import { Helmet } from 'react-helmet-async'
import { Badge, Box, Button, Flex, HStack } from '@chakra-ui/react'
import { Title } from '@/components/title'
import { AddButton } from '@/components/add-button'
import { BackButton } from '@/components/back-button'
import { useModal } from '@/hooks/use-modal'
import { metaTitle } from '@/utils/meta-title'
import { channelAtom, resetChannelAtom } from '@/utils/stores/channel'
import { useAuth } from '@/contexts/use-auth'

const AdvancedProperties = lazy(() =>
  import('@/pages/device-templates/capabilities/advanced-properties').then(
    ({ AdvancedProperties }) => ({
      default: AdvancedProperties,
    })
  )
)

const ButtonBadge = () => {
  const channel = useAtomValue(channelAtom)

  return (
    <Badge
      sx={{
        pos: 'absolute',
        top: '-10px',
        right: '-5px',
        p: 1,
        minW: '22px',
        borderRadius: 'full',
        size: 'xs',
        fontSize: 'xs',
        bg: 'whiteAlpha.800',
        color: 'secondary.500',
        boxShadow: 'md',
        _dark: {
          bg: 'gray.900',
          color: 'secondary.200',
          boxShadow: 'md-dark',
        },
      }}
    >
      {
        [
          ...(Array.isArray(channel.data.properties)
            ? channel.data.properties
            : []),
        ].length
      }
    </Badge>
  )
}

export const EditHeader = () => {
  const modal = useModal()
  const navigate = useNavigate()
  const { typeId, id } = useParams<Dict<string>>()
  const resetChannel = useSetAtom(resetChannelAtom)
  const { isInternalAdmin } = useAuth()

  const isNew = useMemo<boolean>(() => !id || id === 'add', [id])
  const title = useMemo<string>(
    () =>
      isNew ? 'Add Capability Information' : 'Edit Capability Information',
    [isNew]
  )

  const onAdvancedProperties = (): void => {
    modal({
      component: <AdvancedProperties />,
      scrollBehavior: 'inside',
      config: {
        data: null,
        onCallback: () => ({}),
      },
    })
  }

  const onAddDataType = (): void => {
    navigate(`/manage/device-templates/${typeId}/data-type`, {
      viewTransition: true,
    })
  }

  const onGoBack = (): void => {
    resetChannel()
    navigate(`/manage/device-templates/${typeId}/capabilities`, {
      viewTransition: true,
    })
  }

  return (
    <>
      <Helmet>
        <title>{metaTitle([title])}</title>
      </Helmet>
      <Flex
        sx={{
          p: 4,
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <HStack>
          <BackButton onClick={onGoBack} />
          <Title title={title} />
        </HStack>
        <HStack>
          <Button
            type="button"
            aria-label="Advanced Properties"
            colorScheme="primary"
            onClick={onAdvancedProperties}
            sx={{
              pos: 'relative',
              overflow: 'visible',
              _after: {},
            }}
          >
            <Box>Advanced Properties</Box>
            <ButtonBadge />
          </Button>
          <AddButton
            label="Add Data Type"
            onClick={onAddDataType}
            isDisabled={!isInternalAdmin}
          />
        </HStack>
      </Flex>
    </>
  )
}
