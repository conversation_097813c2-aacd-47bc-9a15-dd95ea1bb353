import {
  Box,
  Flex,
  HStack,
  Icon,
  type IconProps,
  type ComponentWithAs,
} from '@chakra-ui/react'
import type { LucideIcon } from 'lucide-react'

export interface SelectWidgetOptionProps {
  name: string
  value: string
  css: string
  icon: LucideIcon | ComponentWithAs<'svg', IconProps>
}

export const SelectWidgetOptionLabel = ({
  name,
  icon,
}: SelectWidgetOptionProps) => (
  <Flex>
    <HStack>
      <Icon as={icon} />
      <Box pl={1}>{name}</Box>
    </HStack>
  </Flex>
)
