import { Box, Flex, Image, HStack, Icon } from '@chakra-ui/react'
import type { IconModel } from '@/types/models/icon'

export const SelectIconOptionLabel = ({
  label,
  value,
  type,
  color,
  icon,
}: IconModel) => (
  <Flex>
    <HStack>
      {type === 'url' ? (
        <Box color={color} sx={{ color, fill: color }}>
          <Image src={value} boxSize="24px" sx={{ color, fill: color }} />
        </Box>
      ) : (
        <Icon as={icon as any} />
      )}
      <Box pl={1}>{label}</Box>
    </HStack>
  </Flex>
)
