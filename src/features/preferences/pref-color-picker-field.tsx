import { useRef, useMemo, useState, useEffect } from 'react'
import chroma from 'chroma-js'
import FocusLock from 'react-focus-lock'
import { useMeasure } from 'react-use'
import { readableColor, parseToHsl, toColorString } from 'polished'
import {
  Box,
  Grid,
  Flex,
  Button,
  IconButton,
  Popover,
  PopoverContent,
  PopoverHeader,
  PopoverBody,
  PopoverTrigger,
  PopoverCloseButton,
  useToken,
  useDisclosure,
  ButtonGroup,
} from '@chakra-ui/react'
import { PaletteIcon, CheckIcon, XIcon } from 'lucide-react'
import { ColorPickerSlider } from '@/components/color-picker-field/color-picker-slider'
import type { PrefProps } from '@/data/preferences'
import { chakraColor } from '@/utils/chakra-color'

export const PrefColorPickerField = ({
  value,
  pref,
  isDisabled,
  onSave,
  onRemove,
}: {
  value: string | undefined
  pref: PrefProps
  isDisabled: boolean
  onSave: (pref: PrefProps, value: string) => void
  onRemove: (pref: PrefProps) => void
}) => {
  const focusRef = useRef<any>(null)
  const [measureRef, m] = useMeasure<HTMLDivElement>()
  const { onOpen, onClose, isOpen } = useDisclosure()
  const [fallbackColor] = useToken('colors', ['red.500'])
  const [color, setColor] = useState<string>(() => {
    return value && chroma.valid(value) ? chroma(value).hex() : fallbackColor
  })

  useEffect(() => {
    if (value && chroma.valid(value)) {
      setColor(chroma(value).hex())
    }
  }, [value])

  const colors = useMemo(() => Object.values(chakraColor(color)), [color])
  const { hue, saturation, lightness } = parseToHsl(color)

  const hGradient = chroma
    .scale(
      Array.from({ length: 36 }).map((_, i) =>
        chroma.hsl(i * 10, saturation, 0.5).hex()
      )
    )
    .mode('lrgb')
    .colors(10)

  const sGradient = chroma
    .scale([
      chroma.hsl(Math.round(hue), 0, 0.5),
      chroma.hsl(Math.round(hue), 1, 0.5),
    ])
    .mode('lrgb')
    .colors(10)

  const onClickColor = (hex: string) => {
    if (chroma.valid(hex)) {
      setColor(hex)
    }
  }

  const isColorChanged =
    chroma.valid(color) && color.toLowerCase() !== value?.toLowerCase()

  const onSaveHandle = (): void => {
    onClose()
    if (isColorChanged) {
      onSave(pref, color)
    }
  }

  const onRemoveHandle = (): void => {
    onRemove(pref)
    onClose()
  }

  return (
    <Popover
      isOpen={isOpen}
      initialFocusRef={focusRef}
      onOpen={onOpen}
      onClose={onClose}
      placement="bottom-end"
      closeOnBlur={false}
      closeOnEsc={false}
    >
      <Flex
        ref={measureRef}
        sx={{
          px: 0,
          w: '100%',
          h: '41px',
          justifyItems: 'center',
        }}
      >
        <Flex
          sx={{
            px: 3,
            gap: 2,
            w: '100%',
            h: 'inherit',
            alignItems: 'center',
            boxShadow: 'none',
          }}
        >
          {chroma.valid(value) && (
            <>
              <Box
                sx={{
                  bg: value,
                  borderWidth: '1px',
                  borderColor: 'blackAlpha.100',
                  boxSize: 4,
                  borderRadius: 3,
                }}
              />
              <Box
                sx={{
                  fontFamily: 'mono',
                  fontSize: 'sm',
                }}
              >
                {value}
              </Box>
            </>
          )}
        </Flex>
        <PopoverTrigger>
          <IconButton
            aria-label="Save"
            variant="link"
            colorScheme="secondary"
            icon={<PaletteIcon size={16} />}
            isDisabled={isDisabled}
          />
        </PopoverTrigger>
      </Flex>
      <PopoverContent
        sx={{
          mt: -2,
          w: m?.width,
        }}
      >
        <FocusLock returnFocus persistentFocus={false}>
          <PopoverCloseButton
            sx={{
              color: readableColor(color),
              right: 1,
              _hover: {},
              _focus: {},
              _active: {},
            }}
          />
          <PopoverHeader
            sx={{
              h: '50px',
              bg: color,
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              borderTopLeftRadius: 5,
              borderTopRightRadius: 5,
              color: readableColor(color),
              userSelect: 'none',
              fontFamily: 'mono',
              fontSize: '0.7rem',
            }}
          >
            <Box>{chroma(color).css()}</Box>
          </PopoverHeader>
          <PopoverBody>
            <Grid
              sx={{
                gap: 2,
                gridTemplateColumns: 'repeat(10, 1fr)',
                gridAutoRows: 'minmax(1.5rem, auto)',
              }}
            >
              {colors.map((c, i) => (
                <Button
                  key={i}
                  aria-label={c}
                  onClick={() => onClickColor(c)}
                  sx={{
                    h: 6,
                    p: 0,
                    background: c,
                    minWidth: 'unset',
                    borderRadius: 3,
                    _hover: {
                      background: c,
                      boxShadow: 'sm',
                    },
                  }}
                />
              ))}
            </Grid>
            <Box w="inherit" pt={3}>
              <ColorPickerSlider
                value={hue}
                min={1}
                max={359}
                step={1}
                gradient={hGradient}
                onChange={(val) => {
                  const v = toColorString({
                    hue: val,
                    saturation,
                    lightness,
                  })
                  setColor(v)
                }}
              />
              <ColorPickerSlider
                value={saturation}
                min={0.01}
                max={1}
                step={0.01}
                gradient={sGradient}
                onChange={(val) => {
                  const v = toColorString({
                    hue,
                    saturation: val,
                    lightness,
                  })
                  setColor(v)
                }}
              />
            </Box>
            <ButtonGroup
              size="sm"
              variant="ghost"
              sx={{
                mt: 4,
                mb: 2,
                display: 'flex',
                justifyContent: 'flex-end',
              }}
            >
              <IconButton
                aria-label="Remove"
                colorScheme="red"
                icon={<XIcon size={20} />}
                onClick={() => onRemoveHandle()}
                isDisabled={!value}
              />
              <IconButton
                aria-label="Save"
                colorScheme="green"
                icon={<CheckIcon size={20} />}
                onClick={() => onSaveHandle()}
                isDisabled={!isColorChanged}
              />
            </ButtonGroup>
          </PopoverBody>
        </FocusLock>
      </PopoverContent>
    </Popover>
  )
}
