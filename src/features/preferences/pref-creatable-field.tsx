import {
  useState,
  useEffect,
  useCallback,
  type <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  type KeyboardEventHandler,
} from 'react'
import { v4 as uuid } from 'uuid'
import chroma from 'chroma-js'
import { startCase } from 'lodash'
import {
  Box,
  Flex,
  ButtonGroup,
  IconButton,
  useTheme,
  useToken,
  useColorMode,
} from '@chakra-ui/react'
import { PencilIcon, PencilOffIcon } from 'lucide-react'
import {
  components,
  type ContainerProps,
  type ControlProps,
  type MultiValueProps,
  type MultiValueGenericProps,
  type MultiValueRemoveProps,
  type ActionMeta,
} from 'react-select'
import CreatableSelect from 'react-select/creatable'
import {
  DndContext,
  useDroppable,
  type UniqueIdentifier,
  type DragEndEvent,
} from '@dnd-kit/core'
import {
  arrayMove,
  SortableContext,
  useSortable,
  rectSortingStrategy,
} from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import { restrictToParentElement } from '@dnd-kit/modifiers'
import type { PrefProps } from '@/data/preferences'
import { selectStyle, selectTheme } from '@/utils/theme/select/md'
import { useToast } from '@/hooks/use-toast'

const featureRegex = /_feature_enabled$/

interface CreatableOption {
  readonly id: UniqueIdentifier
  readonly label: string
  readonly value: string
}

const SelectContainer = ({
  children,
  ...props
}: ContainerProps<CreatableOption>) => (
  <Box w="100%">
    <components.SelectContainer {...props}>
      {children}
    </components.SelectContainer>
  </Box>
)

const Control = (props: ControlProps<CreatableOption>) => {
  const [color] = useToken('colors', ['secondary.200'])
  const { setNodeRef, active } = useDroppable({
    id: 'droppable',
  })

  return (
    <Box
      ref={setNodeRef}
      sx={{
        bgColor: active ? chroma(color).alpha(0.05).hex() : 'inherit',
      }}
    >
      <components.Control {...props} />
    </Box>
  )
}

const MultiValue = (props: MultiValueProps<CreatableOption>) => {
  const [color] = useToken('colors', ['color.300'])

  const onMouseDown: MouseEventHandler<HTMLDivElement> = (event) => {
    event.preventDefault()
    event.stopPropagation()
  }

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isOver,
    isDragging,
  } = useSortable({
    id: props.data.id,
  })

  const active = [isOver, isDragging].some((v) => v)
  const innerProps = { ...props.innerProps, onMouseDown }

  return (
    <Box
      sx={{
        transform: CSS.Transform.toString(transform),
        transition,
        boxShadow: active
          ? `inset 0 0 5px ${chroma(color).alpha(0.5).hex()}`
          : 'none',
      }}
      ref={setNodeRef}
      {...attributes}
      {...listeners}
    >
      <components.MultiValue {...props} innerProps={innerProps} />
    </Box>
  )
}

const MultiValueContainer = (
  props: MultiValueGenericProps<CreatableOption>
) => <components.MultiValueContainer {...props} />

const MultiValueRemove = (props: MultiValueRemoveProps<CreatableOption>) => (
  <components.MultiValueRemove
    {...props}
    innerProps={{
      onPointerDown: (event) => event.stopPropagation(),
      ...props.innerProps,
    }}
  />
)

const createOption = (label: string) => ({
  id: uuid(),
  label: startCase(label),
  value: label,
})

const getName = (name?: string): string => {
  if (!name) {
    return 'debug_me_options'
  }
  return name.replace(featureRegex, '_options')
}

export const PrefCreatableField = ({
  pref,
  defaultValue,
  isDisabled,
  onSave,
}: {
  pref: PrefProps
  defaultValue?: string
  isDisabled: boolean
  onSave: (newValue: string[], action: string) => void
}) => {
  const toast = useToast()
  const { colors } = useTheme()
  const { colorMode } = useColorMode()
  const isDarkMode = colorMode === 'dark'
  const [inputValue, setInputValue] = useState<string>('')
  const [value, setValue] = useState<CreatableOption[]>([])
  const [isEditing, setIsEditing] = useState<boolean>(false)

  const actionName = (action: string) => {
    switch (action) {
      case 'sort-option':
        return 'sorted'
      case 'remove-value':
        return 'removed'
      case 'clear':
        return 'cleared'
      default:
        return 'updated'
    }
  }

  const getDefaultOptions = useCallback((): CreatableOption[] => {
    if (!defaultValue) {
      return []
    }
    try {
      const options = JSON.parse(defaultValue)
      return (options ?? []).map((value: string) => createOption(value))
    } catch {
      return []
    }
  }, [defaultValue])

  useEffect(() => {
    setValue(getDefaultOptions())
  }, [getDefaultOptions])

  const onDragEnd = (event: DragEndEvent) => {
    const { active, over } = event

    if (!(active && over)) return

    setValue((prev) => {
      const oldIndex = prev.findIndex(
        ({ id }: CreatableOption) => id === active.id
      )
      const newIndex = prev.findIndex(
        ({ id }: CreatableOption) => id === over.id
      )
      const newOptions = arrayMove(prev, oldIndex, newIndex)
      onSave(
        newOptions.map(({ value }: CreatableOption) => value),
        actionName('sort-option')
      )
      return newOptions
    })
  }

  const onKeyDown: KeyboardEventHandler = (event) => {
    if (!inputValue) return

    const match = value.find(
      (o) => o.value.toLocaleLowerCase() === inputValue.toLocaleLowerCase()
    )

    switch (event.key) {
      case 'Enter':
      case 'Tab': {
        if (match) {
          toast({
            status: 'error',
            msg: 'Value already exists.',
          })
          event.preventDefault()
          return
        }
        setValue((prev) => [...prev, createOption(inputValue)])
        onSave(
          [...value.map((o) => o.value), inputValue],
          actionName('create-option')
        )
        setInputValue('')
        event.preventDefault()
        break
      }
      default:
        return
    }
  }

  const onChange = (newValue: any, actionMeta: ActionMeta<CreatableOption>) => {
    setValue([...newValue])
    onSave(
      [...newValue].map(({ value }) => value),
      actionName(actionMeta.action)
    )
  }

  return (
    <Flex
      sx={{
        px: 0,
        w: '100%',
        minH: '41px',
        py: 1,
        justifyItems: 'center',
        borderRadius: 'base',
      }}
    >
      <Flex justifyItems="center" w="inherit" h="inherit">
        {!isEditing ? (
          <Flex
            sx={{
              px: 3,
              alignItems: 'center',
              cursor: 'default',
              h: 'inherit',
              boxShadow: 'none',
              w: '100%',
            }}
          >
            {value.map((v) => v.value).join(', ')}
          </Flex>
        ) : (
          <DndContext
            modifiers={[restrictToParentElement]}
            onDragEnd={onDragEnd}
          >
            <SortableContext items={value} strategy={rectSortingStrategy}>
              <CreatableSelect
                name={getName(pref?.name)}
                components={{
                  DropdownIndicator: null,
                  MultiValue,
                  MultiValueContainer,
                  MultiValueRemove,
                  SelectContainer,
                  Control,
                }}
                value={value}
                inputValue={inputValue}
                onKeyDown={onKeyDown}
                onInputChange={(newValue: string) => setInputValue(newValue)}
                onChange={onChange}
                placeholder="Add pre-defined options in app or leave blank"
                styles={selectStyle(colors, isDarkMode) as any}
                theme={(theme) => selectTheme(colors, theme, isDarkMode)}
                menuIsOpen={false}
                isClearable={false}
                isMulti
              />
            </SortableContext>
          </DndContext>
        )}
      </Flex>
      {isEditing ? (
        <ButtonGroup size="md" isAttached>
          <IconButton
            aria-label="Close"
            variant="link"
            colorScheme="gray"
            icon={<PencilOffIcon size={16} />}
            onClick={() => setIsEditing(false)}
          />
        </ButtonGroup>
      ) : (
        <IconButton
          aria-label="Edit"
          variant="link"
          colorScheme="secondary"
          icon={<PencilIcon size={16} />}
          onClick={() => setIsEditing(true)}
          isDisabled={isDisabled}
        />
      )}
    </Flex>
  )
}
