import { useState, useEffect } from 'react'
import { Flex, Input, ButtonGroup, IconButton } from '@chakra-ui/react'
import { PencilIcon, CheckIcon, XIcon } from 'lucide-react'
import type { PrefProps } from '@/data/preferences'

export const PrefEditableField = ({
  value,
  pref,
  onSave,
  isDisabled,
  isMultiLine = false,
}: {
  value: string | undefined
  pref: PrefProps
  onSave: (pref: PrefProps, value: string) => void
  isDisabled: boolean
  isMultiLine?: boolean
}) => {
  const [isEditing, setIsEditing] = useState<boolean>(false)
  const [valueState, setValueState] = useState<string>(value ?? '')

  useEffect(() => {
    setValueState(value ?? '')
  }, [value])

  return (
    <Flex
      sx={{
        px: 0,
        w: '100%',
        h: '41px',
        justifyItems: 'center',
        borderRadius: 'base',
      }}
    >
      <Flex
        sx={{
          justifyItems: 'center',
          w: 'inherit',
          h: 'inherit',
        }}
      >
        {!isEditing ? (
          <Flex
            noOfLines={1}
            sx={{
              px: 3,
              display: 'flex',
              alignItems: 'center',
              cursor: 'default',
              h: 'inherit',
              boxShadow: 'none',
              w: '100%',
            }}
          >
            {isMultiLine ? `${valueState?.slice(0, 40)}...` : valueState}
          </Flex>
        ) : (
          <Input
            sx={{
              px: 3,
              display: 'flex',
              alignItems: 'center',
              boxShadow: 'none',
              borderRadius: 'base',
              border: 0,
              bg: 'white',
              _dark: {
                bg: 'whiteAlpha.100',
              },
            }}
            _focus={{
              boxShadow: 'none',
              border: 0,
            }}
            value={valueState}
            onChange={(event) => setValueState(event.target.value)}
          />
        )}
      </Flex>
      {isEditing ? (
        <ButtonGroup size="md" isAttached>
          <IconButton
            aria-label="Save"
            variant="link"
            colorScheme="green"
            icon={<CheckIcon size={20} />}
            onClick={() => {
              setIsEditing(false)
              onSave(pref, valueState)
            }}
          />
          <IconButton
            aria-label="Close"
            variant="link"
            colorScheme="red"
            icon={<XIcon size={20} />}
            onClick={() => setIsEditing(false)}
          />
        </ButtonGroup>
      ) : (
        <IconButton
          aria-label="Edit"
          variant="link"
          colorScheme="secondary"
          icon={<PencilIcon size={16} />}
          onClick={() => setIsEditing(true)}
          isDisabled={isDisabled}
        />
      )}
    </Flex>
  )
}
