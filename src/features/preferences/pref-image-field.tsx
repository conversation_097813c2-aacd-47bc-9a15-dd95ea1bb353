import { Box, Flex, Image, ButtonGroup, IconButton } from '@chakra-ui/react'
import { ImageUpIcon, TrashIcon } from 'lucide-react'
import { useDropzone } from 'react-dropzone'
import { useToast } from '@/hooks/use-toast'
import type { PrefProps } from '@/data/preferences'

interface PrefImageFieldProps {
  pref: PrefProps
  value?: string
  onSave(pref: PrefProps, value: string): Promise<void>
  onRemove?(pref: PrefProps): void
  isDisabled?: boolean
}

const ImageView = ({ value }: { value?: string }) => {
  if (!value) return <Box />

  return (
    <Box
      as="a"
      href={value}
      target="_blank"
      rel="noopener noreferrer"
      sx={{
        w: '30px',
        h: '30px',
        overflow: 'hidden',
        borderRadius: 'base',
        border: '1px solid',
        cursor: 'pointer',
        borderColor: 'gray.200',
        _hover: {
          opacity: 0.8,
        },
        _dark: {
          borderColor: 'gray.700',
        },
      }}
    >
      <Image
        src={value ?? ''}
        ignoreFallback={true}
        sx={{
          w: '100%',
          h: '100%',
          objectFit: 'cover',
        }}
      />
    </Box>
  )
}

export const PrefImageField = ({
  pref,
  value,
  onSave,
  onRemove,
  isDisabled = false,
}: PrefImageFieldProps) => {
  const toast = useToast()

  const onDrop = async (acceptedFiles: File[]) => {
    const file = acceptedFiles?.[0]
    if (!file) {
      return
    }
    try {
      const reader = new FileReader()
      reader.onabort = () => {
        toast({
          status: 'warning',
          msg: 'File reading was aborted.',
        })
      }
      reader.onerror = () => {
        toast({
          status: 'error',
          msg: 'File reading has failed.',
        })
      }
      reader.onload = async () => {
        const binaryUrl = reader.result as string
        await onSave(pref, binaryUrl)
        toast({
          status: 'success',
          msg: 'Image was uploaded successfully.',
        })
      }
      return reader.readAsDataURL(file)
    } catch (error: unknown) {
      toast({
        status: 'error',
        msg: error instanceof Error ? error?.message : 'Unable to read file.',
      })
    }
  }

  const onDropRejected = () => {
    toast({
      status: 'error',
      msg: 'File type is not valid.',
    })
  }

  const { getInputProps, getRootProps } = useDropzone({
    disabled: isDisabled,
    accept: {
      'image/jpg': ['.jpg'],
      'image/jpeg': ['.jpeg'],
      'image/png': ['.png'],
      'image/svg+xml': ['.svg'],
    },
    maxSize: 2097152, // 2MB
    maxFiles: 1,
    multiple: false,
    onDrop,
    onDropRejected,
  })

  return (
    <Flex
      sx={{
        justifyItems: 'center',
        w: '100%',
        h: '40px',
        alignItems: 'center',
        justifyContent: 'space-between',
      }}
    >
      <ImageView value={value} />
      <ButtonGroup size="md" isAttached>
        {value && onRemove && (
          <IconButton
            aria-label="Remove image"
            icon={<TrashIcon size={16} />}
            variant="link"
            colorScheme="red"
            onClick={() => onRemove(pref)}
            isDisabled={isDisabled}
          />
        )}
        <IconButton
          aria-label="Select image"
          icon={<ImageUpIcon size={16} />}
          variant="link"
          colorScheme="secondary"
          {...getRootProps()}
          isDisabled={isDisabled}
        />
      </ButtonGroup>
      <input {...getInputProps()} />
    </Flex>
  )
}
