import { memo, useCallback, useState } from 'react'
import { Box, Grid } from '@chakra-ui/react'
import { useLogStream } from '@/features/codec-editor/hooks/use-log-stream'

export const LogPanel = memo(() => {
  const { log } = useLogStream()
  const [activeRow, setActiveRow] = useState<string>('')

  const getColor = useCallback((type: string) => {
    switch (type) {
      case 'error':
        return 'red.400'
      case 'info':
        return 'blue.500'
      default:
        return 'gray.600'
    }
  }, [])

  const gridStyle = useCallback(
    (type: string, time: string) => {
      const color = getColor(type)

      return {
        gridTemplateColumns: '120px 1fr',
        wordBreak: 'break-word',
        fontFamily: 'mono',
        fontSize: 'xs',
        cursor: 'pointer',
        p: 1,
        bg: activeRow === time ? color : 'transparent',
        color: activeRow === time ? 'white' : 'gray.600',
        _dark: {
          color: 'white',
        },
        _hover: {
          bg: activeRow === time ? color : 'gray.100',
          color: activeRow === time ? 'white' : 'gray.600',
          _dark: {
            bg: activeRow === time ? color : 'blackAlpha.100',
            color: 'white',
          },
        },
        _active: {
          bg: activeRow === time ? color : 'gray.100',
          color: activeRow === time ? 'white' : 'gray.600',
          _dark: {
            bg: activeRow === time ? color : 'blackAlpha.100',
            color: 'white',
          },
        },
      }
    },
    [activeRow, getColor]
  )

  return (
    <Box
      sx={{
        h: '100%',
        overflowY: 'auto',
      }}
    >
      {log.map(({ type, time, text }, index) => (
        <Grid
          key={`${time}-${index}`}
          onClick={() => setActiveRow(time)}
          sx={gridStyle(type, time)}
        >
          <>
            <Box>{time}</Box>
            <Box
              sx={{
                whiteSpace: 'pre-wrap',
              }}
            >
              {text}
            </Box>
          </>
        </Grid>
      ))}
    </Box>
  )
})
