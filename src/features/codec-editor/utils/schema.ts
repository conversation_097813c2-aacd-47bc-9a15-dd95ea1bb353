import { z } from 'zod'
import { defaultValues } from '@/features/codec-editor/utils/default-values'
import type { CodecClassEnum, DecoderFormatEnum } from '@/types/codec-editor'

export const schema = z.object({
  codec: z.object({
    id: z.string().nullable(),
    name: z.string(),
    official: z.boolean(),
    public: z.boolean(),
    opensource: z.boolean(),
    customID: z.boolean(),
    class: z.custom<CodecClassEnum>(),
    owner: z.boolean(),
    native: z.boolean(),
    timeout: z.number(),
    modules: z.array(z.string()).nullable(),
    files: z.array(
      z.object({
        name: z.string(),
        source: z.string(),
      })
    ),
    selectedFileIndex: z.number(),
  }),
  builder: z.object({
    method: z.string(),
    offset: z.string(),
    channel: z.number(),
    type: z.string(),
    unit: z.string(),
  }),
  decoder: z.object({
    payload: z.string(),
    format: z.custom<DecoderFormatEnum>(),
    fport: z.number(),
  }),
  encoder: z.object({
    channel: z.object({
      numeric: z.boolean(),
      value: z.string(),
    }),
    command: z.object({
      numeric: z.boolean(),
      value: z.string(),
    }),
  }),
})

export const defaultCodec = defaultValues.codec
