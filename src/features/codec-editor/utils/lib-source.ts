export const LIB_SOURCE = `
declare namespace Codec {
  type Dict<T = any> = Record<string, T>

  /**
   * Encoder description.
   */
  export interface Encoder {
    data: {
      channel: number
      value: Dict
    }
    send(args: Dict): void
  }

  interface DecoderSend {
    channel: number
    type: string
    unit: string
    value: string | number
    name: string
  }

  /**
   * Decoder description.
   */
  export interface Decoder {
    data: {
      buffer: CodecLib.Buffer
    }
    send(args: DecoderSend): void
  }

  /**
   * Optimus description.
   */
  export interface Optimus {
    data: {
      format: string
      buffer: CodecLib.Buffer
      text: string
      json: Dict
      fport: number
      timestamp: number
      latency: number
      options: Dict
      session: Dict
      children: Array<any>
      hids: Array<string | number>
    }
    sendSensors(): any
    sendDownlink(): any
    saveSession(): any
    saveOptions(): any
    error(): any
    requestChildren(): any
    done(): any
  }
}

declare const Encoder: Codec.Encoder
declare const Decoder: Codec.Decoder
declare const Optimus: Codec.Optimus
`
