import { NEW_CODEC_FILES } from '@/features/codec-editor/utils/new-codec-files'
import type { DecoderFormatEnum, CodecClassEnum } from '@/types/codec-editor'

export const defaultValues = {
  codec: {
    id: null as string | null,
    name: '',
    official: false,
    public: false,
    opensource: false,
    customID: false,
    class: 'codec' as CodecClassEnum,
    native: false,
    owner: true,
    timeout: 0,
    files: NEW_CODEC_FILES,
    modules: [] as string[] | null,
    selectedFileIndex: 1,
  },
  builder: {
    method: '',
    offset: '',
    channel: 0,
    type: '',
    unit: '',
  },
  decoder: {
    payload: '',
    format: 'hex' as DecoderFormatEnum,
    fport: 99,
  },
  encoder: {
    channel: {
      numeric: false,
      value: '',
    },
    command: {
      numeric: false,
      value: '',
    },
  },
}
