import { atom } from 'jotai'
import { format } from 'date-fns'
import { focusAtom } from 'jotai-optics'
import { atomWithReset } from 'jotai/utils'
import type {
  CodecStateAtomProps,
  EditorAtomProps,
  EditorLog,
} from '@/types/store'

// -- panel size atom state
export const panelSizeAtom = atom<number>(0)

// --- codec decode state

const defaultCodecState = {
  console: '',
  error: '',
  hids: [],
  devices: [],
  options: {},
  session: {},
  sessionRefresh: true,
  optionsRefresh: true,
}

export const codecStateAtom =
  atomWithReset<CodecStateAtomProps>(defaultCodecState)

// --- editor atom state

const defaultEditor = {
  monacoRef: null,
  editorRef: null,
  error: '',
  snippet: '',
}

export const editorAtom = atomWithReset<EditorAtomProps>(defaultEditor)

export const monacoRefAtom = focusAtom(editorAtom, (optic) =>
  optic.prop('monacoRef')
)

export const editorRefAtom = focusAtom(editorAtom, (optic) =>
  optic.prop('editorRef')
)

export const editorSnippetAtom = focusAtom(editorAtom, (optic) =>
  optic.prop('snippet')
)

// --- log panel atom state

const defaultLog: EditorLog = {
  time: format(Date.now(), 'kk:mm:ss.SSS'),
  type: 'idle',
  text: '',
}

const logAtom = atom(defaultLog)

export const editorLogAtom = atom<EditorLog[]>([])

export const resetEditorLogAtom = atom(null, (_get, set) => {
  set(logAtom, defaultLog)
  set(editorLogAtom, [])
})
