import { useEffect } from 'react'
import { useController, type Control } from 'react-hook-form'
import {
  Box,
  Button,
  FormControl,
  FormLabel,
  Input,
  InputGroup,
  InputLeftAddon,
  useColorModeValue,
  type InputProps,
} from '@chakra-ui/react'
import { useUpdateEffect } from '@react-hookz/web'

interface LeftAddonInputFieldProps extends InputProps {
  name: string
  label: string
  control: Control<any, any>
  inputTypeId: string
  isRequired?: boolean
  isDisabled?: boolean
  isReadOnly?: boolean
}

export const LeftAddonInputField = ({
  name,
  label,
  control,
  inputTypeId,
  isRequired = false,
  isDisabled = false,
  isReadOnly = false,
  ...rest
}: LeftAddonInputFieldProps) => {
  const inputVariant = useColorModeValue('outline', 'filled')

  const {
    field: { onChange: setNumeric, value: isNumeric },
  } = useController({
    name: inputTypeId,
    control,
  })

  const {
    field: { onChange, ...inputProps },
    fieldState: { error },
  } = useController({
    name,
    control,
  })

  useEffect(() => {
    if (isNumeric === undefined) {
      setNumeric(false)
      onChange(isNumeric ? 0 : '')
    }
  }, [isNumeric, setNumeric, onChange])

  useUpdateEffect(() => {
    onChange(isNumeric ? 0 : '')
  }, [isNumeric])

  return (
    <FormControl
      as={Box}
      isInvalid={!!error}
      isRequired={isRequired}
      isDisabled={isDisabled}
      isReadOnly={isReadOnly}
      mb={2}
    >
      <FormLabel htmlFor={name} mb={1}>
        {label}
      </FormLabel>
      <InputGroup>
        <InputLeftAddon
          as={Button}
          colorScheme="gray"
          onClick={() => setNumeric(!isNumeric)}
          sx={{
            cursor: 'pointer',
            userSelect: 'none',
            fontFamily: 'mono',
            fontWeight: 'medium',
            fontSize: 'xs',
            textTransform: 'capitalize',
          }}
        >
          {isNumeric ? 'NUM' : 'TXT'}
        </InputLeftAddon>
        <Input
          id={name}
          type={isNumeric ? 'number' : 'text'}
          spellCheck={false}
          variant={inputVariant}
          onChange={(e) => {
            if (isNumeric) {
              const value = e.target.valueAsNumber
              onChange(Number.isNaN(value) ? '' : value)
            } else {
              onChange(e.target.value)
            }
          }}
          {...inputProps}
          {...rest}
        />
      </InputGroup>
    </FormControl>
  )
}
