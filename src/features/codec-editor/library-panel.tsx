import { lazy, memo, useCallback } from 'react'
import { pick } from 'lodash'
import { useReset<PERSON>tom } from 'jotai/utils'
import { useFormContext } from 'react-hook-form'
import { AccordionItem } from '@chakra-ui/react'
import { BookOpenTextIcon } from 'lucide-react'
import { AccordionButton } from '@/features/codec-editor/accordion-button'
import { defaultCodec } from '@/features/codec-editor/utils/schema'
import { defaultValues } from '@/features/codec-editor/utils/default-values'
import { codecStateAtom } from '@/features/codec-editor/utils/store'
import { useModal } from '@/hooks/use-modal'
import type { CodecEditorFormProps } from '@/types/codec-editor'

const Library = lazy(() =>
  import('@/pages/codec-editor/library').then(({ Library }) => ({
    default: Library,
  }))
)

export const LibraryPanel = memo(() => {
  const modal = useModal()
  const resetCodecState = useResetAtom(codecStateAtom)
  const { reset } = useFormContext<CodecEditorFormProps>()

  const onLibrary = useCallback((): void => {
    modal({
      size: '4xl',
      scrollBehavior: 'inside',
      component: <Library />,
      config: {
        data: null,
        onCallback: (result) => {
          resetCodecState()
          reset({
            ...defaultValues,
            codec: {
              ...defaultCodec,
              ...pick(result, Object.keys(defaultCodec)),
            },
          })
        },
      },
    })
  }, [modal, reset, resetCodecState])

  return (
    <AccordionItem
      sx={{
        borderColor: 'blackAlpha.200',
        _dark: {
          borderColor: 'gray.900',
        },
      }}
    >
      <AccordionButton
        label="Codec Library"
        icon={BookOpenTextIcon}
        onClick={onLibrary}
      />
    </AccordionItem>
  )
})
