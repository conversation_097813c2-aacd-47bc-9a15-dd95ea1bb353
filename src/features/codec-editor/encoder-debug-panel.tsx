import { memo, useState } from 'react'
import { useFormContext, useWatch } from 'react-hook-form'
import { useAtom } from 'jotai'
import { is } from 'ramda'
import { useMutation } from '@tanstack/react-query'
import { Button, AccordionItem, AccordionPanel } from '@chakra-ui/react'
import { BugPlayIcon } from 'lucide-react'
import { AccordionButton } from '@/features/codec-editor/accordion-button'
import { LeftAddonInputField } from '@/features/codec-editor/left-addon-input-field'
import { codecStateAtom } from '@/features/codec-editor/utils/store'
import { useLogStream } from '@/features/codec-editor/hooks/use-log-stream'
import { useOrganization } from '@/hooks/use-organization'
import { encodeOptimusCodec } from '@/api/optimus'
import type { OptimusDecodeResponse, OptimusEncodeInput } from '@/types/api'
import type { CodecEditorFormProps } from '@/types/codec-editor'

export const EncoderDebugPanel = memo(() => {
  const { setLog } = useLogStream()
  const { organizationId, applicationId } = useOrganization()
  const [codecState, setCodecState] = useAtom(codecStateAtom)
  const [isSubmitting, setSubmitting] = useState<boolean>(false)

  const { trigger, control, getFieldState } =
    useFormContext<CodecEditorFormProps>()

  const codec = useWatch({
    name: 'codec',
    control,
  })

  const encoder = useWatch({
    name: 'encoder',
    control,
    defaultValue: {
      channel: { numeric: false, value: '' },
      command: { numeric: false, value: '' },
    },
  })

  const { mutateAsync: encodeMutation } = useMutation<
    OptimusDecodeResponse,
    Error,
    {
      organizationId: string
      applicationId: string
      codecId: string
      input: OptimusEncodeInput
    }
  >({
    mutationFn: ({ organizationId, applicationId, codecId, input }) =>
      encodeOptimusCodec({
        organizationId,
        applicationId,
        codecId,
        input,
      }),
  })

  const parseOptionValue = (options: string): Dict | undefined => {
    try {
      const o = JSON.parse(options)
      return Object.keys(o).length > 0 ? o : undefined
    } catch {
      return
    }
  }

  const onEncode = async (): Promise<boolean> => {
    setSubmitting(true)
    const valid = await trigger('encoder')

    if (!valid) {
      setSubmitting(false)
      setLog({
        type: 'error',
        text: 'Invalid encoder data.',
      })
      return false
    }

    if (
      !(organizationId && applicationId && codec.id && encoder) ||
      getFieldState('encoder')?.error
    ) {
      setSubmitting(false)
      setLog({
        type: 'error',
        text: 'Invalid encoder data.',
      })
      return false
    }

    const session = JSON.stringify(codecState.session, undefined, 2)
    const options = JSON.stringify(codecState.options, undefined, 2)

    const input = {
      channel: encoder.channel.value,
      value: encoder.command.value,
      options: parseOptionValue(options),
      session: parseOptionValue(session),
    } satisfies OptimusEncodeInput

    try {
      const result = await encodeMutation({
        organizationId,
        applicationId,
        codecId: codec.id,
        input,
      })

      setCodecState((prev) => ({
        ...prev,
        ...result,
      }))

      if (result.console) {
        setLog({
          type: 'info',
          text: is(Object, result.console)
            ? JSON.stringify(result.console, null, 2)
            : result.console,
        })
      } else {
        setLog({
          type: 'info',
          text: JSON.stringify(result, null, 2),
        })
      }
      return true
    } catch (error: unknown) {
      setLog({
        type: 'error',
        text:
          error instanceof Error
            ? JSON.stringify({ error }, null, 2)
            : 'Unable to encode.',
      })
      return false
    } finally {
      setSubmitting(false)
    }
  }

  if (!codec.id) {
    return null
  }

  return (
    <AccordionItem
      sx={{
        borderColor: 'blackAlpha.200',
        _dark: {
          borderColor: 'gray.900',
        },
      }}
    >
      <AccordionButton label="Encoder Debug" icon={BugPlayIcon} />
      <AccordionPanel
        sx={{
          bg: 'gray.50',
          _dark: {
            bg: 'gray.900',
          },
        }}
      >
        <LeftAddonInputField
          name="encoder.channel.value"
          inputTypeId="encoder.channel.numeric"
          label="Channel"
          control={control}
        />
        <LeftAddonInputField
          name="encoder.command.value"
          inputTypeId="encoder.command.numeric"
          label="Command to encode"
          control={control}
        />
        <Button
          type="button"
          aria-label="Encode"
          w="full"
          colorScheme="primary"
          isLoading={isSubmitting}
          isDisabled={isSubmitting}
          loadingText="Encoding"
          onClick={onEncode}
        >
          Encode
        </Button>
      </AccordionPanel>
    </AccordionItem>
  )
})
