import { chakra } from '@chakra-ui/react'
import { PanelResizeHandle } from 'react-resizable-panels'

export const PanelResize = chakra(PanelResizeHandle, {
  baseStyle: {
    h: '4px',
    w: '100%',
    borderRadius: 0,
    bg: 'transparent',
    '&[data-resize-handle-state="hover"]': {
      bg: 'blackAlpha.100',
    },
    '&[data-resize-handle-state="drag"]': {
      bg: 'blackAlpha.400',
    },
  },
})
