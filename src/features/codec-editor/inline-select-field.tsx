import { useCallback } from 'react'
import { use<PERSON><PERSON>roller } from 'react-hook-form'
import { Box, FormControl, FormLabel, Grid } from '@chakra-ui/react'
import { useUpdateEffect } from '@react-hookz/web'
import { Select } from '@/components/select'
import { useLogStream } from '@/features/codec-editor/hooks/use-log-stream'

export const InlineSelectField = ({
  name,
  label,
  control,
  options = [],
  isRequired = false,
  isDisabled = false,
  isReadOnly = false,
  isSearchable = false,
  isClearable = false,
  isMulti = false,
  ...rest
}: any) => {
  const { setLog } = useLogStream()
  const {
    field: { onChange, onBlur, value, ref: fieldRef },
    fieldState: { error },
  } = useController({
    name,
    control,
  })

  const onChangeOption = useCallback(
    (option: any) =>
      isMulti ? option.map((item: any) => item.value) : option?.value,
    [isMulti]
  )

  const getCurrentOption = useCallback(
    (value: any) => {
      if (options.length === 0) {
        return isMulti ? [] : undefined
      }
      return isMulti
        ? options.filter((option: any) => (value ?? []).includes(option.value))
        : options.find((option: any) => option.value === value)
    },
    [isMulti, options]
  )

  useUpdateEffect(() => {
    if (!error) {
      return
    }
    setLog({
      type: 'error',
      text: error?.message ?? 'Input error',
    })
  }, [error])

  return (
    <Box pb={2}>
      <FormControl
        isInvalid={!!error}
        isRequired={isRequired}
        isDisabled={isDisabled}
        isReadOnly={isReadOnly}
      >
        <Grid
          templateColumns="0.5fr 1fr"
          flex="1"
          alignItems="center"
          justifyContent="space-between"
        >
          <FormLabel
            htmlFor={name}
            sx={{ margin: 0, span: { display: 'none' } }}
          >
            {label}
          </FormLabel>
          <Select
            instanceId={name}
            name={name}
            value={getCurrentOption(value)}
            onBlur={onBlur}
            onChange={(option: any) => onChange(onChangeOption(option))}
            options={options}
            isMulti={isMulti}
            isSearchable={isSearchable}
            isClearable={isClearable}
            ref={fieldRef}
            {...rest}
          />
        </Grid>
      </FormControl>
    </Box>
  )
}
