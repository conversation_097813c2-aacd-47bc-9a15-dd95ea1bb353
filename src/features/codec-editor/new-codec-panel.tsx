import { memo } from 'react'
import { useFormContext } from 'react-hook-form'
import { AccordionItem } from '@chakra-ui/react'
import { PlusIcon } from 'lucide-react'
import { AccordionButton } from '@/features/codec-editor/accordion-button'
import { defaultCodec } from '@/features/codec-editor/utils/schema'
import type { CodecEditorFormProps } from '@/types/codec-editor'

export const NewCodecPanel = memo(() => {
  const { setValue } = useFormContext<CodecEditorFormProps>()

  return (
    <AccordionItem
      sx={{
        borderColor: 'blackAlpha.200',
        _dark: {
          borderColor: 'gray.900',
        },
      }}
    >
      <AccordionButton
        label="New Codec"
        icon={PlusIcon}
        onClick={() => setValue('codec', defaultCodec)}
      />
    </AccordionItem>
  )
})
