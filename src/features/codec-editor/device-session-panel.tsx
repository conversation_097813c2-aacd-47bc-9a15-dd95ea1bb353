import { useState, useEffect } from 'react'
import { useAtom } from 'jotai'
import { equals } from 'ramda'
import { useWatch, type Control } from 'react-hook-form'
import {
  Textarea,
  FormControl,
  AccordionItem,
  AccordionPanel,
} from '@chakra-ui/react'
import { useUpdateEffect } from '@react-hookz/web'
import { RotateCcwIcon } from 'lucide-react'
import { AccordionButton } from '@/features/codec-editor/accordion-button'
import { codecStateAtom } from '@/features/codec-editor/utils/store'
import { useLogStream } from '@/features/codec-editor/hooks/use-log-stream'
import { Select } from '@/components/select'
import { isJson } from '@/utils/is-json'
import type { CodecEditorFormProps } from '@/types/codec-editor'

const refreshOptions: BaseOption<boolean>[] = [
  {
    label: 'Refresh with result',
    value: true,
  },
  {
    label: 'Use custom session',
    value: false,
  },
]

interface Props {
  control: Control<any, any>
}

export const DeviceSessionPanel = ({ control }: Props) => {
  const { setLog } = useLogStream()
  const [codecState, setCodecState] = useAtom(codecStateAtom)
  const [payload, setPayload] = useState<string>('{}')
  const [refreshType, setRefreshType] = useState<BaseOption<boolean>>()
  const [isInvalid, setInvalid] = useState<boolean>(false)
  const [isEditing, setEditing] = useState<boolean>(false)

  const codec = useWatch<CodecEditorFormProps, 'codec'>({
    name: 'codec',
    control,
  })

  const onChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    try {
      setPayload(event.target.value)
    } catch {
      setPayload('{}')
    }
  }

  useEffect(() => {
    setRefreshType(
      refreshOptions.find(({ value }) => value === codecState.sessionRefresh)
    )
  }, [codecState.sessionRefresh])

  useUpdateEffect(() => {
    if (!codecState.sessionRefresh) {
      return
    }

    try {
      if (isEditing) {
        return
      }
      setPayload(JSON.stringify(codecState.session, null, 2))
    } catch {
      setLog({
        type: 'error',
        text: 'Unable to parse decode session.',
      })
    }
  }, [codecState.session])

  useUpdateEffect(() => {
    if (!isJson(payload)) {
      return
    }

    const session = JSON.parse(payload)

    if (!codecState.sessionRefresh) {
      setCodecState((prev) => ({ ...prev, session }))
      setInvalid(false)
      setLog({
        type: 'info',
        text: 'Custom device session updated.',
      })
      return
    }

    try {
      const newString = JSON.stringify(session, null, 2)
      const oldString = JSON.stringify(codecState.session, null, 2)
      if (!equals(newString, oldString)) {
        setCodecState((prev) => ({ ...prev, session }))
        setPayload(newString)
        setInvalid(false)
        setLog({
          type: 'info',
          text: 'Device session updated.',
        })
      }
    } catch {
      setInvalid(true)
      setLog({
        type: 'error',
        text: 'Unable to parse device session.',
      })
    }
  }, [payload])

  if (!codec.id) {
    return null
  }

  return (
    <AccordionItem
      sx={{
        borderColor: 'blackAlpha.200',
        _dark: {
          borderColor: 'gray.900',
        },
      }}
    >
      <AccordionButton label="Device Session" icon={RotateCcwIcon} />
      <AccordionPanel
        sx={{
          bg: 'gray.50',
          _dark: {
            bg: 'gray.900',
          },
        }}
      >
        <Select
          value={refreshType}
          options={refreshOptions}
          onChange={(option: BaseOption<boolean>) => {
            setRefreshType(option)
            setCodecState((prev) => ({ ...prev, sessionRefresh: option.value }))
          }}
        />
        <FormControl isInvalid={isInvalid} id="session">
          <Textarea
            size="sm"
            rows={8}
            variant="filled"
            spellCheck={false}
            value={payload}
            onFocus={() => setEditing(true)}
            onBlur={() => setEditing(false)}
            onChange={onChange}
            sx={{
              mt: 2,
              p: 2,
              fontFamily: 'mono',
              fontSize: 'xs',
              fontWeight: 'medium',
              whiteSpace: 'pre',
            }}
          />
        </FormControl>
      </AccordionPanel>
    </AccordionItem>
  )
}
