import { memo, useCallback, useRef, type ChangeEvent } from 'react'
import { useFormContext } from 'react-hook-form'
import { hasPath } from 'ramda'
import {
  Badge,
  Box,
  Flex,
  Icon,
  Input,
  AccordionButton,
  AccordionItem,
} from '@chakra-ui/react'
import { CloudUploadIcon } from 'lucide-react'
import { defaultCodec } from '@/features/codec-editor/utils/schema'
import { useLogStream } from '@/features/codec-editor/hooks/use-log-stream'
import type { CodecFormProps, CodecEditorFormProps } from '@/types/codec-editor'

const requiredKeys = [
  'class',
  'files',
  'id',
  'modules',
  'name',
  'official',
  'opensource',
  'public',
  'timeout',
] as const

export const ImportPanel = memo(() => {
  const { setLog } = useLogStream()
  const inputFileRef = useRef<HTMLInputElement>(null)
  const { reset } = useFormContext<CodecEditorFormProps>()

  const onFileInput = useCallback((): void => {
    inputFileRef.current?.click()
  }, [])

  const onFileChangeCapture = useCallback(
    (event: ChangeEvent<HTMLInputElement>): void => {
      const file = event.target.files?.[0]
      if (!file) {
        return
      }

      const reader = new FileReader()
      reader.readAsText(file)
      reader.onload = (event: ProgressEvent<FileReader>) => {
        if (!event?.target?.result) {
          return
        }

        try {
          const result = JSON.parse(event?.target?.result.toString())

          // Verify required keys but it does not care about stray keys.
          if (!requiredKeys.every((k) => hasPath([k], result))) {
            throw new Error('Required key missing.')
          }

          const importedCodec = {
            ...defaultCodec, // polyfill
            ...result,
            id: null,
            owned: true,
            selectedFileIndex: 1,
          } satisfies CodecFormProps

          reset({
            codec: importedCodec,
          })

          if (inputFileRef.current) {
            inputFileRef.current.value = ''
          }

          setLog({
            type: 'info',
            text: 'Codec has been imported.',
          })
        } catch {
          setLog({
            type: 'error',
            text: 'Unable to parse imported file.',
          })
        }
      }
    },
    [reset, setLog]
  )

  return (
    <AccordionItem
      sx={{
        borderColor: 'blackAlpha.200',
        _dark: {
          borderColor: 'gray.900',
        },
      }}
    >
      <AccordionButton
        onClick={onFileInput}
        sx={{
          p: 4,
          _hover: {
            color: 'primary.500',
            bg: 'white',
            _dark: {
              color: 'primary.200',
              bg: 'blackAlpha.300',
            },
          },
        }}
      >
        <Flex
          sx={{
            flex: 1,
            textAlign: 'left',
            alignItems: 'center',
          }}
        >
          <Icon as={CloudUploadIcon} />
          <Flex flex="1" pl={2} align="center" justify="space-between">
            <Box>Import Codec</Box>
            <Badge colorScheme="yellow">JSON</Badge>
          </Flex>
        </Flex>
        <Input
          display="none"
          type="file"
          ref={inputFileRef}
          onChangeCapture={onFileChangeCapture}
          accept=".json"
          multiple={false}
        />
      </AccordionButton>
    </AccordionItem>
  )
})
