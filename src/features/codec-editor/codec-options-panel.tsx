import { use<PERSON><PERSON>, Controller, type Control } from 'react-hook-form'
import {
  Box,
  Tooltip,
  Checkbox,
  AccordionItem,
  AccordionPanel,
} from '@chakra-ui/react'
import { SettingsIcon } from 'lucide-react'
import { AccordionButton } from '@/features/codec-editor/accordion-button'

interface Props {
  control: Control<any, any>
}

export const CodecOptionsPanel = ({ control }: Props) => {
  const codec = useWatch({
    name: 'codec',
    control,
  })

  return (
    <AccordionItem
      sx={{
        borderColor: 'blackAlpha.200',
        _dark: {
          borderColor: 'gray.900',
        },
      }}
    >
      <AccordionButton label="Codec Options" icon={SettingsIcon} />
      <AccordionPanel
        sx={{
          bg: 'gray.50',
          _dark: {
            bg: 'gray.900',
          },
        }}
      >
        <Box>
          <Tooltip
            placement="bottom-start"
            label="Official myDevices production code"
          >
            <Box>
              <Controller
                name="codec.official"
                control={control}
                render={({ field: { name, onChange, onBlur, ref } }) => (
                  <Checkbox
                    name={name}
                    isChecked={codec.official}
                    aria-label={name}
                    ref={ref}
                    onBlur={onBlur}
                    onChange={onChange}
                    isDisabled={true}
                    colorScheme="secondary"
                  >
                    Official
                  </Checkbox>
                )}
              />
            </Box>
          </Tooltip>
          <Tooltip placement="bottom-start" label="Allow anyone to decode data">
            <Box>
              <Controller
                name="codec.public"
                control={control}
                render={({ field: { name, onChange, onBlur, ref } }) => (
                  <Checkbox
                    name={name}
                    aria-label={name}
                    isChecked={codec.public}
                    ref={ref}
                    onBlur={onBlur}
                    onChange={onChange}
                    isDisabled={true}
                    colorScheme="secondary"
                  >
                    Public
                  </Checkbox>
                )}
              />
            </Box>
          </Tooltip>
          <Tooltip placement="bottom-start" label="Allow anyone to copy codec">
            <Box>
              <Controller
                name="codec.opensource"
                control={control}
                render={({ field: { name, onChange, onBlur, ref } }) => (
                  <Checkbox
                    name={name}
                    aria-label={name}
                    isChecked={codec.opensource}
                    ref={ref}
                    onBlur={onBlur}
                    onChange={onChange}
                    isDisabled={true}
                    colorScheme="secondary"
                  >
                    Open Source
                  </Checkbox>
                )}
              />
            </Box>
          </Tooltip>
        </Box>
      </AccordionPanel>
    </AccordionItem>
  )
}
