import { memo, useCallback, useEffect, useState } from 'react'
import { useFormContext, useWatch } from 'react-hook-form'
import { isEmpty } from 'ramda'
import { useSetAtom } from 'jotai'
import { useQuery } from '@tanstack/react-query'
import { components, type OptionProps } from 'react-select'
import {
  Box,
  Button,
  Flex,
  Textarea,
  AccordionItem,
  AccordionPanel,
} from '@chakra-ui/react'
import { WrenchIcon } from 'lucide-react'
import { InlineInputField } from '@/features/codec-editor/inline-input-field'
import { InlineNumberField } from '@/features/codec-editor/inline-number-field'
import { InlineSelectField } from '@/features/codec-editor/inline-select-field'
import { AccordionButton } from '@/features/codec-editor/accordion-button'
import { editorSnippetAtom } from '@/features/codec-editor/utils/store'
import { getOptimusRAWDataTypes, getOptimusReadFunctions } from '@/api/optimus'
import type {
  CodecEditorFormProps,
  BuilderFormProps,
} from '@/types/codec-editor'
import type {
  OptimusRawDataType,
  OptimusRawDataTypeUnit,
} from '@/types/models/codec'

const TruncatedOption = (props: OptionProps<any>) => (
  <components.Option {...props}>
    <Flex justify="space-between" align="center">
      <Box noOfLines={1} fontSize="sm">
        {props.children}
      </Box>
    </Flex>
  </components.Option>
)

interface OptimusRawDataTypeOption extends OptimusRawDataType {
  label: string
  value: string
}

interface OptimusRawDataTypeUnitOption extends OptimusRawDataTypeUnit {
  label: string
  value: string
}

const permittedFiles = ['common.js', 'decoder.js']

const collator = new Intl.Collator('en-US', {
  numeric: true,
  sensitivity: 'base',
})

export const DecoderBuilderPanel = memo(() => {
  const setSnippet = useSetAtom(editorSnippetAtom)
  const [unitOptions, setUnitOptions] = useState<BaseOption[]>([])
  const [isSubmitting, setSubmitting] = useState<boolean>(false)
  const [visible, setVisibility] = useState<boolean>(false)

  const { trigger, control, setValue, getValues, getFieldState } =
    useFormContext<CodecEditorFormProps>()

  const codec = useWatch({
    name: 'codec',
    control,
  })

  const builder = useWatch({
    name: 'builder',
    control,
  })

  const { data: rawDataTypeOptions } = useQuery<
    OptimusRawDataType[],
    Error,
    OptimusRawDataTypeOption[]
  >({
    queryKey: ['GetOptimusRAWDataTypes'],
    queryFn: ({ signal }) =>
      getOptimusRAWDataTypes({
        signal,
      }),
    select: useCallback(
      (data: OptimusRawDataType[]) =>
        data
          .sort((a, b) => collator.compare(a.type_constant, b.type_constant))
          .reduce((acc: OptimusRawDataTypeOption[], curr) => {
            /**
             * Filter out duplicated `type_constant` because its used as `id` aka `value`
             * This should be corrected when creating unit so it always provide
             * unique `uuid` or unique `type_constant`.
             */
            if (
              acc.some(
                ({ type_constant }) => type_constant === curr.type_constant
              )
            ) {
              return acc
            }
            acc.push({
              ...curr,
              label: curr.type_constant,
              value: curr.type_constant,
            })
            return acc
          }, []),
      []
    ),
  })

  useEffect(() => {
    if (!rawDataTypeOptions) {
      return
    }

    const initialType = rawDataTypeOptions?.[0]
    if (!initialType) {
      return
    }

    const initialUnits = initialType.units ?? []

    setUnitOptions(getUnitOptions(initialUnits))
    setValue('builder.type', initialType.value)
    setValue('builder.unit', initialUnits?.[0]?.unit_constant ?? '')
  }, [setValue, rawDataTypeOptions])

  const getUnitOptions = (
    units: OptimusRawDataTypeUnit[]
  ): OptimusRawDataTypeUnitOption[] =>
    units
      .sort((a: any, b: any) =>
        collator.compare(a?.unit_constant, b?.unit_constant)
      )
      .reduce((acc: any[], curr: any) => {
        if (!curr?.unit_constant) {
          return acc
        }
        acc.push({
          ...curr,
          label: curr.unit_constant,
          value: curr.unit_constant,
        })
        return acc
      }, [])

  const { data: readFunctionOptions } = useQuery<
    string[],
    Error,
    BaseOption<string>[]
  >({
    queryKey: ['GetOptimusReadFunctions'],
    queryFn: ({ signal }) =>
      getOptimusReadFunctions({
        signal,
      }),
    select: useCallback(
      (data: string[]) =>
        data
          .sort((a: string, b: string) => collator.compare(a, b))
          .reduce((acc: BaseOption<string>[], curr: string) => {
            acc.push({
              label: curr,
              value: curr,
            })
            return acc
          }, []),
      []
    ),
  })

  useEffect(() => {
    if (!readFunctionOptions) {
      return
    }
    setValue('builder.method', readFunctionOptions?.[0]?.value ?? '')
  }, [setValue, readFunctionOptions])

  const sensorSnippet = ({
    method,
    offset,
    channel,
    type,
    unit,
  }: BuilderFormProps): string => {
    return `\n
      Optimus.sendSensors({
        channel: ${channel},
        type: DataTypes.TYPE.${type},
        unit: DataTypes.UNIT.${unit},
        value: ${
          isEmpty(method) ? 'null' : `Optimus.data.buffer.${method}(${offset})`
        }
      });
    `
  }

  useEffect(() => {
    if (!codec.files) {
      return
    }
    const current = codec.files[codec.selectedFileIndex]
    setVisibility((current && permittedFiles.includes(current.name)) ?? false)
  }, [codec.selectedFileIndex, codec.files])

  const onBuild = async (): Promise<boolean> => {
    setSubmitting(true)
    await trigger('builder')
    const values = getValues('builder')

    if (!values || getFieldState('builder')?.error) {
      setSubmitting(false)
      return false
    }

    setSnippet(sensorSnippet(values))
    setSubmitting(false)
    return true
  }

  if (!visible) {
    return null
  }

  return (
    <AccordionItem
      sx={{
        borderColor: 'blackAlpha.200',
        _dark: {
          borderColor: 'gray.900',
        },
      }}
    >
      <AccordionButton label="Decoder Builder" icon={WrenchIcon} />
      <AccordionPanel
        sx={{
          bg: 'gray.50',
          _dark: {
            bg: 'gray.900',
          },
        }}
      >
        <InlineSelectField
          name="builder.method"
          label="Method"
          control={control}
          options={readFunctionOptions}
          components={{ Option: TruncatedOption }}
          isRequired
          isSearchable
        />
        <InlineInputField
          name="builder.offset"
          label="Offset"
          placeholder="Payload Address"
          control={control}
          isRequired
        />
        <InlineNumberField
          name="builder.channel"
          label="Channel"
          control={control}
          isRequired
        />
        <InlineSelectField
          name="builder.type"
          label="Type"
          control={control}
          options={rawDataTypeOptions}
          components={{ Option: TruncatedOption }}
          isRequired
          isSearchable
          onChange={(option: OptimusRawDataTypeOption) => {
            setValue('builder.type', option.value)
            setUnitOptions(getUnitOptions(option.units))
          }}
        />
        <InlineSelectField
          name="builder.unit"
          label="Unit"
          control={control}
          options={unitOptions}
          components={{ Option: TruncatedOption }}
          isRequired
          isSearchable
        />
        <Button
          type="button"
          aria-label="Insert Code"
          w="full"
          colorScheme="primary"
          isLoading={isSubmitting}
          isDisabled={isSubmitting}
          loadingText="Inserting"
          onClick={onBuild}
        >
          Insert Code
        </Button>
        <Textarea
          size="sm"
          rows={8}
          variant="filled"
          value={JSON.stringify(builder, null, 2)}
          readOnly
          sx={{
            mt: 2,
            p: 2,
            fontFamily: 'mono',
            fontSize: 'xs',
            fontWeight: 'medium',
            whiteSpace: 'pre',
          }}
        />
      </AccordionPanel>
    </AccordionItem>
  )
})
