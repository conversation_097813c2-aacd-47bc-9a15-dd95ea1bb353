import { Fragment, useState, useMemo, useEffect, type RefObject } from 'react'
import { useCustomCompareEffect, useUnmountEffect } from '@react-hookz/web'
import type Monaco from 'monaco-editor/esm/vs/editor/editor.api'
import { Editor, type Monaco as MonacoInstance } from '@monaco-editor/react'
import { useWatch, type Control } from 'react-hook-form'
import { isNil, toString as stringify, equals } from 'ramda'
import { useAtomValue, useSet<PERSON>tom } from 'jotai'
import { useResetAtom } from 'jotai/utils'
import { useQueryClient } from '@tanstack/react-query'
import { useColorMode, useTheme } from '@chakra-ui/react'
import { useEditor } from '@/features/codec-editor/hooks/use-editor'
import { LIB_SOURCE } from '@/features/codec-editor/utils/lib-source'
import {
  editor<PERSON>tom,
  editorRef<PERSON>tom,
  editorSnippet<PERSON><PERSON>,
  monacoRefAtom,
  panelSize<PERSON>tom,
} from '@/features/codec-editor/utils/store'
import { getEditorLanguage } from '@/features/codec-editor/utils/get-editor-language'
import { useLogStream } from '@/features/codec-editor/hooks/use-log-stream'
import { getOptimusDataTypes, getOptimusReadFunctions } from '@/api/optimus'
import { useEditorTheme } from '@/hooks/use-editor-theme'
import type { CodecEditorFormProps } from '@/types/codec-editor'

interface MonacoEditorProps {
  saveRef: RefObject<HTMLButtonElement>
  control: Control<any, any>
}

export const MEditor = ({ saveRef, control }: MonacoEditorProps) => {
  const theme = useTheme()
  const { setLog } = useLogStream()
  const { colorMode } = useColorMode()
  const { light, dark } = useEditorTheme()
  const queryClient = useQueryClient()
  const { updateCurrentFile } = useEditor()
  const editor = useAtomValue(editorAtom)
  const setMonacoRef = useSetAtom(monacoRefAtom)
  const setEditorRef = useSetAtom(editorRefAtom)
  const resetEditor = useResetAtom(editorAtom)
  const setSnippet = useSetAtom(editorSnippetAtom)
  const [isEditorReady, setIsEditorReady] = useState<boolean>(false)
  const [currentValue, setCurrentValue] = useState<string>('')
  const panelSize = useAtomValue(panelSizeAtom)

  useEffect(() => {
    if (editor?.editorRef) {
      editor.editorRef.layout()
    }
  }, [panelSize, editor.editorRef])

  const codec = useWatch<CodecEditorFormProps, 'codec'>({
    name: 'codec',
    control,
  })

  const themeId = useMemo<string>(
    () => (colorMode === 'dark' ? 'night' : 'day'),
    [colorMode]
  )

  const currentFile = useMemo<
    | {
        name: string
        source: string
      }
    | undefined
  >(() => {
    return codec.files[codec.selectedFileIndex]
  }, [codec.files, codec.selectedFileIndex])

  const language = useMemo<string>(() => {
    return getEditorLanguage(currentFile?.name ?? 'javascript')
  }, [currentFile?.name])

  useCustomCompareEffect(
    () => {
      if (!currentFile || currentFile?.source === currentValue) {
        return
      }
      setCurrentValue(currentFile?.source)
    },
    [currentFile],
    (a, b) => equals(a, b)
  )

  useUnmountEffect(() => {
    if (editor?.editorRef) {
      editor.editorRef.dispose()
    }
  })

  const onWillMount = async (monaco: MonacoInstance) => {
    // Configure if all existing models should be eagerly sync'd to the worker on start or restart.
    monaco.languages.typescript.javascriptDefaults.setEagerModelSync(true)

    // Configure whether syntactic and/or semantic validation should be performed.
    monaco.languages.typescript.javascriptDefaults.setDiagnosticsOptions({
      noSemanticValidation: true,
      noSyntaxValidation: false,
    })

    // Register compiler options.
    monaco.languages.typescript.javascriptDefaults.setCompilerOptions({
      target: monaco.languages.typescript.ScriptTarget.ES5,
      allowNonTsExtensions: true,
      noImplicitAny: false,
      strictNullChecks: false,
      strict: false,
      allowJs: true,
      checkJs: true,
    })

    if (monaco.editor.getModels().length === 0) {
      const libUri = 'ts:filename/codec-editor.d.ts'
      let libSource = `
        ${LIB_SOURCE}
        declare namespace CodecLib {
          export interface DataTypes {
            TYPE: any
            UNIT: any
          }
          export interface Buffer {}
        }
        declare const DataTypes: CodecLib.DataTypes
        declare const Buffer: CodecLib.Buffer
      `

      try {
        const dataTypes = await queryClient.fetchQuery({
          queryKey: ['GetOptimusDataTypes'],
          queryFn: ({ signal }) =>
            getOptimusDataTypes({
              signal,
            }),
        })

        const readFunctions = await queryClient.fetchQuery({
          queryKey: ['GetOptimusReadFunctions'],
          queryFn: ({ signal }) =>
            getOptimusReadFunctions({
              signal,
            }),
        })

        const fns = readFunctions
          .map((fn) => `${fn}(...args: any): any`)
          .join('\n')

        libSource = `
          ${LIB_SOURCE}
          declare namespace CodecLib {
            export interface DataTypes ${stringify(
              dataTypes ?? '{ TYPE: any, UNIT: any }'
            )}
            export interface Buffer {
              ${fns}
            }
          }
          declare const DataTypes: CodecLib.DataTypes
          declare const Buffer: CodecLib.Buffer
        `
      } catch {
        setLog({
          type: 'error',
          text: 'Unable to fetch type declarations.',
        })
      }

      // Add an additional source file to the language service.
      monaco.languages.typescript.javascriptDefaults.addExtraLib(
        libSource,
        libUri
      )

      /**
       * When resolving definitions and references, the editor will try
       * to use created models. Creating a model for the library allows
       * "peek definition/references" commands to work with the library.
       */
      monaco.editor.createModel(
        libSource,
        'typescript',
        monaco.Uri.parse(libUri)
      )
    }

    /**
     * Register a completion item provider for custom autocomplete suggestions.
     * Suggestions classes etc should be declared also in `libSource.ts`.
     */
    monaco.languages.registerCompletionItemProvider('javascript', {
      provideCompletionItems: (
        model: Monaco.editor.ITextModel,
        position: Monaco.Position
      ) => {
        const word = model.getWordUntilPosition(position)
        const range = {
          startLineNumber: position.lineNumber,
          endLineNumber: position.lineNumber,
          startColumn: word.startColumn,
          endColumn: word.endColumn,
        }
        return {
          suggestions: [
            {
              label: 'Decoder',
              kind: monaco.languages.CompletionItemKind.Method,
              documentation: 'Information about Decoder...',
              insertText: `
                Decoder.send({
                  channel: 0,
                  type: DataTypes.TYPE,
                  unit: DataTypes.UNIT,
                  value: '',
                  name: ''
                })\n`,
              range: range,
            },
          ],
        }
      },
    })
  }

  const onMount = (
    editor: Monaco.editor.IStandaloneCodeEditor,
    monaco: MonacoInstance
  ) => {
    monaco.editor.defineTheme('day', light)
    monaco.editor.defineTheme('night', dark)
    monaco.editor.setTheme(themeId)

    setMonacoRef(monaco)
    setEditorRef(editor)
    setIsEditorReady(true)

    editor.addAction({
      id: 'save',
      label: 'Save File',
      keybindings: [
        (monaco?.KeyMod?.CtrlCmd || 0) | (monaco?.KeyCode?.KeyS || 0),
      ],
      contextMenuGroupId: 'navigation',
      contextMenuOrder: 1.5,
      run: () => {
        if (saveRef?.current) {
          saveRef?.current?.click()
        }
      },
    })

    // Event emitted when the editor has been disposed.
    editor.onDidDispose(() => {
      resetEditor()
    })
  }

  // Change handler is not synced with validation that occurs after.
  const onChange = (value: string | undefined): void => {
    if (value) {
      setCurrentValue(value)
      updateCurrentFile(value)
    }
  }

  /**
   * Listen to snippet atom value to inject code in
   * editor area at cursor position.
   */
  useCustomCompareEffect(
    () => {
      if (!(isEditorReady && editor?.editorRef) || isNil(editor?.snippet)) {
        return
      }

      const range = editor.editorRef.getSelection()
      if (!range) {
        return
      }

      // Inject code into editor at cursor.
      editor.editorRef.executeEdits('insert', [
        {
          text: editor.snippet,
          range,
          forceMoveMarkers: true,
        },
      ])

      // Format document.
      const action = editor.editorRef.getAction('editor.action.formatDocument')
      if (action) {
        action.run()
      }
      setSnippet('')
    },
    [editor?.snippet],
    (a, b) => equals(a, b)
  )

  return (
    <Editor
      height="100%"
      width="100%"
      theme={themeId}
      language={language}
      value={currentValue}
      beforeMount={onWillMount}
      onMount={onMount}
      onChange={onChange}
      loading={<Fragment />}
      options={{
        fontSize: 13,
        fontFamily: theme.fonts.mono,
        fontWeight: '500',
        tabSize: 2,
        cursorBlinking: 'smooth',
        wordWrap: 'on',
        renderWhitespace: 'all',
        scrollBeyondLastLine: false,
        selectionHighlight: false,
        overviewRulerBorder: false,
        hideCursorInOverviewRuler: true,
        selectOnLineNumbers: true,
        scrollbar: {
          horizontal: 'hidden',
          vertical: 'visible',
          verticalHasArrows: false,
          useShadows: false,
          verticalScrollbarSize: 5,
        },
        automaticLayout: true,
        minimap: {
          enabled: false,
        },
        padding: {
          top: 10,
        },
      }}
    />
  )
}
