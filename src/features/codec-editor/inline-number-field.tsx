import type { ReactNode } from 'react'
import { useController, type Control } from 'react-hook-form'
import {
  Box,
  FormControl,
  FormLabel,
  Grid,
  Input,
  useColorModeValue,
  type InputProps,
} from '@chakra-ui/react'
import { useUpdateEffect } from '@react-hookz/web'
import { useLogStream } from '@/features/codec-editor/hooks/use-log-stream'

interface NumberFieldProps extends InputProps {
  name: string
  label: string
  control: Control<any, any>
  info?: string
  link?: ReactNode
  isRequired?: boolean
  isDisabled?: boolean
  isReadOnly?: boolean
  hasLabel?: boolean
}

export const InlineNumberField = ({
  name,
  label,
  control,
  isRequired = false,
  isDisabled = false,
  isReadOnly = false,
  ...rest
}: NumberFieldProps) => {
  const { setLog } = useLogStream()
  const inputVariant = useColorModeValue('outline', 'filled')
  const {
    field: { onChange, ...inputProps },
    fieldState: { error },
  } = useController({
    name,
    control,
  })

  useUpdateEffect(() => {
    if (!error) {
      return
    }
    setLog({
      type: 'error',
      text: error?.message ?? 'Input error',
    })
  }, [error])

  return (
    <Box pb={2}>
      <FormControl
        isInvalid={!!error}
        isRequired={isRequired}
        isDisabled={isDisabled}
        isReadOnly={isReadOnly}
      >
        <Grid
          alignItems="center"
          justifyContent="space-between"
          templateColumns="0.5fr 1fr"
        >
          <FormLabel
            htmlFor={name}
            sx={{ margin: 0, span: { display: 'none' } }}
          >
            {label}
          </FormLabel>
          <Input
            id={name}
            type="number"
            spellCheck="false"
            variant={inputVariant}
            {...inputProps}
            onChange={(e) => {
              const value = e.target.valueAsNumber
              onChange(Number.isNaN(value) ? '' : value)
            }}
            {...rest}
          />
        </Grid>
      </FormControl>
    </Box>
  )
}
