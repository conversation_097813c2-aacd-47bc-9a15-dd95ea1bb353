import { useRef } from 'react'
import { use<PERSON>tom } from 'jotai'
import { useReset<PERSON>tom } from 'jotai/utils'
import { format } from 'date-fns'
import {
  editorLog<PERSON>tom,
  resetEditorLogAtom,
} from '@/features/codec-editor/utils/store'
import type { EditorLog } from '@/types/store'

export const useLogStream = () => {
  const timeoutRef = useRef<number>(undefined)
  const [log, setLogState] = useAtom(editorLog<PERSON>tom)
  const reset = useResetAtom(resetEditorLogAtom)

  const setLog = (input: Omit<EditorLog, 'time'>) => {
    timeoutRef.current = window.setTimeout(() => {
      setLogState((prev) => [
        {
          time: format(Date.now(), 'kk:mm:ss.SSS'),
          ...input,
        },
        ...prev,
      ])
    }, 200)
  }

  const resetLog = () => {
    reset()
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
  }

  return {
    log,
    setLog,
    resetLog,
  }
}
