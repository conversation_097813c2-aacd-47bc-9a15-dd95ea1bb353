import { useMemo } from 'react'
import { useAtomValue } from 'jotai'
import { isEmpty } from 'ramda'
import { useFormContext, useWatch } from 'react-hook-form'
import { getEditorLanguage } from '@/features/codec-editor/utils/get-editor-language'
import { editorAtom } from '@/features/codec-editor/utils/store'
import type { CodecEditorFormProps } from '@/types/codec-editor'

export const useEditor = () => {
  const editor = useAtomValue(editorAtom)
  const { control, setValue } = useFormContext<CodecEditorFormProps>()

  const codec = useWatch({
    name: 'codec',
    control,
  })

  const currentFile = useMemo<
    | {
        name: string
        source: string
      }
    | undefined
  >(() => {
    return codec.files[codec.selectedFileIndex]
  }, [codec.files, codec.selectedFileIndex])

  const updateCurrentFile = (value: string) => {
    if (!(currentFile && codec.files && isEmpty(editor?.error))) {
      return
    }

    const { name } = currentFile

    setValue('codec', {
      ...codec,
      files: codec.files.map((file: any) =>
        file.name === name
          ? {
              name,
              source: value,
            }
          : file
      ),
    })
  }

  const getCurrentSource = (): {
    mode: string
    code: string
  } => {
    let result = {
      mode: 'javascript',
      code: '',
    }

    if (currentFile) {
      result = {
        mode: getEditorLanguage(currentFile.name),
        code: currentFile.source ?? '',
      }
    }

    return result
  }

  return {
    getCurrentSource,
    updateCurrentFile,
  }
}
