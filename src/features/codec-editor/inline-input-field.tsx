import type { ReactNode } from 'react'
import { useController, type Control } from 'react-hook-form'
import {
  Box,
  FormControl,
  FormLabel,
  Grid,
  Input,
  useColorModeValue,
  type InputProps,
} from '@chakra-ui/react'
import { useUpdateEffect } from '@react-hookz/web'
import { useLogStream } from '@/features/codec-editor/hooks/use-log-stream'

interface InputFieldProps extends InputProps {
  name: string
  label: string
  control: Control<any, any>
  type?: 'text' | 'number' | 'email' | 'url'
  info?: string
  link?: ReactNode
  isRequired?: boolean
  isDisabled?: boolean
  isReadOnly?: boolean
  hasLabel?: boolean
}

export const InlineInputField = ({
  name,
  label,
  control,
  type = 'text',
  isRequired = false,
  isDisabled = false,
  isReadOnly = false,
  ...rest
}: InputFieldProps) => {
  const { setLog } = useLogStream()
  const inputVariant = useColorModeValue('outline', 'filled')
  const {
    field,
    fieldState: { error },
  } = useController({
    name,
    control,
  })

  useUpdateEffect(() => {
    if (!error) {
      return
    }
    setLog({
      type: 'error',
      text: error?.message ?? 'Input error',
    })
  }, [error])

  return (
    <Box pb={2}>
      <FormControl
        isInvalid={!!error}
        isRequired={isRequired}
        isDisabled={isDisabled}
        isReadOnly={isReadOnly}
      >
        <Grid
          alignItems="center"
          justifyContent="space-between"
          templateColumns="0.5fr 1fr"
        >
          <FormLabel
            htmlFor={name}
            sx={{ margin: 0, span: { display: 'none' } }}
          >
            {label}
          </FormLabel>
          <Input
            id={name}
            type={type}
            spellCheck="false"
            variant={inputVariant}
            {...field}
            {...rest}
          />
        </Grid>
      </FormControl>
    </Box>
  )
}
