import {
  Box,
  Flex,
  Icon,
  AccordionIcon,
  AccordionButton as ChakraAccordionButton,
  type IconProps,
  type ComponentWithAs,
} from '@chakra-ui/react'
import type { LucideIcon } from 'lucide-react'

interface AccordionButtonProps {
  label: string
  icon: LucideIcon | ComponentWithAs<'svg', IconProps>
  onClick?: () => void
}

export const AccordionButton = ({
  label,
  icon,
  onClick,
}: AccordionButtonProps) => {
  const isFn = typeof onClick === 'function'

  return (
    <ChakraAccordionButton
      onClick={onClick}
      sx={{
        p: 4,
        _hover: {
          color: 'primary.500',
          bg: 'white',
          _dark: {
            color: 'secondary.200',
            bg: 'blackAlpha.300',
          },
        },
      }}
    >
      <Flex
        sx={{
          flex: 1,
          textAlign: 'left',
          alignItems: 'center',
        }}
      >
        <Icon as={icon} />
        <Box as="span" pl={2}>
          {label}
        </Box>
      </Flex>
      {isFn ? null : <AccordionIcon />}
    </ChakraAccordionButton>
  )
}
