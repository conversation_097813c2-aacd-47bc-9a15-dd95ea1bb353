import { memo, useState } from 'react'
import { useFormContext, useWatch } from 'react-hook-form'
import { isEmpty } from 'ramda'
import { useAtom } from 'jotai'
import { useQueryClient, useMutation } from '@tanstack/react-query'
import { Button, AccordionItem, AccordionPanel } from '@chakra-ui/react'
import { BugPlayIcon } from 'lucide-react'
import { InlineInputField } from '@/features/codec-editor/inline-input-field'
import { InlineNumberField } from '@/features/codec-editor/inline-number-field'
import { InlineSelectField } from '@/features/codec-editor/inline-select-field'
import { AccordionButton } from '@/features/codec-editor/accordion-button'
import { codecStateAtom } from '@/features/codec-editor/utils/store'
import { useLogStream } from '@/features/codec-editor/hooks/use-log-stream'
import { useOrganization } from '@/hooks/use-organization'
import { decodeOptimusCodec } from '@/api/optimus'
import type { OptimusDecodeResponse, OptimusDecodeInput } from '@/types/api'
import type { OptimusDecodeModel } from '@/types/models/codec'
import type {
  DecoderFormatEnum,
  CodecEditorFormProps,
  DecoderFormProps,
} from '@/types/codec-editor'

const formatOptions = [
  { label: 'Hex', value: 'hex' },
  { label: 'Base64', value: 'base64' },
  { label: 'Text', value: 'text' },
  { label: 'JSON', value: 'json' },
] satisfies BaseOption<DecoderFormatEnum>[]

export const DecoderDebugPanel = memo(() => {
  const { setLog } = useLogStream()
  const queryClient = useQueryClient()
  const { organizationId, applicationId } = useOrganization()
  const [codecState, setCodecState] = useAtom(codecStateAtom)
  const [isSubmitting, setSubmitting] = useState<boolean>(false)

  const { trigger, control, getFieldState } =
    useFormContext<CodecEditorFormProps>()

  const codec = useWatch({
    name: 'codec',
    control,
  })

  const decoder = useWatch({
    name: 'decoder',
    control,
  })

  const { mutateAsync: decodeCodecMutation } = useMutation<
    OptimusDecodeResponse,
    Error,
    {
      organizationId: string
      applicationId: string
      codecId: string
      input: OptimusDecodeInput
    }
  >({
    mutationFn: ({ organizationId, applicationId, codecId, input }) =>
      decodeOptimusCodec({
        organizationId,
        applicationId,
        codecId,
        input,
      }),
    onSuccess: () =>
      queryClient.invalidateQueries({
        queryKey: ['GetOptimusCodecs'],
      }),
  })

  const parsePayloadValue = ({
    format,
    payload,
  }: DecoderFormProps): string | undefined => {
    const value = ['hex', 'base64'].includes(format) ? payload : undefined
    return value && format === 'hex' ? (value ?? '').replace(/ /g, '') : value
  }

  const parseTextValue = ({
    format,
    payload,
  }: DecoderFormProps): string | undefined => {
    return format === 'text' ? payload : undefined
  }

  const parseJsonValue = ({
    format,
    payload,
  }: DecoderFormProps): string | undefined => {
    try {
      return format === 'json' ? JSON.parse(payload) : undefined
    } catch {
      return
    }
  }

  const parseObjValue = (json: string): Dict | undefined => {
    try {
      const o = JSON.parse(json)
      return Object.keys(o).length > 0 ? o : undefined
    } catch {
      return
    }
  }

  const cleanResponse = (str: string): string => {
    return (str ?? '').replace(/[\n\r]/g, '\n')
  }

  const decodeCodec = async (values: DecoderFormProps): Promise<boolean> => {
    if (!(organizationId && applicationId && codec.id)) {
      return false
    }

    const session = JSON.stringify(codecState.session, undefined, 2)
    const options = JSON.stringify(codecState.options, undefined, 2)

    const input = {
      format: values.format,
      fport: values.fport,
      data: parsePayloadValue(values) ?? '',
      text: parseTextValue(values),
      json: parseJsonValue(values),
      options: parseObjValue(options),
      session: parseObjValue(session),
    } satisfies OptimusDecodeInput

    try {
      const { data } = await decodeCodecMutation({
        organizationId,
        applicationId,
        codecId: codec.id,
        input,
      })

      let result: OptimusDecodeModel = {
        ...data,
        error: data?.error ?? '',
      }

      if (data?.console && !isEmpty(data.console)) {
        setLog({
          type: 'info',
          text: cleanResponse(data.console),
        })
      }

      if (data?.error && !isEmpty(data.error)) {
        setLog({
          type: 'error',
          text: cleanResponse(data.error),
        })
      }

      if (!codecState.sessionRefresh) {
        result = {
          ...result,
          session: codecState.session,
        }
      }

      if (!codecState.optionsRefresh) {
        result = {
          ...result,
          options: codecState.options,
        }
      }

      setCodecState((prev) => ({
        ...prev,
        ...result,
      }))
      return true
    } catch (error: unknown) {
      if (error instanceof Error) {
        setLog({
          type: 'error',
          text: error?.message ?? 'Parse error',
        })
      } else {
        setLog({
          type: 'error',
          text: 'Unable to fetch decoder debug data.',
        })
      }
      return false
    } finally {
      setSubmitting(false)
    }
  }

  const onDecode = async (): Promise<boolean> => {
    setSubmitting(true)
    await trigger('decoder')

    if (!(codec.id && decoder) || getFieldState('decoder')?.error) {
      setSubmitting(false)
      return false
    }

    return codec.class === 'codec' ? await decodeCodec(decoder) : false
  }

  if (!codec.id) {
    return null
  }

  return (
    <AccordionItem
      sx={{
        borderColor: 'blackAlpha.200',
        _dark: {
          borderColor: 'gray.900',
        },
      }}
    >
      <AccordionButton label="Decoder Debug" icon={BugPlayIcon} />
      <AccordionPanel
        sx={{
          bg: 'gray.50',
          _dark: {
            bg: 'gray.900',
          },
        }}
      >
        <InlineInputField
          name="decoder.payload"
          label="Payload"
          placeholder="0123456789ABCDEF"
          control={control}
        />
        <InlineSelectField
          name="decoder.format"
          label="Format"
          control={control}
          options={formatOptions}
        />
        <InlineNumberField
          name="decoder.fport"
          label="FPort"
          control={control}
        />
        <Button
          type="button"
          aria-label="Decode"
          w="full"
          colorScheme="primary"
          isLoading={isSubmitting}
          isDisabled={isSubmitting}
          loadingText="Decoding"
          onClick={onDecode}
        >
          Decode
        </Button>
      </AccordionPanel>
    </AccordionItem>
  )
})
