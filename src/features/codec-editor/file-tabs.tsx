import { memo, useMemo } from 'react'
import { useAtomValue } from 'jotai'
import { isEmpty } from 'ramda'
import { useFormContext, useWatch } from 'react-hook-form'
import { Box, Flex, Tab, TabList, Tabs } from '@chakra-ui/react'
import { editorAtom } from '@/features/codec-editor/utils/store'
import type { CodecEditorFormProps } from '@/types/codec-editor'

const ErrorIcon = memo(() => {
  const editor = useAtomValue(editorAtom)

  const bgColor = useMemo(() => {
    return isEmpty(editor?.error) ? 'green.500' : 'red.500'
  }, [editor?.error])

  return (
    <Flex
      sx={{
        w: '50px',
        justifyContent: 'center',
        alignItems: 'center',
        borderBottomWidth: '2px',
        borderBottomStyle: 'solid',
        _dark: {
          borderBottomColor: 'inherit',
        },
      }}
    >
      <Box
        sx={{
          boxSize: 2,
          borderRadius: 'full',
          bg: bgColor,
        }}
      />
    </Flex>
  )
})

const tabStyles = {
  _hover: {
    color: 'primary.500',
    _dark: {
      color: 'primary.200',
    },
  },
  _active: {
    color: 'primary.500',
    _dark: {
      color: 'primary.200',
    },
  },
  _selected: {
    color: 'primary.500',
    borderBottomColor: 'primary.500',
    _dark: {
      color: 'primary.200',
      borderBottomColor: 'primary.200',
    },
  },
}

export const FileTabs = memo(() => {
  const { control, setValue } = useFormContext<CodecEditorFormProps>()

  const codec = useWatch({
    name: 'codec',
    control,
  })

  const onChangeTab = (index: number): void => {
    setValue('codec.selectedFileIndex', index)
  }

  return (
    <Flex
      sx={{
        w: 'full',
        justifyContent: 'space-between',
        bg: 'white',
        _dark: {
          bg: 'gray.900',
        },
      }}
    >
      <Tabs
        index={codec.selectedFileIndex}
        onChange={onChangeTab}
        sx={{
          flex: 1,
          w: '100%',
        }}
      >
        <TabList>
          {codec.files.map(({ name }, key) => (
            <Tab key={key} sx={tabStyles}>
              <Box noOfLines={1}>{name}</Box>
            </Tab>
          ))}
        </TabList>
      </Tabs>
      <ErrorIcon />
    </Flex>
  )
})
