import { memo, useMemo } from 'react'
import { useParams } from 'react-router'
import { useSuspenseQuery } from '@tanstack/react-query'
import {
  Box,
  VStack,
  StackDivider,
  SimpleGrid,
  Heading,
} from '@chakra-ui/react'
import { useOrganization } from '@/hooks/use-organization'
import { getGateway } from '@/api/gateways'
import { formatDate } from '@/utils/date/format-date'

interface MetaItem {
  name: string
  value: string | number | null | undefined
}

const getValueOrDefault = (
  value: string | number | undefined | null,
  defaultValue = '-'
) => {
  if (value === undefined || value === null) return defaultValue
  if (typeof value === 'number') return String(value)
  return value.trim() !== '' ? value : defaultValue
}

export const Overview = memo(() => {
  const params = useParams()
  const { organizationId, applicationId } = useOrganization()
  const hardwareId = useMemo<string | undefined>(() => params?.id, [params])

  const { data } = useSuspenseQuery({
    queryKey: ['GetGateway', organizationId, applicationId, hardwareId],
    queryFn: ({ signal }) =>
      organizationId && applicationId && hardwareId
        ? getGateway({
            organizationId,
            applicationId,
            hardwareId,
            signal,
          })
        : null,
  })

  const primaryMeta = useMemo(
    (): MetaItem[] => [
      {
        name: 'EUI',
        value: getValueOrDefault(data?.gateway.metadata.eui),
      },
      {
        name: 'Last Seen',
        value: data?.lastSeenAt
          ? formatDate(data.lastSeenAt, 'PPppp')
          : 'Never',
      },
      {
        name: 'Chirpstack Version',
        value: getValueOrDefault(data?.gateway.metadata.chirpstack_version),
      },
      {
        name: 'DPS Client Version',
        value: getValueOrDefault(data?.gateway.metadata.dps_client_version),
      },
      {
        name: 'APN',
        value: getValueOrDefault(data?.gateway.metadata.apn),
      },
      {
        name: 'WWAN IP',
        value: getValueOrDefault(data?.gateway.metadata.wwan_ip),
      },
      {
        name: 'ETH IP',
        value: getValueOrDefault(data?.gateway.metadata.eth_ip),
      },
    ],
    [data]
  )

  const secondaryMeta = useMemo(
    (): MetaItem[] => [
      {
        name: 'Internal ID',
        value: getValueOrDefault(data?.gateway.id),
      },
      {
        name: 'Altitude',
        value: getValueOrDefault(data?.gateway.location.altitude),
      },
      {
        name: 'GPS Coordinates',
        value:
          data?.gateway.location.latitude && data?.gateway.location.longitude
            ? [
                data.gateway.location.latitude,
                data.gateway.location.longitude,
              ].join(', ')
            : '-',
      },
      {
        name: 'Battery Critical',
        value: getValueOrDefault(data?.gateway.metadata.battery_critical),
      },
      {
        name: 'Cert Expiration',
        value: getValueOrDefault(data?.gateway.metadata.cert_expiration),
      },
      {
        name: 'Firmware Version',
        value: getValueOrDefault(data?.gateway.metadata.firmware_version),
      },
      {
        name: 'IMEI',
        value: getValueOrDefault(data?.gateway.metadata.imei),
      },
      {
        name: 'IMSI',
        value: getValueOrDefault(data?.gateway.metadata.imsi),
      },
      {
        name: 'Mac',
        value: getValueOrDefault(data?.gateway.metadata.mac),
      },
      {
        name: 'Marshaler',
        value: getValueOrDefault(data?.gateway.metadata.marshaler),
      },
      {
        name: 'Manufacturer',
        value: getValueOrDefault(data?.gateway.metadata.manufacturer),
      },
      {
        name: 'Model',
        value: getValueOrDefault(data?.gateway.metadata.model),
      },
      {
        name: 'Serial',
        value: getValueOrDefault(data?.gateway.metadata.serial),
      },
      // Only add these fields if they are present in the metadata
      // these fields are only present on Tektelic gateways
      // and are not present on other gateways
      ...[
        'power_source',
        'voltage',
        'charge_complete',
        'charge_fault',
        'charging',
      ]
        .filter((key) => {
          const metadata = data?.gateway.metadata as Record<
            string,
            string | number | null | undefined
          >
          return Boolean(metadata?.[key])
        })
        .map((key) => {
          const metadata = data?.gateway.metadata as Record<
            string,
            string | number | null | undefined
          >
          return {
            name: key
              .replace(/_/g, ' ')
              .replace(/\b\w/g, (char) => char.toUpperCase()),
            value: getValueOrDefault(metadata?.[key]),
          } satisfies MetaItem
        }),
    ],
    [data]
  )

  return (
    <VStack spacing="20px" align="stretch">
      <Box p={3}>
        <Heading size="md" fontWeight="semibold" mb={4}>
          Main Settings
        </Heading>
        <VStack
          spacing="9px"
          align="stretch"
          divider={
            <StackDivider
              sx={{
                borderStyle: 'dashed',
                borderColor: 'blackAlpha.300',
              }}
            />
          }
        >
          {primaryMeta.map(({ name, value }) => (
            <SimpleGrid columns={2} key={name}>
              <Box fontWeight="medium">{name}</Box>
              <Box>{value ?? '-'}</Box>
            </SimpleGrid>
          ))}
        </VStack>
      </Box>

      <Box p={3}>
        <Heading size="md" fontWeight="semibold" mb={4}>
          Additional Information
        </Heading>
        <VStack
          spacing="9px"
          align="stretch"
          divider={
            <StackDivider
              sx={{
                borderStyle: 'dashed',
                borderColor: 'blackAlpha.300',
              }}
            />
          }
        >
          {secondaryMeta.map(({ name, value }) => (
            <SimpleGrid columns={2} key={name}>
              <Box fontWeight="medium">{name}</Box>
              <Box>{value ?? '-'}</Box>
            </SimpleGrid>
          ))}
        </VStack>
      </Box>
    </VStack>
  )
})
