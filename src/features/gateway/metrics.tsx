import { memo, lazy, Suspense } from 'react'
import { V<PERSON><PERSON>ck, StackDivider, Skeleton } from '@chakra-ui/react'
import { DateFilter } from '@/features/gateway/date-filter/date-filter'

const GatewayPings = lazy(() =>
  import('@/features/gateway/gateway-pings').then(({ GatewayPings }) => ({
    default: GatewayPings,
  }))
)

const GatewayStats = lazy(() =>
  import('@/features/gateway/gateway-stats').then(({ GatewayStats }) => ({
    default: GatewayStats,
  }))
)

export const Metrics = memo(() => (
  <VStack
    spacing={2}
    align="stretch"
    mb={6}
    divider={
      <StackDivider
        sx={{
          borderColor: 'blackAlpha.300',
          borderStyle: 'dashed',
        }}
      />
    }
  >
    <DateFilter />
    <Suspense fallback={<Skeleton height="150px" />}>
      <GatewayPings />
    </Suspense>
    <Suspense fallback={<Skeleton height="600px" />}>
      <GatewayStats />
    </Suspense>
  </VStack>
))
