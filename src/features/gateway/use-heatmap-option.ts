import { useToken } from '@chakra-ui/react'
import { formatDate } from '@/utils/date/format-date'
import type { GatewayStatsModel } from '@/types/models/gateway'

export const useHeatmapOption = () => {
  const [blackAlpha500, teal, blue] = useToken('colors', [
    'blackAlpha.500',
    'teal.300',
    'blue.400',
  ])

  const heatmapOption = ({
    result,
    dbKey,
  }: {
    result: GatewayStatsModel[]
    dbKey: string
  }) => {
    if (result.length === 0) {
      return {}
    }

    const mapLabels = () => {
      const match = result.find((item) => Object.keys(item[dbKey]).length > 0)
      if (!match) {
        return []
      }
      return Object.keys(match[dbKey])
    }

    const mapDays = () => {
      return result.reduce((acc: Array<string | null>, item) => {
        acc.push(formatDate(item.timestamp, 'MMM dd'))
        return acc
      }, [])
    }

    const labels = mapLabels()
    const days = mapDays()

    const heatmapData = () =>
      result
        .reduce((acc: number[][][], item, y) => {
          const a = Object.values(item[dbKey])
          const o = a.length > 0 ? a : Array(labels.length).fill(0)
          const d = o.map((v, x) => [y, x, v === 0 ? '-' : v])
          acc.push(d)
          return acc
        }, [])
        .flat()

    const max = Math.max(
      ...result.map((item) => {
        const a = Object.values(item[dbKey]) as number[]
        return a.length > 0 ? Math.max(...a) : 0
      })
    )

    return {
      tooltip: {
        position: 'top',
      },
      grid: {
        left: 60,
        right: 10,
        bottom: 30,
        top: 10,
      },
      visualMap: {
        show: false,
        min: 0,
        max,
        calculable: true,
        orient: 'horizontal',
        left: 'center',
        bottom: '15%',
        inRange: {
          color: [teal, blue],
        },
      },
      xAxis: {
        type: 'category',
        data: days,
        splitArea: {
          show: true,
        },
        axisLabel: {
          interval: 1,
          fontSize: 10,
        },
      },
      yAxis: {
        type: 'category',
        data: labels,
        splitArea: {
          show: true,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          fontSize: 10,
        },
      },
      series: [
        {
          name: 'Hits',
          type: 'heatmap',
          data: heatmapData(),
          label: {
            show: true,
            fontSize: 7,
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: blackAlpha500,
            },
          },
        },
      ],
    }
  }

  return {
    heatmapOption,
  }
}
