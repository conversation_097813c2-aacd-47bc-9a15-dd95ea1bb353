import { memo, lazy, useMemo } from 'react'
import { useParams } from 'react-router'
import { useSuspenseQuery } from '@tanstack/react-query'
import { VStack } from '@chakra-ui/react'
import { useOrganization } from '@/hooks/use-organization'
import { getGatewayStats } from '@/api/gateways'
import { formatDate } from '@/utils/date/format-date'
import { useHeatmapOption } from '@/features/gateway/use-heatmap-option'
import type { GatewayStatsModel } from '@/types/models/gateway'

const MetricItem = lazy(() =>
  import('@/features/gateway/metric-item').then(({ MetricItem }) => ({
    default: MetricItem,
  }))
)

export const GatewayStats = memo(() => {
  const params = useParams()
  const { organizationId, applicationId } = useOrganization()
  const { heatmapOption } = useHeatmapOption()
  const hardwareId = useMemo<string | undefined>(() => params?.id, [params])

  const { data } = useSuspenseQuery({
    queryKey: ['GetGatewayStats', organizationId, applicationId, hardwareId],
    queryFn: ({ signal }) =>
      organizationId && applicationId && hardwareId
        ? getGatewayStats({
            organizationId,
            applicationId,
            hardwareId,
            signal,
          })
        : null,
  })

  const rxReceivedLineOption = useMemo(() => {
    if (!data || data.length === 0) {
      return {}
    }

    const xAxisData = Array.from(
      new Set(
        data.map((item: GatewayStatsModel) =>
          formatDate(item.timestamp, 'MMM dd')
        )
      )
    )

    const seriesData = Array.from(
      new Set(
        data.map(
          (item: { rxPacketsReceived?: number }) => item.rxPacketsReceived ?? 0
        )
      )
    )

    return {
      tooltip: {
        trigger: 'axis',
      },
      grid: {
        containLabel: true,
        left: 0,
        right: 20,
        bottom: 10,
        top: 20,
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        axisLabel: {
          fontSize: 10,
        },
        data: xAxisData,
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          fontSize: 10,
        },
      },
      series: [
        {
          name: 'RX Packets Received',
          stack: 'RX Packets Received',
          type: 'line',
          data: seriesData,
        },
      ],
    }
  }, [data])

  const txReceivedLineOption = useMemo(() => {
    if (!data || data?.length === 0) {
      return {}
    }

    const xAxisData = Array.from(
      new Set(
        data.map((item: GatewayStatsModel) =>
          formatDate(item.timestamp, 'MMM dd')
        )
      )
    )

    const seriesData = Array.from(
      new Set(
        data.map(
          (item: { txPacketsReceived?: number }) => item.txPacketsReceived ?? 0
        )
      )
    )

    return {
      tooltip: {
        trigger: 'axis',
      },
      grid: {
        containLabel: true,
        left: 0,
        right: 20,
        bottom: 10,
        top: 20,
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        axisLabel: {
          fontSize: 10,
        },
        data: xAxisData,
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          fontSize: 10,
        },
      },
      series: [
        {
          name: 'TX Packets Received',
          stack: 'TX Packets',
          type: 'line',
          data: seriesData,
        },
      ],
    }
  }, [data])

  const txStatusLineOption = useMemo(() => {
    if (!data || data.length === 0) {
      return {}
    }

    const xAxisData = Array.from(
      new Set(
        data.map((item: GatewayStatsModel) =>
          formatDate(item.timestamp, 'MMM dd')
        )
      )
    )

    return {
      width: 'auto',
      height: 'auto',
      tooltip: {
        trigger: 'axis',
      },
      grid: {
        containLabel: true,
        left: 0,
        right: 20,
        bottom: 10,
        top: 20,
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        axisLabel: {
          fontSize: 10,
        },
        data: xAxisData,
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          fontSize: 10,
        },
      },
      series: [
        {
          name: 'TX Packets OK',
          stack: 'TX Packets',
          type: 'line',
          data: Array.from(
            new Set(
              data.map(
                (item: { txPacketsPerStatus: { OK?: number } }) =>
                  item.txPacketsPerStatus.OK ?? 0
              )
            )
          ),
        },
        {
          name: 'TX Packets Too Early',
          stack: 'TX Packets',
          type: 'line',
          data: Array.from(
            new Set(
              data.map(
                (item: { txPacketsPerStatus: { TOO_EARLY?: number } }) =>
                  item.txPacketsPerStatus.TOO_EARLY ?? 0
              )
            )
          ),
        },
        {
          name: 'TX Packets Too Late',
          stack: 'TX Packets',
          type: 'line',
          data: Array.from(
            new Set(
              data.map(
                (item: { txPacketsPerStatus: { TOO_LATE?: number } }) =>
                  item.txPacketsPerStatus.TOO_LATE ?? 0
              )
            )
          ),
        },
      ],
    }
  }, [data])

  return (
    <VStack spacing={2} align="stretch">
      <MetricItem title="Received" option={rxReceivedLineOption} />
      <MetricItem title="Transmitted" option={txReceivedLineOption} />
      <MetricItem
        title="Received / Frequency"
        option={heatmapOption({
          result: data ?? [],
          dbKey: 'rxPacketsPerFrequency',
        })}
      />
      <MetricItem
        title="Transmitted / Frequency"
        option={heatmapOption({
          result: data ?? [],
          dbKey: 'txPacketsPerFrequency',
        })}
      />
      <MetricItem
        title="Received / DR"
        option={heatmapOption({
          result: data ?? [],
          dbKey: 'rxPacketsPerDr',
        })}
      />
      <MetricItem
        title="Transmitted / DR"
        option={heatmapOption({
          result: data ?? [],
          dbKey: 'txPacketsPerDr',
        })}
      />
      <MetricItem
        title="Transmission / ACK Status"
        option={txStatusLineOption}
      />
    </VStack>
  )
})
