import { memo, useState, useEffect } from 'react'
import { Link, Alert, AlertIcon, AlertDescription } from '@chakra-ui/react'
import { useShop } from '@/hooks/use-shop'

export const NoMeta = memo(() => {
  const { getUrl } = useShop()
  const [url, setUrl] = useState<string>()

  useEffect(() => {
    getUrl().then((s) => setUrl(s))
  }, [getUrl])

  return (
    <Alert status="info" mb={4}>
      <AlertIcon />
      <AlertDescription>
        Gateways Overview and Metrics are not supported with this gateway. To
        use this feature,{' '}
        <Link href={url} target="_blank" rel="noopener noreferrer">
          please purchase a new gateway from myDevices
        </Link>
        .
      </AlertDescription>
    </Alert>
  )
})
