import { memo, useMemo, Suspense } from 'react'
import { useParams } from 'react-router'
import { format, fromUnixTime } from 'date-fns'
import { useAtomValue } from 'jotai'
import { useSuspenseQuery } from '@tanstack/react-query'
import { useToken, Skeleton } from '@chakra-ui/react'
import { useOrganization } from '@/hooks/use-organization'
import { getGatewayPingHistogram } from '@/api/gateways'
import { dateFilterAtom } from '@/utils/stores/date-filter'
import { MetricItem } from '@/features/gateway/metric-item'

export const GatewayPings = memo(() => {
  const params = useParams()
  const { organizationId, applicationId } = useOrganization()
  const hardwareId = useMemo<string | undefined>(() => params?.id, [params])
  const [green] = useToken('colors', ['green.500'])
  const dateFilter = useAtomValue(dateFilterAtom)

  const { data: pings } = useSuspenseQuery({
    queryKey: [
      'GetGatewayPingHistogram',
      organizationId,
      applicationId,
      hardwareId,
      dateFilter,
    ],
    queryFn: async ({ signal }) => {
      try {
        if (!(organizationId && applicationId && hardwareId)) {
          return null
        }
        return await getGatewayPingHistogram({
          organizationId,
          applicationId,
          hardwareId,
          signal,
          ...dateFilter,
        })
      } catch (error) {
        if (error instanceof Error && error.message.includes('500')) {
          return null
        }
        throw error
      }
    },
  })

  const histogramOption = useMemo(() => {
    if (!pings || pings?.length === 0) {
      return {}
    }

    const xAxisData = Array.from(
      new Set(
        pings.map((item: { timestamp: number }) =>
          dateFilter.type === 'day'
            ? format(fromUnixTime(item.timestamp), 'p')
            : format(fromUnixTime(item.timestamp), 'MMM d')
        )
      )
    )

    const seriesData = pings.map((item: { count: number }) => item.count)

    return {
      width: 'auto',
      height: 'auto',
      tooltip: {
        trigger: 'axis',
      },
      color: [green],
      grid: {
        containLabel: true,
        left: 0,
        right: 20,
        bottom: 10,
        top: 20,
      },
      xAxis: {
        show: true,
        type: 'category',
        boundaryGap: true,
        axisLabel: {
          fontSize: 10,
        },
        data: xAxisData,
      },
      yAxis: {
        show: true,
        type: 'value',
        minInterval: 1,
        axisLabel: {
          fontSize: 10,
        },
      },
      series: [
        {
          type: 'bar',
          barCategoryGap: 2,
          data: seriesData,
        },
      ],
    }
  }, [green, pings, dateFilter.type])

  return (
    <Suspense fallback={<Skeleton minHeight="150px" />}>
      <MetricItem
        title="Gateway Pings"
        option={histogramOption}
        minHeight="150px"
      />
    </Suspense>
  )
})
