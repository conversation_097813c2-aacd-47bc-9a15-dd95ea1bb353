import { useEffect } from 'react'
import { DatePicker, useDatePickerContext } from '@ark-ui/react/date-picker'
import { Box, HStack, Grid, IconButton } from '@chakra-ui/react'
import { ChevronLeftIcon, ChevronRightIcon } from 'lucide-react'
import { DayView } from '@/features/gateway/date-filter/day-view'

interface NewRangeProps {
  onRangeChange?: (startDateTime: string, endDateTime: string) => void
}

export const RangePicker = ({ onRangeChange }: NewRangeProps) => {
  const datePicker = useDatePickerContext()

  useEffect(() => {
    if (onRangeChange && datePicker.value.length === 2) {
      const [start, end] = datePicker.value
      if (start && end && start !== end) {
        const startDateTime = start.toString()
        const endDateTime = end.toString()
        onRangeChange(startDateTime, endDateTime)
      }
    }
  }, [datePicker.value, onRangeChange])

  return (
    <DatePicker.Content>
      <DatePicker.View view="day">
        <Grid
          sx={{
            gridTemplateColumns: '1fr',
            gap: 0,
            borderRadius: 'md',
            boxShadow: 'xl',
            bg: 'white',
            overflow: 'hidden',
            width: 'fit-content',
            _dark: {
              bg: 'gray.900',
              boxShadow: 'dark-lg',
            },
          }}
        >
          <Box
            sx={{
              p: 3,
              width: '100%',
              borderRight: '1px solid',
              borderColor: 'gray.100',
              _dark: {
                borderColor: 'gray.700',
              },
            }}
          >
            <HStack
              align="center"
              justify="space-between"
              fontWeight="medium"
              mb={3}
            >
              <IconButton
                isRound
                as={DatePicker.PrevTrigger}
                variant="ghost"
                colorScheme="gray"
                size="sm"
                boxSize="32px"
                aria-label="Previous month"
              >
                <ChevronLeftIcon size={18} />
              </IconButton>
              <HStack
                flex={1}
                justify="space-around"
                fontWeight="semibold"
                fontSize="sm"
              >
                <Box>{datePicker.visibleRangeText.start}</Box>
                <Box>{datePicker.visibleRangeText.end}</Box>
              </HStack>
              <IconButton
                as={DatePicker.NextTrigger}
                isRound
                variant="ghost"
                colorScheme="gray"
                size="sm"
                boxSize="32px"
                aria-label="Next month"
              >
                <ChevronRightIcon size={18} />
              </IconButton>
            </HStack>
            <HStack align="start">
              <DayView monthOffset={0} />
              <DayView monthOffset={1} />
            </HStack>
          </Box>
        </Grid>
      </DatePicker.View>
    </DatePicker.Content>
  )
}
