import { useCallback, useEffect, useState, memo } from 'react'
import { useAtom, useAtomValue, useSetAtom } from 'jotai'
import { Flex, HStack, Button } from '@chakra-ui/react'
import { DatePicker, useDatePicker } from '@ark-ui/react/date-picker'
import { dateRange } from '@/utils/date/date-range'
import {
  dateFilterAtom,
  dateFilterTypeAtom,
  dateFilterRangeAtom,
  dateQueryOptionsAtom,
  type DateFilterType,
  type DateQueryOption,
} from '@/utils/stores/date-filter'
import { isValid, startOfDay, addMonths, subDays } from 'date-fns'
import { RangePicker } from '@/features/gateway/date-filter/range-picker'
import { today, getLocalTimeZone } from '@internationalized/date'

const dateTimeFormatter = new Intl.DateTimeFormat('en-US', {
  month: 'short',
  day: 'numeric',
  year: 'numeric',
})

export const DateFilter = memo(() => {
  const [filter, setFilter] = useAtom(dateFilter<PERSON>tom)
  const setFilterType = useSetAtom(dateFilterTypeAtom)
  const setFilterRange = useSetAtom(dateFilterRangeAtom)
  const dateOptions = useAtomValue(dateQueryOptionsAtom)
  const [selectedType, setSelectedType] = useState<DateFilterType>(filter.type)
  const [isCustomOpen, setIsCustomOpen] = useState(false)
  const [displayValue, setDisplayValue] = useState('')

  const minDate = today(getLocalTimeZone()).subtract({ months: 1 })
  const maxDate = today(getLocalTimeZone())

  useEffect(() => {
    if (!(filter.start_date && filter.end_date)) {
      const newFilter = dateRange('day')
      setFilter(newFilter)
    }
  }, [])

  const datePicker = useDatePicker({
    selectionMode: 'range',
    numOfMonths: 2,
    startOfWeek: 1,
    open: isCustomOpen,
    closeOnSelect: false,
    min: minDate,
    max: maxDate,
    onOpenChange: ({ open }) => setIsCustomOpen(open),
    onValueChange: (details) => {
      if (details.valueAsString.length === 2) {
        const [start, end] = details.valueAsString
        if (start && end) {
          const startDate = new Date(start)
          const endDate = new Date(end)
          const diffInMonths =
            (endDate.getFullYear() - startDate.getFullYear()) * 12 +
            (endDate.getMonth() - startDate.getMonth())

          if (diffInMonths <= 1) {
            onCustomDateChange(start, end)
          }
        }
      }
    },
  })

  const getDateRangeDisplay = (
    type: DateFilterType,
    start: number,
    end: number
  ) => {
    const startDate = new Date(start * 1000)
    const endDate = new Date(end * 1000)

    switch (type) {
      case 'day': {
        const dayStart = startOfDay(startDate)
        return dateTimeFormatter.format(dayStart)
      }
      case 'week': {
        const weekEnd = startOfDay(endDate)
        const weekStart = startOfDay(subDays(weekEnd, 6))
        return `${dateTimeFormatter.format(weekStart)} - ${dateTimeFormatter.format(weekEnd)}`
      }
      case '1month': {
        const rollingStart = startOfDay(startDate)
        const rollingEnd = addMonths(rollingStart, 1)
        return `${dateTimeFormatter.format(rollingStart)} - ${dateTimeFormatter.format(rollingEnd)}`
      }
      case 'custom':
        return `${dateTimeFormatter.format(startDate)} - ${dateTimeFormatter.format(endDate)}`
      default:
        return ''
    }
  }

  const onCustomDateChange = useCallback(
    (startDateTime: string, endDateTime: string) => {
      if (startDateTime && endDateTime) {
        const startDate = new Date(startDateTime)
        const endDate = new Date(endDateTime)

        if (isValid(startDate) && isValid(endDate)) {
          const start_date = Math.floor(startDate.getTime() / 1000)
          const end_date = Math.floor(endDate.getTime() / 1000)

          setFilterType('custom')
          setFilterRange({ start_date, end_date })
          setDisplayValue(getDateRangeDisplay('custom', start_date, end_date))
        }
      }
    },
    [setFilterType, setFilterRange]
  )

  const onTypeChange = useCallback(
    (type: string) => {
      setSelectedType(type as DateFilterType)
      if (type !== 'custom') {
        setIsCustomOpen(false)
        const newFilter = dateRange(type as DateFilterType)
        datePicker.setValue([]) // update the date picker value
        setFilter(newFilter)
        setDisplayValue(
          getDateRangeDisplay(
            type as DateFilterType,
            newFilter.start_date as number,
            newFilter.end_date as number
          )
        )
      }
    },
    [setFilter]
  )

  useEffect(() => {
    setSelectedType(filter.type)
    if (filter.start_date && filter.end_date) {
      setDisplayValue(
        getDateRangeDisplay(filter.type, filter.start_date, filter.end_date)
      )
    }
  }, [filter])

  return (
    <HStack
      spacing={1}
      zIndex="1"
      position="relative"
      justifyContent="flex-start"
      alignItems="center"
    >
      {dateOptions
        .filter(({ value }) => value !== 'custom')
        .map((option: DateQueryOption) => (
          <Button
            key={option.value}
            size="xs"
            colorScheme="gray"
            isActive={selectedType === option.value}
            _active={{
              color: 'white',
              bg: 'secondary.500',
              _dark: {
                color: 'gray.900',
                bg: 'secondary.200',
              },
            }}
            onClick={() => {
              onTypeChange(option.value)
              setIsCustomOpen(false)
            }}
          >
            {option.label}
          </Button>
        ))}

      <DatePicker.RootProvider value={datePicker}>
        <DatePicker.Control>
          <DatePicker.Trigger asChild>
            <Button
              size="xs"
              colorScheme="gray"
              isActive={selectedType === 'custom'}
              _active={{
                color: 'white',
                bg: 'secondary.500',
                _dark: {
                  color: 'gray.900',
                  bg: 'secondary.200',
                },
              }}
              onClick={() => {
                onTypeChange('custom')
                setIsCustomOpen(!isCustomOpen)
              }}
            >
              Custom
            </Button>
          </DatePicker.Trigger>
        </DatePicker.Control>
        <DatePicker.Positioner>
          <RangePicker onRangeChange={onCustomDateChange} />
        </DatePicker.Positioner>
      </DatePicker.RootProvider>

      <Flex
        minH="24px"
        align="center"
        justify="center"
        userSelect="none"
        px={2}
        fontSize="xs"
        border="1px solid"
        borderColor="gray.100"
        borderRadius="base"
        color="gray.500"
        _dark={{
          color: 'gray.200',
          borderColor: 'gray.700',
        }}
      >
        {displayValue}
      </Flex>
    </HStack>
  )
})
