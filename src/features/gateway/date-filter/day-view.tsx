import { DatePicker, useDatePickerContext } from '@ark-ui/react/date-picker'
import {
  Button,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  TableContainer,
} from '@chakra-ui/react'

interface DayViewProps {
  monthOffset: number
}

export const DayView = ({ monthOffset }: DayViewProps) => {
  const datePicker = useDatePickerContext()
  const offset = datePicker.getOffset({ months: monthOffset })

  return (
    <TableContainer width="100%">
      <Table as={DatePicker.Table} size="sm" variant="unstyled">
        <Thead as={DatePicker.TableHead}>
          <Tr as={DatePicker.TableRow}>
            {datePicker.weekDays.map((weekDay, id) => (
              <Th
                key={id}
                as={DatePicker.TableHeader}
                sx={{
                  p: 0,
                  textAlign: 'center',
                  fontWeight: 'semibold',
                  width: '30px',
                  height: '30px',
                  color: 'gray.400',
                  fontSize: 'xs',
                  _dark: {
                    color: 'gray.100',
                  },
                }}
              >
                {weekDay.narrow}
              </Th>
            ))}
          </Tr>
        </Thead>
        <Tbody>
          {offset.weeks.map((week, weekIndex) => (
            <Tr as={DatePicker.TableRow} key={weekIndex}>
              {week.map((day, dayIndex) => (
                <Td
                  key={dayIndex}
                  as={DatePicker.TableCell}
                  value={day}
                  visibleRange={offset.visibleRange}
                  sx={{
                    p: 0,
                    py: 0.5,
                    borderRadius: 'base',
                    position: 'relative',
                    '&[data-in-range]': {
                      bg: 'secondary.200',
                      _dark: {
                        bg: 'secondary.200',
                      },
                      _before: {
                        content: '""',
                        position: 'absolute',
                        top: 0,
                        bottom: 0,
                        left: 0,
                        right: 0,
                        zIndex: -1,
                        bg: 'secondary.100',
                        _dark: {
                          bg: 'secondary.200',
                        },
                      },
                    },
                    '&[data-selected]': {
                      bg: 'secondary.500',
                      color: 'white',
                      _dark: {
                        bg: 'secondary.200',
                      },
                      _hover: {
                        bg: 'secondary.200',
                        _dark: {
                          bg: 'secondary.200',
                        },
                      },
                      _before: {
                        content: '""',
                        position: 'absolute',
                        top: 0,
                        bottom: 0,
                        left: 0,
                        right: 0,
                        bg: 'secondary.500',
                        zIndex: -1,
                        _dark: {
                          bg: 'secondary.200',
                        },
                      },
                    },
                    '&[data-today]': {
                      border: '2px solid',
                      borderColor: 'secondary.500',
                      _dark: {
                        borderColor: 'secondary.200',
                      },
                    },
                  }}
                >
                  <Button
                    as={DatePicker.TableCellTrigger}
                    variant="ghost"
                    size="xs"
                    isDisabled={!day}
                    sx={{
                      p: 0.5,
                      position: 'relative',
                      width: '30px',
                      height: '30px',
                      borderRadius: 'base',
                      color: !day ? 'gray.300' : 'gray.700',
                      _dark: {
                        color: !day ? 'gray.900' : 'gray.900',
                      },
                      '&[data-in-range]': {
                        bg: 'secondary.200',
                        borderRadius: '0',
                        _before: {
                          content: '""',
                          position: 'absolute',
                          top: 0,
                          bottom: 0,
                          left: 0,
                          right: 0,
                          bg: 'secondary.100',
                          zIndex: -1,
                          _dark: {
                            bg: 'secondary.100',
                          },
                        },
                        _dark: {
                          bg: 'secondary.200',
                        },
                      },
                      '&[data-selected]': {
                        '&[data-range-end]': {
                          borderTopRightRadius: 'base',
                          borderBottomRightRadius: 'base',
                        },
                        '&[data-range-start]': {
                          borderTopLeftRadius: 'base',
                          borderBottomLeftRadius: 'base',
                        },
                        bg: 'secondary.500',
                        color: 'white',
                        _hover: {
                          bg: 'secondary.600',
                          _dark: {
                            bg: 'secondary.200',
                          },
                        },
                        _dark: {
                          bg: 'secondary.200',
                        },
                        _before: {
                          content: '""',
                          position: 'absolute',
                          top: 0,
                          bottom: 0,
                          left: 0,
                          right: 0,
                          zIndex: -1,
                          bg: 'secondary.500',
                          _dark: {
                            bg: 'secondary.500',
                          },
                        },
                      },
                      '&[data-today]': {
                        border: '2px solid',
                        borderColor: 'secondary.500',
                        _dark: {
                          borderColor: 'secondary.500',
                        },
                      },
                    }}
                  >
                    {day?.day}
                  </Button>
                </Td>
              ))}
            </Tr>
          ))}
        </Tbody>
      </Table>
    </TableContainer>
  )
}
