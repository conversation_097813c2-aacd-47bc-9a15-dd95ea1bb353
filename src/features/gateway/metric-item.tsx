import { lazy, Suspense } from 'react'
import { Box, Flex } from '@chakra-ui/react'

const EChart = lazy(() =>
  import('@/components/echart').then(({ EChart }) => ({
    default: EChart,
  }))
)

interface MetricItemProps {
  title: string
  option: Dict
  minHeight?: string
}

export const MetricItem = ({
  title,
  option,
  minHeight = '250px',
}: MetricItemProps) => (
  <Box
    sx={{
      display: 'grid',
      gridTemplateRows: 'auto 1fr',
      gridGap: 0,
    }}
  >
    <Box>{title}</Box>
    <Box
      sx={{
        minHeight,
      }}
    >
      {Object.keys(option).length > 0 ? (
        <Suspense fallback={null}>
          <EChart option={option} />
        </Suspense>
      ) : (
        <Flex
          sx={{
            h: '100%',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          No data received.
        </Flex>
      )}
    </Box>
  </Box>
)
