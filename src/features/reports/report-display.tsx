import { memo } from 'react'
import { useAtomValue } from 'jotai'
import { useQuery } from '@tanstack/react-query'
import { Box, Flex, Grid, Skeleton } from '@chakra-ui/react'
import { useOrganization } from '@/hooks/use-organization'
import { reportAtom } from '@/utils/stores/report'
import { getReportTemplate } from '@/api/reports'
import type { ReportTemplateModel } from '@/types/models/report'

export const ReportDisplay = memo(() => {
  const { applicationId } = useOrganization()
  const selectedId = useAtomValue(reportAtom)

  const { data } = useQuery<ReportTemplateModel | null, Error>({
    queryKey: ['GetReportTemplate', selectedId, applicationId],
    queryFn: ({ signal }) =>
      selectedId && applicationId
        ? getReportTemplate({
            id: selectedId,
            applicationId,
            signal,
          })
        : null,
    enabled: !!selectedId || !!applicationId,
  })

  return (
    <Grid
      sx={{
        gridTemplateRows: '40px 1fr',
        gap: 0,
        boxSize: '100%',
        bg: 'blue.500',
      }}
    >
      <Flex
        sx={{
          p: 4,
          width: '100%',
          alignItems: 'center',
          justifyContent: 'space-between',
          color: 'white',
          fontSize: 'lg',
          fontWeight: 'medium',
          textTransform: 'uppercase',
        }}
      >
        <Flex>{data?.display_name ?? ''}</Flex>
        <Flex>Sample</Flex>
      </Flex>
      {data?.preview ? (
        <Box
          as="iframe"
          w="100%"
          h="100%"
          frameBorder={0}
          allowFullScreen
          x-frame-options="allow-all"
          allow="accelerometer;autoplay;clipboard-write;encrypted-media;gyroscope;picture-in-picture"
          src={data?.preview ?? ''}
        />
      ) : (
        <Skeleton sx={{ boxSize: '100%' }} />
      )}
    </Grid>
  )
})
