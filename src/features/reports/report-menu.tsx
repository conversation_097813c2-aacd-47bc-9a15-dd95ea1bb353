import { memo, lazy, useEffect } from 'react'
import { useAtom } from 'jotai'
import { head } from 'ramda'
import { useQuery } from '@tanstack/react-query'
import {
  Box,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
} from '@chakra-ui/react'
import { useOrganization } from '@/hooks/use-organization'
import { reportAtom } from '@/utils/stores/report'
import { sortByPriority } from '@/utils/sort-by-priority'
import { getReportTemplates, getReportCategories } from '@/api/reports'
import type {
  ReportTemplateModel,
  ReportCategoryModel,
} from '@/types/models/report'

const MenuList = lazy(() =>
  import('@/features/reports/menu-list').then(({ MenuList }) => ({
    default: MenuList,
  }))
)

export const ReportMenu = memo(() => {
  const { applicationId } = useOrganization()
  const [selectedId, setSelectedId] = useAtom(reportAtom)

  const { data: templates } = useQuery<ReportTemplateModel[], Error>({
    queryKey: ['GetReportTemplates', 'app', applicationId],
    queryFn: ({ signal }) =>
      getReportTemplates({
        type: 'app',
        signal,
      }),
    refetchOnWindowFocus: false,
    enabled: !!applicationId,
  })

  const { data: categories } = useQuery<ReportCategoryModel[], Error>({
    queryKey: ['GetReportCategories'],
    queryFn: ({ signal }) =>
      getReportCategories({
        signal,
      }),
    refetchOnWindowFocus: false,
    select: (data) =>
      data.reduce(
        (acc: ReportCategoryModel[], category) =>
          acc.concat({
            ...category,
            report_templates: Array.from(
              sortByPriority(
                category.report_templates.reduce(
                  (
                    ac: Omit<ReportTemplateModel, 'report_categories'>[],
                    report
                  ) =>
                    ac.concat({
                      ...report,
                    }),
                  []
                )
              )
            ),
          }),
        []
      ),
  })

  useEffect(() => {
    if (!selectedId && templates) {
      const firstTemplate = head(templates)
      if (firstTemplate) {
        setSelectedId(firstTemplate?.id)
      }
    }
  }, [selectedId, setSelectedId, templates])

  if (!categories) {
    return null
  }

  return (
    <Accordion allowMultiple>
      {categories.map((group) => (
        <AccordionItem
          key={['group', group.id].join('-')}
          isDisabled={group.report_templates.length === 0}
          sx={{
            border: 0,
            _focus: {
              boxShadow: 'none',
            },
          }}
        >
          <AccordionButton
            sx={{
              px: '0.7rem',
              py: '0.5rem',
              border: 0,
              bg: 'blackAlpha.100',
              _hover: {
                bg: 'blackAlpha.200',
              },
            }}
          >
            <Box
              as="span"
              noOfLines={1}
              sx={{
                flex: 1,
                textAlign: 'left',
                fontWeight: 'medium',
              }}
            >
              {group.display_name}
            </Box>
            <AccordionIcon />
          </AccordionButton>
          <AccordionPanel
            sx={{
              fontWeight: 'normal',
              p: 0,
            }}
          >
            <MenuList groupId={group.id} templates={group.report_templates} />
          </AccordionPanel>
        </AccordionItem>
      ))}
    </Accordion>
  )
})
