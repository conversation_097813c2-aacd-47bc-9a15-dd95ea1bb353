import { memo, useMemo } from 'react'
import { useNavigate } from 'react-router'
import { useQuery } from '@tanstack/react-query'
import { Flex } from '@chakra-ui/react'
import { Title } from '@/components/title'
import { AddButton } from '@/components/add-button'
import { useRouter } from '@/hooks/use-router'
import { useOrganization } from '@/hooks/use-organization'
import { useAuth } from '@/contexts/use-auth'
import { getReportTemplates } from '@/api/reports'
import type { ReportTemplateModel } from '@/types/models/report'

export const ReportHeader = memo(() => {
  const navigate = useNavigate()
  const { title } = useRouter()
  const { applicationId } = useOrganization()
  const { isInternalAdmin } = useAuth()

  const { data } = useQuery<ReportTemplateModel[], Error>({
    queryKey: ['GetReportTemplates', 'app', applicationId],
    queryFn: ({ signal }) =>
      getReportTemplates({
        type: 'app',
        signal,
      }),
    enabled: !!applicationId,
  })

  const count = useMemo<number>(() => data?.length ?? 0, [data])

  const onAdd = (): void => {
    navigate('/manage/reports/add', {
      viewTransition: true,
    })
  }

  return (
    <Flex align="center" justify="space-between" p={4}>
      <Title title={title} count={count} suffix="Record" />
      {isInternalAdmin && <AddButton label="Add Report" onClick={onAdd} />}
    </Flex>
  )
})
