import { lazy, type BaseSyntheticEvent } from 'react'
import { useParams } from 'react-router'
import { Box, Flex, IconButton } from '@chakra-ui/react'
import { components } from 'react-select'
import { PencilIcon } from 'lucide-react'
import { useModal } from '@/hooks/use-modal'

const EditCategory = lazy(() =>
  import('@/pages/reports/edit-category').then(({ EditCategory }) => ({
    default: EditCategory,
  }))
)

export const CategoryOption = (props: any) => {
  const modal = useModal()
  const { id: reportId } = useParams<{ id: string }>()
  const { value } = props.data

  const onEdit = (event: BaseSyntheticEvent): void => {
    event.preventDefault()

    modal({
      component: <EditCategory />,
      config: {
        title: 'Edit Category',
        data: {
          reportId,
          id: value,
        },
        onCallback: () => ({}),
      },
    })
  }

  return (
    <>
      <components.Option {...props}>
        <Flex justify="space-between" align="center">
          <Box>{props.children}</Box>
          <IconButton
            isRound
            size="sm"
            variant="ghost"
            colorScheme="blue"
            aria-label="Edit Data Type"
            icon={<PencilIcon size={16} />}
            onClick={onEdit}
          />
        </Flex>
      </components.Option>
    </>
  )
}
