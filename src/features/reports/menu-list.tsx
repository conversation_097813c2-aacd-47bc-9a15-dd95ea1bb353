import { lazy, useState } from 'react'
import pMap from 'p-map'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { arrayMove } from '@dnd-kit/sortable'
import { DragDropContext, Droppable, type DropResult } from '@hello-pangea/dnd'
import { Box } from '@chakra-ui/react'
import { useToast } from '@/hooks/use-toast'
import { updateReportTemplate } from '@/api/reports'
import type { ReportTemplateModel } from '@/types/models/report'
import type { ReportTemplateInput } from '@/types/api'

const MenuItem = lazy(() =>
  import('@/features/reports/menu-item').then(({ MenuItem }) => ({
    default: MenuItem,
  }))
)

interface MenuListProps {
  groupId: number
  templates: Omit<ReportTemplateModel, 'report_categories'>[]
}

export const MenuList = ({ groupId, templates }: MenuListProps) => {
  const toast = useToast()
  const queryClient = useQueryClient()
  const [items, setItems] = useState(() => templates)

  const { mutateAsync: updateReportTemplateMutation } = useMutation<
    any,
    Error,
    {
      id: number
      input: Partial<ReportTemplateInput>
    }
  >({
    mutationFn: ({ id, input }) => updateReportTemplate(id, input),
    onSuccess: () => {
      const cache = [
        'GetReportTemplate',
        'GetReportTemplates',
        'GetReportCategories',
      ]
      cache.map((cacheKey: string) =>
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      )
    },
  })

  const onDragEnd = async (result: DropResult) => {
    const { destination, source } = result

    if (!destination) {
      return
    }

    if (
      destination.droppableId === source.droppableId &&
      destination.index === source.index
    ) {
      return
    }

    const newList = arrayMove(templates, source.index, destination.index)

    setItems(newList)

    await pMap(
      newList,
      async (template, index) => {
        try {
          await updateReportTemplateMutation({
            id: template.id,
            input: {
              name: template.name,
              display_name: template.display_name,
              priority: index,
            },
          })
        } catch {
          toast({
            msg: 'Unable to update priority.',
            status: 'error',
          })
        }
      },
      { concurrency: 1 }
    )
  }

  return (
    <DragDropContext onDragEnd={onDragEnd}>
      <Droppable droppableId={`group-${groupId}`}>
        {(droppableProvided, snapshot) => (
          <Box
            ref={droppableProvided.innerRef}
            {...droppableProvided.droppableProps}
            className={snapshot.isDraggingOver ? ' isDraggingOver' : ''}
            sx={{
              display: 'flex',
              flexDirection: 'column',
              '&.isDraggingOver': {
                bgColor: 'blue.200',
              },
            }}
          >
            {items.map((template, index) => (
              <MenuItem key={template.id} {...template} index={index} />
            ))}
            {droppableProvided.placeholder}
          </Box>
        )}
      </Droppable>
    </DragDropContext>
  )
}
