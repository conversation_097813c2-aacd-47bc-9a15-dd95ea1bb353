import { useAtom } from 'jotai'
import { Draggable } from '@hello-pangea/dnd'
import { Box, Flex, Text, Icon, Image } from '@chakra-ui/react'
import { ChevronRightIcon } from 'lucide-react'
import { reportAtom } from '@/utils/stores/report'
import { blank64Gif } from '@/utils/constants'

interface MenuItemProps {
  id: number
  display_name: string
  thumbnail: string | null
  index: number
}

export const MenuItem = ({
  id,
  display_name,
  thumbnail,
  index,
}: MenuItemProps) => {
  const [selectedId, setSelectedId] = useAtom(reportAtom)

  return (
    <Draggable draggableId={String(id)} index={index}>
      {(provided, snapshot) => (
        <Box
          {...provided.draggableProps}
          {...provided.dragHandleProps}
          ref={provided.innerRef}
        >
          <Box
            onClick={() => setSelectedId(id)}
            sx={{
              w: '100%',
              px: '5px',
              cursor: 'pointer',
              opacity: snapshot.isDragging ? 0.9 : 1,
              bg:
                selectedId === id || snapshot.isDragging
                  ? 'primary.100'
                  : 'grey.50',
              _hover: {
                bg: 'primary.100',
              },
            }}
          >
            <Flex
              sx={{
                flexDir: 'row',
                alignItems: 'center',
                pos: 'relative',
                py: '0.5rem',
              }}
            >
              <Image
                src={thumbnail ?? blank64Gif}
                sx={{
                  mr: '10px',
                  h: '50px',
                  w: '80px',
                  minW: '80px',
                  rounded: 'md',
                  objectFit: 'contain',
                  userSelect: 'none',
                  bg: 'white',
                }}
              />
              <Text
                sx={{
                  fontSize: 'sm',
                  userSelect: 'none',
                  pr: '1rem',
                }}
              >
                {display_name}
              </Text>
              <Icon
                as={ChevronRightIcon}
                boxSize={4}
                sx={{
                  ml: 'auto',
                  color: selectedId === id ? 'blue.500' : 'inherit',
                }}
              />
            </Flex>
          </Box>
        </Box>
      )}
    </Draggable>
  )
}
