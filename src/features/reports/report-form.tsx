import { memo, lazy, useCallback, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { useAtomValue } from 'jotai'
import { mergeDeepLeft, omit } from 'ramda'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { Box, Button, Flex, Grid } from '@chakra-ui/react'
import { CategoryOption } from '@/features/reports/category-option'
import { AddButton } from '@/components/add-button'
import { InputField } from '@/components/input-field'
import { SelectField } from '@/components/select-field'
import { CheckboxField } from '@/components/checkbox-field'
import { TextareaField } from '@/components/textarea-field'
import { useModal } from '@/hooks/use-modal'
import { useToast } from '@/hooks/use-toast'
import { useOrganization } from '@/hooks/use-organization'
import { reportAtom } from '@/utils/stores/report'
import {
  getReportCategories,
  getReportTemplate,
  updateReportTemplate,
} from '@/api/reports'
import type {
  ReportTemplateModel,
  ReportCategoryModel,
} from '@/types/models/report'
import type { ReportTemplateInput } from '@/types/api'

const EditCategory = lazy(() =>
  import('@/pages/reports/edit-category').then(({ EditCategory }) => ({
    default: EditCategory,
  }))
)

const schema = z.object({
  visible: z.boolean().default(true),
  name: z.string().min(1, 'Name is required.'),
  display_name: z.string().min(1, 'Display Name is required.'),
  description: z.string().min(1, 'Description is required.'),
  embed: z.boolean().default(false),
  thumbnail: z.string().min(1, 'Thumbnail URL is required.').url(),
  preview: z.string().min(1, 'PDF Preview URL is required.').url(),
  report_template_id: z.string(),
  report_categories: z.array(z.number()),
  config: z.string(),
  ui_settings: z.object({
    daily_report: z.boolean(),
    weekly_report: z.boolean(),
    monthly_report: z.boolean(),
    custom_report: z.boolean(),
    select_devices: z.boolean(),
  }),
})

type FormInputProps = z.infer<typeof schema>

export const ReportForm = memo(() => {
  const toast = useToast()
  const modal = useModal()
  const queryClient = useQueryClient()
  const { applicationId } = useOrganization()
  const selectedId = useAtomValue(reportAtom)

  const {
    reset,
    control,
    handleSubmit,
    formState: { isValid, isSubmitting },
  } = useForm({
    mode: 'onTouched',
    resolver: zodResolver(schema),
    defaultValues: {
      name: '',
      display_name: '',
      description: '',
      embed: false,
      thumbnail: '',
      preview: '',
      visible: true,
      report_template_id: '',
      report_categories: [] as number[],
      config: JSON.stringify({ is_location: false }),
      ui_settings: {
        daily_report: false,
        weekly_report: false,
        monthly_report: false,
        custom_report: false,
        select_devices: false,
      },
    },
  })

  const { mutateAsync: updateReportTemplateMutation } = useMutation<
    ReportTemplateModel,
    Error,
    {
      id: number
      input: ReportTemplateInput
    }
  >({
    mutationFn: ({ id, input }) => updateReportTemplate(id, input),
    onSuccess: () => {
      const cache = [
        'GetReportTemplate',
        'GetReportTemplates',
        'GetReportCategories',
      ]
      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const { data } = useQuery<ReportTemplateModel | null, Error>({
    queryKey: ['GetReportTemplate', selectedId, applicationId],
    queryFn: ({ signal }) =>
      selectedId && applicationId
        ? getReportTemplate({
            id: selectedId,
            applicationId,
            signal,
          })
        : null,
    refetchOnWindowFocus: false,
    enabled: !!selectedId && !!applicationId,
  })

  const { data: categoryOptions, isFetching: isFetchingCategories } = useQuery<
    any,
    Error,
    BaseOption[]
  >({
    queryKey: ['GetReportCategories'],
    queryFn: ({ signal }) =>
      getReportCategories({
        signal,
      }),
    refetchOnWindowFocus: false,
    select: useCallback(
      (data: ReportCategoryModel[]) =>
        data.reduce(
          (acc: BaseOption[], { id, display_name }: any) =>
            acc.concat({
              label: display_name,
              value: id,
            }),
          []
        ),
      []
    ),
  })

  const onAddCategory = (): void => {
    modal({
      component: <EditCategory />,
      config: {
        title: 'Add Category',
        data: {},
        onCallback: () => ({}),
      },
    })
  }

  const onSubmit = async (values: FormInputProps): Promise<boolean> => {
    if (!selectedId) return false

    const input = {
      ...omit(['report_template_id'], values),
      config: JSON.parse(values.config),
    } satisfies ReportTemplateInput

    try {
      await updateReportTemplateMutation({
        id: selectedId,
        input,
      })
      toast({
        msg: 'Report has been updated.',
        status: 'success',
      })
      return true
    } catch {
      toast({
        msg: 'Unable to update report.',
        status: 'error',
      })
      return false
    }
  }

  useEffect(() => {
    if (!data?.id) return

    reset({
      name: data.name ?? '',
      display_name: data.display_name ?? '',
      description: data.description ?? '',
      embed: data.embed ?? false,
      thumbnail: data.thumbnail ?? '',
      preview: data.preview ?? '',
      visible: data.visible ?? true,
      config: JSON.stringify(
        mergeDeepLeft(data?.config ?? {}, { is_location: false }),
        null,
        2
      ),
      report_template_id: data.id != null ? String(data.id) : '',
      report_categories:
        (data?.report_categories ?? []).map(({ id }) => id) ?? [],
      ui_settings: {
        daily_report: data.ui_settings?.daily_report ?? false,
        weekly_report: data.ui_settings?.weekly_report ?? false,
        monthly_report: data.ui_settings?.monthly_report ?? false,
        custom_report: data.ui_settings?.custom_report ?? false,
        select_devices: data.ui_settings?.select_devices ?? false,
      },
    })
  }, [reset, data])

  return (
    <Grid
      gap={2}
      templateRows="1fr auto"
      sx={{
        p: 2,
        h: '100%',
      }}
    >
      <Flex>
        <Box as="form" noValidate onSubmit={handleSubmit(onSubmit)}>
          <Grid gap={4} templateColumns={{ base: '1fr', lg: 'repeat(2, 1fr)' }}>
            <Box gridColumn="1 / -1">
              <CheckboxField name="visible" label="Visible" control={control} />
            </Box>
            <Box gridColumn="1 / -1">
              <CheckboxField
                name="embed"
                label="Embed (Power BI)"
                control={control}
              />
            </Box>
            <InputField name="name" label="Name" control={control} isRequired />
            <InputField
              name="display_name"
              label="Display Name"
              control={control}
              isRequired
            />
            <Box gridColumn="1 / -1">
              <SelectField
                name="report_categories"
                label="Categories"
                control={control}
                options={categoryOptions}
                components={{ Option: CategoryOption }}
                isLoading={isFetchingCategories}
                isRequired
                isSearchable
                isClearable
                isMulti
                render={
                  <AddButton
                    variant="link"
                    label="Add Category"
                    onClick={onAddCategory}
                    _hover={{
                      textDecoration: 'none',
                    }}
                  />
                }
              />
            </Box>
            <Box gridColumn="1 / -1">
              <TextareaField
                name="description"
                label="Description"
                control={control}
                isRequired
              />
            </Box>
            <InputField
              name="thumbnail"
              label="Thumbnail URL"
              type="url"
              control={control}
              isRequired
            />
            <InputField
              name="preview"
              label="Example URL"
              type="url"
              control={control}
              isRequired
            />
            <Box gridColumn="1 / -1">
              <TextareaField name="config" label="Config" control={control} />
            </Box>
            {data?.id && (
              <Box gridColumn="1 / -1">
                <InputField
                  name="report_template_id"
                  label="Report Template ID"
                  control={control}
                  isDisabled
                  isReadOnly
                />
              </Box>
            )}
            <Box gridColumn="1 / -1">
              <CheckboxField
                name="ui_settings.daily_report"
                label="Show Daily Reports"
                control={control}
              />
            </Box>
            <Box gridColumn="1 / -1">
              <CheckboxField
                name="ui_settings.weekly_report"
                label="Show Weekly Reports"
                control={control}
              />
            </Box>
            <Box gridColumn="1 / -1">
              <CheckboxField
                name="ui_settings.monthly_report"
                label="Show Monthly Reports"
                control={control}
              />
            </Box>
            <Box gridColumn="1 / -1">
              <CheckboxField
                name="ui_settings.custom_report"
                label="Show Custom Reports"
                control={control}
              />
            </Box>
            <Box gridColumn="1 / -1">
              <CheckboxField
                name="ui_settings.select_devices"
                label="Show Select Devices"
                control={control}
              />
            </Box>
          </Grid>
        </Box>
      </Flex>
      <Flex
        sx={{
          justifyContent: 'flex-end',
          alignItems: 'center',
        }}
      >
        <Button
          type="submit"
          aria-label="Save"
          colorScheme="teal"
          minW={120}
          isLoading={isSubmitting}
          isDisabled={!isValid || isSubmitting}
          onClick={handleSubmit(onSubmit)}
          loadingText="Saving"
        >
          Save
        </Button>
      </Flex>
    </Grid>
  )
})
