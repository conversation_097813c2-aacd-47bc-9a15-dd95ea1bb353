import { useEffect, useMemo, useDeferredValue, memo } from 'react'
import { useMountEffect } from '@react-hookz/web'
import { useQuery, keepPreviousData } from '@tanstack/react-query'
import { useAtom, useSetAtom } from 'jotai'
import { Box } from '@chakra-ui/react'
import { templateCatalogAtom } from '@/features/device-templates/utils/catalog'
import { Clipboard } from '@/components/data-table/clipboard'
import { DataTable } from '@/components/data-table/data-table'
import { useDeviceTemplates } from '@/features/device-templates/hooks/use-device-templates'
import { useOrganization } from '@/hooks/use-organization'
import { formatDate } from '@/utils/date/format-date'
import { tableAtom, recordCountAtom } from '@/utils/stores/table'
import { getTypes } from '@/api/things/types'
import type { TypeModel } from '@/types/models/type'
import type { GetCursorResponse } from '@/types/api'
import type { ColumnProps } from '@/types/table'

export const TableView = memo(() => {
  const tableName = 'device-templates'
  const setCount = useSetAtom(recordCountAtom)
  const { applicationId, organizationId } = useOrganization()
  const [catalog] = useAtom(templateCatalogAtom)
  const [{ limit, currPage, filter }, setTable] = useAtom(tableAtom(tableName))
  const deferredFilter = useDeferredValue(filter)
  const { actions } = useDeviceTemplates()

  useMountEffect(() => {
    setTable((prev) => ({
      ...prev,
      hiddenColumns: ['application_id'],
    }))
  })

  const { data } = useQuery<GetCursorResponse<TypeModel> | null, Error>({
    queryKey: [
      'GetTypes',
      catalog,
      organizationId,
      applicationId,
      limit,
      currPage,
      deferredFilter,
    ],
    queryFn: ({ signal }) =>
      getTypes({
        organizationId,
        applicationId,
        limit,
        page: currPage,
        filter: deferredFilter,
        catalog,
        signal,
      }),
    placeholderData: keepPreviousData,
    enabled: !!organizationId && !!applicationId,
  })

  useEffect(() => {
    if (!data) {
      return
    }
    setCount(data?.count)
  }, [data, setCount])

  const columns = useMemo<ColumnProps<TypeModel>[]>(
    () => [
      {
        name: 'ID',
        id: 'id',
        canHide: true,
        filter: {
          type: 'input',
          condition: 'like',
        },
        cell: ({ id }) => <Clipboard>{id}</Clipboard>,
      },
      {
        name: 'Name',
        id: 'name',
        filter: {
          type: 'input',
          condition: 'like',
        },
        cell: ({ name }) => <Clipboard>{name}</Clipboard>,
      },
      {
        name: 'Manufacturer',
        id: 'manufacturer',
        canHide: true,
        filter: {
          type: 'input',
          condition: 'like',
        },
        cell: ({ manufacturer }) =>
          manufacturer && <Clipboard>{manufacturer}</Clipboard>,
      },
      {
        name: 'Model',
        id: 'model',
        canHide: true,
        filter: {
          type: 'input',
          condition: 'like',
        },
        cell: ({ model }) => model && <Clipboard>{model}</Clipboard>,
      },
      {
        name: 'Codec ID',
        id: 'codec',
        canHide: true,
        filter: {
          type: 'input',
          condition: 'like',
        },
        cell: ({ codec }) => codec && <Clipboard>{codec}</Clipboard>,
      },
      {
        name: 'Application ID',
        id: 'application_id',
        canHide: true,
        filter: {
          type: 'input',
          condition: 'like',
        },
        cell: ({ application_id }) =>
          application_id && <Clipboard>{application_id}</Clipboard>,
      },
      {
        name: 'Created',
        id: 'created_at',
        w: '10%',
        cell: ({ created_at }) =>
          created_at && <Box noOfLines={1}>{formatDate(created_at)}</Box>,
      },
      {
        name: 'Updated',
        id: 'updated_at',
        w: '10%',
        cell: ({ updated_at }) =>
          updated_at && <Box noOfLines={1}>{formatDate(updated_at)}</Box>,
      },
    ],
    []
  )

  return (
    <DataTable
      tableName={tableName}
      data={data ?? { count: 0, rows: [] }}
      columns={columns}
      actions={actions}
    />
  )
})
