import {
  lazy,
  useEffect,
  useDeferredValue,
  Fragment,
  Suspense,
  memo,
} from 'react'
import { useInfiniteQuery } from '@tanstack/react-query'
import { useInView } from 'react-intersection-observer'
import { useAtom, useSetAtom } from 'jotai'
import { <PERSON>rid, Center, <PERSON><PERSON>, Button } from '@chakra-ui/react'
import { ArrowDownIcon } from 'lucide-react'
import { templateCatalogAtom } from '@/features/device-templates/utils/catalog'
import { Spinner } from '@/components/spinner'
import { tableAtom, recordCountAtom } from '@/utils/stores/table'
import { getTypes } from '@/api/things/types'
import { useOrganization } from '@/hooks/use-organization'

const GridItem = lazy(() =>
  import('@/features/device-templates/grid-item').then(({ GridItem }) => ({
    default: GridItem,
  }))
)

export const GridView = memo(() => {
  const tableName = 'device-templates'
  const { ref, inView } = useInView()
  const setCount = useSetAtom(recordCountAtom)
  const { applicationId, organizationId } = useOrganization()
  const [catalog] = useAtom(templateCatalogAtom)
  const [{ currPage, filter }] = useAtom(tableAtom(tableName))
  const deferredFilter = useDeferredValue(filter)

  const { data, status, isFetchingNextPage, fetchNextPage, hasNextPage } =
    useInfiniteQuery({
      queryKey: [
        'GetInfinityTypes',
        10,
        currPage,
        deferredFilter,
        catalog,
        organizationId,
        applicationId,
      ],
      queryFn: ({ pageParam = 0, signal }) =>
        getTypes({
          organizationId,
          applicationId,
          limit: 10,
          page: Number(pageParam),
          filter: deferredFilter,
          signal,
          catalog,
        }),
      initialPageParam: 0,
      enabled: !!organizationId && !!applicationId,
      getNextPageParam: (lastPage) => lastPage?.nextCursor,
    })

  useEffect(() => {
    if (!data) {
      return
    }
    setCount(data.pages?.[0]?.count ?? 0)
  }, [data, setCount])

  useEffect(() => {
    if (inView) {
      fetchNextPage()
    }
  }, [inView, fetchNextPage])

  return (
    <>
      {status === 'pending' ? (
        <Spinner />
      ) : status === 'error' ? (
        <Alert status="error">Error loading data</Alert>
      ) : (
        <>
          <Grid
            sx={{
              pb: 4,
              gap: 4,
              gridTemplateColumns: 'repeat(auto-fill, minmax(400px, 1fr))',
            }}
          >
            {data.pages.map((page) => (
              <Fragment key={`${page?.nextCursor}`}>
                {page?.rows.map((row) => (
                  <Suspense key={row.id} fallback={null}>
                    <GridItem row={row} />
                  </Suspense>
                ))}
              </Fragment>
            ))}
          </Grid>
          {hasNextPage && (
            <Center>
              <Button
                ref={ref}
                variant="ghost"
                colorScheme="secondary"
                onClick={() => fetchNextPage()}
                isDisabled={!hasNextPage || isFetchingNextPage}
                leftIcon={<ArrowDownIcon size={16} />}
                isLoading={isFetchingNextPage}
              >
                {isFetchingNextPage
                  ? 'Loading more ...'
                  : hasNextPage
                    ? 'Load More'
                    : 'Nothing more to load'}
              </Button>
            </Center>
          )}
        </>
      )}
    </>
  )
})
