import { useMemo } from 'react'
import {
  Box,
  <PERSON>lex,
  Grid,
  Image,
  Text,
  Tooltip,
  VStack,
  StackDivider,
} from '@chakra-ui/react'
import { useQuery } from '@tanstack/react-query'
import { useDeviceTemplates } from '@/features/device-templates/hooks/use-device-templates'
import { Actions } from '@/components/data-table/actions'
import { onImageError } from '@/utils/on-image-error'
import { formatDate } from '@/utils/date/format-date'
import { getMetas } from '@/api/things/metas'
import { useOrganization } from '@/hooks/use-organization'
import { useDarkerBg } from '@/hooks/use-darker-bg'
import type { TypeModel } from '@/types/models/type'
import type { PaginatedQueryResponse } from '@/types/api'
import type { MetaModel } from '@/types/models/meta'

interface GridItemProps {
  row: TypeModel
}

export const GridItem = ({ row }: GridItemProps) => {
  const { id, name, codec, updated_at } = row
  const updatedAt = useMemo<Maybe<string>>(
    () => formatDate(updated_at, 'PPpp'),
    [updated_at]
  )

  const { actions } = useDeviceTemplates()
  const { organizationId, applicationId } = useOrganization()
  const darkerBg = useDarkerBg()

  const { data } = useQuery<PaginatedQueryResponse<MetaModel>, Error>({
    queryKey: ['GetMetas', organizationId, applicationId, id],
    queryFn: ({ signal }) =>
      getMetas({
        organizationId,
        applicationId,
        typeId: id,
        signal,
      }),
    enabled: !!organizationId && !!applicationId && !!id,
  })

  const imageUrl = useMemo<string>(
    () =>
      (data?.rows ?? []).find(({ key }) => key === 'image_url')?.value ?? '',
    [data]
  )

  return (
    <Grid
      sx={{
        p: 4,
        gap: 4,
        gridTemplateRows: '200px 1fr',
        borderRadius: 'md',
        shadow: 'md',
        bg: 'white',
        _dark: {
          shadow: 'md-dark',
          bg: darkerBg,
        },
      }}
    >
      <Flex
        sx={{
          p: 2,
          pos: 'relative',
          borderRadius: 'md',
          bg: 'white',
          justifyContent: 'center',
          alignItems: 'center',
          opacity: 1,
          _dark: {
            opacity: 0.7,
          },
        }}
      >
        <Image
          src={imageUrl}
          h="180px"
          w="auto"
          pos="relative"
          margin="0 auto"
          onError={onImageError}
        />
      </Flex>
      <VStack
        divider={
          <StackDivider
            sx={{
              borderColor: 'gray.100',
              _dark: {
                borderColor: 'blackAlpha.300',
              },
            }}
          />
        }
        spacing={2}
        align="stretch"
        overflow="hidden"
      >
        <Box overflow="hidden">
          <Tooltip hasArrow label={name} placement="bottom-start">
            <Text fontWeight="bold" noOfLines={1}>
              {name}
            </Text>
          </Tooltip>
        </Box>
        <Box overflow="hidden">
          <Box
            textTransform="uppercase"
            color="gray.400"
            fontWeight="semibold"
            fontSize="xs"
          >
            Id:
          </Box>
          <Box fontSize="sm" noOfLines={1}>
            {id}
          </Box>
        </Box>
        <Box>
          <Box
            textTransform="uppercase"
            color="gray.400"
            fontWeight="semibold"
            fontSize="xs"
          >
            Codec:
          </Box>
          <Box fontSize="sm" noOfLines={1}>
            {codec ?? '-'}
          </Box>
        </Box>
        <Flex align="center" justify="space-between">
          <Actions row={row} actions={actions} />
          <Box
            color="gray.400"
            fontWeight="semibold"
            fontSize="xs"
            textTransform="uppercase"
          >
            Updated: {updatedAt ?? 'Unknown'}
          </Box>
        </Flex>
      </VStack>
    </Grid>
  )
}
