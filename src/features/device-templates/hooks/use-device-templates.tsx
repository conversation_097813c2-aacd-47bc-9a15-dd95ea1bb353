import * as Sentry from '@sentry/react'
import { lazy, useCallback, useMemo } from 'react'
import { useNavigate } from 'react-router'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { omit, pickBy, isNotEmpty } from 'ramda'
import pMap from 'p-map'
import { useModal } from '@/hooks/use-modal'
import { useOrganization } from '@/hooks/use-organization'
import { useToast } from '@/hooks/use-toast'
import {
  createChannel,
  getChannels,
  updateChannel,
} from '@/api/things/channels'
import { createType, deleteType } from '@/api/things/types'
import { createMeta, getMetas } from '@/api/things/metas'
import { createUse, getUses, updateUse } from '@/api/things/uses'
import type { TypeModel } from '@/types/models/type'
import type { ChannelModel, ChannelRuleTemplate } from '@/types/models/channel'
import type { UseModel, UseRuleModel } from '@/types/models/use'
import type { TypeInput, MetaInput, UseInput } from '@/types/api'
import type { ActionProps } from '@/types/table'

const ConfirmDialog = lazy(() =>
  import('@/components/confirm-dialog').then(({ ConfirmDialog }) => ({
    default: ConfirmDialog,
  }))
)

const Export = lazy(() =>
  import('@/pages/device-templates/export').then(({ Export }) => ({
    default: Export,
  }))
)

export const useDeviceTemplates = () => {
  const modal = useModal()
  const toast = useToast()
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  const { organizationId, applicationId } = useOrganization()

  const { mutateAsync: deleteTypeMutation } = useMutation<
    boolean,
    Error,
    string
  >({
    mutationFn: (typeId) =>
      deleteType({ organizationId, applicationId, typeId }),

    onSuccess: () => {
      const cache = [
        'GetTypes',
        'GetInfinityTypes',
        'GetChannels',
        'GetMetas',
        'GetUses',
      ]

      for (const cacheKey of cache) {
        queryClient.invalidateQueries({ queryKey: [cacheKey] })
      }
    },
  })

  const onEdit = useCallback(
    ({ id }: TypeModel): void => {
      navigate(`/manage/device-templates/${id}`, {
        viewTransition: true,
      })
    },
    [navigate]
  )

  const getChannelIdByName = useCallback(
    (usedName: string, channels: ChannelModel[]): number | undefined => {
      const match = channels.find((channel) => channel.name === usedName)
      return match?.id ?? undefined
    },
    []
  )

  const onDuplicate = useCallback(
    async (row: TypeModel): Promise<any> => {
      const errors = []

      try {
        const { id, name } = row

        // Get all relations to copy.
        const { rows: metas } = await queryClient.fetchQuery({
          queryKey: ['GetMetas', id, organizationId, applicationId],
          queryFn: ({ signal }) =>
            getMetas({
              organizationId,
              applicationId,
              typeId: id,
              signal,
            }),
        })

        const { rows: channels } = await queryClient.fetchQuery({
          queryKey: ['GetChannels', id, organizationId, applicationId],
          queryFn: ({ signal }) =>
            getChannels({
              organizationId,
              applicationId,
              typeId: id,
              signal,
            }),
        })

        const { rows: uses } = await queryClient.fetchQuery({
          queryKey: ['GetUses', id, organizationId, applicationId],
          queryFn: ({ signal }) =>
            getUses({
              organizationId,
              applicationId,
              typeId: id,
              signal,
            }),
        })

        const input = {
          ...omit(
            ['id', 'meta', 'device_use', 'channels'],
            pickBy((row) => isNotEmpty(row), row)
          ),
          application_id: applicationId,
          organization_id: organizationId,
          is_example: false,
          is_public: false,
          is_approved: false,
          version: row.version ?? undefined,
          protocol_version: row.protocol_version ?? undefined,
          name: `${row.name} (copy)`,
        } satisfies Omit<TypeInput, 'id' | 'meta' | 'device_use' | 'channels'>

        // Create new device template.
        const newType = await createType({
          organizationId,
          applicationId,
          input,
        })

        const newId = newType?.id

        // Copy all relations ...

        await pMap(
          metas,
          async (meta) => {
            try {
              const input = {
                ...meta,
                device_type_id: newId,
              } satisfies MetaInput
              await createMeta({
                organizationId,
                applicationId,
                typeId: newId,
                input: omit(['id'], input),
              })
            } catch (err: unknown) {
              Sentry.captureException(err)
              errors.push(err)
            }
          },
          { concurrency: 1 }
        )

        await pMap(
          channels,
          async (channel) => {
            try {
              const input = {
                ...channel,
                device_type_id: newId,
              } satisfies ChannelModel
              await createChannel({
                organizationId,
                applicationId,
                typeId: newId,
                input: omit(['id'], {
                  ...input,
                  data: input?.data ?? {},
                }),
              })
            } catch (err: unknown) {
              Sentry.captureException(err)
              errors.push(err)
            }
          },
          { concurrency: 1 }
        )

        await pMap(
          uses,
          async (use) => {
            try {
              const input = {
                ...use,
                device_type_id: newId,
              } satisfies UseInput
              await createUse({
                organizationId,
                applicationId,
                typeId: newId,
                input: omit(['id'], input),
              })
            } catch (err: unknown) {
              Sentry.captureException(err)
              errors.push(err)
            }
          },
          { concurrency: 1 }
        )

        // Fetch and modify new relations ...
        const { rows: newChannels } = await queryClient.fetchQuery({
          queryKey: ['GetChannels', newId, organizationId, applicationId],
          queryFn: ({ signal }) =>
            getChannels({
              organizationId,
              applicationId,
              typeId: newId,
              signal,
            }),
        })

        const { rows: newUses } = await queryClient.fetchQuery({
          queryKey: ['GetUses', newId, organizationId, applicationId],
          queryFn: ({ signal }) =>
            getUses({
              organizationId,
              applicationId,
              typeId: newId,
              signal,
            }),
        })

        await pMap(
          newChannels.reduce((acc: ChannelModel[], channel) => {
            acc.push({
              ...channel,
              data: {
                ...(channel?.data ?? {}),
                rule_templates: (channel?.data?.rule_templates ?? []).reduce(
                  (ac: ChannelRuleTemplate[], rule) => {
                    ac.push({
                      ...rule,
                      // channel_id: channel?.id ?? null,
                    })
                    return ac
                  },
                  []
                ),
              },
            })
            return acc
          }, [] as ChannelModel[]),
          async (channel) => {
            if (!channel?.id) {
              console.info('channel has no id.')
              return
            }
            try {
              await updateChannel({
                organizationId,
                applicationId,
                typeId: newId,
                channelId: channel.id,
                input: omit(['id'], {
                  ...channel,
                  data: channel?.data ?? {},
                }),
              })
            } catch (err: unknown) {
              Sentry.captureException(err)
              errors.push(err)
            }
          },
          { concurrency: 1 }
        )

        await pMap(
          newUses.reduce((acc: UseModel[], use) => {
            const newUse = {
              ...use,
              settings: {
                ...(use?.settings ?? {}),
                rules: (use?.settings?.rules ?? []).reduce(
                  (ac: UseRuleModel[], rule) => {
                    const channelId = getChannelIdByName(rule.name, channels)
                    if (channelId) {
                      ac.push({
                        ...rule,
                        channel_id: channelId,
                      })
                    }
                    return ac
                  },
                  []
                ),
              },
            }
            acc.push(newUse)
            return acc
          }, []),
          async (use) => {
            try {
              await updateUse({
                organizationId,
                applicationId,
                typeId: newId,
                useId: use.id,
                input: omit(['id'], use),
              })
            } catch (err: unknown) {
              Sentry.captureException(err)
              errors.push(err)
            }
          },
          { concurrency: 1 }
        )

        if (errors.length > 0) {
          // Remove failed device template and hope it cascade stuff.
          try {
            return await deleteTypeMutation(newId)
          } finally {
            Sentry.captureMessage('Failed to copy and revert device template.')
            toast({
              status: 'error',
              msg: `${errors.length} errors occurred when copying device template.`,
            })
          }
        }

        toast({
          status: 'success',
          msg: `“${name}” has been duplicated.`,
        })

        navigate(`/manage/device-templates/${newId}`, {
          viewTransition: true,
        })
      } catch (error: unknown) {
        Sentry.captureException(error)
        toast({
          status: 'error',
          msg: 'Unable to copy device template.',
        })
      }
    },
    [
      toast,
      navigate,
      queryClient,
      organizationId,
      applicationId,
      deleteTypeMutation,
      getChannelIdByName,
    ]
  )

  const onRemove = useCallback(
    ({ id, name }: TypeModel): void => {
      modal({
        size: 'md',
        component: <ConfirmDialog />,
        config: {
          title: 'Remove Device Template?',
          description: `Are you sure you want to remove “${name}”?`,
          confirmLabel: 'Remove',
          onCallback: async () => {
            try {
              await deleteTypeMutation(id)
              toast({
                status: 'success',
                msg: `“${name}” has been removed.`,
              })
            } catch (error: unknown) {
              Sentry.captureException(error)
              toast({
                status: 'error',
                msg: `Unable to remove “${name}”.`,
              })
            }
          },
        },
      })
    },
    [modal, toast, deleteTypeMutation]
  )

  const onExport = useCallback(
    (row: TypeModel): void => {
      modal({
        component: <Export />,
        scrollBehavior: 'inside',
        config: {
          data: row,
          onCallback: () => ({}),
        },
      })
    },
    [modal]
  )

  const actions = useMemo<ActionProps<TypeModel>[]>(
    () => [
      {
        label: 'Edit Device Template',
        onClick: onEdit,
        canView: () => true,
      },
      {
        label: 'Duplicate Device Template',
        onClick: onDuplicate,
        canView: () => true,
      },
      {
        label: 'Remove Device Template',
        onClick: onRemove,
        canView: () => true,
      },
      {
        label: 'Export Azure DMC',
        onClick: onExport,
        canView: () => true,
      },
    ],
    [onEdit, onDuplicate, onRemove, onExport]
  )

  return {
    actions,
  }
}
