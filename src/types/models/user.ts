export interface CurrentUserModel {
  federated_user_id: string
  tina_user_id: Maybe<string>
  firstName: string
  lastName: string
  first_name: string
  last_name: string
  email: string
  username: string
  enabled: boolean
  locale: Maybe<string>
  phone_number: Maybe<string>
  attributes: {
    phoneNumber?: string[]
    inviteStatus?: string[]
    invite_status?: string[]
    requiredActions?: string[]
    adminHasOrgRole?: string[]
    adminHasRole?: string[]
    billing_customer_id?: string[]
  }
}

export interface UserModel {
  id: string
  federated_user_id: string
  tina_user_id: string
  firstName?: string
  lastName?: string
  username: string
  email: string
  enabled: boolean
  emailVerified: boolean
  createdTimestamp: number
  application_id: string
  requiredActions: string[]
  federatedIdentities: Maybe<string[]>
  notBefore: number
  totalLocations: number
  attributes: {
    accepted_timestamp?: string[]
    is_invited?: string[]
    invite_timestamp?: string[]
    inviteStatus?: string[]
    invite_status?: string[]
    application_id?: string[]
    login_count?: string[]
    phoneNumber?: string[]
    sign_up_source?: string[]
    last_login?: string[]
    invite_code?: string[]
    locale?: string[]
    first_login?: string[]
  }
  access: {
    manageGroupMembership: boolean
    view: boolean
    mapRoles: boolean
    impersonate: boolean
    manage: boolean
  }
}

export interface UserRoleModel {
  [key: string]: any
  id: string
  tina_user_id: string
  federated_user_id: string
  firstName: string
  lastName: string
  email: string
  enabled: boolean
  createdAt: string
  attributes: {
    inviteStatus?: string[]
    invite_status?: string[]
    phoneNumber?: string[]
    last_login?: string[]
    locale?: string[]
  }
  roles: Array<{
    role_id: number
    scope: string
    role: {
      id: number
      name: string
      description: Maybe<string>
      is_built_in: number
    }
  }>
  // virtual
  name: string
  phoneNumber: string
  scopes?: Array<{
    role_id: number
    application: Maybe<string>
    name: string
  }>
}
