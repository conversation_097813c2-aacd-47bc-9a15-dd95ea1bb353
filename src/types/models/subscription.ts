export interface SubscriptionModel {
  id: string
  billing_customer_id: string
  billing_subscription_id: string
  product_id: string
  organization_id: string
  application_id: string | null
  user_id: string
  status: string
  quantity: number
  start_date: string
  end_date: string
  cancellation_date: string | null
  created_at: string
  updated_at: string
  deleted_at: string | null
  createdAt: string
  updatedAt: string
  product?: {
    id: string
    billing_product_id: string
    default_price: string
    name: string | null
    sku: string | null
    description: string | null
    category: string
    organization_id: string
    price: string | null
    created_at: string
    updated_at: string
    deleted_at: string | null
    createdAt: string
    updatedAt: string
  }
  stripe_price?: {
    id: string
    object: string
    active: boolean
    billing_scheme: string
    created: number
    currency: string
    custom_unit_amount: string | null
    livemode: boolean
    lookup_key: string | null
    metadata: Record<string, unknown>
    nickname: string | null
    product: string
    recurring: {
      aggregate_usage: string | null
      interval: string
      interval_count: number
      meter: string | null
      trial_period_days: number | null
      usage_type: string
    }
    tax_behavior: string
    tiers_mode: string | null
    transform_quantity: string | null
    type: string
    unit_amount: number
    unit_amount_decimal: string
  }
}

export interface SubscriptionUserModel {
  id: string
  createdTimestamp: number
  username: string
  enabled: boolean
  totp: boolean
  emailVerified: boolean
  firstName: string
  lastName: string
  email: string
  attributes: {
    accepted_timestamp?: string[]
    login_count?: string[]
    phoneNumber?: string[]
    sign_up_source?: string[]
    is_invited?: string[]
    last_login?: string[]
    invite_timestamp?: string[]
    invite_status?: string[]
    locale?: string[]
    first_login?: string[]
    adminHasOrgRole?: string[]
    adminHasRole?: string[]
    adminHasAppRole?: string[]
  }
  disableableCredentialTypes: string[]
  requiredActions: string[]
  federatedIdentities: unknown[]
  notBefore: number
  access: {
    manageGroupMembership: boolean
    view: boolean
    mapRoles: boolean
    impersonate: boolean
    manage: boolean
  }
  user_id: string
  tina_user_id: string
  federated_user_id: string
  subscription_id: string
}

export interface ConsoleTriggersSubscriptionStatus {
  is_enabled: boolean
  is_valid: boolean
}
