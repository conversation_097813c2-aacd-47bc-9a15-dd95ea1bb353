export interface StripeListResponse<TData = any> {
  object: 'list'
  data: TData[]
  has_more: boolean
  url: string
}

export type SubscriptionUserListResponse = {
  user_id: string
  subscriptions: string[]
}

export interface BillingAddress {
  city: string | null
  country: string | null
  line1: string | null
  line2: string | null
  postal_code: string | null
  state: string | null
}

export interface BillingCard {
  id: string
  object: string
  address_city: string | null
  address_country: string | null
  address_line1: string | null
  address_line1_check: string | null
  address_line2: string | null
  address_state: string | null
  address_zip: string | null
  address_zip_check: string | null
  brand: string
  country: string | null
  customer: string | null
  cvc_check: string | null
  dynamic_last4: string | null
  exp_month: number
  exp_year: number
  fingerprint: string | null
  funding: 'credit' | 'debit' | 'prepaid' | 'unknown'
  last4: string
  metadata: Record<string, any>
  name: string | null
  tokenization_method: string | null
  wallet: {
    apple_pay: object | null
    type: 'apple_pay'
  } | null
}

export interface BillingCustomer {
  id: string
  object: string
  address: BillingAddress | null
  balance: number
  created: number
  currency: string | null
  default_source: string | null
  delinquent: boolean
  description: null
  discount: {
    id: string
    object: string
    checkout_session: string | null
    coupon: object | any
    customer: string | null
    end: number | null
    invoice: string | null
    invoice_item: string | null
    promotion_code: string | null
    start: number | null
    subscription: string | null
    subscription_item: string | null
  } | null
  email: string | null
  invoice_prefix: string | null
  invoice_settings: {
    custom_fields: { name: string; value: string }[] | null
    default_payment_method: string | null
    footer: string | null
    rendering_options: {
      amount_tax_display: string | null
      template: string | null
    } | null
  }
  livemode: boolean
  metadata: Record<string, any>
  name: string | null
  next_invoice_sequence: number | null
  phone: string | null
  preferred_locales: string[] | null
  shipping: {
    address: BillingAddress
    name: string
    phone: string | null
  } | null
  tax_exempt: 'none' | 'exempt' | 'reverse' | null
  test_clock: string | null
}

export interface BillingPlan {
  id: string
  object: string
  active: boolean
  aggregate_usage: string | null
  amount: number
  amount_decimal: string
  billing_scheme: string
  created: number
  currency: string
  interval: 'day' | 'week' | 'month' | 'year'
  interval_count: number
  livemode: boolean
  metadata: Record<string, any>
  meter: number | null
  nickname: string | null
  product: string
  tiers_mode: string | null
  transform_usage: string | null
  trial_period_days: number | null
  usage_type: string
}

export interface BillingProduct {
  id: string
  object: string
  active: boolean
  attributes: any[]
  created: number
  default_price: string
  description: string
  images: string[]
  livemode: boolean
  marketing_features: string[]
  metadata: Record<string, string>
  name: string
  package_dimensions: number | null
  shippable: string | null
  statement_descriptor: string | null
  tax_code: string | null
  type: string
  unit_label: string | null
  updated: number
  url: string | null
}

export interface BillingStatus {
  customer_id: string
  user_id: string
  subscription: {
    status?: SubscriptionStatus
    plan?: BillingPlan
  }
  product?: BillingProduct
}

export interface CheckoutSession {
  id: string
  object: 'checkout.session'
  cancel_url: string
  success_url: string
  url: string
}

export interface CustomerSession {
  object: 'customer_session'
  client_secret: string
  components: {
    buy_button: {
      enabled: boolean
    }
    payment_element: {
      enabled: boolean
      features: string | null
    }
    pricing_table: {
      enabled: boolean
    }
  }
  created: number
  customer: string
  expires_at: number
  livemode: boolean
}

export interface DeletedBillingCustomer {
  id: string
  object: string
  deleted: boolean
}

export type InvoiceStatus = 'draft' | 'open' | 'paid' | 'uncollectible' | 'void'

export interface Invoice {
  id: string
  object: string
  account_country: string
  account_name: string
  account_tax_ids: string[] | null
  amount_due: number
  amount_paid: number
  amount_remaining: number
  amount_shipping: number
  application: string | null
  application_fee_amount: number | null
  attempt_count: number
  attempted: boolean
  auto_advance: boolean
  automatic_tax: {
    enabled: boolean
    liability: { type: 'account' | 'self' } | null
    status: string | null
  }
  billing_reason:
    | 'manual'
    | 'subscription_create'
    | 'subscription_cycle'
    | 'subscription_update'
    | 'subscription'
    | 'manual'
    | 'upcoming'
    | null
  charge: string | null
  collection_method: 'charge_automatically' | 'send_invoice'
  created: number
  currency: string
  custom_fields: { name: string; value: string }[] | null
  customer: string
  customer_address: string | null
  customer_email: string | null
  customer_name: string | null
  customer_phone: string | null
  customer_shipping: string | null
  customer_tax_exempt: 'none' | 'exempt' | 'reverse'
  customer_tax_ids: any[] | null
  default_payment_method: string | null
  default_source: string | null
  default_tax_rates: []
  description: string | null
  discount: string | null
  discounts: string[] | null
  due_date: number | null
  ending_balance: number | null
  footer: string | null
  from_invoice: { action: string; invoice: string } | null
  hosted_invoice_url: string | null
  invoice_pdf: string | null
  issuer: {
    account: string | null
    type: 'self' | 'account'
  }
  last_finalization_error: string | null
  latest_revision: string | null
  lines: {
    object: string
    data: {
      id: string
      object: string
      amount: number
      amount_excluding_tax: number | null
      currency: string
      description: string | null
      discount_amounts: { amount: number; discount: string }[] | null
      discountable: boolean
      discounts: string[] | null
      invoice: string | null
      invoice_item: string | null
      livemode: boolean
      metadata: Record<string, any>
      period: { end: number; start: number }
      pretax_credit_amounts: { amount: number; credit: string }[] | null
      proration: boolean
      proration_details: {
        credited_items: { invoice: string; invoice_line_items: string[] }
      } | null
      quantity: number | null
      subscription: string | null
      subscription_item: string | null
      tax_amounts: { amount: number; rate: string }[] | null
      tax_rates: any[] | null
      type: 'invoiceitem' | 'subscription'
      unit_amount_excluding_tax: number | null
    }[]
    has_more: boolean
    url: string
  }
  livemode: boolean
  metadata: Record<string, any>
  next_payment_attempt: number | null
  number: string | null
  on_behalf_of: string | null
  paid: boolean
  paid_out_of_band: boolean
  payment_intent: string | null
  payment_settings: {
    default_mandate: string | null
    payment_method_options: object | null
    payment_method_types: string[] | null
  }
  period_end: number
  period_start: number
  post_payment_credit_notes_amount: number
  pre_payment_credit_notes_amount: number
  quote: string | null
  receipt_number: string | null
  rendering_options: null
  shipping_cost: {
    amount_subtotal: number
    amount_tax: number
    amount_total: number
    shipping_rate: string | null
    taxes: object[] | null
  } | null
  shipping_details: {
    name: string
    phone: string | null
    address: BillingAddress
  } | null
  starting_balance: number
  statement_descriptor: string | null
  status: InvoiceStatus
  status_transitions: {
    finalized_at: number | null
    marked_uncollectible_at: number | null
    paid_at: number | null
    voided_at: number | null
  }
  subscription: string | null
  subtotal: number
  subtotal_excluding_tax: number | null
  tax: number | null
  test_clock: string | null
  total: number
  total_discount_amounts: { amount: number; discount: string }[]
  total_excluding_tax: number | null
  total_tax_amounts: {
    amount: number
    inclusive: boolean
    tax_rate: string
    taxability_reason:
      | 'customer_exempt'
      | 'not_collecting'
      | 'not_subject_to_tax'
      | 'not_supported'
      | 'portion_product_exempt'
      | 'portion_reduced_rated'
      | 'portion_standard_rated'
      | 'product_exempt'
      | 'product_exempt_holiday'
      | 'proportionally_rated'
      | 'reduced_rated'
      | 'reverse_charge'
      | 'standard_rated'
      | 'taxable_basis_reduced'
      | 'zero_rated'
  }[]
  transfer_data: { amount: number | null; destination: string } | null
  webhooks_delivered_at: number | null
}

export interface LocationSubscription {
  location_id: number
  user_id: string
  email: string
  subscription_name: string
  is_valid: boolean
}

export interface PortalSession {
  id: string
  object: 'billing_portal.session'
  configuration: string
  created: number
  customer: string
  flow: string | null
  livemode: boolean
  on_behalf_of: string | null
  return_url: string
  url: string
}

export interface StripeTier {
  flat_amount: number | null
  flat_amount_decimal: string | null
  unit_amount: number | null
  unit_amount_decimal: string | null
  up_to: number | null
}

export interface StripeRecurring {
  aggregate_usage: string | null
  interval: 'day' | 'week' | 'month' | 'year'
  interval_count: number
  meter: string | null
  trial_period_days: number | null
  usage_type: 'licensed' | 'metered'
}

export interface StripePrice {
  id: string
  object: 'price'
  active: boolean
  billing_scheme: 'per_unit' | 'tiered'
  created: number
  currency: string
  custom_unit_amount: unknown | null
  livemode: boolean
  lookup_key: string | null
  metadata: Record<string, any>
  nickname: string | null
  product: string
  recurring: StripeRecurring
  tax_behavior: 'inclusive' | 'exclusive' | 'unspecified'
  tiers?: StripeTier[]
  tiers_mode?: 'graduated' | 'volume'
  transform_quantity: unknown | null
  type: 'one_time' | 'recurring'
  unit_amount: number | null
  unit_amount_decimal: string | null
}

export type SubscriptionCategory = 'app' | 'app-triggers' | 'app-powerbi'

export type SubscriptionStatus =
  | 'trialing'
  | 'active'
  | 'past_due'
  | 'canceled'
  | 'unpaid'
  | 'paused'

export interface Subscription {
  id: string
  billing_customer_id: string | null
  product_id: string
  organization_id: string
  application_id: string
  user_id: string
  status: SubscriptionStatus
  quantity?: number
  start_date: string
  end_date: string
  cancellation_date: string | null
  created_at: string
  updated_at: string
  deleted_at: string | null
  createdAt: string
  updatedAt: string
}

export interface SubscriptionLicensePayload {
  subscription_id: string
  user_ids: string[]
}

export interface SubscriptionUser {
  id: string
  username: string
  firstName: string
  lastName: string
  email: string
  user_id: string
  tina_user_id: string
  federated_user_id: string
  subscription_id: string
  // has even more props
}

export interface SubscriptionWithProduct extends Subscription {
  product: {
    id: string
    billing_product_id: string
    default_price: string
    name: string
    sku: string | null
    description: string
    category: SubscriptionCategory
    organization_id: string
    price: number | null
    created_at: string
    updated_at: string
    deleted_at: string | null
    createdAt: string
    updatedAt: string
  }
  stripe_price: StripePrice
}
