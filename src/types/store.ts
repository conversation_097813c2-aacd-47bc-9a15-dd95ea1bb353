import type { ReactElement } from 'react'
import type Monaco from 'monaco-editor/esm/vs/editor/editor.api'
import type { Monaco as MonacoInstance } from '@monaco-editor/react'
import type { OptimusDecodeModel } from '@/types/models/codec'

interface ModalAtomDataProps<TData = any> {
  id?: Maybe<string | number>
  title?: Maybe<string>
  description?: Maybe<string>
  confirmLabel?: Maybe<string>
  inputLabel?: Maybe<string>
  data?: TData
  onCallback(callback?: any): void
}

type ChakraSizes =
  | 'sm'
  | 'md'
  | 'lg'
  | 'xl'
  | '2xl'
  | 'xs'
  | '3xl'
  | '4xl'
  | '5xl'
  | '6xl'
  | 'full'
  | string

type ModalScrollBehavior = 'inside' | 'outside'

export interface ModalAtomProps<TData = any> {
  open?: boolean
  size?: ChakraSizes
  scrollBehavior?: ModalScrollBehavior
  component: Maybe<ReactElement>
  config: ModalAtomDataProps<TData>
}

interface TableAtomFilter {
  id: string
  value: string
  condition: string
  query: string
}

export interface TableAtomFamilyProps {
  limitBase: number
  limitOptions: number[]
  limit: number
  currPage: number
  filter?: string
  filters: TableAtomFilter[]
  hiddenColumns: string[]
  checkedIds: Array<string | number>
  visibleIds: Array<string | number>
}

export interface CodecStateAtomProps extends OptimusDecodeModel {
  sessionRefresh: boolean
  optionsRefresh: boolean
}

export interface EditorAtomProps {
  monacoRef: Maybe<MonacoInstance>
  editorRef: Maybe<Monaco.editor.IStandaloneCodeEditor>
  error: string
  snippet: string
}

export interface EditorLog {
  type: 'idle' | 'error' | 'info'
  text: string
  time: string
}
