import { memo, useMemo } from 'react'
import { Link as RouterLink } from 'react-router'
import {
  Box,
  Flex,
  Icon,
  Grid,
  List,
  ListItem,
  Tooltip,
  useColorMode,
  useColorModeValue,
} from '@chakra-ui/react'
import {
  MoonIcon,
  SunIcon,
  ChevronFirstIcon,
  ChevronLastIcon,
  CpuIcon,
  UsersIcon,
  RouterIcon,
  WebhookIcon,
  QrCodeIcon,
  FileTextIcon,
  FileChartPieIcon,
  TerminalIcon,
  Layers2Icon,
  Grid2X2PlusIcon,
  SlidersHorizontalIcon,
} from 'lucide-react'
import { useAtom } from 'jotai'
import { NavLink } from '@/components/navigation/nav-link'
import { navAtom } from '@/utils/stores/nav'
import { useEnv } from '@/hooks/use-env'
import { useAbility } from '@/hooks/use-ability'
import { useAuth } from '@/contexts/use-auth'
import { UrlLogoDashboard } from '@/components/url-logo-dashboard'
import { UrlIcon } from '@/components/url-icon'
import { useDarkerBg } from '@/hooks/use-darker-bg'

const NAV_ROUTES = [
  {
    path: '/manage/customers',
    title: 'Customers',
    icon: UsersIcon,
    scopes: ['view:applications'],
  },
  {
    path: '/manage/device-registry',
    title: 'Device Registry',
    icon: Grid2X2PlusIcon,
    scopes: ['view:applications'],
  },
  {
    path: '/manage/gateways',
    title: 'Gateways',
    icon: RouterIcon,
    scopes: ['edit:applications'],
  },
  {
    path: '/manage/devices',
    title: 'Devices',
    icon: CpuIcon,
    scopes: ['manage:all'],
  },
  {
    path: '/manage/reports',
    title: 'Reports',
    icon: FileChartPieIcon,
    scopes: ['view:reports'],
  },
  {
    path: '/manage/report-builder',
    title: 'App Report Builder',
    icon: FileChartPieIcon,
    scopes: ['manage:all'],
  },
  {
    path: '/manage/device-templates',
    title: 'Device Templates',
    icon: Layers2Icon,
    scopes: ['edit:applications'],
  },
  {
    path: '/codec-editor',
    title: 'Codec Editor',
    icon: TerminalIcon,
    scopes: ['view:applications'],
  },
  {
    path: '/manage/settings',
    title: 'Settings',
    icon: SlidersHorizontalIcon,
    scopes: ['view:applications'],
  },
  {
    path: '/manage/help',
    title: 'Help',
    icon: FileTextIcon,
    scopes: [],
  },
  {
    path: '/manage/integration-templates',
    title: 'Integration Templates',
    icon: Layers2Icon,
    scopes: ['manage:all'],
  },
  {
    path: '/manage/integrations-active',
    title: 'Active Integrations',
    icon: WebhookIcon,
    scopes: ['manage:all'],
  },
  {
    path: '/manage/integrations',
    title: 'Integrations',
    icon: WebhookIcon,
    scopes: ['view:applications'],
  },
  {
    path: '/manage/qr',
    title: 'QR Reader',
    icon: QrCodeIcon,
    scopes: ['manage:all'],
  },
  // {
  //   path: '/builder',
  //   title: 'White Label Builder',
  //   icon: BlendIcon,
  //   scopes: ['manage:all'],
  // },
]

export const Navigation = memo(() => {
  const env = useEnv()
  const { colorMode, toggleColorMode } = useColorMode()
  const [{ open }, setNav] = useAtom(navAtom)
  const { isInternalAdmin } = useAuth()
  const { can, abilityScope } = useAbility()

  const darkerBg = useDarkerBg()
  const bg = useColorModeValue('white', darkerBg)
  const color = useColorModeValue('gray.700', 'white')
  const activeLinkColor = useColorModeValue('primary.500', 'primary.200')
  const borderBottomColor = useColorModeValue('gray.50', darkerBg)

  const navRoutes = useMemo(() => {
    if (isInternalAdmin) {
      return NAV_ROUTES
    }

    return NAV_ROUTES.filter((route) => {
      const scopes = route.scopes ?? []

      if (scopes.length === 0) {
        return true
      }

      return scopes.some((scope: string) => {
        const { subject, action } = abilityScope(scope)
        return can([action, subject])
      })
    })
  }, [isInternalAdmin, can, abilityScope])

  const actions = useMemo(
    () => [
      {
        label: 'Collapse',
        tip: 'Expand',
        icon: open ? ChevronFirstIcon : ChevronLastIcon,
        onClick: () => setNav({ open: !open }),
        enabled: true,
      },
      {
        label: colorMode === 'dark' ? 'Light Mode' : 'Dark Mode',
        tip: colorMode === 'dark' ? 'Light Mode' : 'Dark Mode',
        icon: colorMode === 'dark' ? SunIcon : MoonIcon,
        onClick: toggleColorMode,
        enabled: (env?.theme_id ?? 'iotinabox').startsWith('iotinabox'),
      },
    ],
    [colorMode, open, setNav, toggleColorMode, env?.theme_id]
  )

  return (
    <Grid
      gridArea="nav"
      sx={{
        gridTemplateRows: '65px 1fr auto',
        gridTemplateColumns: '1fr',
        h: '100vh',
        bg,
      }}
    >
      <Flex
        as={RouterLink}
        to="/"
        sx={{
          pos: 'relative',
          flexDir: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          borderBottom: '1px solid',
          borderBottomColor,
          h: '65px',
          maxW: '250px',
        }}
      >
        {open ? <UrlLogoDashboard /> : <UrlIcon boxSize="40px" />}
      </Flex>
      <List
        sx={{
          w: 'full',
          pos: 'relative',
          overflowY: 'auto',
          overflowX: 'hidden',
          'a:first-type-of': {
            border: 0,
          },
          'a:last-type-of': {
            border: 0,
          },
        }}
      >
        {navRoutes.map((route) => {
          const { path, title, icon } = route
          return (
            <NavLink to={path} handle={route} key={path}>
              <Icon as={icon} boxSize={4} aria-label={title} />
              {open && (
                <Box
                  as="span"
                  sx={{
                    ml: 2,
                    fontSize: 'md',
                    userSelect: 'none',
                  }}
                >
                  {title}
                </Box>
              )}
            </NavLink>
          )
        })}
      </List>
      <List
        sx={{
          w: 'full',
          pos: 'relative',
          flexDir: 'column',
          borderTopWidth: '1px',
          borderTopColor: borderBottomColor,
        }}
      >
        {actions
          .filter(({ enabled }) => enabled)
          .map((action, key) => (
            <Tooltip
              key={key}
              hasArrow
              label={action.tip}
              isDisabled={open}
              placement="right"
              fontSize="md"
            >
              <ListItem
                as={Flex}
                onClick={action.onClick}
                sx={{
                  h: '50px',
                  w: 'full',
                  px: 4,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: open ? 'left' : 'center',
                  color,
                  cursor: 'pointer',
                  _hover: {
                    color: activeLinkColor,
                    textDecoration: 'none',
                  },
                  _activeLink: {
                    color: activeLinkColor,
                  },
                }}
              >
                <Icon as={action.icon} boxSize={4} aria-label={action.label} />
                {open && (
                  <Box
                    as="span"
                    sx={{
                      ml: 2,
                      fontSize: 'md',
                      userSelect: 'none',
                    }}
                  >
                    {action.label}
                  </Box>
                )}
              </ListItem>
            </Tooltip>
          ))}
      </List>
    </Grid>
  )
})
