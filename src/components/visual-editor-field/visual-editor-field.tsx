import { useLayoutEffect, useEffect, type ReactNode } from 'react'
import purify from 'dompurify'
import { transparentize } from 'polished'
import { useController, type Control } from 'react-hook-form'
import { useEditor, EditorContent } from '@tiptap/react'
import { Document } from '@tiptap/extension-document'
import { Paragraph } from '@tiptap/extension-paragraph'
import { Text } from '@tiptap/extension-text'
import { Bold } from '@tiptap/extension-bold'
import { BulletList } from '@tiptap/extension-bullet-list'
import { Code } from '@tiptap/extension-code'
import { CodeBlock } from '@tiptap/extension-code-block'
import { Heading } from '@tiptap/extension-heading'
import { History } from '@tiptap/extension-history'
import { Italic } from '@tiptap/extension-italic'
import { ListItem } from '@tiptap/extension-list-item'
import { OrderedList } from '@tiptap/extension-ordered-list'
import { Strike } from '@tiptap/extension-strike'
import { Underline } from '@tiptap/extension-underline'
import { Link } from '@tiptap/extension-link'
import { Prose } from '@nikolovlazar/chakra-ui-prose'
import {
  Box,
  Icon,
  HStack,
  FormControl,
  FormLabel,
  FormHelperText,
  Popover,
  PopoverTrigger,
  PopoverContent,
  PopoverBody,
  useToken,
  type InputProps,
  type SystemStyleObject,
} from '@chakra-ui/react'
import { useToggle } from 'react-use'
import { InfoIcon } from 'lucide-react'
import { Toolbar } from '@/components/visual-editor-field/toolbar'

interface VisualEditorFieldProps extends InputProps {
  name: string
  label: string
  control: Control<any, any>
  shortInfo?: string
  longInfo?: string | ReactNode
  isRequired?: boolean
  isDisabled?: boolean
  isReadOnly?: boolean
  isMinimal?: boolean
  sx?: SystemStyleObject
}

export const VisualEditorField = ({
  name,
  label,
  control,
  shortInfo,
  longInfo,
  isRequired = false,
  isDisabled = false,
  isReadOnly = false,
  isMinimal = false,
  sx,
}: VisualEditorFieldProps) => {
  const [toggle, setToggle] = useToggle(false)
  const [shadowColor] = useToken('colors', ['secondary.500'])

  const {
    field: { onChange, value },
    fieldState: { error, invalid },
  } = useController({
    name,
    control,
  })

  const extensions = [
    Document,
    History,
    Heading,
    Paragraph,
    Text,
    Bold,
    Italic,
    Code,
    CodeBlock,
    ListItem,
    BulletList,
    OrderedList,
    Underline,
    Strike,
    Link,
  ]

  const editor = useEditor({
    editable: !(isDisabled || isReadOnly),
    extensions,
    content: '',
    onBlur: ({ editor }) => {
      const html = purify.sanitize(editor.getHTML()).replace('<p></p>', '')
      onChange(html)
    },
  })

  useLayoutEffect(() => {
    if (!editor) {
      return
    }
    editor?.commands.setContent(value)
  }, [editor, value])

  useEffect(() => {
    return () => {
      if (editor) {
        editor.destroy()
      }
    }
  }, [editor])

  return (
    <FormControl
      as={Box}
      isInvalid={invalid}
      isRequired={isRequired}
      isDisabled={isDisabled}
      isReadOnly={isReadOnly}
      sx={{
        ...sx,
        mb: 3,
      }}
    >
      <HStack justify="space-between" align="center">
        <FormLabel htmlFor={name}>{label}</FormLabel>
        {longInfo && (
          <Popover
            isOpen={toggle}
            onOpen={() => setToggle(true)}
            onClose={() => setToggle(false)}
            placement="bottom-end"
          >
            <PopoverTrigger>
              <Box cursor="pointer" onMouseOver={() => setToggle(true)}>
                <Icon
                  as={InfoIcon}
                  sx={{
                    color: 'blue.500',
                    _dark: {
                      color: 'blue.200',
                    },
                  }}
                />
              </Box>
            </PopoverTrigger>
            <PopoverContent
              sx={{
                top: '-10px',
                right: '-5px',
                fontSize: 'xs',
              }}
            >
              <PopoverBody>{longInfo}</PopoverBody>
            </PopoverContent>
          </Popover>
        )}
      </HStack>
      <Box
        sx={{
          borderRadius: 'base',
          borderWidth: 1,
          bg: 'inherit',
          _dark: {
            bg: 'whiteAlpha.50',
          },
          _hover: {
            boxShadow: editor?.isFocused
              ? `0 0 0 0.2rem ${transparentize(0.8, shadowColor)}`
              : 'none',
            borderColor: 'secondary.500',
            _dark: {
              borderColor: 'secondary.200',
            },
          },
        }}
      >
        <Toolbar editor={editor} isMinimal={isMinimal} />
        <Box px={4} py={2} pb={6}>
          <Prose>
            <EditorContent editor={editor} />
          </Prose>
        </Box>
      </Box>
      {invalid && (
        <FormHelperText
          aria-live="polite"
          color="red.600"
          mt={0}
          fontSize="75%"
          pos="absolute"
        >
          {error?.message ?? ''}
        </FormHelperText>
      )}
      {shortInfo && !invalid && (
        <FormHelperText
          aria-live="polite"
          color="gray.600"
          mt={0}
          fontSize="75%"
          pos="absolute"
        >
          {shortInfo}
        </FormHelperText>
      )}
    </FormControl>
  )
}
