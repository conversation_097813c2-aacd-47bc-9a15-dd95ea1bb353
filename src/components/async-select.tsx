import type { Ref } from 'react'
import { useColorMode, useTheme } from '@chakra-ui/react'
import {
  AsyncPaginate,
  type AsyncPaginateProps,
} from 'react-select-async-paginate'
import { selectStyle, selectTheme } from '@/utils/theme/select/md'

interface AsyncSelectProps
  extends Omit<AsyncPaginateProps<any, any, any, any>, 'name'> {
  id?: string
  innerRef?: Ref<any>
}

export const AsyncSelect = ({ id, innerRef, ...rest }: AsyncSelectProps) => {
  const { colors } = useTheme()
  const { colorMode } = useColorMode()
  const isDarkMode = colorMode === 'dark'

  return (
    <AsyncPaginate
      name={id}
      styles={selectStyle(colors, isDarkMode)}
      theme={(theme) => selectTheme(colors, theme, isDarkMode)}
      selectRef={innerRef}
      {...rest}
    />
  )
}
