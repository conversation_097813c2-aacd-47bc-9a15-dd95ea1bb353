import type { TreeNode } from '@/types/tree'

const MAX_RECURSION_DEPTH = 10

export const extendedNodes = (nodes: TreeNode[], depth = 0): any => {
  if (depth > MAX_RECURSION_DEPTH) {
    console.warn(
      `Tree recursion depth exceeded ${MAX_RECURSION_DEPTH}, stopping to prevent stack overflow`
    )
    return []
  }

  return nodes.reduce((acc: TreeNode[], childNode) => {
    acc.push({
      ...childNode,
      leaf: !childNode.descendants,
      checked: false,
      intermediate: false,
    })
    if (childNode.descendants) {
      const ns = childNode.descendants.reduce((a: TreeNode[], n: TreeNode) => {
        acc.push({
          ...n,
          leaf: !n.descendants,
          checked: false,
          intermediate: false,
        })
        if (n.descendants) {
          a.push(...extendedNodes(n.descendants, depth + 1))
        }
        return a
      }, [])
      acc.push(...ns)
    }
    return acc
  }, [])
}
