import type { PropsWithChildren } from 'react'
import { useEffect, useState, useMemo, useCallback } from 'react'
import { useNavigate, useParams, useLoaderData } from 'react-router'
import {
  Tabs as ChakraTabs,
  Tab,
  TabList,
  TabPanel,
  TabPanels,
} from '@chakra-ui/react'

interface TabItem {
  path: string
  title: string
  isDisabled?: boolean
}

interface TabProps extends PropsWithChildren {
  current?: string
  isDisabled?: boolean
}

const tabStyles = {
  _hover: {
    color: 'primary.500',
    _dark: {
      color: 'primary.200',
    },
  },
  _active: {
    color: 'primary.500',
    _dark: {
      color: 'primary.200',
    },
  },
  _selected: {
    color: 'primary.500',
    borderBottomColor: 'primary.500',
    _dark: {
      color: 'primary.200',
      borderBottomColor: 'primary.200',
    },
  },
}

export const Tabs = ({ current, isDisabled = false, children }: TabProps) => {
  const navigate = useNavigate()
  const params = useParams()
  const loaderData = useLoaderData() as any
  const [tabIndex, setTabIndex] = useState<number>(0)

  const tabs = useMemo((): TabItem[] => {
    return loaderData?.tabs || []
  }, [loaderData?.tabs])

  const onChangeTab = useCallback(
    (index: number) => {
      setTabIndex(index)
      const tab = tabs[index]
      if (tab) {
        const path = tab.path.replace(
          /:(\w+)/g,
          (_, param) => params?.[param] ?? ''
        )
        navigate(path, {
          viewTransition: true,
        })
      }
    },
    [tabs, navigate, params]
  )

  useEffect(() => {
    if (tabs.length > 0) {
      const index = tabs.findIndex(({ path }) => {
        const normalizedPath = path.replace(
          /:(\w+)/g,
          (_, param) => params?.[param] ?? ''
        )
        return normalizedPath === current
      })
      setTabIndex(index >= 0 ? index : 0)
    }
  }, [current, tabs, params])

  const tabList = useMemo(
    () => (
      <TabList>
        {tabs.map((tab, key: number) => (
          <Tab
            key={tab.path}
            noOfLines={1}
            isDisabled={tab.isDisabled || (isDisabled && key > 0)}
            sx={tabStyles}
          >
            {tab.title}
          </Tab>
        ))}
      </TabList>
    ),
    [tabs, isDisabled]
  )

  const tabPanels = useMemo(
    () => (
      <TabPanels>
        {tabs.map((tab) => (
          <TabPanel p={0} key={tab.path}>
            {children}
          </TabPanel>
        ))}
      </TabPanels>
    ),
    [tabs, children]
  )

  if (tabs.length === 0) {
    return (
      <ChakraTabs isLazy isManual variant="line" colorScheme="secondary">
        <TabList>
          <Tab sx={tabStyles}>No tabs available</Tab>
        </TabList>
        <TabPanels>
          <TabPanel p={0}>{children}</TabPanel>
        </TabPanels>
      </ChakraTabs>
    )
  }

  return (
    <ChakraTabs
      isLazy
      isManual
      variant="line"
      colorScheme="secondary"
      index={tabIndex}
      onChange={onChangeTab}
    >
      {tabList}
      {tabPanels}
    </ChakraTabs>
  )
}
