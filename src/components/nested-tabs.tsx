import type { PropsWithChildren } from 'react'
import { useEffect, useState, useMemo, useCallback } from 'react'
import { useNavigate, useParams, useLoaderData } from 'react-router'
import {
  Tabs as ChakraTabs,
  Tab,
  TabList,
  TabPanel,
  TabPanels,
  Box,
} from '@chakra-ui/react'

interface TabItem {
  path: string
  title: string
  isDisabled?: boolean
  isSubTab?: boolean
  parentPath?: string
}

interface NestedTabProps extends PropsWithChildren {
  current?: string
  isDisabled?: boolean
  subTabGroup?: string // Name of the sub-tab group to load
}

const tabStyles = {
  _hover: {
    color: 'primary.500',
    _dark: {
      color: 'primary.200',
    },
  },
  _active: {
    color: 'primary.500',
    _dark: {
      color: 'primary.200',
    },
  },
  _selected: {
    color: 'primary.500',
    borderBottomColor: 'primary.500',
    _dark: {
      color: 'primary.200',
      borderBottomColor: 'primary.200',
    },
  },
}

const subTabStyles = {
  ...tabStyles,
  fontSize: 'sm',
  py: 2,
  _selected: {
    ...tabStyles._selected,
    borderBottomWidth: '2px',
  },
}

export const NestedTabs = ({
  current,
  isDisabled = false,
  subTabGroup,
  children,
}: NestedTabProps) => {
  const navigate = useNavigate()
  const params = useParams()
  const loaderData = useLoaderData() as any
  const [mainTabIndex, setMainTabIndex] = useState<number>(0)
  const [subTabIndex, setSubTabIndex] = useState<number>(0)

  // Get main tabs (excluding sub-tabs)
  const mainTabs = useMemo((): TabItem[] => {
    const allTabs = loaderData?.tabs || []
    return allTabs.filter((tab: TabItem) => !tab.isSubTab)
  }, [loaderData?.tabs])

  // Get sub-tabs for the current context
  const subTabs = useMemo((): TabItem[] => {
    if (!subTabGroup) return []
    return loaderData?.subTabs || []
  }, [loaderData?.subTabs, subTabGroup])

  // Determine if current path is a sub-tab path
  const isCurrentPathSubTab = useMemo(() => {
    if (!(current && subTabs.length > 0)) return false
    return subTabs.some((tab) => {
      const normalizedPath = tab.path.replace(
        /:(\w+)/g,
        (_, param) => params?.[param] ?? ''
      )
      return normalizedPath === current
    })
  }, [current, subTabs, params])

  const onChangeMainTab = useCallback(
    (index: number) => {
      setMainTabIndex(index)
      const tab = mainTabs[index]
      if (tab) {
        const path = tab.path.replace(
          /:(\w+)/g,
          (_, param) => params?.[param] ?? ''
        )
        navigate(path, {
          viewTransition: true,
        })
      }
    },
    [mainTabs, navigate, params]
  )

  const onChangeSubTab = useCallback(
    (index: number) => {
      setSubTabIndex(index)
      const tab = subTabs[index]
      if (tab) {
        const path = tab.path.replace(
          /:(\w+)/g,
          (_, param) => params?.[param] ?? ''
        )
        navigate(path, {
          viewTransition: true,
        })
      }
    },
    [subTabs, navigate, params]
  )

  // Set main tab index based on current path
  useEffect(() => {
    if (mainTabs.length > 0 && current) {
      const index = mainTabs.findIndex(({ path }) => {
        const normalizedPath = path.replace(
          /:(\w+)/g,
          (_, param) => params?.[param] ?? ''
        )
        return current.startsWith(normalizedPath)
      })
      setMainTabIndex(index >= 0 ? index : 0)
    }
  }, [current, mainTabs, params])

  // Set sub-tab index based on current path
  useEffect(() => {
    if (subTabs.length > 0 && current && isCurrentPathSubTab) {
      const index = subTabs.findIndex(({ path }) => {
        const normalizedPath = path.replace(
          /:(\w+)/g,
          (_, param) => params?.[param] ?? ''
        )
        return normalizedPath === current
      })
      setSubTabIndex(index >= 0 ? index : 0)
    }
  }, [current, subTabs, params, isCurrentPathSubTab])

  const mainTabList = useMemo(
    () => (
      <TabList>
        {mainTabs.map((tab, key: number) => (
          <Tab
            key={tab.path}
            noOfLines={1}
            isDisabled={tab.isDisabled || (isDisabled && key > 0)}
            sx={tabStyles}
          >
            {tab.title}
          </Tab>
        ))}
      </TabList>
    ),
    [mainTabs, isDisabled]
  )

  const subTabList = useMemo(() => {
    if (!(subTabs.length > 0 && isCurrentPathSubTab)) return null

    return (
      <Box
        borderBottom="1px"
        borderColor="gray.200"
        _dark={{ borderColor: 'gray.600' }}
      >
        <ChakraTabs
          isLazy
          isManual
          variant="line"
          colorScheme="secondary"
          index={subTabIndex}
          onChange={onChangeSubTab}
          size="sm"
        >
          <TabList>
            {subTabs.map((tab) => (
              <Tab
                key={tab.path}
                noOfLines={1}
                isDisabled={tab.isDisabled}
                sx={subTabStyles}
              >
                {tab.title}
              </Tab>
            ))}
          </TabList>
        </ChakraTabs>
      </Box>
    )
  }, [subTabs, isCurrentPathSubTab, subTabIndex, onChangeSubTab])

  const tabPanels = useMemo(
    () => (
      <TabPanels>
        {mainTabs.map((tab) => (
          <TabPanel p={0} key={tab.path}>
            {subTabList}
            {children}
          </TabPanel>
        ))}
      </TabPanels>
    ),
    [mainTabs, children, subTabList]
  )

  if (mainTabs.length === 0) {
    return (
      <ChakraTabs isLazy isManual variant="line" colorScheme="secondary">
        <TabList>
          <Tab sx={tabStyles}>No tabs available</Tab>
        </TabList>
        <TabPanels>
          <TabPanel p={0}>
            {subTabList}
            {children}
          </TabPanel>
        </TabPanels>
      </ChakraTabs>
    )
  }

  return (
    <ChakraTabs
      isLazy
      isManual
      variant="line"
      colorScheme="secondary"
      index={mainTabIndex}
      onChange={onChangeMainTab}
    >
      {mainTabList}
      {tabPanels}
    </ChakraTabs>
  )
}
