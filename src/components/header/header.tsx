import { useMemo, useState, useEffect, Fragment, memo } from 'react'
import { NavLink, useLocation, useNavigate } from 'react-router'
import { useQuery } from '@tanstack/react-query'
import {
  Box,
  Flex,
  Icon,
  IconButton,
  Link,
  HStack,
  Menu,
  MenuButton,
  MenuItem,
  MenuList,
  useColorModeValue,
} from '@chakra-ui/react'
import { useMediaQuery } from '@react-hookz/web'
import { MenuIcon, ChevronDownIcon } from 'lucide-react'
import { SelectOrganization } from '@/components/header/select-organization'
import { SelectApplication } from '@/components/header/select-application'
import { useShop } from '@/hooks/use-shop'
import { useOrganization } from '@/hooks/use-organization'
import { useAuth } from '@/contexts/use-auth'
import { useAbility } from '@/hooks/use-ability'
import { useDarkerBg } from '@/hooks/use-darker-bg'
import { getSettings } from '@/api/settings'
import type { SettingModel } from '@/types/models/setting'
import type { PaginatedQueryResponse } from '@/types/api'
import { AddButton } from '@/components/add-button'

const HEADER_ROUTES = [
  {
    path: '/manage',
    title: 'Manage',
    scopes: ['view:reports'],
  },
  {
    path: '/applications',
    title: 'Applications',
    scopes: ['manage:all'],
  },
  {
    path: '/users',
    title: 'Users',
    scopes: ['view:applications'],
  },
  {
    path: '/account',
    title: 'Account',
    scopes: [],
  },
]

const ORG_SELECT_PATHS = [
  '/applications',
  '/users',
  '/manage/customers',
  '/manage/device-registry',
  '/manage/gateways',
  '/manage/devices',
  '/manage/settings',
  '/manage/reports',
  '/manage/device-templates',
  '/manage/integrations',
]

const APP_SELECT_PATHS = [
  '/users',
  '/manage/customers',
  '/manage/device-registry',
  '/manage/gateways',
  '/manage/devices',
  '/manage/settings',
  '/manage/reports',
  '/manage/device-templates',
  '/manage/integrations',
]

const CREATE_ORG_PATH = ['/create-organization']

const baseLinkActiveStyle = {
  textDecoration: 'none',
  bg: 'gray.50',
  color: 'primary.500',
  _dark: {
    bg: 'blackAlpha.300',
    color: 'primary.200',
  },
}

const baseLinkStyle = {
  color: 'gray.700',
  fontWeight: 'normal',
  fontSize: 'md',
  _dark: {
    color: 'white',
  },
  _hover: {
    ...baseLinkActiveStyle,
  },
  _activeLink: {
    ...baseLinkActiveStyle,
  },
}

const linkStyle = {
  ...baseLinkStyle,
  p: 2,
  h: 'inherit',
  display: 'inline-flex',
  alignItems: 'center',
}

export const Header = memo(() => {
  const { pathname } = useLocation()
  const { isInternalAdmin } = useAuth()
  const { getUrl } = useShop()
  const [url, setUrl] = useState<string>()
  const darkerBg = useDarkerBg()
  const bg = useColorModeValue('white', darkerBg)
  const breakpoint = useMediaQuery('(max-width: 1560px)')
  const [isOpen, setOpen] = useState<boolean>(false)
  const { applicationId } = useOrganization()
  const { can, abilityScope } = useAbility()
  const navigate = useNavigate()
  const onClose = () => isOpen && setOpen(false)
  const onToggle = () => setOpen(!isOpen)

  const hasOrgSelect = useMemo<boolean>(() => {
    if (!isInternalAdmin) {
      return false
    }
    return ORG_SELECT_PATHS.some((path: string) => pathname.startsWith(path))
  }, [pathname, isInternalAdmin])

  const hasAppSelect = useMemo<boolean>(() => {
    return APP_SELECT_PATHS.some((path: string) => pathname.startsWith(path))
  }, [pathname])

  const hasCreateOrg = useMemo<boolean>(() => {
    if (!can(['manage', 'all'])) {
      return false
    }
    return CREATE_ORG_PATH.some((path: string) => !pathname.startsWith(path))
  }, [pathname, can])

  // Filter header routes by current user permissions
  const routes = useMemo(() => {
    // Internal admins see all routes
    if (isInternalAdmin) {
      return HEADER_ROUTES
    }

    // Filter routes by user permissions
    return HEADER_ROUTES.filter((route) => {
      const scopes = route.scopes || []

      // Accept routes with no scopes (public routes like Account)
      if (scopes.length === 0) {
        return true
      }

      // Check if user has permission for any of the route scopes
      return scopes.some((scope: string) => {
        const { subject, action } = abilityScope(scope)
        return can([action, subject])
      })
    })
  }, [isInternalAdmin, can, abilityScope])

  const { data: qs } = useQuery<
    PaginatedQueryResponse<SettingModel> | null,
    Error,
    string | undefined
  >({
    queryKey: ['GetSettings', applicationId],
    queryFn: ({ signal }) =>
      applicationId
        ? getSettings({
            applicationId,
            signal,
          })
        : null,
    enabled: !!applicationId,
    select: (data) => {
      if (!data) {
        return
      }
      const match = data.rows.find(({ name }) => name === 'company')
      return match
        ? new URLSearchParams({
            customfield_10981: match.value,
          }).toString()
        : undefined
    },
  })

  const links = useMemo(
    () => [
      {
        name: 'Provide Feedback',
        href: 'https://mydevices.atlassian.net/servicedesk/customer/portal/9/group/23/create/141',
      },
      {
        name: 'Need Sensor Advice',
        href: `https://mydevices.atlassian.net/servicedesk/customer/portal/19/group/38/create/250?${qs}`,
      },
      {
        name: 'System Status',
        href: 'https://status.mydevices.com',
      },
      {
        name: 'Trust Center',
        href: 'https://trust.mydevices.com',
      },
      {
        name: 'Feature Roadmap',
        href: 'https://app-rm.roadmunk.com/publish/b18660ff26e45deeb59a3b7f5e02e851fb163de0',
      },
      {
        name: 'Developer Roadmap',
        href: 'https://app-rm.roadmunk.com/publish/3cb287e35db785f904cf76a8917215697f5d1673',
        canView: isInternalAdmin,
      },
      {
        name: 'DevOps Roadmap',
        href: 'https://app-rm.roadmunk.com/publish/0a25458700b7fcb815671ea765d89ff6ccaf8408',
        canView: isInternalAdmin,
      },
      {
        name: 'API Docs',
        href: 'https://docs.mydevices.com',
      },
      {
        name: 'Console Docs',
        href: 'https://iot-help.scrollhelp.site/partners',
      },
      {
        name: 'App Docs',
        href: 'https://iot-help.scrollhelp.site/iotkb',
      },
    ],
    [isInternalAdmin, qs]
  )

  useEffect(() => {
    getUrl().then((s) => setUrl(s))
  }, [getUrl])

  return (
    <Flex
      as="header"
      gridArea="header"
      sx={{
        h: '100%',
        bg,
        borderBottom: '1px',

        justifyContent: 'space-between',
        alignItems: 'center',
        zIndex: 2,
        borderBottomColor: 'gray.50',
        _dark: {
          borderBottomColor: 'transparent',
        },
      }}
    >
      <Flex
        sx={{
          flex: 1,
          h: 'inherit',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <Flex pl={4} align="center">
          {hasOrgSelect && <SelectOrganization />}
          {hasAppSelect && <SelectApplication />}
          {hasCreateOrg && (
            <Box pl={2}>
              <AddButton
                label="Create Organization"
                variant="outline"
                onClick={() => {
                  navigate('/create-organization')
                }}
                sx={{
                  maxH: '38px',
                }}
              />
            </Box>
          )}
        </Flex>
        <Flex
          sx={{
            mr: 4,
            h: 'inherit',
            justifyContent: 'space-between',
            alignItems: 'center',
            display: breakpoint ? 'none' : 'flex',
          }}
        >
          {routes.map((route, key) => {
            const { path, title } = route
            return (
              path && (
                <Fragment key={path}>
                  {key === routes.length - 1 && url && (
                    <>
                      <Menu isLazy>
                        <MenuButton sx={linkStyle}>
                          <HStack
                            gap={1}
                            sx={{
                              justifyContent: 'space-between',
                              color: 'gray.700',
                              _dark: {
                                color: 'primary.200',
                              },
                            }}
                          >
                            <Box>Help</Box>
                            <Icon boxSize={2} as={ChevronDownIcon} />
                          </HStack>
                        </MenuButton>
                        <MenuList sx={{ p: 2, bg }}>
                          {links.map(({ name, href, canView }, key) => {
                            if (canView === false) {
                              return null
                            }
                            return (
                              <MenuItem
                                key={key}
                                as={Link}
                                href={href}
                                target="_blank"
                                rel="noopener noreferrer"
                                sx={{
                                  ...baseLinkStyle,
                                  bg: 'transparent',
                                }}
                              >
                                {name}
                              </MenuItem>
                            )
                          })}
                        </MenuList>
                      </Menu>
                      {can(['view', 'applications']) && (
                        <Link
                          href={url}
                          sx={linkStyle}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          Quotes & Orders
                        </Link>
                      )}
                    </>
                  )}
                  <Link
                    viewTransition
                    as={NavLink}
                    rel="prefetch"
                    to={path}
                    sx={linkStyle}
                  >
                    {title}
                  </Link>
                </Fragment>
              )
            )
          })}
        </Flex>
        {breakpoint && routes.length > 0 && (
          <Flex mr={4} ml={2} align="center" justify="center" h="inherit">
            <Menu isLazy>
              <MenuButton
                as={IconButton}
                aria-label="Menu"
                icon={<MenuIcon size={16} />}
                colorScheme="primary"
                borderRadius="full"
                size="md"
              />
              <MenuList sx={{ p: 2 }}>
                {routes.map((route, key) => {
                  const { path, title } = route
                  return (
                    path && (
                      <Fragment key={path}>
                        {key === routes.length - 1 && url && (
                          <>
                            <Menu
                              isOpen={isOpen}
                              placement="right-start"
                              onClose={onClose}
                            >
                              <MenuButton
                                as={MenuItem}
                                onClick={onToggle}
                                sx={baseLinkStyle}
                              >
                                <HStack gap={1} justify="space-between">
                                  <Box>Help</Box>
                                  <Icon boxSize={2} as={ChevronDownIcon} />
                                </HStack>
                              </MenuButton>
                              <MenuList sx={{ p: 2 }}>
                                {links.map(({ name, href }, key) => (
                                  <MenuItem
                                    key={key}
                                    as={Link}
                                    href={href}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    sx={baseLinkStyle}
                                  >
                                    {name}
                                  </MenuItem>
                                ))}
                              </MenuList>
                            </Menu>
                            {can(['view', 'applications']) && (
                              <MenuItem
                                as={Link}
                                href={url}
                                target="_blank"
                                rel="noopener noreferrer"
                                sx={baseLinkStyle}
                              >
                                Quotes & Orders
                              </MenuItem>
                            )}
                          </>
                        )}
                        <MenuItem
                          viewTransition
                          as={NavLink}
                          to={path}
                          sx={baseLinkStyle}
                        >
                          {title}
                        </MenuItem>
                      </Fragment>
                    )
                  )
                })}
              </MenuList>
            </Menu>
          </Flex>
        )}
      </Flex>
    </Flex>
  )
})
