import { useCallback, useEffect, useRef, useState } from 'react'
import {
  fetchEventSource,
  type EventSourceMessage,
} from '@microsoft/fetch-event-source'
import { baseStore } from '@/utils/stores/store'
import { userAtom } from '@/utils/stores/auth'
import { useReffed } from '@/hooks/use-reffed'

const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))

const ReadyState = {
  UNINSTANTIATED: -1,
  CONNECTING: 0,
  OPEN: 1,
  CLOSED: 2,
} as const

type ReadyState = (typeof ReadyState)[keyof typeof ReadyState]

const readyStateString = (readyState: ReadyState) => {
  switch (readyState) {
    case ReadyState.UNINSTANTIATED:
      return 'Uninstantiated'
    case ReadyState.CONNECTING:
      return 'Connecting'
    case ReadyState.OPEN:
      return 'Open'
    case ReadyState.CLOSED:
      return 'Closed'
    default:
      return 'Unknown'
  }
}

interface UseStreamOptions {
  onMessage: (data: EventSourceMessage) => void
  onClose?: VoidFunction
  onError?: (event: Event) => void
  onOpen?: (event: Response) => void
}

class AbortedError extends Error {}

export const useStream = ({
  onClose,
  onError,
  onMessage,
  onOpen,
}: UseStreamOptions) => {
  const abortControllerRef = useRef<AbortController>(undefined)

  // Event Listener Refs
  const onCloseRef = useReffed(onClose)
  const onErrorRef = useReffed(onError)
  const onMessageRef = useReffed(onMessage)
  const onOpenRef = useReffed(onOpen)

  // State
  const [error, setError] = useState<boolean>(false)
  const [readyState, setReadyState] = useState<ReadyState>(
    ReadyState.UNINSTANTIATED
  )

  const connect = async <TData = unknown>(path: string, data: TData) => {
    abortControllerRef.current = new AbortController()
    const url = new URL(path, import.meta.env.VITE_API_URL).toString()

    try {
      setReadyState(ReadyState.CONNECTING)
      const token = baseStore.get(userAtom).access_token

      await fetchEventSource(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(data),
        signal: abortControllerRef?.current?.signal,
        onclose: () => {
          // @todo - this is not being called
          setReadyState(ReadyState.CLOSED)
          const onClose = onCloseRef.current
          onClose?.()
        },
        onerror: (e) => {
          setError(true)
          setReadyState(ReadyState.CLOSED)
          const onError = onErrorRef.current
          onError?.(e)
          // If an error occurs we want to throw to abort the connection
          throw e instanceof Error ? e : new Error(String(e))
        },
        onmessage: (event) => {
          if (event.id) {
            onMessageRef.current(event)
            try {
              const parsed = JSON.parse(event.data)
              const evt = parsed?.event
              // If the event is one of these, we want to close the connection
              if (evt && ['completed', 'failed', 'cancelled'].includes(evt)) {
                setReadyState(ReadyState.CLOSED)
                abortControllerRef.current?.abort()
              }
            } catch (error) {
              console.warn('Failed to parse event data:', event.data, error)
              // Continue processing even if JSON parsing fails
            }
          }
        },
        onopen: async (response) => {
          if (abortControllerRef.current?.signal.aborted) {
            throw new AbortedError()
          }

          setReadyState(ReadyState.OPEN)
          setError(false)

          const onOpen = onOpenRef.current
          onOpen?.(response)

          await sleep(1) // Satisfy the async method
        },
      })
    } catch (error) {
      if (error instanceof AbortedError) {
        setReadyState(ReadyState.CLOSED)
      } else {
        setError(true)
      }
    }
  }

  const disconnect = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
      setReadyState(ReadyState.CLOSED)
    }
  }, [])

  useEffect(() => {
    return () => {
      disconnect()
    }
  }, [])

  return {
    connect,
    disconnect,
    error,
    readyState,
    state: readyStateString(readyState),
  }
}
