import { useCallback } from 'react'
import { useSearchParams } from 'react-router'

export const useSearchQuery = () => {
  const [searchParams, setSearchParams] = useSearchParams()
  const query = searchParams.get('search') ?? ''

  const setQuery = useCallback(
    (search: string) => {
      searchParams.delete('search')
      setSearchParams(search ? { ...searchParams, search } : searchParams)
    },
    [searchParams, setSearchParams]
  )

  return [query, setQuery] as const
}
