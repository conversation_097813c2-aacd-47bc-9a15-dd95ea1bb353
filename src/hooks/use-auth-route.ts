import { useState, useCallback } from 'react'
import { useCustomCompareEffect } from '@react-hookz/web'
import { equals } from 'ramda'
import { useNavigate } from 'react-router'
import { useAuth } from '@/contexts/use-auth'

export const useAuthRoute = () => {
  const navigate = useNavigate()
  const { user, verify, isInvited } = useAuth()
  const [verified, setVerified] = useState<boolean>(false)

  const verifyAuthorization = useCallback(async () => {
    try {
      // Verify token is still valid
      const verified = await verify()
      if (!verified) {
        navigate('/signin', {
          viewTransition: true,
        })
        return false
      }

      // Check if user needs to complete invite flow
      if (user?.access_token) {
        const invited = await isInvited(user.access_token)
        if (invited) {
          navigate('/invite/welcome', {
            viewTransition: true,
            replace: true,
          })
        }
      }

      return true
    } catch {
      return false
    }
  }, [user?.access_token, verify, isInvited, navigate])

  useCustomCompareEffect(
    () => {
      verifyAuthorization().then((ok) => {
        setVerified(ok)
      })
    },
    [user?.access_token],
    (a, b) => equals(a, b)
  )

  return {
    verified,
  }
}
