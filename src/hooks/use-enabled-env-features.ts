import { useQuery } from '@tanstack/react-query'
import type { AxiosError } from 'axios'
import type { PaginatedQueryResponse } from '@/types/api'
import { getSettings } from '@/api/settings'
import type { SettingModel } from '@/types/models/setting'
import { PREFERENCES } from '@/data/preferences'
import { useOrganization } from '@/hooks/use-organization'

// Create a map of feature names without _feature_enabled suffix
const featureMap = PREFERENCES.reduce(
  (acc, category) => {
    for (const pref of category.preferences) {
      if (pref.name) {
        const baseName = pref.name.replace('_feature_enabled', '')
        acc[baseName] = true
      }
    }
    return acc
  },
  {} as Record<string, boolean>
)

export const useEnabledEnvFeatures = () => {
  const { applicationId } = useOrganization()

  const { data } = useQuery<
    PaginatedQueryResponse<SettingModel>,
    AxiosError,
    Record<string, string | boolean>
  >({
    queryKey: ['GetSettings', applicationId],
    queryFn: ({ signal }) =>
      getSettings({
        applicationId: applicationId ?? '',
        signal,
      }),
    select: (data) => {
      return data.rows.reduce(
        (acc, row) => {
          // Skip settings containing @
          if (row.name.includes('@')) return acc

          // Check if the setting name starts with any of our base feature names
          const matchesFeature = Object.keys(featureMap).some((baseName) =>
            row.name.startsWith(baseName)
          )
          if (matchesFeature) {
            acc[row.name] = row.name.endsWith('_feature_enabled')
              ? row.value === 'true'
              : row.value
          }
          return acc
        },
        {} as Record<string, string | boolean>
      )
    },
    enabled: !!applicationId,
  })

  return data ?? {}
}
