import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import type { AxiosError } from 'axios'
import {
  createPowerBiSubscription,
  getPowerBiSubscription,
  updateSubscriptionQuantity,
  assignLicenses,
  removeLicenses,
  getSubscriptionUsers,
} from '@/api/subscriptions'
import type {
  SubscriptionModel,
  SubscriptionUserModel,
} from '@/types/models/subscription'
import { getUsers } from '@/api/users'
import { useOrganization } from '@/hooks/use-organization'

export const useUsers = () => {
  const { organizationId, applicationId } = useOrganization()
  const { data } = useQuery({
    queryKey: ['Users', organizationId, applicationId],
    queryFn: () =>
      organizationId && applicationId
        ? getUsers({ organizationId, applicationId }).then((res) => res.rows)
        : [],
    enabled: !!organizationId && !!applicationId,
  })
  return data
}

export const usePowerBiSubscription = (organizationId?: string) => {
  const { data: subscription, isPending } = useQuery<
    SubscriptionModel,
    AxiosError
  >({
    queryKey: ['Subscription', 'PowerBi'],
    queryFn: () => getPowerBiSubscription(organizationId ?? ''),
    enabled: !!organizationId,
  })

  const { data: assignedUsers } = useSubscriptionUsers(
    subscription?.id ?? '',
    organizationId ?? ''
  )

  const availableLicenses = subscription
    ? subscription.quantity - (assignedUsers?.length ?? 0)
    : 0

  return {
    data: subscription,
    availableLicenses,
    isPending,
  }
}

export const useCreatePowerBiSubscription = (organizationId?: string) => {
  const queryClient = useQueryClient()
  return useMutation<SubscriptionModel, AxiosError, number>({
    mutationFn: (quantity) =>
      createPowerBiSubscription(organizationId ?? '', quantity),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['Subscription'] })
    },
  })
}

export const useUpdateSubscriptionQuantity = (organizationId?: string) => {
  const queryClient = useQueryClient()
  return useMutation<
    { success: boolean },
    AxiosError,
    { subscriptionId: string; quantity: number }
  >({
    mutationFn: ({ subscriptionId, quantity }) =>
      updateSubscriptionQuantity(
        organizationId ?? '',
        subscriptionId,
        quantity
      ),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['Subscription'] })
    },
  })
}

export const useAssignLicenses = (organizationId?: string) => {
  const queryClient = useQueryClient()
  return useMutation<
    { success: boolean },
    AxiosError,
    { subscriptionId: string; userIds: string[] }
  >({
    mutationFn: ({ subscriptionId, userIds }) =>
      assignLicenses(organizationId ?? '', subscriptionId, userIds),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['Subscription'],
      })
    },
  })
}

export const useRemoveLicenses = (organizationId?: string) => {
  const queryClient = useQueryClient()
  return useMutation<
    { success: boolean },
    AxiosError,
    { subscriptionId: string; userIds: string[] }
  >({
    mutationFn: ({ subscriptionId, userIds }) =>
      removeLicenses(organizationId ?? '', subscriptionId, userIds),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['Subscription'],
      })
    },
  })
}

export const useSubscriptionUsers = (
  subscriptionId: string,
  organizationId?: string
) => {
  return useQuery<SubscriptionUserModel[], AxiosError>({
    queryKey: ['Subscription', 'Users', subscriptionId],
    queryFn: () => getSubscriptionUsers(organizationId ?? '', subscriptionId),
    enabled: !!organizationId && !!subscriptionId,
  })
}
