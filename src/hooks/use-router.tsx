import { useMemo } from 'react'
import { useLocation, useMatches } from 'react-router'

interface RouteLoaderData {
  title: string
  scopes: string[]
}

export const useRouter = () => {
  const location = useLocation()
  const matches = useMatches()

  const currentRouteData = useMemo<RouteLoaderData | null>(() => {
    // Get the last match (current route)
    const currentMatch = matches[matches.length - 1]
    return (currentMatch?.data as RouteLoaderData) || null
  }, [matches])

  const getAllRouteData = useMemo(() => {
    return matches
      .filter(({ data }) => data)
      .map(({ pathname, data }) => ({
        pathname,
        data: data as RouteLoaderData,
      }))
  }, [matches])

  const getRouteDataByPath = useMemo(() => {
    return (pathname: string) => {
      const match = matches.find((m) => m.pathname === pathname)
      return (match?.data as RouteLoaderData) || null
    }
  }, [matches])

  const currentPath = useMemo(() => {
    return location.pathname
  }, [location.pathname])

  return {
    title: currentRouteData?.title ?? '',
    currentPath,
    currentRouteData,
    getAllRouteData,
    getRouteDataByPath,
  }
}
