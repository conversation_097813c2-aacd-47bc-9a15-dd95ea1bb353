import { tinaClient } from '@/api/clients/tina-client'
import type {
  SubscriptionModel,
  SubscriptionUserModel,
  ConsoleTriggersSubscriptionStatus,
} from '@/types/models/subscription'

export const getPowerBiSubscription = async (
  organizationId: string
): Promise<SubscriptionModel> => {
  const { data } = await tinaClient.get<SubscriptionModel>(
    `/v1.1/organizations/${organizationId}/subscriptions/powerbi`
  )
  return data
}

export const createPowerBiSubscription = async (
  organizationId: string,
  quantity: number
): Promise<SubscriptionModel> => {
  const { data } = await tinaClient.post<SubscriptionModel>(
    `/v1.1/organizations/${organizationId}/subscriptions/powerbi`,
    {
      quantity,
    }
  )
  return data
}

export const createTriggersSubscription =
  async (): Promise<SubscriptionModel> => {
    const { data } = await tinaClient.post<SubscriptionModel>(
      '/v1.2/subscriptions/triggers'
    )
    return data
  }

export const updateSubscriptionQuantity = async (
  organizationId: string,
  subscriptionId: string,
  quantity: number
): Promise<{ success: boolean }> => {
  const { data } = await tinaClient.put<{ success: boolean }>(
    `/v1.1/organizations/${organizationId}/subscriptions/powerbi/${subscriptionId}`,
    { quantity }
  )
  return data
}

export const assignLicenses = async (
  organizationId: string,
  subscriptionId: string,
  userIds: string[]
): Promise<{ success: boolean }> => {
  const { data } = await tinaClient.put<{ success: boolean }>(
    `/v1.1/organizations/${organizationId}/subscriptions/licenses`,
    {
      subscription_id: subscriptionId,
      user_ids: userIds,
    }
  )
  return data
}

export const removeLicenses = async (
  organizationId: string,
  subscriptionId: string,
  userIds: string[]
): Promise<{ success: boolean }> => {
  const { data } = await tinaClient.delete<{ success: boolean }>(
    `/v1.1/organizations/${organizationId}/subscriptions/licenses`,
    {
      data: {
        subscription_id: subscriptionId,
        user_ids: userIds,
      },
    }
  )
  return data
}

export const getSubscriptionUsers = async (
  organizationId: string,
  subscriptionId: string
): Promise<SubscriptionUserModel[]> => {
  const { data } = await tinaClient.get<SubscriptionUserModel[]>(
    `/v1.1/organizations/${organizationId}/subscriptions/${subscriptionId}/users`
  )
  return data
}

export const getConsoleTriggersSubscriptionStatus =
  async (): Promise<ConsoleTriggersSubscriptionStatus> => {
    const { data } = await tinaClient.get<ConsoleTriggersSubscriptionStatus>(
      '/v1.2/subscriptions/console/triggers/status'
    )
    return data
  }

export const getConsoleTriggersSubscription = async (
  organizationId: string
): Promise<SubscriptionModel> => {
  const { data } = await tinaClient.get<SubscriptionModel>(
    `/v1.1/organizations/${organizationId}/subscriptions/triggers`
  )
  return data
}

export const createConsoleTriggersSubscription = async (
  organizationId: string
): Promise<SubscriptionModel> => {
  const { data } = await tinaClient.post<SubscriptionModel>(
    `/v1.1/organizations/${organizationId}/subscriptions/triggers`
  )
  return data
}
