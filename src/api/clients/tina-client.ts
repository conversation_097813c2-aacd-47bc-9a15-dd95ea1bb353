import axios, {
  type AxiosError,
  type InternalAxiosRequestConfig,
  type AxiosResponse,
} from 'axios'
import { baseStore } from '@/utils/stores/store'
import {
  realmAtom,
  accessTokenAtom,
  refreshTokenAtom,
} from '@/utils/stores/auth'
import { idpRefresh } from '@/api/keycloak'

// Token refresh mutex to prevent concurrent refresh attempts
let isRefreshing = false
let refreshPromise: Promise<{
  access_token: string
  refresh_token: string
}> | null = null

const instance = axios.create({
  baseURL: import.meta.env.VITE_TINA_URL,
})

instance.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const token = baseStore.get(accessTokenAtom)

    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    return config
  },
  async (error: AxiosError) => Promise.reject(error)
)

instance.interceptors.response.use(
  async (response: AxiosResponse) => response,
  async (error) => {
    const originalRequest = error?.config

    if (
      [401, 403].includes(error?.response?.status ?? '') &&
      !originalRequest?._retry
    ) {
      if (originalRequest) {
        originalRequest._retry = true
      }

      const realm = baseStore.get(realmAtom)
      const refreshToken = baseStore.get(refreshTokenAtom)
      const isInternal = realm === 'master'

      if (!(realm && refreshToken)) {
        return
      }

      try {
        // Use mutex to prevent concurrent refresh attempts
        if (isRefreshing && refreshPromise) {
          const tokens = await refreshPromise
          if (originalRequest) {
            originalRequest.headers.Authorization = `Bearer ${tokens.access_token}`
            return instance(originalRequest)
          }
        } else if (!isRefreshing) {
          isRefreshing = true
          refreshPromise = idpRefresh({
            realm,
            refreshToken,
            isInternal,
          })

          const { access_token, refresh_token } = await refreshPromise

          // This is not updating atoms, just the cookies.
          baseStore.set(accessTokenAtom, access_token)
          baseStore.set(refreshTokenAtom, refresh_token)

          axios.defaults.headers.common.Authorization = `Bearer ${access_token}`

          isRefreshing = false
          refreshPromise = null

          if (originalRequest) {
            originalRequest.headers.Authorization = `Bearer ${access_token}`
            return instance(originalRequest)
          }
        }
      } catch (error) {
        isRefreshing = false
        refreshPromise = null
        console.error(error)
      }
    }

    return Promise.reject(error)
  }
)

export { instance as tinaClient }
