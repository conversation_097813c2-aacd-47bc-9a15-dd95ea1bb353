import { tinaClient } from 'src/api/clients/tina-client'
import type {
  BillingStatus,
  SubscriptionUserListResponse,
  StripeListResponse,
  SubscriptionWithProduct,
  SubscriptionUser,
  LocationSubscription,
  SubscriptionLicensePayload,
  BillingCard,
  Invoice,
  BillingCustomer,
  DeletedBillingCustomer,
  CustomerSession,
  PortalSession,
  CheckoutSession,
  Subscription,
} from '@/types/models/billing'

// export const getBillingStatus = async ({
//   signal,
// }: {
//   signal?: AbortSignal
// }) => {
//   const { data } = await tinaClient.get<BillingStatus>('/v1.2/billing/status', {
//     signal,
//   })

//   return data
// }

// export const getBillingCards = async ({ signal }: { signal?: AbortSignal }) => {
//   const { data } = await tinaClient.get<StripeListResponse>(
//     '/v1.2/billing/cards',
//     {
//       signal,
//     }
//   )

//   return data
// }

// export const getBillingCard = async ({
//   id,
//   signal,
// }: {
//   id?: string
//   signal?: AbortSignal
// }) => {
//   if (!id) return null
//   const { data } = await tinaClient.get<any>(`/v1.2/billing/cards/${id}`, {
//     signal,
//   })

//   return data
// }

// export const getBillingInvoices = async ({
//   signal,
// }: {
//   signal?: AbortSignal
// }) => {
//   const { data } = await tinaClient.get<StripeListResponse>(
//     '/v1.2/billing/invoices',
//     {
//       signal,
//     }
//   )

//   return data
// }

// export const getBillingCustomer = async ({
//   signal,
// }: {
//   signal?: AbortSignal
// }) => {
//   const { data } = await tinaClient.get<any>('/v1.2/billing/customer', {
//     signal,
//   })

//   return data
// }

// new api

export async function getSubscriptions(signal?: AbortSignal) {
  const { data } = await tinaClient.get<SubscriptionWithProduct[]>(
    '/v1.2/subscriptions',
    { signal }
  )
  return data
}

export async function getSubscriptionUsers(signal?: AbortSignal) {
  const { data } = await tinaClient.get<SubscriptionUserListResponse[]>(
    '/v1.2/subscriptions/users',
    { signal }
  )
  return data
}

export async function getSubscriptionUsersBySubscriptionId(
  subscriptionId: string,
  signal?: AbortSignal
) {
  const { data } = await tinaClient.get<SubscriptionUser[]>(
    `/v1.2/subscriptions/${subscriptionId}/users`,
    { signal }
  )
  return data
}

export async function getLocationSubscriptions(signal?: AbortSignal) {
  const { data } = await tinaClient.get<LocationSubscription[]>(
    '/v1.2/subscriptions/locations',
    { signal }
  )
  return data
}

export async function getLocationTriggersSubscriptions(signal?: AbortSignal) {
  const { data } = await tinaClient.get<LocationSubscription[]>(
    '/v1.2/subscriptions/locations/triggers',
    { signal }
  )
  return data
}

export async function createSubscription() {
  const { data } = await tinaClient.post<Subscription>('/v1.2/subscriptions')
  return data
}

export async function createPowerBiSubscription(payload: { quantity: number }) {
  const { data } = await tinaClient.post<Subscription>(
    '/v1.2/subscriptions/powerbi',
    payload
  )
  return data
}
export async function createTriggersSubscription() {
  const { data } = await tinaClient.post<Subscription>(
    '/v1.2/subscriptions/triggers'
  )
  return data
}

export async function updatePowerBiSubscription(
  subscriptionId: string,
  payload: { quantity: number }
) {
  const { data } = await tinaClient.put<{ success: boolean }>(
    `/v1.2/subscriptions/powerbi/${subscriptionId}`,
    payload
  )
  return data
}

export async function hasPowerBiSubscription(signal?: AbortSignal) {
  const { data } = await tinaClient.get<boolean>(
    '/v1.2/subscriptions/powerbi/haslicense',
    { signal }
  )
  return data
}

export async function updateSubscriptionLicenses(
  payload: SubscriptionLicensePayload
) {
  const { data } = await tinaClient.put('/v1.2/subscriptions/licenses', payload)
  return data
}

export async function deleteSubscriptionLicenses(
  payload: SubscriptionLicensePayload
) {
  const { data } = await tinaClient.delete<{ success: boolean }>(
    '/v1.2/subscriptions/licenses',
    { data: payload }
  )
  return data
}

export async function getBillingStatus(
  subscriptionId: string,
  signal?: AbortSignal
) {
  const { data } = await tinaClient.get<BillingStatus>(
    `/v1.2/billing/status/${subscriptionId}`,
    { signal }
  )
  return data
}

export async function getBillingCards(
  subscriptionId: string,
  signal?: AbortSignal
) {
  const { data } = await tinaClient.get<StripeListResponse<BillingCard>>(
    `/v1.2/billing/cards/${subscriptionId}`,
    { signal }
  )
  return data
}

export async function getBillingCard(
  subscriptionId: string,
  id?: string,
  signal?: AbortSignal
) {
  if (!id) return null
  const { data } = await tinaClient.get<BillingCard>(
    `/v1.2/billing/${subscriptionId}/cards/${id}`,
    { signal }
  )
  return data
}

export async function getBillingInvoices(
  subscriptionId: string,
  signal?: AbortSignal
) {
  const { data } = await tinaClient.get<StripeListResponse<Invoice>>(
    `/v1.2/billing/invoices/${subscriptionId}`,
    { signal }
  )
  return data
}

export async function getBillingCustomer(
  subscriptionId: string,
  signal?: AbortSignal
) {
  const { data } = await tinaClient.get<
    BillingCustomer | DeletedBillingCustomer
  >(`/v1.2/billing/customer/${subscriptionId}`, { signal })
  if (data && 'deleted' in data) {
    throw new Error('Customer has been deleted')
  }
  return data
}

export async function getCustomerSession(
  subscriptionId: string,
  signal?: AbortSignal
) {
  const { data } = await tinaClient.get<CustomerSession>(
    `/v1.2/billing/customersession/${subscriptionId}`,
    { signal }
  )
  return data
}

export async function getPortalSession(
  subscriptionId: string,
  params: { return_url: string },
  signal?: AbortSignal
) {
  const { data } = await tinaClient.get<PortalSession>(
    `/v1.2/billing/portalsession/${subscriptionId}`,
    { signal, params }
  )
  return data
}

export async function getCheckoutSession(
  subscriptionId: string,
  params: { cancel_url: string; success_url: string },
  signal?: AbortSignal
) {
  const { data } = await tinaClient.get<CheckoutSession>(
    `/v1.2/billing/checkoutsession/${subscriptionId}`,
    { signal, params }
  )
  return data
}
