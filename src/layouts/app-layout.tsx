import { memo } from 'react'
import { Navigate, NavLink as RouterLink, Outlet } from 'react-router'
import { useAtomValue } from 'jotai'
import { Box, Flex, Grid } from '@chakra-ui/react'
import { Toaster } from 'sonner'
import { Header } from '@/components/header/header'
import { UrlLogoDashboard } from '@/components/url-logo-dashboard'
import { UrlIcon } from '@/components/url-icon'
import { ModalContainer } from '@/components/modal-container'
import { PageSpinner } from '@/components/page-spinner'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { useAuthRoute } from '@/hooks/use-auth-route'
import { navAtom } from '@/utils/stores/nav'
import { useDarkerBg } from '@/hooks/use-darker-bg'
import { useAuth } from '@/contexts/use-auth'

const AppLayout = memo(() => {
  const { user } = useAuth()
  const { verified } = useAuthRoute()
  const { open, width } = useAtomValue(navAtom)
  const darkerBg = useDarkerBg()

  if (!user?.access_token) return <Navigate to="/signin" />
  if (!verified) return <PageSpinner />

  return (
    <>
      <Grid
        sx={{
          gridTemplateColumns: `${width} 1fr`,
          gridTemplateRows: '65px 1fr',
          gridTemplateAreas: "'nav header' 'main main'",
          gap: 0,
          h: '100vh',
        }}
      >
        <Header />
        <Flex
          gridArea="nav"
          as={RouterLink}
          to="/"
          viewTransition
          sx={{
            pos: 'relative',
            flexDir: 'column',
            maxW: '250px',
            h: '65px',
            alignItems: 'center',
            justifyContent: 'center',
            bg: 'white',
            _dark: {
              bg: darkerBg,
            },
          }}
        >
          {open ? <UrlLogoDashboard /> : <UrlIcon boxSize="40px" />}
        </Flex>
        <Flex
          as="main"
          gridArea="main"
          sx={{
            flex: 1,
            alignItems: 'start',
            overflow: 'auto',
            bg: 'gray.50',
            _dark: {
              bg: 'gray.900',
            },
          }}
        >
          <Box w="100%" pos="relative">
            <Outlet />
          </Box>
        </Flex>
      </Grid>
      <ModalContainer />
      <Toaster />
    </>
  )
})

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <AppLayout />
}
