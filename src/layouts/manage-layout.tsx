import { Suspense, memo } from 'react'
import { Outlet, Navigate } from 'react-router'
import { useAtomValue } from 'jotai'
import { Box, Flex, Grid } from '@chakra-ui/react'
import { Toaster } from 'sonner'
import { Header } from '@/components/header/header'
import { Navigation } from '@/components/navigation/navigation'
import { ModalContainer } from '@/components/modal-container'
import { PageSpinner } from '@/components/page-spinner'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { useAuthRoute } from '@/hooks/use-auth-route'
import { navAtom } from '@/utils/stores/nav'
import { useAuth } from '@/contexts/use-auth'

const ManageLayout = memo(() => {
  const { user } = useAuth()
  const { verified } = useAuthRoute()
  const { width } = useAtomValue(navAtom)

  if (!user?.access_token) return <Navigate to="/signin" />
  if (!verified) return <PageSpinner />

  return (
    <>
      <Suspense fallback={null}>
        <Grid
          sx={{
            gridTemplateColumns: `${width} 1fr`,
            gridTemplateRows: '65px 1fr',
            gridTemplateAreas: "'nav header' 'nav main'",
            gap: 0,
            h: '100vh',
          }}
        >
          <Header />
          <Navigation />
          <Flex
            as="main"
            gridArea="main"
            sx={{
              flex: 1,
              overflow: 'auto',
              bg: 'gray.50',
              _dark: {
                bg: 'gray.900',
              },
            }}
          >
            <Box w="100%" pos="relative">
              <Outlet />
            </Box>
          </Flex>
        </Grid>
      </Suspense>
      <ModalContainer />
      <Toaster />
    </>
  )
})

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <ManageLayout />
}
