import { memo } from 'react'
import { Outlet, Navigate } from 'react-router'
import { Toaster } from 'sonner'
import { Grid } from '@chakra-ui/react'
import { ModalContainer } from '@/components/modal-container'
import { PageSpinner } from '@/components/page-spinner'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { useAuthRoute } from '@/hooks/use-auth-route'
import { useAuth } from '@/contexts/use-auth'

const EditorLayout = memo(() => {
  const { user } = useAuth()
  const { verified } = useAuthRoute()

  if (!user?.access_token) return <Navigate to="/signin" />
  if (!verified) return <PageSpinner />

  return (
    <>
      <Grid
        sx={{
          h: 'auto',
          minH: '100vh',
          gridTemplateColumns: '1fr',
          bg: 'gray.50',
          _dark: {
            bg: 'gray.900',
          },
        }}
      >
        <Outlet />
      </Grid>
      <ModalContainer />
      <Toaster />
    </>
  )
})

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <EditorLayout />
}
