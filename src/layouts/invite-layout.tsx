import { memo } from 'react'
import { Outlet, Navigate, Link as RouterLink } from 'react-router'
import { Toaster } from 'sonner'
import { Flex, Grid } from '@chakra-ui/react'
import { UrlLogoDashboard } from '@/components/url-logo-dashboard'
import { ModalContainer } from '@/components/modal-container'
import { PageSpinner } from '@/components/page-spinner'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { useAuthRoute } from '@/hooks/use-auth-route'
import { useDarkerBg } from '@/hooks/use-darker-bg'
import { useAuth } from '@/contexts/use-auth'

const InviteLayout = memo(() => {
  const { user } = useAuth()
  const { verified } = useAuthRoute()
  const darkerBg = useDarkerBg()

  if (!user?.access_token) return <Navigate to="/signin" />
  if (!verified) return <PageSpinner />

  return (
    <>
      <Grid
        sx={{
          gridTemplateColumns: '1fr',
          gridTemplateRows: '65px 1fr',
          gap: 0,
          h: '100vh',
        }}
      >
        <Flex
          as="header"
          sx={{
            h: '100%',
            alignItems: 'center',
            zIndex: 2,
            bg: 'white',
            _dark: {
              bg: darkerBg,
            },
          }}
        >
          <Flex
            as={RouterLink}
            viewTransition
            to="/"
            sx={{
              w: '250px',
              h: '65px',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <UrlLogoDashboard />
          </Flex>
        </Flex>
        <Flex
          as="main"
          sx={{
            flex: 1,
            alignItems: 'start',
            overflow: 'auto',
            bg: 'gray.50',
            _dark: {
              bg: 'gray.900',
            },
          }}
        >
          <Outlet />
        </Flex>
      </Grid>
      <ModalContainer />
      <Toaster />
    </>
  )
})

export function ErrorBoundary() {
  return <RouteErrorBoundary />
}

export function Component() {
  return <InviteLayout />
}
