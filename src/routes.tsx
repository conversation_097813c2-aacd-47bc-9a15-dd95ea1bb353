import { redirect } from 'react-router'
import {
  CpuIcon,
  UsersIcon,
  RouterIcon,
  WebhookIcon,
  QrCodeIcon,
  FileTextIcon,
  FileChartPieIcon,
  TerminalIcon,
  Layers2Icon,
  Grid2X2PlusIcon,
  SlidersHorizontalIcon,
} from 'lucide-react'
import { PageSpinner } from '@/components/page-spinner'
import type { RouteObjectWithHandle } from '@/types/router'

export const routes = [
  {
    path: '/',
    lazy: () => import('src/root'),
    hydrateFallbackElement: <PageSpinner />,
    children: [
      {
        index: true,
        lazy: () => import('src/pages/signin/index'),
      },
      {
        path: 'signin',
        lazy: () => import('src/pages/signin/index'),
        handle: {
          title: 'Signin',
          group: ['public'],
          scopes: [],
        },
      },
      {
        path: '/internal',
        lazy: () => import('src/pages/internal/index'),
        handle: {
          title: 'Internal Signin',
          group: ['public'],
          scopes: [],
        },
      },
      {
        path: '/invite',
        lazy: () => import('src/layouts/invite-layout'),
        children: [
          {
            path: 'welcome',
            lazy: () => import('src/pages/welcome/index'),
            handle: {
              title: 'Welcome',
              group: ['public', 'invite'],
              scopes: [],
            },
          },
          {
            path: 'permissions',
            lazy: () => import('src/pages/permissions/index'),
            handle: {
              title: 'Permissions',
              group: ['public', 'invite'],
              scopes: [],
            },
          },
        ],
      },
      {
        lazy: () => import('src/layouts/app-layout'),
        children: [
          {
            path: '/applications',
            lazy: () => import('src/pages/applications/index'),
            handle: {
              title: 'Applications',
              group: ['app', 'header'],
              scopes: ['manage:all'],
              order: 2,
            },
          },
          {
            path: '/users',
            lazy: () => import('src/pages/users/index'),
            handle: {
              title: 'Users',
              group: ['app', 'header'],
              scopes: ['view:applications'],
              order: 3,
            },
          },
          {
            path: '/account',
            loader: async () => redirect('/account/profile'),
            handle: {
              title: 'Account',
              group: ['app', 'header'],
              scopes: [],
              order: 6,
            },
          },
          {
            path: '/account/profile',
            lazy: () => import('src/pages/account/profile'),
            handle: {
              title: 'Profile',
              group: ['app', 'account-tabs'],
              scopes: [],
            },
          },
          {
            path: '/account/permissions',
            lazy: () => import('src/pages/account/permissions'),
            handle: {
              title: 'Permissions',
              group: ['app', 'account-tabs'],
              scopes: [],
            },
          },
          {
            path: '/account/organization',
            lazy: () => import('src/pages/account/organization'),
            handle: {
              title: 'Organization',
              group: ['app', 'account-tabs'],
              scopes: [],
            },
          },
          {
            path: '/create-organization',
            lazy: () => import('@/pages/create-organization/index'),
            handle: {
              title: 'Create Organization',
              group: ['app'],
              scopes: ['manage:all'],
            },
          },
        ],
      },
      {
        lazy: () => import('src/layouts/public-layout'),
        children: [
          {
            path: '/change-password',
            lazy: () => import('src/pages/change-password/index'),
            handle: {
              title: 'Change Password',
              group: ['app'],
              scopes: [],
            },
          },
        ],
      },
      {
        lazy: () => import('src/layouts/manage-layout'),
        children: [
          {
            path: '/manage',
            loader: async () => redirect('/manage/customers'),
            handle: {
              title: 'Manage',
              group: ['manage', 'header'],
              scopes: ['view:reports'],
              order: 1,
            },
          },
          {
            path: '/manage/customers',
            lazy: () => import('src/pages/customers/index'),
            handle: {
              title: 'Customers',
              icon: UsersIcon,
              group: ['manage', 'nav'],
              scopes: ['view:applications'],
              order: 1,
            },
          },
          {
            path: '/manage/device-registry',
            lazy: () => import('src/pages/device-registry/index'),
            handle: {
              title: 'Device Registry',
              icon: Grid2X2PlusIcon,
              group: ['manage', 'nav'],
              scopes: ['view:applications'],
              order: 2,
            },
          },
          {
            path: '/manage/device-registry/upload-devices',
            lazy: () => import('src/pages/device-registry/upload-devices'),
            handle: {
              title: 'Upload Devices',
              scopes: ['view:applications'],
            },
          },
          {
            path: '/manage/gateways',
            lazy: () => import('src/pages/gateways/index'),
            handle: {
              title: 'Gateways',
              icon: RouterIcon,
              group: ['manage', 'nav'],
              scopes: ['edit:applications'],
              order: 2,
            },
          },
          {
            path: '/manage/gateways/:id',
            loader: () => import('src/pages/gateways/details'),
            handle: {
              title: 'Details',
              group: ['manage'],
              scopes: ['edit:applications'],
            },
          },
          {
            path: '/manage/gateways/:id/details',
            lazy: () => import('src/pages/gateways/details'),
            handle: {
              title: 'Details',
              group: ['manage', 'metrics-tabs'],
              scopes: ['edit:applications'],
            },
            children: [
              {
                path: 'remote-shell',
                lazy: () => import('src/pages/gateways/remote-shell'),
                handle: {
                  title: 'Remote Shell',
                  group: [],
                  scopes: ['edit:applications'],
                },
              },
            ],
          },
          {
            path: '/manage/reports',
            lazy: () => import('src/pages/reports/index'),
            handle: {
              title: 'Reports',
              icon: FileChartPieIcon,
              group: ['manage', 'nav'],
              scopes: ['view:reports'],
              order: 3,
            },
          },
          {
            path: '/manage/report-builder',
            lazy: () => import('src/pages/reports/report-builder'),
            handle: {
              title: 'App Report Builder',
              icon: FileChartPieIcon,
              group: ['manage', 'nav'],
              scopes: ['manage:all'],
              order: 4,
            },
          },
          {
            path: '/manage/reports/add',
            lazy: () => import('src/pages/reports/add'),
            handle: {
              title: 'Add Report',
              group: ['manage'],
              scopes: ['manage:all'],
            },
          },
          {
            path: '/manage/reports/:id/edit',
            lazy: () => import('src/pages/reports/edit'),
            handle: {
              title: 'Edit Report',
              group: ['manage'],
              scopes: ['manage:all'],
            },
          },
          {
            path: '/manage/reports/:id/view',
            lazy: () => import('src/pages/reports/view'),
            handle: {
              title: 'View Report',
              group: ['manage'],
              scopes: ['manage:all'],
            },
          },
          {
            path: '/manage/settings',
            loader: async () => redirect('/manage/settings/product'),
            handle: {
              title: 'Settings',
              icon: SlidersHorizontalIcon,
              group: ['manage', 'nav'],
              scopes: ['view:applications'],
              order: 6,
            },
          },
          {
            path: '/manage/settings/product',
            lazy: () => import('src/pages/settings/product'),
            handle: {
              title: 'Product',
              group: ['manage', 'settings-tabs'],
              scopes: ['view:applications'],
            },
          },
          {
            path: '/manage/settings/customize',
            lazy: () => import('src/pages/settings/customize-legacy'),
            handle: {
              title: 'Customize',
              group: ['manage', 'settings-tabs'],
              scopes: ['view:applications'],
            },
          },
          {
            path: '/manage/settings/customize-internal',
            lazy: () => import('src/pages/settings/customize'),
            handle: {
              title: 'Customize (Internal)',
              group: ['manage', 'settings-tabs'],
              scopes: ['manage:all'],
            },
          },
          {
            path: '/manage/settings/email',
            lazy: () => import('src/pages/settings/email'),
            handle: {
              title: 'Email',
              group: ['manage', 'settings-tabs'],
              scopes: ['view:applications'],
            },
          },
          {
            path: '/manage/settings/preferences',
            lazy: () => import('src/pages/settings/preferences'),
            handle: {
              title: 'Show / Hide UI',
              group: ['manage', 'settings-tabs'],
              scopes: ['view:applications'],
            },
          },
          {
            path: '/manage/settings/admin-api',
            lazy: () => import('src/pages/settings/admin-api'),
            handle: {
              title: 'Admin API',
              group: ['manage', 'settings-tabs'],
              scopes: ['view:applications'],
            },
          },
          {
            path: '/manage/device-templates',
            lazy: () => import('src/pages/device-templates/index'),
            handle: {
              title: 'Device Templates',
              icon: Layers2Icon,
              group: ['manage', 'nav'],
              scopes: ['edit:applications'],
              order: 4,
            },
          },

          {
            path: '/manage/device-templates/add',
            lazy: () => import('src/pages/device-templates/general'),
            handle: {
              title: 'Add Device Template',
              group: ['manage', 'template-add'],
              scopes: ['edit:applications'],
            },
          },
          {
            path: '/manage/device-templates/:id',
            lazy: () => import('src/pages/device-templates/general'),
            handle: {
              title: 'Edit Device Template',
              group: ['manage'],
              scopes: ['edit:applications'],
            },
          },
          {
            path: '/manage/device-templates/:id/general',
            lazy: () => import('src/pages/device-templates/general'),
            handle: {
              title: 'General',
              group: ['manage', 'template-tabs'],
              scopes: ['edit:applications'],
            },
          },
          {
            path: '/manage/device-templates/:id/capabilities',
            lazy: () => import('src/pages/device-templates/capabilities/index'),
            handle: {
              title: 'Capabilities',
              group: ['manage', 'template-tabs'],
              scopes: ['edit:applications'],
            },
          },
          {
            path: '/manage/device-templates/:typeId/capabilities/:id',
            lazy: () => import('src/pages/device-templates/capabilities/edit'),
            handle: {
              title: 'Capability',
              group: ['manage'],
              scopes: ['edit:applications'],
            },
          },
          {
            path: '/manage/device-templates/:typeId/data-type',
            lazy: () => import('src/pages/device-templates/data-type/index'),
            handle: {
              title: 'Add Data Type',
              group: ['manage'],
              scopes: ['edit:applications'],
            },
          },
          {
            path: '/manage/device-templates/:typeId/data-type/:id',
            lazy: () => import('src/pages/device-templates/data-type/index'),
            handle: {
              title: 'Edit Data Type',
              group: ['manage'],
              scopes: ['edit:applications'],
            },
          },
          {
            path: '/manage/device-templates/:id/attributes',
            lazy: () => import('src/pages/device-templates/attributes/index'),
            handle: {
              title: 'Attributes',
              group: ['manage', 'template-tabs'],
              scopes: ['edit:applications'],
            },
          },
          {
            path: '/manage/device-templates/:id/alert-types',
            lazy: () => import('src/pages/device-templates/alert-types/index'),
            handle: {
              title: 'Alert Types',
              group: ['manage', 'template-tabs'],
              scopes: ['edit:applications'],
            },
          },
          {
            path: '/manage/device-templates/:id/device-uses',
            lazy: () => import('src/pages/device-templates/device-uses/index'),
            handle: {
              title: 'Device Uses',
              group: ['manage', 'template-tabs'],
              scopes: ['edit:applications'],
            },
          },
          {
            path: '/manage/device-templates/:id/store-resources',
            lazy: () => import('src/pages/device-templates/store-resources'),
            handle: {
              title: 'Store Resources',
              group: ['manage', 'template-tabs'],
              scopes: ['edit:applications'],
            },
          },
          {
            path: '/manage/devices',
            lazy: () => import('src/pages/devices/index'),
            handle: {
              title: 'Devices',
              icon: CpuIcon,
              group: ['manage', 'nav'],
              scopes: ['manage:all'],
              order: 2,
            },
          },
          {
            path: '/manage/qr',
            lazy: () => import('src/pages/qr-reader/index'),
            handle: {
              title: 'QR Reader',
              icon: QrCodeIcon,
              group: ['manage', 'nav'],
              scopes: ['manage:all'],
              order: 12,
            },
          },
          {
            path: '/manage/integration-templates',
            lazy: () => import('src/pages/integration-templates/index'),
            handle: {
              title: 'Integration Templates',
              icon: Layers2Icon,
              group: ['manage', 'nav'],
              scopes: ['manage:all'],
              order: 9,
            },
          },
          {
            path: '/manage/integration-templates/:id',
            lazy: () => import('src/pages/integration-templates/general'),
            handle: {
              title: 'Edit Integration',
              group: ['manage'],
              scopes: ['manage:all'],
            },
          },
          {
            path: '/manage/integration-templates/:id/general',
            lazy: () => import('src/pages/integration-templates/general'),
            handle: {
              title: 'General',
              group: ['manage', 'integration-template-tabs'],
              scopes: ['manage:all'],
            },
          },
          {
            path: '/manage/integration-templates/:id/settings',
            lazy: () =>
              import('src/pages/integration-templates/settings/index'),
            handle: {
              title: 'Settings',
              group: ['manage', 'integration-template-tabs'],
              scopes: ['manage:all'],
            },
          },
          {
            path: '/manage/setup-integration',
            lazy: () => import('src/pages/integration-setup/index'),
            handle: {
              title: 'Setup Integration',
              icon: WebhookIcon,
              group: ['manage'],
              scopes: ['manage:all'],
            },
          },
          {
            path: '/manage/setup-integration/:integrationId/add',
            lazy: () => import('src/pages/integration-setup/add'),
            handle: {
              title: 'Setup Integration',
              group: ['manage'],
              scopes: ['manage:all'],
            },
          },
          {
            path: '/manage/active-integrations',
            lazy: () => import('src/pages/integrations-active/index'),
            handle: {
              title: 'Active Integrations',
              icon: WebhookIcon,
              group: ['manage', 'nav'],
              scopes: ['manage:all'],
              order: 10,
            },
          },
          {
            path: '/manage/integrations',
            loader: async () => redirect('/manage/integrations/sources'),
            handle: {
              title: 'Integrations',
              icon: WebhookIcon,
              group: ['manage', 'nav'],
              scopes: ['view:applications'],
              order: 11,
            },
          },
          {
            path: '/manage/integrations/sources',
            lazy: () => import('src/pages/integrations/sources'),
            handle: {
              title: 'Sources',
              group: ['manage', 'integration-tabs'],
              scopes: ['view:applications'],
            },
          },
          {
            path: '/manage/integrations/connect/:integrationId',
            lazy: () => import('src/pages/integrations/connect'),
            handle: {
              title: 'Connect',
              group: ['manage'],
              scopes: ['edit:applications'],
            },
          },
          {
            path: '/manage/integrations/connect/:integrationId/edit/:sourceId',
            lazy: () => import('src/pages/integrations/connect'),
            handle: {
              title: 'Connect',
              group: ['manage'],
              scopes: ['edit:applications'],
            },
          },
          {
            path: '/manage/integrations/my-integrations',
            lazy: () => import('src/pages/integrations/my-integrations'),
            handle: {
              title: 'My Integrations',
              group: ['manage', 'integration-tabs'],
              scopes: ['view:applications'],
            },
          },
          {
            path: '/manage/help',
            lazy: () => import('src/pages/help/index'),
            handle: {
              title: 'Help',
              icon: FileTextIcon,
              group: ['manage', 'nav'],
              scopes: [],
              order: 7,
            },
          },
        ],
      },
      {
        lazy: () => import('src/layouts/editor-layout'),
        children: [
          {
            path: '/codec-editor',
            lazy: () => import('src/pages/codec-editor/index'),
            handle: {
              title: 'Codec Editor',
              icon: TerminalIcon,
              group: ['manage', 'nav'],
              scopes: ['view:applications'],
              order: 5,
            },
          },
          // {
          //   path: '/builder',
          //   lazy: () => import('src/pages/builder/index'),
          //   handle: {
          //     title: 'White Label Builder',
          //     icon: BlendIcon,
          //     group: ['manage', 'nav'],
          //     scopes: ['manage:all'],
          //     order: 12,
          //   },
          // },
        ],
      },
    ],
  },
  {
    path: '*',
    lazy: () => import('src/pages/not-found/index'),
    handle: {
      title: 'Not Found',
      group: ['public'],
      scopes: [],
    },
  },
] satisfies RouteObjectWithHandle[]
