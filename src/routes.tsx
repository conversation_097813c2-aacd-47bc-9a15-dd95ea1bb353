import { redirect, type RouteObject } from 'react-router'
import { PageSpinner } from '@/components/page-spinner'

export const routes = [
  {
    path: '/',
    lazy: () => import('@/root'),
    hydrateFallbackElement: <PageSpinner />,
    children: [
      {
        index: true,
        lazy: () => import('@/pages/signin/index'),
      },
      {
        path: 'signin',
        lazy: () => import('@/pages/signin/index'),
      },
      {
        path: '/internal',
        lazy: () => import('@/pages/internal/index'),
      },
      {
        path: '/invite',
        lazy: () => import('@/layouts/invite-layout'),
        hydrateFallbackElement: <PageSpinner />,
        children: [
          {
            path: 'welcome',
            lazy: () => import('@/pages/welcome/index'),
          },
          {
            path: 'permissions',
            lazy: () => import('@/pages/permissions/index'),
          },
        ],
      },
      {
        lazy: () => import('@/layouts/app-layout'),
        hydrateFallbackElement: <PageSpinner />,
        children: [
          {
            path: '/applications',
            lazy: () => import('@/pages/applications/index'),
          },
          {
            path: '/users',
            lazy: () => import('@/pages/users/index'),
          },
          {
            path: '/account',
            loader: async () => redirect('/account/profile'),
          },
          {
            path: '/account/profile',
            lazy: () => import('@/pages/account/profile'),
          },
          {
            path: '/account/permissions',
            lazy: () => import('@/pages/account/permissions'),
          },
          {
            path: '/account/organization',
            lazy: () => import('@/pages/account/organization'),
          },
          {
            path: '/create-organization',
            lazy: () => import('@/pages/create-organization/index'),
          },
          {
            path: '/account/subscription',
            lazy: () => import('@/pages/account/subscription'),
          },
          {
            path: '/account/subscription/create',
            lazy: () => import('@/pages/account/create-subscription'),
          },
          {
            path: '/account/subscription/:subscriptionId',
            lazy: () => import('@/pages/account/subscription-details'),
          },
          {
            path: '/account/subscription/users',
            lazy: () => import('@/pages/account/subscription-users'),
          },
        ],
      },
      {
        lazy: () => import('@/layouts/public-layout'),
        hydrateFallbackElement: <PageSpinner />,
        children: [
          {
            path: '/change-password',
            lazy: () => import('@/pages/change-password/index'),
          },
        ],
      },
      {
        lazy: () => import('@/layouts/manage-layout'),
        hydrateFallbackElement: <PageSpinner />,
        children: [
          {
            path: '/manage',
            loader: async () => redirect('/manage/customers'),
          },
          {
            path: '/manage/customers',
            lazy: () => import('@/pages/customers/index'),
          },
          {
            path: '/manage/device-registry',
            lazy: () => import('@/pages/device-registry/index'),
          },
          {
            path: '/manage/device-registry/upload-devices',
            lazy: () => import('@/pages/device-registry/upload-devices'),
          },
          {
            path: '/manage/gateways',
            lazy: () => import('@/pages/gateways/index'),
          },
          {
            path: '/manage/gateways/:id',
            loader: () => import('@/pages/gateways/details'),
          },
          {
            path: '/manage/gateways/:id/details',
            lazy: () => import('@/pages/gateways/details'),
            children: [
              {
                path: 'remote-shell',
                lazy: () => import('@/pages/gateways/remote-shell'),
              },
            ],
          },
          {
            path: '/manage/reports',
            lazy: () => import('@/pages/reports/index'),
          },
          {
            path: '/manage/report-builder',
            lazy: () => import('@/pages/reports/report-builder'),
          },
          {
            path: '/manage/reports/add',
            lazy: () => import('@/pages/reports/add'),
          },
          {
            path: '/manage/reports/:id/edit',
            lazy: () => import('@/pages/reports/edit'),
          },
          {
            path: '/manage/reports/:id/view',
            lazy: () => import('@/pages/reports/view'),
          },
          {
            path: '/manage/settings',
            loader: async () => redirect('/manage/settings/product'),
          },
          {
            path: '/manage/settings/product',
            lazy: () => import('@/pages/settings/product'),
          },
          {
            path: '/manage/settings/customize',
            lazy: () => import('@/pages/settings/customize-legacy'),
          },
          {
            path: '/manage/settings/customize-internal',
            lazy: () => import('@/pages/settings/customize'),
          },
          {
            path: '/manage/settings/email',
            lazy: () => import('@/pages/settings/email'),
          },
          {
            path: '/manage/settings/preferences',
            lazy: () => import('@/pages/settings/preferences'),
          },
          {
            path: '/manage/settings/admin-api',
            lazy: () => import('@/pages/settings/admin-api'),
          },
          {
            path: '/manage/device-templates',
            lazy: () => import('@/pages/device-templates/index'),
          },
          {
            path: '/manage/device-templates/add',
            lazy: () => import('@/pages/device-templates/general'),
          },
          {
            path: '/manage/device-templates/:id',
            lazy: () => import('@/pages/device-templates/general'),
          },
          {
            path: '/manage/device-templates/:id/general',
            lazy: () => import('@/pages/device-templates/general'),
          },
          {
            path: '/manage/device-templates/:id/capabilities',
            lazy: () => import('@/pages/device-templates/capabilities/index'),
          },
          {
            path: '/manage/device-templates/:typeId/capabilities/:id',
            lazy: () => import('@/pages/device-templates/capabilities/edit'),
          },
          {
            path: '/manage/device-templates/:typeId/data-type',
            lazy: () => import('@/pages/device-templates/data-type/index'),
          },
          {
            path: '/manage/device-templates/:typeId/data-type/:id',
            lazy: () => import('@/pages/device-templates/data-type/index'),
          },
          {
            path: '/manage/device-templates/:id/attributes',
            lazy: () => import('@/pages/device-templates/attributes/index'),
          },
          {
            path: '/manage/device-templates/:id/alert-types',
            lazy: () => import('@/pages/device-templates/alert-types/index'),
          },
          {
            path: '/manage/device-templates/:id/device-uses',
            lazy: () => import('@/pages/device-templates/device-uses/index'),
          },
          {
            path: '/manage/device-templates/:id/store-resources',
            lazy: () => import('@/pages/device-templates/store-resources'),
          },
          {
            path: '/manage/devices',
            lazy: () => import('@/pages/devices/index'),
          },
          {
            path: '/manage/qr',
            lazy: () => import('@/pages/qr-reader/index'),
          },
          {
            path: '/manage/integration-templates',
            lazy: () => import('@/pages/integration-templates/index'),
          },
          {
            path: '/manage/integration-templates/:id',
            lazy: () => import('@/pages/integration-templates/general'),
          },
          {
            path: '/manage/integration-templates/:id/general',
            lazy: () => import('@/pages/integration-templates/general'),
          },
          {
            path: '/manage/integration-templates/:id/settings',
            lazy: () => import('@/pages/integration-templates/settings/index'),
          },
          {
            path: '/manage/setup-integration',
            lazy: () => import('@/pages/integration-setup/index'),
          },
          {
            path: '/manage/setup-integration/:integrationId/add',
            lazy: () => import('@/pages/integration-setup/add'),
          },
          {
            path: '/manage/active-integrations',
            lazy: () => import('@/pages/integrations-active/index'),
          },
          {
            path: '/manage/integrations',
            loader: async () => redirect('/manage/integrations/sources'),
          },
          {
            path: '/manage/integrations/sources',
            lazy: () => import('@/pages/integrations/sources'),
            handle: {
              title: 'Sources',
              group: ['manage', 'integration-tabs'],
              scopes: ['view:applications'],
            },
          },
          {
            path: '/manage/integrations/connect/:integrationId',
            lazy: () => import('@/pages/integrations/connect'),
          },
          {
            path: '/manage/integrations/connect/:integrationId/edit/:sourceId',
            lazy: () => import('@/pages/integrations/connect'),
          },
          {
            path: '/manage/integrations/my-integrations',
            lazy: () => import('@/pages/integrations/my-integrations'),
          },
          {
            path: '/manage/help',
            lazy: () => import('@/pages/help/index'),
          },
        ],
      },
      {
        lazy: () => import('@/layouts/editor-layout'),
        hydrateFallbackElement: <PageSpinner />,
        children: [
          {
            path: '/codec-editor',
            lazy: () => import('@/pages/codec-editor/index'),
          },
          // {
          //   path: '/builder',
          //   lazy: () => import('@/pages/builder/index'),
          // },
        ],
      },
    ],
  },
  {
    path: '*',
    lazy: () => import('@/pages/not-found/index'),
  },
] satisfies RouteObject[]
