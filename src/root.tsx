import 'focus-visible/dist/focus-visible'

import { memo, useEffect, useMemo } from 'react'
import { Outlet, useLocation } from 'react-router'
import { Helmet } from 'react-helmet-async'
import { useSetAtom } from 'jotai'
import { ChakraProvider, useMediaQuery } from '@chakra-ui/react'
import { RouteErrorBoundary } from '@/components/route-error-boundary'
import { AuthProvider } from '@/contexts/auth-provider'
import { metaTitle } from '@/utils/meta-title'
import { useTheme } from '@/hooks/use-theme'
import { navAtom } from '@/utils/stores/nav'
import { lastLocationAtom } from '@/utils/stores/last-location'

const EXCLUDED_PATHS = [/\/signin/, /\/signout/, /\/internal/]

const Root = memo(() => {
  const theme = useTheme()
  /** We forced to use hook from chakra here or login will not work ... no clue why */
  const [isLessThanHd] = useMediaQuery(['only screen and (max-width: 80em)'])
  const setNav = useSetAtom(navAtom)

  const location = useLocation()
  const setLastLocation = useSetAtom(lastLocationAtom)

  useEffect(() => {
    const { pathname } = location
    if (pathname === '/') {
      return
    }

    if (EXCLUDED_PATHS.some((regex) => regex.test(pathname))) {
      return
    }

    setLastLocation(location)
  }, [location, setLastLocation])

  useEffect(() => {
    if (isLessThanHd) {
      setNav({ open: false })
    }
  }, [isLessThanHd, setNav])

  const title = useMemo(() => metaTitle([]), [])

  return (
    <>
      <Helmet>
        <title>{title}</title>
      </Helmet>
      <ChakraProvider resetCSS theme={theme}>
        <AuthProvider>
          <Outlet />
        </AuthProvider>
      </ChakraProvider>
    </>
  )
})

export const ErrorBoundary = () => {
  return <RouteErrorBoundary />
}

export const Component = () => {
  return <Root />
}
