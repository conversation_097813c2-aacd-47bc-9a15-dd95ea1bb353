import { redirect } from 'react-router'
import { createMongoAbility } from '@casl/ability'
import { baseStore } from '@/utils/stores/store'
import { realmAtom, userAtom, resetUserAtom } from '@/utils/stores/auth'
import type {
  AbilityRule,
  AbilityActionType,
  AbilitySubjectType,
} from '@/types/ability'

/**
 * Parse scope string into action and subject
 */
const parseScope = (scope: string): { action: string; subject: string } => {
  const [action, subject] = scope.split(':')

  if (!(action && subject)) {
    throw new Error(
      `Invalid scope format: ${scope}. Expected format: "action:subject"`
    )
  }

  return {
    action,
    subject,
  }
}

/**
 * Check if user has required permissions using CASL
 */
const checkPermissions = (
  userRules: AbilityRule[],
  requiredScopes: string[]
): boolean => {
  if (userRules?.length === 0) return false

  const ability = createMongoAbility(userRules)

  return requiredScopes.every((scope) => {
    const { action, subject } = parseScope(scope)
    return ability.can(
      action as AbilityActionType,
      subject as AbilitySubjectType
    )
  })
}

/**
 * Get user data from Jotai store
 */
const getUserFromStore = () => {
  if (typeof window === 'undefined') return null

  try {
    return baseStore.get(userAtom)
  } catch {
    return null
  }
}

/**
 * Check if user is internal admin
 */
const isInternalAdmin = (): boolean => {
  if (typeof window === 'undefined') return false

  try {
    const realm = baseStore.get(realmAtom)
    return realm === 'master'
  } catch {
    return false
  }
}

/**
 * Check permissions in loader function
 */
export const checkLoaderPermissions = async (
  requiredScopes: string[],
  redirectTo = '/signin'
): Promise<void> => {
  // Internal admins have access to everything
  if (isInternalAdmin()) return

  // Skip if no scopes required
  if (requiredScopes?.length === 0) return

  const user = getUserFromStore()

  // If user has a session (access_token) but no rules, logout and redirect
  // This indicates an invalid/expired session
  if (user?.access_token && user?.rules?.length === 0) {
    if (typeof window !== 'undefined') {
      baseStore.set(resetUserAtom)
    }
    throw redirect(redirectTo)
  }

  // Allow access if no user data (initial load scenario - no session initiated)
  if (user?.rules?.length === 0) return

  const hasPermission = checkPermissions(user?.rules || [], requiredScopes)

  if (!hasPermission) {
    throw redirect(redirectTo)
  }
}
