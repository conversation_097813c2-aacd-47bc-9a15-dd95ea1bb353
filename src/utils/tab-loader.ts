import { baseStore } from '@/utils/stores/store'
import { realmAtom, userAtom } from '@/utils/stores/auth'
import { createMongoAbility } from '@casl/ability'
import type {
  AbilityRule,
  AbilityActionType,
  AbilitySubjectType,
} from '@/types/ability'

interface TabDefinition {
  path: string
  title: string
  scopes: string[]
  isVisible?: (context: {
    isInternalAdmin: boolean
    params?: Record<string, string | undefined>
  }) => boolean
  isDisabled?: (context: {
    isInternalAdmin: boolean
    params?: Record<string, string | undefined>
  }) => boolean
}

interface TabItem {
  path: string
  title: string
  isDisabled?: boolean
}

const TAB_GROUPS: Record<string, TabDefinition[]> = {
  'settings-tabs': [
    {
      path: '/manage/settings/product',
      title: 'Product',
      scopes: ['view:applications'],
    },
    {
      path: '/manage/settings/customize',
      title: 'Customize',
      scopes: ['view:applications'],
      isVisible: ({ isInternalAdmin }) => !isInternalAdmin,
    },
    {
      path: '/manage/settings/customize-internal',
      title: 'Customize',
      scopes: ['manage:all'],
      isVisible: ({ isInternalAdmin }) => isInternalAdmin,
    },
    {
      path: '/manage/settings/email',
      title: 'Email',
      scopes: ['view:applications'],
    },
    {
      path: '/manage/settings/preferences',
      title: 'Show / Hide UI',
      scopes: ['view:applications'],
    },
    {
      path: '/manage/settings/admin-api',
      title: 'Admin API',
      scopes: ['view:applications'],
    },
  ],
  'account-tabs': [
    {
      path: '/account/profile',
      title: 'Profile',
      scopes: [],
    },
    {
      path: '/account/permissions',
      title: 'Permissions',
      scopes: [],
    },
    {
      path: '/account/organization',
      title: 'Organization',
      scopes: [],
    },
    {
      path: '/account/subscription',
      title: 'Subscriptions',
      scopes: ['edit:organizations'],
    },
    {
      path: '/account/subscription/:id',
      title: 'Subscription Details',
      scopes: ['edit:organizations'],
    },
    {
      path: '/account/subscription/users',
      title: 'Subscription Users',
      scopes: ['edit:organizations'],
    },
    {
      path: '/account/subscription/create',
      title: 'Create Subscription',
      scopes: ['edit:organizations'],
    },
  ],
  'integration-tabs': [
    {
      path: '/manage/integrations/sources',
      title: 'Sources',
      scopes: ['view:applications'],
    },
    {
      path: '/manage/integrations/my-integrations',
      title: 'My Integrations',
      scopes: ['view:applications'],
    },
  ],
  'integration-template-tabs': [
    {
      path: '/manage/integration-templates/:id',
      title: 'General',
      scopes: ['manage:all'],
    },
    {
      path: '/manage/integration-templates/:id/settings',
      title: 'Settings',
      scopes: ['manage:all'],
    },
  ],
  'template-tabs': [
    {
      path: '/manage/device-templates/:id',
      title: 'General',
      scopes: ['edit:applications'],
    },
    {
      path: '/manage/device-templates/:id/capabilities',
      title: 'Capabilities',
      scopes: ['edit:applications'],
    },
    {
      path: '/manage/device-templates/:id/attributes',
      title: 'Attributes',
      scopes: ['edit:applications'],
    },
    {
      path: '/manage/device-templates/:id/alert-types',
      title: 'Alert Types',
      scopes: ['edit:applications'],
    },
    {
      path: '/manage/device-templates/:id/device-uses',
      title: 'Device Uses',
      scopes: ['edit:applications'],
    },
    {
      path: '/manage/device-templates/:id/store-resources',
      title: 'Store Resources',
      scopes: ['edit:applications'],
    },
  ],
}

/**
 * Parse scope string into action and subject
 */
const parseScope = (scope: string): { action: string; subject: string } => {
  const [action, subject] = scope.split(':')
  if (!(action && subject)) {
    throw new Error(
      `Invalid scope format: ${scope}. Expected format: "action:subject"`
    )
  }
  return { action, subject }
}

/**
 * Check if user has required permissions using CASL
 */
const checkPermissions = (
  userRules: AbilityRule[],
  requiredScopes: string[]
): boolean => {
  if (userRules?.length === 0) return false

  const ability = createMongoAbility(userRules)

  return requiredScopes.every((scope) => {
    const { action, subject } = parseScope(scope)
    return ability.can(
      action as AbilityActionType,
      subject as AbilitySubjectType
    )
  })
}

/**
 * Get user data from Jotai store
 */
const getUserFromStore = () => {
  if (typeof window === 'undefined') return null

  try {
    return baseStore.get(userAtom)
  } catch {
    return null
  }
}

/**
 * Check if user is internal admin
 */
const isInternalAdmin = (): boolean => {
  if (typeof window === 'undefined') return false

  try {
    const realm = baseStore.get(realmAtom)
    return realm === 'master'
  } catch {
    return false
  }
}

/**
 * Filter tabs by permissions for use in loaders
 * Usage in loader:
 *
 * export async function loader({ params }) {
 *   const tabs = await getFilteredTabs('settings-tabs', params)
 *   return { tabs, ...otherData }
 * }
 */
export const getFilteredTabs = async (
  groupName: string,
  params?: Record<string, string | undefined>
): Promise<TabItem[]> => {
  const tabDefinitions = TAB_GROUPS[groupName] || []
  const user = getUserFromStore()
  const isAdmin = isInternalAdmin()

  const filteredTabs: TabItem[] = []

  for (const tab of tabDefinitions) {
    // Check permissions
    const scopes = tab.scopes || []
    if (scopes.length > 0 && !isAdmin) {
      if (user?.rules?.length === 0) continue

      const hasPermission = checkPermissions(user?.rules || [], scopes)
      if (!hasPermission) continue
    }

    // Check visibility condition
    if (tab.isVisible) {
      const context = { isInternalAdmin: isAdmin, params }
      const isVisible = tab.isVisible(context)
      if (!isVisible) continue
    }

    // Check disabled condition
    let isDisabled = false
    if (tab.isDisabled) {
      const context = { isInternalAdmin: isAdmin, params }
      isDisabled = tab.isDisabled(context)
    }

    // Replace route parameters with actual values
    const path = tab.path.replace(
      /:(\w+)/g,
      (_, param) => params?.[param] ?? `:${param}`
    )

    filteredTabs.push({
      path,
      title: tab.title,
      isDisabled,
    })
  }

  return filteredTabs
}
