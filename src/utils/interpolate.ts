type InterpolationMap = Record<string, any>

const isValidKey = (key: string): boolean => {
  // Prevent prototype pollution by blocking dangerous keys
  const dangerousKeys = ['__proto__', 'constructor', 'prototype']
  return !dangerousKeys.includes(key.toLowerCase())
}

const safeAccess = (obj: any, key: string): any => {
  if (!isValid<PERSON><PERSON>(key) || obj == null || typeof obj !== 'object') {
    return undefined
  }
  return obj[key]
}

export const interpolate = (
  template: string,
  map: InterpolationMap,
  fallback?: any
): string => {
  if (!template || typeof template !== 'string') {
    return String(template || '')
  }

  if (!map || typeof map !== 'object') {
    return template
  }

  return template.replace(/\$\{[^}]+\}/g, (match) => {
    const path = match.slice(2, -1).trim()

    if (!path) {
      return fallback ?? match
    }

    const keys = path.split('.')
    let result = map

    for (const key of keys) {
      if (!isVali<PERSON><PERSON><PERSON>(key)) {
        return fallback ?? match
      }
      result = safeAccess(result, key)
      if (result === undefined) {
        break
      }
    }

    return result !== undefined ? String(result) : (fallback ?? match)
  })
}
