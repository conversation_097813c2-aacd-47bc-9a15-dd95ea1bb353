import { describe, it, expect } from 'vitest'
import { isSubPathOf } from './tab-loader'

describe('tab-loader', () => {
  describe('isSubPathOf', () => {
    it('should return true for valid sub-paths', () => {
      expect(
        isSubPathOf('/account/subscription/create', '/account/subscription')
      ).toBe(true)
      expect(
        isSubPathOf(
          '/account/subscription/:subscriptionId',
          '/account/subscription'
        )
      ).toBe(true)
      expect(
        isSubPathOf('/account/subscription/users', '/account/subscription')
      ).toBe(true)
    })

    it('should return false for same paths', () => {
      expect(
        isSubPathOf('/account/subscription', '/account/subscription')
      ).toBe(false)
    })

    it('should return false for unrelated paths', () => {
      expect(isSubPathOf('/account/profile', '/account/subscription')).toBe(
        false
      )
      expect(isSubPathOf('/manage/devices', '/account/subscription')).toBe(
        false
      )
    })

    it('should return false when child is shorter than parent', () => {
      expect(isSubPathOf('/account', '/account/subscription')).toBe(false)
    })

    it('should handle paths with parameters correctly', () => {
      expect(
        isSubPathOf(
          '/manage/device-templates/:id/capabilities',
          '/manage/device-templates/:id'
        )
      ).toBe(true)
      expect(
        isSubPathOf('/manage/device-templates/:id', '/manage/device-templates')
      ).toBe(true)
    })

    it('should handle trailing slashes', () => {
      expect(
        isSubPathOf('/account/subscription/create/', '/account/subscription/')
      ).toBe(true)
      expect(
        isSubPathOf('/account/subscription/create', '/account/subscription/')
      ).toBe(true)
      expect(
        isSubPathOf('/account/subscription/create/', '/account/subscription')
      ).toBe(true)
    })
  })
})
